{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptors } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { importProvidersFrom } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n// Routes\nconst routes = [{\n  path: '',\n  loadComponent: () => import('./app/pages/home/<USER>').then(m => m.HomeComponent)\n}, {\n  path: '**',\n  redirectTo: ''\n}];\n// HTTP Interceptors\nconst authInterceptor = (req, next) => {\n  const token = localStorage.getItem('onlywomans_token');\n  if (token) {\n    req = req.clone({\n      setHeaders: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  return next(req);\n};\nconst errorInterceptor = (req, next) => {\n  return next(req);\n};\nbootstrapApplication(AppComponent, {\n  providers: [provideRouter(routes), provideHttpClient(withInterceptors([authInterceptor, errorInterceptor])), provideAnimations(), importProvidersFrom(CommonModule)]\n}).catch(err => console.error(err));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}