/* OnlyWomans Shop Page Styles */

.ow-header {
  --background: linear-gradient(135deg, var(--ow-rose-500) 0%, var(--ow-purple-500) 100%);
  --color: white;
  box-shadow: var(--ow-shadow-lg);
}

.ow-toolbar {
  --background: transparent;
  --color: white;
  
  ion-button {
    --color: white;
    position: relative;
    
    .cart-badge {
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 0.6rem;
      min-width: 16px;
      height: 16px;
    }
  }
}

.ow-content {
  --background: linear-gradient(135deg, var(--ow-rose-50) 0%, var(--ow-purple-50) 100%);
}

/* Search Section */
.search-section {
  padding: var(--ow-space-md);
  
  .ow-search {
    --background: white;
    --border-radius: var(--ow-radius-lg);
    --box-shadow: var(--ow-shadow-md);
    --placeholder-color: var(--ion-color-medium);
    --icon-color: var(--ion-color-primary);
  }
}

/* Categories Section */
.categories-section {
  padding: 0 var(--ow-space-md) var(--ow-space-md);
  
  .ow-segment {
    --background: white;
    border-radius: var(--ow-radius-lg);
    box-shadow: var(--ow-shadow-sm);
    padding: 4px;
    
    ion-segment-button {
      --color: var(--ion-color-medium);
      --color-checked: var(--ion-color-primary);
      --background-checked: var(--ow-rose-100);
      --border-radius: var(--ow-radius-md);
      margin: 2px;
      font-size: 0.85rem;
      font-weight: 500;
      
      &.active {
        --color: var(--ion-color-primary);
        --background: var(--ow-rose-100);
      }
    }
  }
}

/* Products Section */
.products-section {
  padding: 0 var(--ow-space-md);
  
  .products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--ow-space-md);
    
    .product-card {
      background: white;
      border-radius: var(--ow-radius-lg);
      overflow: hidden;
      box-shadow: var(--ow-shadow-sm);
      transition: all 0.3s ease;
      
      &:active {
        transform: translateY(-2px);
        box-shadow: var(--ow-shadow-lg);
      }
      
      .product-image-container {
        position: relative;
        height: 140px;
        overflow: hidden;
        
        .product-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .wishlist-btn {
          position: absolute;
          top: var(--ow-space-sm);
          right: var(--ow-space-sm);
          --background: rgba(255, 255, 255, 0.9);
          --color: var(--ion-color-primary);
          --border-radius: var(--ow-radius-full);
          width: 32px;
          height: 32px;
        }
      }
      
      .product-info {
        padding: var(--ow-space-md);
        
        .product-name {
          margin: 0 0 var(--ow-space-xs) 0;
          color: var(--ion-color-dark);
          font-size: 0.9rem;
          line-height: 1.3;
        }
        
        .product-rating {
          display: flex;
          align-items: center;
          margin-bottom: var(--ow-space-sm);
          
          .star-icon {
            color: var(--ow-gold-500);
            font-size: 0.8rem;
            margin-right: 2px;
          }
          
          .rating-value {
            font-size: 0.8rem;
            font-weight: 500;
            color: var(--ion-color-dark);
            margin-right: 4px;
          }
          
          .rating-count {
            font-size: 0.75rem;
            color: var(--ion-color-medium);
          }
        }
        
        .product-price {
          margin-bottom: var(--ow-space-md);
          
          .current-price {
            font-size: 1rem;
            font-weight: 600;
            margin-right: var(--ow-space-sm);
          }
          
          .original-price {
            font-size: 0.85rem;
            color: var(--ion-color-medium);
            text-decoration: line-through;
          }
        }
        
        .add-to-cart-btn {
          width: 100%;
          --border-radius: var(--ow-radius-md);
          font-size: 0.8rem;
        }
      }
    }
  }
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--ow-space-3xl) var(--ow-space-md);
  
  .empty-icon {
    font-size: 4rem;
    color: var(--ion-color-medium);
    margin-bottom: var(--ow-space-md);
  }
  
  h3 {
    margin: 0 0 var(--ow-space-sm) 0;
    color: var(--ion-color-dark);
  }
  
  p {
    color: var(--ion-color-medium);
    margin: 0;
  }
}

/* Bottom Spacing */
.bottom-spacing {
  height: var(--ow-space-xl);
}
