<ion-header [translucent]="true" class="ow-header">
  <ion-toolbar class="ow-toolbar">
    <ion-title class="ow-heading">Shop</ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear">
        <ion-icon name="filter" class="ow-icon"></ion-icon>
      </ion-button>
      <ion-button fill="clear">
        <ion-icon name="bag" class="ow-icon"></ion-icon>
        <ion-badge color="primary" class="cart-badge">2</ion-badge>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ow-content">
  <!-- Search Bar -->
  <div class="search-section">
    <ion-searchbar 
      placeholder="Search products..." 
      class="ow-search"
      (ionInput)="onSearch($event)">
    </ion-searchbar>
  </div>

  <!-- Categories Filter -->
  <div class="categories-section">
    <ion-segment 
      scrollable="true" 
      class="ow-segment"
      value="all">
      <ion-segment-button 
        *ngFor="let category of categories"
        [value]="category.name.toLowerCase()"
        [class.active]="category.active"
        (click)="onCategorySelect(category)">
        <ion-label>{{ category.name }}</ion-label>
      </ion-segment-button>
    </ion-segment>
  </div>

  <!-- Products Grid -->
  <div class="products-section">
    <div class="products-grid">
      <div 
        *ngFor="let product of filteredProducts" 
        class="product-card ow-product-card"
        (click)="onProductClick(product)">
        
        <div class="product-image-container">
          <img [src]="product.image" [alt]="product.name" class="product-image">
          <ion-button 
            fill="clear" 
            class="wishlist-btn"
            (click)="addToWishlist(product, $event)">
            <ion-icon name="heart-outline" class="ow-icon"></ion-icon>
          </ion-button>
        </div>
        
        <div class="product-info">
          <h4 class="product-name ow-heading-sm">{{ product.name }}</h4>
          <div class="product-rating">
            <ion-icon name="star" class="star-icon"></ion-icon>
            <span class="rating-value">{{ product.rating }}</span>
            <span class="rating-count">({{ product.reviews }})</span>
          </div>
          <div class="product-price">
            <span class="current-price ow-text-primary">₹{{ product.price }}</span>
            <span class="original-price">₹{{ product.originalPrice }}</span>
          </div>
          <ion-button 
            fill="solid" 
            size="small" 
            class="ow-btn ow-btn-primary add-to-cart-btn"
            (click)="addToCart(product, $event)">
            Add to Cart
          </ion-button>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="filteredProducts.length === 0" class="empty-state">
    <ion-icon name="search" class="empty-icon"></ion-icon>
    <h3 class="ow-heading-md">No products found</h3>
    <p>Try adjusting your search or filters</p>
  </div>

  <!-- Bottom Spacing -->
  <div class="bottom-spacing"></div>
</ion-content>
