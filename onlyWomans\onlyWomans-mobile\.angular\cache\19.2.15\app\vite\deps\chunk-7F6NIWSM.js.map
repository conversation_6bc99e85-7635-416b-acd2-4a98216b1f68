{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/theme-DiVJyqlX.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n  return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n  return typeof color === 'string' && color.length > 0 ? Object.assign({\n    'ion-color': true,\n    [`ion-color-${color}`]: true\n  }, cssClassMap) : cssClassMap;\n};\nconst getClassList = classes => {\n  if (classes !== undefined) {\n    const array = Array.isArray(classes) ? classes : classes.split(' ');\n    return array.filter(c => c != null).map(c => c.trim()).filter(c => c !== '');\n  }\n  return [];\n};\nconst getClassMap = classes => {\n  const map = {};\n  getClassList(classes).forEach(c => map[c] = true);\n  return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n  if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n    const router = document.querySelector('ion-router');\n    if (router) {\n      if (ev != null) {\n        ev.preventDefault();\n      }\n      return router.push(url, direction, animation);\n    }\n  }\n  return false;\n};\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };"], "mappings": ";;;;;AAGA,IAAM,cAAc,CAAC,UAAU,OAAO;AACpC,SAAO,GAAG,QAAQ,QAAQ,MAAM;AAClC;AAIA,IAAM,qBAAqB,CAAC,OAAO,gBAAgB;AACjD,SAAO,OAAO,UAAU,YAAY,MAAM,SAAS,IAAI,OAAO,OAAO;AAAA,IACnE,aAAa;AAAA,IACb,CAAC,aAAa,KAAK,EAAE,GAAG;AAAA,EAC1B,GAAG,WAAW,IAAI;AACpB;AACA,IAAM,eAAe,aAAW;AAC9B,MAAI,YAAY,QAAW;AACzB,UAAM,QAAQ,MAAM,QAAQ,OAAO,IAAI,UAAU,QAAQ,MAAM,GAAG;AAClE,WAAO,MAAM,OAAO,OAAK,KAAK,IAAI,EAAE,IAAI,OAAK,EAAE,KAAK,CAAC,EAAE,OAAO,OAAK,MAAM,EAAE;AAAA,EAC7E;AACA,SAAO,CAAC;AACV;AACA,IAAM,cAAc,aAAW;AAC7B,QAAM,MAAM,CAAC;AACb,eAAa,OAAO,EAAE,QAAQ,OAAK,IAAI,CAAC,IAAI,IAAI;AAChD,SAAO;AACT;AACA,IAAM,SAAS;AACf,IAAM,UAAU,CAAO,KAAK,IAAI,WAAW,cAAc;AACvD,MAAI,OAAO,QAAQ,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,KAAK,GAAG,GAAG;AACtD,UAAM,SAAS,SAAS,cAAc,YAAY;AAClD,QAAI,QAAQ;AACV,UAAI,MAAM,MAAM;AACd,WAAG,eAAe;AAAA,MACpB;AACA,aAAO,OAAO,KAAK,KAAK,WAAW,SAAS;AAAA,IAC9C;AAAA,EACF;AACA,SAAO;AACT;", "names": []}