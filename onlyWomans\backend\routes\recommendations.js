const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');

// @route   GET /api/recommendations
// @desc    Get personalized recommendations
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    // Mock recommendations
    const recommendations = [
      {
        id: 1,
        name: 'Recommended for You',
        products: [
          { id: 1, name: 'Floral Dress', price: 2499, image: '/assets/products/dress1.jpg' }
        ]
      }
    ];
    
    res.json({
      success: true,
      data: recommendations
    });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Server Error' });
  }
});

module.exports = router;
