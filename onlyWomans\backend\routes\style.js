const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');

// @route   GET /api/style/guides
// @desc    Get style guides
// @access  Public
router.get('/guides', async (req, res) => {
  try {
    const guides = [
      {
        id: 1,
        title: 'How to Style Ethnic Wear',
        category: 'ethnic',
        image: '/assets/style/ethnic-guide.jpg',
        content: 'Complete guide to styling traditional Indian wear...',
        tags: ['saree', 'lehenga', 'styling'],
        readTime: '8 min'
      },
      {
        id: 2,
        title: 'Western Wear for Office',
        category: 'western',
        image: '/assets/style/office-wear.jpg',
        content: 'Professional western outfits for working women...',
        tags: ['office', 'formal', 'professional'],
        readTime: '6 min'
      }
    ];
    
    res.json({
      success: true,
      data: guides
    });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Server Error' });
  }
});

// @route   POST /api/style/quiz
// @desc    Style quiz for personalized recommendations
// @access  Private
router.post('/quiz', auth, async (req, res) => {
  try {
    const { answers } = req.body;
    
    // Mock style analysis
    const styleProfile = {
      primaryStyle: 'Elegant Classic',
      secondaryStyle: 'Bohemian Chic',
      colorPalette: ['Navy', 'Cream', 'Rose Gold'],
      recommendations: [
        'Try more structured blazers',
        'Experiment with statement jewelry',
        'Add more neutral tones to your wardrobe'
      ]
    };
    
    res.json({
      success: true,
      data: styleProfile
    });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Server Error' });
  }
});

module.exports = router;
