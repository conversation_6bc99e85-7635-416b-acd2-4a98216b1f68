{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/spinner-configs-D4RIp70E.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst spinners = {\n  bubbles: {\n    dur: 1000,\n    circles: 9,\n    fn: (dur, index, total) => {\n      const animationDelay = `${dur * index / total - dur}ms`;\n      const angle = 2 * Math.PI * index / total;\n      return {\n        r: 5,\n        style: {\n          top: `${32 * Math.sin(angle)}%`,\n          left: `${32 * Math.cos(angle)}%`,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  circles: {\n    dur: 1000,\n    circles: 8,\n    fn: (dur, index, total) => {\n      const step = index / total;\n      const animationDelay = `${dur * step - dur}ms`;\n      const angle = 2 * Math.PI * step;\n      return {\n        r: 5,\n        style: {\n          top: `${32 * Math.sin(angle)}%`,\n          left: `${32 * Math.cos(angle)}%`,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  circular: {\n    dur: 1400,\n    elmDuration: true,\n    circles: 1,\n    fn: () => {\n      return {\n        r: 20,\n        cx: 48,\n        cy: 48,\n        fill: 'none',\n        viewBox: '24 24 48 48',\n        transform: 'translate(0,0)',\n        style: {}\n      };\n    }\n  },\n  crescent: {\n    dur: 750,\n    circles: 1,\n    fn: () => {\n      return {\n        r: 26,\n        style: {}\n      };\n    }\n  },\n  dots: {\n    dur: 750,\n    circles: 3,\n    fn: (_, index) => {\n      const animationDelay = -(110 * index) + 'ms';\n      return {\n        r: 6,\n        style: {\n          left: `${32 - 32 * index}%`,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  lines: {\n    dur: 1000,\n    lines: 8,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${360 / total * index + (index < total / 2 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 14,\n        y2: 26,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  'lines-small': {\n    dur: 1000,\n    lines: 8,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${360 / total * index + (index < total / 2 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 12,\n        y2: 20,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  'lines-sharp': {\n    dur: 1000,\n    lines: 12,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${30 * index + (index < 6 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 17,\n        y2: 29,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  'lines-sharp-small': {\n    dur: 1000,\n    lines: 12,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${30 * index + (index < 6 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 12,\n        y2: 20,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  }\n};\nconst SPINNERS = spinners;\nexport { SPINNERS as S };"], "mappings": ";AAGA,IAAM,WAAW;AAAA,EACf,SAAS;AAAA,IACP,KAAK;AAAA,IACL,SAAS;AAAA,IACT,IAAI,CAAC,KAAK,OAAO,UAAU;AACzB,YAAM,iBAAiB,GAAG,MAAM,QAAQ,QAAQ,GAAG;AACnD,YAAM,QAAQ,IAAI,KAAK,KAAK,QAAQ;AACpC,aAAO;AAAA,QACL,GAAG;AAAA,QACH,OAAO;AAAA,UACL,KAAK,GAAG,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,UAC5B,MAAM,GAAG,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,UAC7B,mBAAmB;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,IACL,SAAS;AAAA,IACT,IAAI,CAAC,KAAK,OAAO,UAAU;AACzB,YAAM,OAAO,QAAQ;AACrB,YAAM,iBAAiB,GAAG,MAAM,OAAO,GAAG;AAC1C,YAAM,QAAQ,IAAI,KAAK,KAAK;AAC5B,aAAO;AAAA,QACL,GAAG;AAAA,QACH,OAAO;AAAA,UACL,KAAK,GAAG,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,UAC5B,MAAM,GAAG,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,UAC7B,mBAAmB;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,aAAa;AAAA,IACb,SAAS;AAAA,IACT,IAAI,MAAM;AACR,aAAO;AAAA,QACL,GAAG;AAAA,QACH,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,WAAW;AAAA,QACX,OAAO,CAAC;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,SAAS;AAAA,IACT,IAAI,MAAM;AACR,aAAO;AAAA,QACL,GAAG;AAAA,QACH,OAAO,CAAC;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ,KAAK;AAAA,IACL,SAAS;AAAA,IACT,IAAI,CAAC,GAAG,UAAU;AAChB,YAAM,iBAAiB,EAAE,MAAM,SAAS;AACxC,aAAO;AAAA,QACL,GAAG;AAAA,QACH,OAAO;AAAA,UACL,MAAM,GAAG,KAAK,KAAK,KAAK;AAAA,UACxB,mBAAmB;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,IAAI,CAAC,KAAK,OAAO,UAAU;AACzB,YAAM,YAAY,UAAU,MAAM,QAAQ,SAAS,QAAQ,QAAQ,IAAI,MAAM,KAAK;AAClF,YAAM,iBAAiB,GAAG,MAAM,QAAQ,QAAQ,GAAG;AACnD,aAAO;AAAA,QACL,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,OAAO;AAAA,UACL;AAAA,UACA,mBAAmB;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe;AAAA,IACb,KAAK;AAAA,IACL,OAAO;AAAA,IACP,IAAI,CAAC,KAAK,OAAO,UAAU;AACzB,YAAM,YAAY,UAAU,MAAM,QAAQ,SAAS,QAAQ,QAAQ,IAAI,MAAM,KAAK;AAClF,YAAM,iBAAiB,GAAG,MAAM,QAAQ,QAAQ,GAAG;AACnD,aAAO;AAAA,QACL,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,OAAO;AAAA,UACL;AAAA,UACA,mBAAmB;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe;AAAA,IACb,KAAK;AAAA,IACL,OAAO;AAAA,IACP,IAAI,CAAC,KAAK,OAAO,UAAU;AACzB,YAAM,YAAY,UAAU,KAAK,SAAS,QAAQ,IAAI,MAAM,KAAK;AACjE,YAAM,iBAAiB,GAAG,MAAM,QAAQ,QAAQ,GAAG;AACnD,aAAO;AAAA,QACL,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,OAAO;AAAA,UACL;AAAA,UACA,mBAAmB;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,qBAAqB;AAAA,IACnB,KAAK;AAAA,IACL,OAAO;AAAA,IACP,IAAI,CAAC,KAAK,OAAO,UAAU;AACzB,YAAM,YAAY,UAAU,KAAK,SAAS,QAAQ,IAAI,MAAM,KAAK;AACjE,YAAM,iBAAiB,GAAG,MAAM,QAAQ,QAAQ,GAAG;AACnD,aAAO;AAAA,QACL,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,OAAO;AAAA,UACL;AAAA,UACA,mBAAmB;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,WAAW;", "names": []}