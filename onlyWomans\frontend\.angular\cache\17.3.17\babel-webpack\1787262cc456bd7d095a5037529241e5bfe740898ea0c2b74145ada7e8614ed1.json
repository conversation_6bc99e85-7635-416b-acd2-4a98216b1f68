{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function retryWhen(notifier) {\n  return operate((source, subscriber) => {\n    let innerSub;\n    let syncResub = false;\n    let errors$;\n    const subscribeForRetryWhen = () => {\n      innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, undefined, err => {\n        if (!errors$) {\n          errors$ = new Subject();\n          innerFrom(notifier(errors$)).subscribe(createOperatorSubscriber(subscriber, () => innerSub ? subscribeForRetryWhen() : syncResub = true));\n        }\n        if (errors$) {\n          errors$.next(err);\n        }\n      }));\n      if (syncResub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        syncResub = false;\n        subscribeForRetryWhen();\n      }\n    };\n    subscribeForRetryWhen();\n  });\n}\n//# sourceMappingURL=retryWhen.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}