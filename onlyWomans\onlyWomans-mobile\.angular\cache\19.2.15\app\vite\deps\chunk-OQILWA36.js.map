{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/haptic-DzAMWJuk.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor-CFERIeaU.js';\nvar ImpactStyle;\n(function (ImpactStyle) {\n  /**\n   * A collision between large, heavy user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Heavy\"] = \"HEAVY\";\n  /**\n   * A collision between moderately sized user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Medium\"] = \"MEDIUM\";\n  /**\n   * A collision between small, light user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Light\"] = \"LIGHT\";\n})(ImpactStyle || (ImpactStyle = {}));\nvar NotificationType;\n(function (NotificationType) {\n  /**\n   * A notification feedback type indicating that a task has completed successfully\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Success\"] = \"SUCCESS\";\n  /**\n   * A notification feedback type indicating that a task has produced a warning\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Warning\"] = \"WARNING\";\n  /**\n   * A notification feedback type indicating that a task has failed\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Error\"] = \"ERROR\";\n})(NotificationType || (NotificationType = {}));\nconst HapticEngine = {\n  getEngine() {\n    const capacitor = getCapacitor();\n    if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Haptics')) {\n      // Capacitor\n      return capacitor.Plugins.Haptics;\n    }\n    return undefined;\n  },\n  available() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return false;\n    }\n    const capacitor = getCapacitor();\n    /**\n     * Developers can manually import the\n     * Haptics plugin in their app which will cause\n     * getEngine to return the Haptics engine. However,\n     * the Haptics engine will throw an error if\n     * used in a web browser that does not support\n     * the Vibrate API. This check avoids that error\n     * if the browser does not support the Vibrate API.\n     */\n    if ((capacitor === null || capacitor === void 0 ? void 0 : capacitor.getPlatform()) === 'web') {\n      // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n      return typeof navigator !== 'undefined' && navigator.vibrate !== undefined;\n    }\n    return true;\n  },\n  impact(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.impact({\n      style: options.style\n    });\n  },\n  notification(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.notification({\n      type: options.type\n    });\n  },\n  selection() {\n    this.impact({\n      style: ImpactStyle.Light\n    });\n  },\n  selectionStart() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.selectionStart();\n  },\n  selectionChanged() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.selectionChanged();\n  },\n  selectionEnd() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.selectionEnd();\n  }\n};\n/**\n * Check to see if the Haptic Plugin is available\n * @return Returns `true` or false if the plugin is available\n */\nconst hapticAvailable = () => {\n  return HapticEngine.available();\n};\n/**\n * Trigger a selection changed haptic event. Good for one-time events\n * (not for gestures)\n */\nconst hapticSelection = () => {\n  hapticAvailable() && HapticEngine.selection();\n};\n/**\n * Tell the haptic engine that a gesture for a selection change is starting.\n */\nconst hapticSelectionStart = () => {\n  hapticAvailable() && HapticEngine.selectionStart();\n};\n/**\n * Tell the haptic engine that a selection changed during a gesture.\n */\nconst hapticSelectionChanged = () => {\n  hapticAvailable() && HapticEngine.selectionChanged();\n};\n/**\n * Tell the haptic engine we are done with a gesture. This needs to be\n * called lest resources are not properly recycled.\n */\nconst hapticSelectionEnd = () => {\n  hapticAvailable() && HapticEngine.selectionEnd();\n};\n/**\n * Use this to indicate success/failure/warning to the user.\n * options should be of the type `{ style: ImpactStyle.LIGHT }` (or `MEDIUM`/`HEAVY`)\n */\nconst hapticImpact = options => {\n  hapticAvailable() && HapticEngine.impact(options);\n};\nexport { ImpactStyle as I, hapticSelectionChanged as a, hapticSelectionStart as b, hapticSelection as c, hapticImpact as d, hapticSelectionEnd as h };"], "mappings": ";;;;;AAIA,IAAI;AAAA,CACH,SAAUA,cAAa;AAMtB,EAAAA,aAAY,OAAO,IAAI;AAMvB,EAAAA,aAAY,QAAQ,IAAI;AAMxB,EAAAA,aAAY,OAAO,IAAI;AACzB,GAAG,gBAAgB,cAAc,CAAC,EAAE;AACpC,IAAI;AAAA,CACH,SAAUC,mBAAkB;AAM3B,EAAAA,kBAAiB,SAAS,IAAI;AAM9B,EAAAA,kBAAiB,SAAS,IAAI;AAM9B,EAAAA,kBAAiB,OAAO,IAAI;AAC9B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAM,eAAe;AAAA,EACnB,YAAY;AACV,UAAM,YAAY,aAAa;AAC/B,QAAI,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,kBAAkB,SAAS,GAAG;AAEhG,aAAO,UAAU,QAAQ;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY;AACV,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,UAAM,YAAY,aAAa;AAU/B,SAAK,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,YAAY,OAAO,OAAO;AAE7F,aAAO,OAAO,cAAc,eAAe,UAAU,YAAY;AAAA,IACnE;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAAS;AACd,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,WAAO,OAAO;AAAA,MACZ,OAAO,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH;AAAA,EACA,aAAa,SAAS;AACpB,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,WAAO,aAAa;AAAA,MAClB,MAAM,QAAQ;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,YAAY;AACV,SAAK,OAAO;AAAA,MACV,OAAO,YAAY;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,WAAO,eAAe;AAAA,EACxB;AAAA,EACA,mBAAmB;AACjB,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,eAAe;AACb,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,WAAO,aAAa;AAAA,EACtB;AACF;AAKA,IAAM,kBAAkB,MAAM;AAC5B,SAAO,aAAa,UAAU;AAChC;AAKA,IAAM,kBAAkB,MAAM;AAC5B,kBAAgB,KAAK,aAAa,UAAU;AAC9C;AAIA,IAAM,uBAAuB,MAAM;AACjC,kBAAgB,KAAK,aAAa,eAAe;AACnD;AAIA,IAAM,yBAAyB,MAAM;AACnC,kBAAgB,KAAK,aAAa,iBAAiB;AACrD;AAKA,IAAM,qBAAqB,MAAM;AAC/B,kBAAgB,KAAK,aAAa,aAAa;AACjD;AAKA,IAAM,eAAe,aAAW;AAC9B,kBAAgB,KAAK,aAAa,OAAO,OAAO;AAClD;", "names": ["ImpactStyle", "NotificationType"]}