.ow-header {
  --background: linear-gradient(135deg, var(--ow-rose-500) 0%, var(--ow-purple-500) 100%);
  --color: white;
}

.ow-content {
  --background: linear-gradient(135deg, var(--ow-rose-50) 0%, var(--ow-purple-50) 100%);
}

.wishlist-content {
  padding: var(--ow-space-md);
}

.wishlist-item {
  display: flex;
  margin-bottom: var(--ow-space-md);
  padding: var(--ow-space-md);
  
  .item-image {
    width: 80px;
    height: 80px;
    border-radius: var(--ow-radius-md);
    overflow: hidden;
    margin-right: var(--ow-space-md);
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .item-details {
    flex: 1;
    
    .item-name {
      margin: 0 0 var(--ow-space-xs) 0;
    }
    
    .item-rating {
      display: flex;
      align-items: center;
      margin-bottom: var(--ow-space-sm);
      font-size: 0.8rem;
      
      .star-icon {
        color: var(--ow-gold-500);
        margin-right: 4px;
      }
    }
    
    .item-price {
      margin-bottom: var(--ow-space-md);
      
      .current-price {
        font-weight: 600;
        margin-right: var(--ow-space-sm);
      }
      
      .original-price {
        text-decoration: line-through;
        color: var(--ion-color-medium);
      }
    }
    
    .item-actions {
      display: flex;
      align-items: center;
      gap: var(--ow-space-sm);
      
      .remove-btn {
        --color: var(--ion-color-medium);
      }
    }
  }
}

.empty-wishlist {
  text-align: center;
  padding: var(--ow-space-3xl) var(--ow-space-md);
  
  .empty-icon {
    font-size: 4rem;
    color: var(--ion-color-medium);
    margin-bottom: var(--ow-space-md);
  }
  
  h3 {
    margin-bottom: var(--ow-space-sm);
  }
  
  p {
    color: var(--ion-color-medium);
    margin-bottom: var(--ow-space-lg);
  }
}
