{"ast": null, "code": "import { concatMap } from './concatMap';\nimport { isFunction } from '../util/isFunction';\nexport function concatMapTo(innerObservable, resultSelector) {\n  return isFunction(resultSelector) ? concatMap(() => innerObservable, resultSelector) : concatMap(() => innerObservable);\n}", "map": {"version": 3, "names": ["concatMap", "isFunction", "concatMapTo", "innerObservable", "resultSelector"], "sources": ["E:/Fahion/DFashion/onlyWomans/frontend/node_modules/rxjs/dist/esm/internal/operators/concatMapTo.js"], "sourcesContent": ["import { concatMap } from './concatMap';\nimport { isFunction } from '../util/isFunction';\nexport function concatMapTo(innerObservable, resultSelector) {\n    return isFunction(resultSelector) ? concatMap(() => innerObservable, resultSelector) : concatMap(() => innerObservable);\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAO,SAASC,WAAWA,CAACC,eAAe,EAAEC,cAAc,EAAE;EACzD,OAAOH,UAAU,CAACG,cAAc,CAAC,GAAGJ,SAAS,CAAC,MAAMG,eAAe,EAAEC,cAAc,CAAC,GAAGJ,SAAS,CAAC,MAAMG,eAAe,CAAC;AAC3H", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}