{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function isEmpty() {\n  return operate((source, subscriber) => {\n    source.subscribe(createOperatorSubscriber(subscriber, () => {\n      subscriber.next(false);\n      subscriber.complete();\n    }, () => {\n      subscriber.next(true);\n      subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "isEmpty", "source", "subscriber", "subscribe", "next", "complete"], "sources": ["E:/Fahion/DFashion/onlyWomans/frontend/node_modules/rxjs/dist/esm/internal/operators/isEmpty.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function isEmpty() {\n    return operate((source, subscriber) => {\n        source.subscribe(createOperatorSubscriber(subscriber, () => {\n            subscriber.next(false);\n            subscriber.complete();\n        }, () => {\n            subscriber.next(true);\n            subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,OAAOA,CAAA,EAAG;EACtB,OAAOF,OAAO,CAAC,CAACG,MAAM,EAAEC,UAAU,KAAK;IACnCD,MAAM,CAACE,SAAS,CAACJ,wBAAwB,CAACG,UAAU,EAAE,MAAM;MACxDA,UAAU,CAACE,IAAI,CAAC,KAAK,CAAC;MACtBF,UAAU,CAACG,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAE,MAAM;MACLH,UAAU,CAACE,IAAI,CAAC,IAAI,CAAC;MACrBF,UAAU,CAACG,QAAQ,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}