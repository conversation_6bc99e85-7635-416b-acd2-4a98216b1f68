{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-refresher_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as writeTask, r as registerInstance, d as createEvent, e as getIonMode, f as readTask, o as printIonError, h, j as Host, k as getElement, l as config } from './index-B_U9CtaY.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-hHmYLOfE.js';\nimport { I as ION_CONTENT_CLASS_SELECTOR, b as ION_CONTENT_ELEMENT_SELECTOR, p as printIonContentErrorMsg, g as getScrollElement } from './index-BlJTBdxG.js';\nimport { c as componentOnReady, t as transitionEndAsync, e as clamp, g as getElementRoot, r as raf } from './helpers-1O4D2b7y.js';\nimport { d as hapticImpact, I as ImpactStyle } from './haptic-DzAMWJuk.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-AaTyISnm.js';\nimport { h as arrowDown, i as caretBackSharp } from './index-BLV6ykCk.js';\nimport { S as SPINNERS } from './spinner-configs-D4RIp70E.js';\nimport './capacitor-CFERIeaU.js';\nimport './index-ZjP4CjeZ.js';\nconst getRefresherAnimationType = contentEl => {\n  const previousSibling = contentEl.previousElementSibling;\n  const hasHeader = previousSibling !== null && previousSibling.tagName === 'ION-HEADER';\n  return hasHeader ? 'translate' : 'scale';\n};\nconst createPullingAnimation = (type, pullingSpinner, refresherEl) => {\n  return type === 'scale' ? createScaleAnimation(pullingSpinner, refresherEl) : createTranslateAnimation(pullingSpinner, refresherEl);\n};\nconst createBaseAnimation = pullingRefresherIcon => {\n  const spinner = pullingRefresherIcon.querySelector('ion-spinner');\n  const circle = spinner.shadowRoot.querySelector('circle');\n  const spinnerArrowContainer = pullingRefresherIcon.querySelector('.spinner-arrow-container');\n  const arrowContainer = pullingRefresherIcon.querySelector('.arrow-container');\n  const arrow = arrowContainer ? arrowContainer.querySelector('ion-icon') : null;\n  const baseAnimation = createAnimation().duration(1000).easing('ease-out');\n  const spinnerArrowContainerAnimation = createAnimation().addElement(spinnerArrowContainer).keyframes([{\n    offset: 0,\n    opacity: '0.3'\n  }, {\n    offset: 0.45,\n    opacity: '0.3'\n  }, {\n    offset: 0.55,\n    opacity: '1'\n  }, {\n    offset: 1,\n    opacity: '1'\n  }]);\n  const circleInnerAnimation = createAnimation().addElement(circle).keyframes([{\n    offset: 0,\n    strokeDasharray: '1px, 200px'\n  }, {\n    offset: 0.2,\n    strokeDasharray: '1px, 200px'\n  }, {\n    offset: 0.55,\n    strokeDasharray: '100px, 200px'\n  }, {\n    offset: 1,\n    strokeDasharray: '100px, 200px'\n  }]);\n  const circleOuterAnimation = createAnimation().addElement(spinner).keyframes([{\n    offset: 0,\n    transform: 'rotate(-90deg)'\n  }, {\n    offset: 1,\n    transform: 'rotate(210deg)'\n  }]);\n  /**\n   * Only add arrow animation if present\n   * this allows users to customize the spinners\n   * without errors being thrown\n   */\n  if (arrowContainer && arrow) {\n    const arrowContainerAnimation = createAnimation().addElement(arrowContainer).keyframes([{\n      offset: 0,\n      transform: 'rotate(0deg)'\n    }, {\n      offset: 0.3,\n      transform: 'rotate(0deg)'\n    }, {\n      offset: 0.55,\n      transform: 'rotate(280deg)'\n    }, {\n      offset: 1,\n      transform: 'rotate(400deg)'\n    }]);\n    const arrowAnimation = createAnimation().addElement(arrow).keyframes([{\n      offset: 0,\n      transform: 'translateX(2px) scale(0)'\n    }, {\n      offset: 0.3,\n      transform: 'translateX(2px) scale(0)'\n    }, {\n      offset: 0.55,\n      transform: 'translateX(-1.5px) scale(1)'\n    }, {\n      offset: 1,\n      transform: 'translateX(-1.5px) scale(1)'\n    }]);\n    baseAnimation.addAnimation([arrowContainerAnimation, arrowAnimation]);\n  }\n  return baseAnimation.addAnimation([spinnerArrowContainerAnimation, circleInnerAnimation, circleOuterAnimation]);\n};\nconst createScaleAnimation = (pullingRefresherIcon, refresherEl) => {\n  /**\n   * Do not take the height of the refresher icon\n   * because at this point the DOM has not updated,\n   * so the refresher icon is still hidden with\n   * display: none.\n   * The `ion-refresher` container height\n   * is roughly the amount we need to offset\n   * the icon by when pulling down.\n   */\n  const height = refresherEl.clientHeight;\n  const spinnerAnimation = createAnimation().addElement(pullingRefresherIcon).keyframes([{\n    offset: 0,\n    transform: `scale(0) translateY(-${height}px)`\n  }, {\n    offset: 1,\n    transform: 'scale(1) translateY(100px)'\n  }]);\n  return createBaseAnimation(pullingRefresherIcon).addAnimation([spinnerAnimation]);\n};\nconst createTranslateAnimation = (pullingRefresherIcon, refresherEl) => {\n  /**\n   * Do not take the height of the refresher icon\n   * because at this point the DOM has not updated,\n   * so the refresher icon is still hidden with\n   * display: none.\n   * The `ion-refresher` container height\n   * is roughly the amount we need to offset\n   * the icon by when pulling down.\n   */\n  const height = refresherEl.clientHeight;\n  const spinnerAnimation = createAnimation().addElement(pullingRefresherIcon).keyframes([{\n    offset: 0,\n    transform: `translateY(-${height}px)`\n  }, {\n    offset: 1,\n    transform: 'translateY(100px)'\n  }]);\n  return createBaseAnimation(pullingRefresherIcon).addAnimation([spinnerAnimation]);\n};\nconst createSnapBackAnimation = pullingRefresherIcon => {\n  return createAnimation().duration(125).addElement(pullingRefresherIcon).fromTo('transform', 'translateY(var(--ion-pulling-refresher-translate, 100px))', 'translateY(0px)');\n};\n// iOS Native Refresher\n// -----------------------------\nconst setSpinnerOpacity = (spinner, opacity) => {\n  spinner.style.setProperty('opacity', opacity.toString());\n};\nconst handleScrollWhilePulling = (ticks, numTicks, pullAmount) => {\n  const max = 1;\n  writeTask(() => {\n    ticks.forEach((el, i) => {\n      /**\n       * Compute the opacity of each tick\n       * mark as a percentage of the pullAmount\n       * offset by max / numTicks so\n       * the tick marks are shown staggered.\n       */\n      const min = i * (max / numTicks);\n      const range = max - min;\n      const start = pullAmount - min;\n      const progression = clamp(0, start / range, 1);\n      el.style.setProperty('opacity', progression.toString());\n    });\n  });\n};\nconst handleScrollWhileRefreshing = (spinner, lastVelocityY) => {\n  writeTask(() => {\n    // If user pulls down quickly, the spinner should spin faster\n    spinner.style.setProperty('--refreshing-rotation-duration', lastVelocityY >= 1.0 ? '0.5s' : '2s');\n    spinner.style.setProperty('opacity', '1');\n  });\n};\nconst translateElement = (el, value, duration = 200) => {\n  if (!el) {\n    return Promise.resolve();\n  }\n  const trans = transitionEndAsync(el, duration);\n  writeTask(() => {\n    el.style.setProperty('transition', `${duration}ms all ease-out`);\n    if (value === undefined) {\n      el.style.removeProperty('transform');\n    } else {\n      el.style.setProperty('transform', `translate3d(0px, ${value}, 0px)`);\n    }\n  });\n  return trans;\n};\n// Utils\n// -----------------------------\n/**\n * In order to use the native iOS refresher the device must support rubber band scrolling.\n * As part of this, we need to exclude Desktop Safari because it has a slightly different rubber band effect that is not compatible with the native refresher in Ionic.\n *\n * We also need to be careful not to include devices that spoof their user agent.\n * For example, when using iOS emulation in Chrome the user agent will be spoofed such that\n * navigator.maxTouchPointer > 0. To work around this,\n * we check to see if the apple-pay-logo is supported as a named image which is only\n * true on Apple devices.\n *\n * We previously checked referencEl.style.webkitOverflowScrolling to explicitly check\n * for rubber band support. However, this property was removed on iPadOS and it's possible\n * that this will be removed on iOS in the future too.\n *\n */\nconst supportsRubberBandScrolling = () => {\n  return navigator.maxTouchPoints > 0 && CSS.supports('background: -webkit-named-image(apple-pay-logo-black)');\n};\nconst shouldUseNativeRefresher = async (referenceEl, mode) => {\n  const refresherContent = referenceEl.querySelector('ion-refresher-content');\n  if (!refresherContent) {\n    return Promise.resolve(false);\n  }\n  await new Promise(resolve => componentOnReady(refresherContent, resolve));\n  const pullingSpinner = referenceEl.querySelector('ion-refresher-content .refresher-pulling ion-spinner');\n  const refreshingSpinner = referenceEl.querySelector('ion-refresher-content .refresher-refreshing ion-spinner');\n  return pullingSpinner !== null && refreshingSpinner !== null && (mode === 'ios' && supportsRubberBandScrolling() || mode === 'md');\n};\nconst refresherIosCss = \"ion-refresher{top:0;display:none;position:absolute;width:100%;height:60px;pointer-events:none;z-index:-1}ion-refresher{inset-inline-start:0}ion-refresher.refresher-active{display:block}ion-refresher-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;height:100%}.refresher-pulling,.refresher-refreshing{display:none;width:100%}.refresher-pulling-icon,.refresher-refreshing-icon{-webkit-transform-origin:center;transform-origin:center;-webkit-transition:200ms;transition:200ms;font-size:30px;text-align:center}:host-context([dir=rtl]) .refresher-pulling-icon,:host-context([dir=rtl]) .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] .refresher-pulling-icon,[dir=rtl] .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){.refresher-pulling-icon:dir(rtl),.refresher-refreshing-icon:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}.refresher-pulling-text,.refresher-refreshing-text{font-size:16px;text-align:center}ion-refresher-content .arrow-container{display:none}.refresher-pulling ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.refresher-refreshing ion-refresher-content .refresher-refreshing{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-completing ion-refresher-content .refresher-refreshing{display:block}.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-native .refresher-pulling-text,.refresher-native .refresher-refreshing-text{display:none}.refresher-ios .refresher-pulling-icon,.refresher-ios .refresher-refreshing-icon{color:var(--ion-text-color, #000)}.refresher-ios .refresher-pulling-text,.refresher-ios .refresher-refreshing-text{color:var(--ion-text-color, #000)}.refresher-ios .refresher-refreshing .spinner-lines-ios line,.refresher-ios .refresher-refreshing .spinner-lines-small-ios line,.refresher-ios .refresher-refreshing .spinner-crescent circle{stroke:var(--ion-text-color, #000)}.refresher-ios .refresher-refreshing .spinner-bubbles circle,.refresher-ios .refresher-refreshing .spinner-circles circle,.refresher-ios .refresher-refreshing .spinner-dots circle{fill:var(--ion-text-color, #000)}ion-refresher.refresher-native{display:block;z-index:1}ion-refresher.refresher-native ion-spinner{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}.refresher-native .refresher-refreshing ion-spinner{--refreshing-rotation-duration:2s;display:none;-webkit-animation:var(--refreshing-rotation-duration) ease-out refresher-rotate forwards;animation:var(--refreshing-rotation-duration) ease-out refresher-rotate forwards}.refresher-native .refresher-refreshing{display:none;-webkit-animation:250ms linear refresher-pop forwards;animation:250ms linear refresher-pop forwards}.refresher-native ion-spinner{width:32px;height:32px;color:var(--ion-color-step-450, var(--ion-background-color-step-450, #747577))}.refresher-native.refresher-refreshing .refresher-pulling ion-spinner,.refresher-native.refresher-completing .refresher-pulling ion-spinner{display:none}.refresher-native.refresher-refreshing .refresher-refreshing ion-spinner,.refresher-native.refresher-completing .refresher-refreshing ion-spinner{display:block}.refresher-native.refresher-pulling .refresher-pulling ion-spinner{display:block}.refresher-native.refresher-pulling .refresher-refreshing ion-spinner{display:none}.refresher-native.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0) rotate(180deg);transform:scale(0) rotate(180deg);-webkit-transition:300ms;transition:300ms}@-webkit-keyframes refresher-pop{0%{-webkit-transform:scale(1);transform:scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}50%{-webkit-transform:scale(1.2);transform:scale(1.2);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}100%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes refresher-pop{0%{-webkit-transform:scale(1);transform:scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}50%{-webkit-transform:scale(1.2);transform:scale(1.2);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}100%{-webkit-transform:scale(1);transform:scale(1)}}@-webkit-keyframes refresher-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}@keyframes refresher-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}\";\nconst refresherMdCss = \"ion-refresher{top:0;display:none;position:absolute;width:100%;height:60px;pointer-events:none;z-index:-1}ion-refresher{inset-inline-start:0}ion-refresher.refresher-active{display:block}ion-refresher-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;height:100%}.refresher-pulling,.refresher-refreshing{display:none;width:100%}.refresher-pulling-icon,.refresher-refreshing-icon{-webkit-transform-origin:center;transform-origin:center;-webkit-transition:200ms;transition:200ms;font-size:30px;text-align:center}:host-context([dir=rtl]) .refresher-pulling-icon,:host-context([dir=rtl]) .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] .refresher-pulling-icon,[dir=rtl] .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){.refresher-pulling-icon:dir(rtl),.refresher-refreshing-icon:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}.refresher-pulling-text,.refresher-refreshing-text{font-size:16px;text-align:center}ion-refresher-content .arrow-container{display:none}.refresher-pulling ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.refresher-refreshing ion-refresher-content .refresher-refreshing{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-completing ion-refresher-content .refresher-refreshing{display:block}.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-native .refresher-pulling-text,.refresher-native .refresher-refreshing-text{display:none}.refresher-md .refresher-pulling-icon,.refresher-md .refresher-refreshing-icon{color:var(--ion-text-color, #000)}.refresher-md .refresher-pulling-text,.refresher-md .refresher-refreshing-text{color:var(--ion-text-color, #000)}.refresher-md .refresher-refreshing .spinner-lines-md line,.refresher-md .refresher-refreshing .spinner-lines-small-md line,.refresher-md .refresher-refreshing .spinner-crescent circle{stroke:var(--ion-text-color, #000)}.refresher-md .refresher-refreshing .spinner-bubbles circle,.refresher-md .refresher-refreshing .spinner-circles circle,.refresher-md .refresher-refreshing .spinner-dots circle{fill:var(--ion-text-color, #000)}ion-refresher.refresher-native{display:block;z-index:1}ion-refresher.refresher-native ion-spinner{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;width:24px;height:24px;color:var(--ion-color-primary, #0054e9)}ion-refresher.refresher-native .spinner-arrow-container{display:inherit}ion-refresher.refresher-native .arrow-container{display:block;position:absolute;width:24px;height:24px}ion-refresher.refresher-native .arrow-container ion-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;left:0;right:0;bottom:-4px;position:absolute;color:var(--ion-color-primary, #0054e9);font-size:12px}ion-refresher.refresher-native.refresher-pulling ion-refresher-content .refresher-pulling,ion-refresher.refresher-native.refresher-ready ion-refresher-content .refresher-pulling{display:-ms-flexbox;display:flex}ion-refresher.refresher-native.refresher-refreshing ion-refresher-content .refresher-refreshing,ion-refresher.refresher-native.refresher-completing ion-refresher-content .refresher-refreshing,ion-refresher.refresher-native.refresher-cancelling ion-refresher-content .refresher-refreshing{display:-ms-flexbox;display:flex}ion-refresher.refresher-native .refresher-pulling-icon{-webkit-transform:translateY(calc(-100% - 10px));transform:translateY(calc(-100% - 10px))}ion-refresher.refresher-native .refresher-pulling-icon,ion-refresher.refresher-native .refresher-refreshing-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;border-radius:100%;-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;display:-ms-flexbox;display:flex;border:1px solid var(--ion-color-step-200, var(--ion-background-color-step-200, #ececec));background:var(--ion-color-step-250, var(--ion-background-color-step-250, #ffffff));-webkit-box-shadow:0px 1px 6px rgba(0, 0, 0, 0.1);box-shadow:0px 1px 6px rgba(0, 0, 0, 0.1)}\";\nconst Refresher = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionRefresh = createEvent(this, \"ionRefresh\", 7);\n    this.ionPull = createEvent(this, \"ionPull\", 7);\n    this.ionStart = createEvent(this, \"ionStart\", 7);\n    this.appliedStyles = false;\n    this.didStart = false;\n    this.progress = 0;\n    this.pointerDown = false;\n    this.needsCompletion = false;\n    this.didRefresh = false;\n    this.contentFullscreen = false;\n    this.lastVelocityY = 0;\n    this.animations = [];\n    this.nativeRefresher = false;\n    /**\n     * The current state which the refresher is in. The refresher's states include:\n     *\n     * - `inactive` - The refresher is not being pulled down or refreshing and is currently hidden.\n     * - `pulling` - The user is actively pulling down the refresher, but has not reached the point yet that if the user lets go, it'll refresh.\n     * - `cancelling` - The user pulled down the refresher and let go, but did not pull down far enough to kick off the `refreshing` state. After letting go, the refresher is in the `cancelling` state while it is closing, and will go back to the `inactive` state once closed.\n     * - `ready` - The user has pulled down the refresher far enough that if they let go, it'll begin the `refreshing` state.\n     * - `refreshing` - The refresher is actively waiting on the async operation to end. Once the refresh handler calls `complete()` it will begin the `completing` state.\n     * - `completing` - The `refreshing` state has finished and the refresher is in the way of closing itself. Once closed, the refresher will go back to the `inactive` state.\n     */\n    this.state = 1 /* RefresherState.Inactive */;\n    /**\n     * The minimum distance the user must pull down until the\n     * refresher will go into the `refreshing` state.\n     * Does not apply when the refresher content uses a spinner,\n     * enabling the native refresher.\n     */\n    this.pullMin = 60;\n    /**\n     * The maximum distance of the pull until the refresher\n     * will automatically go into the `refreshing` state.\n     * Defaults to the result of `pullMin + 60`.\n     * Does not apply when  the refresher content uses a spinner,\n     * enabling the native refresher.\n     */\n    this.pullMax = this.pullMin + 60;\n    /**\n     * Time it takes to close the refresher.\n     * Does not apply when the refresher content uses a spinner,\n     * enabling the native refresher.\n     */\n    this.closeDuration = '280ms';\n    /**\n     * Time it takes the refresher to snap back to the `refreshing` state.\n     * Does not apply when the refresher content uses a spinner,\n     * enabling the native refresher.\n     */\n    this.snapbackDuration = '280ms';\n    /**\n     * How much to multiply the pull speed by. To slow the pull animation down,\n     * pass a number less than `1`. To speed up the pull, pass a number greater\n     * than `1`. The default value is `1` which is equal to the speed of the cursor.\n     * If a negative value is passed in, the factor will be `1` instead.\n     *\n     * For example: If the value passed is `1.2` and the content is dragged by\n     * `10` pixels, instead of `10` pixels the content will be pulled by `12` pixels\n     * (an increase of 20 percent). If the value passed is `0.8`, the dragged amount\n     * will be `8` pixels, less than the amount the cursor has moved.\n     *\n     * Does not apply when the refresher content uses a spinner,\n     * enabling the native refresher.\n     */\n    this.pullFactor = 1;\n    /**\n     * If `true`, the refresher will be hidden.\n     */\n    this.disabled = false;\n  }\n  disabledChanged() {\n    if (this.gesture) {\n      this.gesture.enable(!this.disabled);\n    }\n  }\n  async checkNativeRefresher() {\n    const useNativeRefresher = await shouldUseNativeRefresher(this.el, getIonMode(this));\n    if (useNativeRefresher && !this.nativeRefresher) {\n      const contentEl = this.el.closest('ion-content');\n      this.setupNativeRefresher(contentEl);\n    } else if (!useNativeRefresher) {\n      this.destroyNativeRefresher();\n    }\n  }\n  destroyNativeRefresher() {\n    if (this.scrollEl && this.scrollListenerCallback) {\n      this.scrollEl.removeEventListener('scroll', this.scrollListenerCallback);\n      this.scrollListenerCallback = undefined;\n    }\n    this.nativeRefresher = false;\n  }\n  async resetNativeRefresher(el, state) {\n    this.state = state;\n    if (getIonMode(this) === 'ios') {\n      await translateElement(el, undefined, 300);\n    } else {\n      await transitionEndAsync(this.el.querySelector('.refresher-refreshing-icon'), 200);\n    }\n    this.didRefresh = false;\n    this.needsCompletion = false;\n    this.pointerDown = false;\n    this.animations.forEach(ani => ani.destroy());\n    this.animations = [];\n    this.progress = 0;\n    this.state = 1 /* RefresherState.Inactive */;\n  }\n  async setupiOSNativeRefresher(pullingSpinner, refreshingSpinner) {\n    this.elementToTransform = this.scrollEl;\n    const ticks = pullingSpinner.shadowRoot.querySelectorAll('svg');\n    let MAX_PULL = this.scrollEl.clientHeight * 0.16;\n    const NUM_TICKS = ticks.length;\n    writeTask(() => ticks.forEach(el => el.style.setProperty('animation', 'none')));\n    this.scrollListenerCallback = () => {\n      // If pointer is not on screen or refresher is not active, ignore scroll\n      if (!this.pointerDown && this.state === 1 /* RefresherState.Inactive */) {\n        return;\n      }\n      readTask(() => {\n        // PTR should only be active when overflow scrolling at the top\n        const scrollTop = this.scrollEl.scrollTop;\n        const refresherHeight = this.el.clientHeight;\n        if (scrollTop > 0) {\n          /**\n           * If refresher is refreshing and user tries to scroll\n           * progressively fade refresher out/in\n           */\n          if (this.state === 8 /* RefresherState.Refreshing */) {\n            const ratio = clamp(0, scrollTop / (refresherHeight * 0.5), 1);\n            writeTask(() => setSpinnerOpacity(refreshingSpinner, 1 - ratio));\n            return;\n          }\n          return;\n        }\n        if (this.pointerDown) {\n          if (!this.didStart) {\n            this.didStart = true;\n            this.ionStart.emit();\n          }\n          // emit \"pulling\" on every move\n          if (this.pointerDown) {\n            this.ionPull.emit();\n          }\n        }\n        /**\n         * We want to delay the start of this gesture by ~30px\n         * when initially pulling down so the refresher does not\n         * overlap with the content. But when letting go of the\n         * gesture before the refresher completes, we want the\n         * refresher tick marks to quickly fade out.\n         */\n        const offset = this.didStart ? 30 : 0;\n        const pullAmount = this.progress = clamp(0, (Math.abs(scrollTop) - offset) / MAX_PULL, 1);\n        const shouldShowRefreshingSpinner = this.state === 8 /* RefresherState.Refreshing */ || pullAmount === 1;\n        if (shouldShowRefreshingSpinner) {\n          if (this.pointerDown) {\n            handleScrollWhileRefreshing(refreshingSpinner, this.lastVelocityY);\n          }\n          if (!this.didRefresh) {\n            this.beginRefresh();\n            this.didRefresh = true;\n            hapticImpact({\n              style: ImpactStyle.Light\n            });\n            /**\n             * Translate the content element otherwise when pointer is removed\n             * from screen the scroll content will bounce back over the refresher\n             */\n            if (!this.pointerDown) {\n              translateElement(this.elementToTransform, `${refresherHeight}px`);\n            }\n          }\n        } else {\n          this.state = 2 /* RefresherState.Pulling */;\n          handleScrollWhilePulling(ticks, NUM_TICKS, pullAmount);\n        }\n      });\n    };\n    this.scrollEl.addEventListener('scroll', this.scrollListenerCallback);\n    this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n      el: this.scrollEl,\n      gestureName: 'refresher',\n      gesturePriority: 31,\n      direction: 'y',\n      threshold: 5,\n      onStart: () => {\n        this.pointerDown = true;\n        if (!this.didRefresh) {\n          translateElement(this.elementToTransform, '0px');\n        }\n        /**\n         * If the content had `display: none` when\n         * the refresher was initialized, its clientHeight\n         * will be 0. When the gesture starts, the content\n         * will be visible, so try to get the correct\n         * client height again. This is most common when\n         * using the refresher in an ion-menu.\n         */\n        if (MAX_PULL === 0) {\n          MAX_PULL = this.scrollEl.clientHeight * 0.16;\n        }\n      },\n      onMove: ev => {\n        this.lastVelocityY = ev.velocityY;\n      },\n      onEnd: () => {\n        this.pointerDown = false;\n        this.didStart = false;\n        if (this.needsCompletion) {\n          this.resetNativeRefresher(this.elementToTransform, 32 /* RefresherState.Completing */);\n          this.needsCompletion = false;\n        } else if (this.didRefresh) {\n          readTask(() => translateElement(this.elementToTransform, `${this.el.clientHeight}px`));\n        }\n      }\n    });\n    this.disabledChanged();\n  }\n  async setupMDNativeRefresher(contentEl, pullingSpinner, refreshingSpinner) {\n    const circle = getElementRoot(pullingSpinner).querySelector('circle');\n    const pullingRefresherIcon = this.el.querySelector('ion-refresher-content .refresher-pulling-icon');\n    const refreshingCircle = getElementRoot(refreshingSpinner).querySelector('circle');\n    if (circle !== null && refreshingCircle !== null) {\n      writeTask(() => {\n        circle.style.setProperty('animation', 'none');\n        // This lines up the animation on the refreshing spinner with the pulling spinner\n        refreshingSpinner.style.setProperty('animation-delay', '-655ms');\n        refreshingCircle.style.setProperty('animation-delay', '-655ms');\n      });\n    }\n    this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n      el: this.scrollEl,\n      gestureName: 'refresher',\n      gesturePriority: 31,\n      direction: 'y',\n      threshold: 5,\n      canStart: () => this.state !== 8 /* RefresherState.Refreshing */ && this.state !== 32 /* RefresherState.Completing */ && this.scrollEl.scrollTop === 0,\n      onStart: ev => {\n        this.progress = 0;\n        ev.data = {\n          animation: undefined,\n          didStart: false,\n          cancelled: false\n        };\n      },\n      onMove: ev => {\n        if (ev.velocityY < 0 && this.progress === 0 && !ev.data.didStart || ev.data.cancelled) {\n          ev.data.cancelled = true;\n          return;\n        }\n        if (!ev.data.didStart) {\n          ev.data.didStart = true;\n          this.state = 2 /* RefresherState.Pulling */;\n          // When ion-refresher is being used with a custom scroll target, the overflow styles need to be applied directly instead of via a css variable\n          const {\n            scrollEl\n          } = this;\n          const overflowProperty = scrollEl.matches(ION_CONTENT_CLASS_SELECTOR) ? 'overflow' : '--overflow';\n          writeTask(() => scrollEl.style.setProperty(overflowProperty, 'hidden'));\n          const animationType = getRefresherAnimationType(contentEl);\n          const animation = createPullingAnimation(animationType, pullingRefresherIcon, this.el);\n          ev.data.animation = animation;\n          animation.progressStart(false, 0);\n          this.ionStart.emit();\n          this.animations.push(animation);\n          return;\n        }\n        // Since we are using an easing curve, slow the gesture tracking down a bit\n        this.progress = clamp(0, ev.deltaY / 180 * 0.5, 1);\n        ev.data.animation.progressStep(this.progress);\n        this.ionPull.emit();\n      },\n      onEnd: ev => {\n        if (!ev.data.didStart) {\n          return;\n        }\n        this.gesture.enable(false);\n        const {\n          scrollEl\n        } = this;\n        const overflowProperty = scrollEl.matches(ION_CONTENT_CLASS_SELECTOR) ? 'overflow' : '--overflow';\n        writeTask(() => scrollEl.style.removeProperty(overflowProperty));\n        if (this.progress <= 0.4) {\n          ev.data.animation.progressEnd(0, this.progress, 500).onFinish(() => {\n            this.animations.forEach(ani => ani.destroy());\n            this.animations = [];\n            this.gesture.enable(true);\n            this.state = 1 /* RefresherState.Inactive */;\n          });\n          return;\n        }\n        const progress = getTimeGivenProgression([0, 0], [0, 0], [1, 1], [1, 1], this.progress)[0];\n        const snapBackAnimation = createSnapBackAnimation(pullingRefresherIcon);\n        this.animations.push(snapBackAnimation);\n        writeTask(async () => {\n          pullingRefresherIcon.style.setProperty('--ion-pulling-refresher-translate', `${progress * 100}px`);\n          ev.data.animation.progressEnd();\n          await snapBackAnimation.play();\n          this.beginRefresh();\n          ev.data.animation.destroy();\n          this.gesture.enable(true);\n        });\n      }\n    });\n    this.disabledChanged();\n  }\n  async setupNativeRefresher(contentEl) {\n    if (this.scrollListenerCallback || !contentEl || this.nativeRefresher || !this.scrollEl) {\n      return;\n    }\n    /**\n     * If using non-native refresher before make sure\n     * we clean up any old CSS. This can happen when\n     * a user manually calls the refresh method in a\n     * component create callback before the native\n     * refresher is setup.\n     */\n    this.setCss(0, '', false, '');\n    this.nativeRefresher = true;\n    const pullingSpinner = this.el.querySelector('ion-refresher-content .refresher-pulling ion-spinner');\n    const refreshingSpinner = this.el.querySelector('ion-refresher-content .refresher-refreshing ion-spinner');\n    if (getIonMode(this) === 'ios') {\n      this.setupiOSNativeRefresher(pullingSpinner, refreshingSpinner);\n    } else {\n      this.setupMDNativeRefresher(contentEl, pullingSpinner, refreshingSpinner);\n    }\n  }\n  componentDidUpdate() {\n    this.checkNativeRefresher();\n  }\n  async connectedCallback() {\n    if (this.el.getAttribute('slot') !== 'fixed') {\n      printIonError('[ion-refresher] - Make sure you use: <ion-refresher slot=\"fixed\">');\n      return;\n    }\n    const contentEl = this.el.closest(ION_CONTENT_ELEMENT_SELECTOR);\n    if (!contentEl) {\n      printIonContentErrorMsg(this.el);\n      return;\n    }\n    /**\n     * Waits for the content to be ready before querying the scroll\n     * or the background content element.\n     */\n    componentOnReady(contentEl, async () => {\n      const customScrollTarget = contentEl.querySelector(ION_CONTENT_CLASS_SELECTOR);\n      /**\n       * Query the custom scroll target (if available), first. In refresher implementations,\n       * the ion-refresher element will always be a direct child of ion-content (slot=\"fixed\"). By\n       * querying the custom scroll target first and falling back to the ion-content element,\n       * the correct scroll element will be returned by the implementation.\n       */\n      this.scrollEl = await getScrollElement(customScrollTarget !== null && customScrollTarget !== void 0 ? customScrollTarget : contentEl);\n      /**\n       * Query the background content element from the host ion-content element directly.\n       */\n      this.backgroundContentEl = await contentEl.getBackgroundElement();\n      /**\n       * Check if the content element is fullscreen to apply the correct styles\n       * when the refresher is refreshing. Otherwise, the refresher will be\n       * hidden because it is positioned behind the background content element.\n       */\n      this.contentFullscreen = contentEl.fullscreen;\n      if (await shouldUseNativeRefresher(this.el, getIonMode(this))) {\n        this.setupNativeRefresher(contentEl);\n      } else {\n        this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n          el: contentEl,\n          gestureName: 'refresher',\n          gesturePriority: 31,\n          direction: 'y',\n          threshold: 20,\n          passive: false,\n          canStart: () => this.canStart(),\n          onStart: () => this.onStart(),\n          onMove: ev => this.onMove(ev),\n          onEnd: () => this.onEnd()\n        });\n        this.disabledChanged();\n      }\n    });\n  }\n  disconnectedCallback() {\n    this.destroyNativeRefresher();\n    this.scrollEl = undefined;\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n  }\n  /**\n   * Call `complete()` when your async operation has completed.\n   * For example, the `refreshing` state is while the app is performing\n   * an asynchronous operation, such as receiving more data from an\n   * AJAX request. Once the data has been received, you then call this\n   * method to signify that the refreshing has completed and to close\n   * the refresher. This method also changes the refresher's state from\n   * `refreshing` to `completing`.\n   */\n  async complete() {\n    if (this.nativeRefresher) {\n      this.needsCompletion = true;\n      // Do not reset scroll el until user removes pointer from screen\n      if (!this.pointerDown) {\n        raf(() => raf(() => this.resetNativeRefresher(this.elementToTransform, 32 /* RefresherState.Completing */)));\n      }\n    } else {\n      this.close(32 /* RefresherState.Completing */, '120ms');\n    }\n  }\n  /**\n   * Changes the refresher's state from `refreshing` to `cancelling`.\n   */\n  async cancel() {\n    if (this.nativeRefresher) {\n      // Do not reset scroll el until user removes pointer from screen\n      if (!this.pointerDown) {\n        raf(() => raf(() => this.resetNativeRefresher(this.elementToTransform, 16 /* RefresherState.Cancelling */)));\n      }\n    } else {\n      this.close(16 /* RefresherState.Cancelling */, '');\n    }\n  }\n  /**\n   * A number representing how far down the user has pulled.\n   * The number `0` represents the user hasn't pulled down at all. The\n   * number `1`, and anything greater than `1`, represents that the user\n   * has pulled far enough down that when they let go then the refresh will\n   * happen. If they let go and the number is less than `1`, then the\n   * refresh will not happen, and the content will return to it's original\n   * position.\n   */\n  getProgress() {\n    return Promise.resolve(this.progress);\n  }\n  canStart() {\n    if (!this.scrollEl) {\n      return false;\n    }\n    if (this.state !== 1 /* RefresherState.Inactive */) {\n      return false;\n    }\n    // if the scrollTop is greater than zero then it's\n    // not possible to pull the content down yet\n    if (this.scrollEl.scrollTop > 0) {\n      return false;\n    }\n    return true;\n  }\n  onStart() {\n    this.progress = 0;\n    this.state = 1 /* RefresherState.Inactive */;\n    this.memoizeOverflowStyle();\n    /**\n     * If the content is fullscreen, then we need to\n     * set the offset-top style on the background content\n     * element to ensure that the refresher is shown.\n     */\n    if (this.contentFullscreen && this.backgroundContentEl) {\n      this.backgroundContentEl.style.setProperty('--offset-top', '0px');\n    }\n  }\n  onMove(detail) {\n    if (!this.scrollEl) {\n      return;\n    }\n    // this method can get called like a bazillion times per second,\n    // so it's built to be as efficient as possible, and does its\n    // best to do any DOM read/writes only when absolutely necessary\n    // if multi-touch then get out immediately\n    const ev = detail.event;\n    if (ev.touches !== undefined && ev.touches.length > 1) {\n      return;\n    }\n    // do nothing if it's actively refreshing\n    // or it's in the way of closing\n    // or this was never a startY\n    if ((this.state & 56 /* RefresherState._BUSY_ */) !== 0) {\n      return;\n    }\n    const pullFactor = Number.isNaN(this.pullFactor) || this.pullFactor < 0 ? 1 : this.pullFactor;\n    const deltaY = detail.deltaY * pullFactor;\n    // don't bother if they're scrolling up\n    // and have not already started dragging\n    if (deltaY <= 0) {\n      // the current Y is higher than the starting Y\n      // so they scrolled up enough to be ignored\n      this.progress = 0;\n      this.state = 1 /* RefresherState.Inactive */;\n      if (this.appliedStyles) {\n        // reset the styles only if they were applied\n        this.setCss(0, '', false, '');\n        return;\n      }\n      return;\n    }\n    if (this.state === 1 /* RefresherState.Inactive */) {\n      // this refresh is not already actively pulling down\n      // get the content's scrollTop\n      const scrollHostScrollTop = this.scrollEl.scrollTop;\n      // if the scrollTop is greater than zero then it's\n      // not possible to pull the content down yet\n      if (scrollHostScrollTop > 0) {\n        this.progress = 0;\n        return;\n      }\n      // content scrolled all the way to the top, and dragging down\n      this.state = 2 /* RefresherState.Pulling */;\n    }\n    // prevent native scroll events\n    if (ev.cancelable) {\n      ev.preventDefault();\n    }\n    // the refresher is actively pulling at this point\n    // move the scroll element within the content element\n    this.setCss(deltaY, '0ms', true, '');\n    if (deltaY === 0) {\n      // don't continue if there's no delta yet\n      this.progress = 0;\n      return;\n    }\n    const pullMin = this.pullMin;\n    // set pull progress\n    this.progress = deltaY / pullMin;\n    // emit \"start\" if it hasn't started yet\n    if (!this.didStart) {\n      this.didStart = true;\n      this.ionStart.emit();\n    }\n    // emit \"pulling\" on every move\n    this.ionPull.emit();\n    // do nothing if the delta is less than the pull threshold\n    if (deltaY < pullMin) {\n      // ensure it stays in the pulling state, cuz its not ready yet\n      this.state = 2 /* RefresherState.Pulling */;\n      return;\n    }\n    if (deltaY > this.pullMax) {\n      // they pulled farther than the max, so kick off the refresh\n      this.beginRefresh();\n      return;\n    }\n    // pulled farther than the pull min!!\n    // it is now in the `ready` state!!\n    // if they let go then it'll refresh, kerpow!!\n    this.state = 4 /* RefresherState.Ready */;\n    return;\n  }\n  onEnd() {\n    // only run in a zone when absolutely necessary\n    if (this.state === 4 /* RefresherState.Ready */) {\n      // they pulled down far enough, so it's ready to refresh\n      this.beginRefresh();\n    } else if (this.state === 2 /* RefresherState.Pulling */) {\n      // they were pulling down, but didn't pull down far enough\n      // set the content back to it's original location\n      // and close the refresher\n      // set that the refresh is actively cancelling\n      this.cancel();\n    } else if (this.state === 1 /* RefresherState.Inactive */) {\n      /**\n       * The pull to refresh gesture was aborted\n       * so we should immediately restore any overflow styles\n       * that have been modified. Do not call this.cancel\n       * because the styles will only be reset after a timeout.\n       * If the gesture is aborted then scrolling should be\n       * available right away.\n       */\n      this.restoreOverflowStyle();\n    }\n  }\n  beginRefresh() {\n    // assumes we're already back in a zone\n    // they pulled down far enough, so it's ready to refresh\n    this.state = 8 /* RefresherState.Refreshing */;\n    // place the content in a hangout position while it thinks\n    this.setCss(this.pullMin, this.snapbackDuration, true, '');\n    // emit \"refresh\" because it was pulled down far enough\n    // and they let go to begin refreshing\n    this.ionRefresh.emit({\n      complete: this.complete.bind(this)\n    });\n  }\n  close(state, delay) {\n    // create fallback timer incase something goes wrong with transitionEnd event\n    setTimeout(() => {\n      var _a;\n      this.state = 1 /* RefresherState.Inactive */;\n      this.progress = 0;\n      this.didStart = false;\n      /**\n       * Reset any overflow styles so the\n       * user can scroll again.\n       */\n      this.setCss(0, '0ms', false, '', true);\n      /**\n       * Reset the offset-top style on the background content\n       * when the refresher is no longer refreshing and the\n       * content is fullscreen.\n       *\n       * This ensures that the behavior of background content\n       * does not change when refreshing is complete.\n       */\n      if (this.contentFullscreen && this.backgroundContentEl) {\n        (_a = this.backgroundContentEl) === null || _a === void 0 ? void 0 : _a.style.removeProperty('--offset-top');\n      }\n    }, 600);\n    // reset the styles on the scroll element\n    // set that the refresh is actively cancelling/completing\n    this.state = state;\n    this.setCss(0, this.closeDuration, true, delay);\n  }\n  setCss(y, duration, overflowVisible, delay, shouldRestoreOverflowStyle = false) {\n    if (this.nativeRefresher) {\n      return;\n    }\n    this.appliedStyles = y > 0;\n    writeTask(() => {\n      if (this.scrollEl && this.backgroundContentEl) {\n        const scrollStyle = this.scrollEl.style;\n        const backgroundStyle = this.backgroundContentEl.style;\n        scrollStyle.transform = backgroundStyle.transform = y > 0 ? `translateY(${y}px) translateZ(0px)` : '';\n        scrollStyle.transitionDuration = backgroundStyle.transitionDuration = duration;\n        scrollStyle.transitionDelay = backgroundStyle.transitionDelay = delay;\n        scrollStyle.overflow = overflowVisible ? 'hidden' : '';\n      }\n      /**\n       * Reset the overflow styles only once\n       * the pull to refresh effect has been closed.\n       * This ensures that the gesture is done\n       * and the refresh operation has either\n       * been aborted or has completed.\n       */\n      if (shouldRestoreOverflowStyle) {\n        this.restoreOverflowStyle();\n      }\n    });\n  }\n  memoizeOverflowStyle() {\n    if (this.scrollEl) {\n      const {\n        overflow,\n        overflowX,\n        overflowY\n      } = this.scrollEl.style;\n      this.overflowStyles = {\n        overflow: overflow !== null && overflow !== void 0 ? overflow : '',\n        overflowX: overflowX !== null && overflowX !== void 0 ? overflowX : '',\n        overflowY: overflowY !== null && overflowY !== void 0 ? overflowY : ''\n      };\n    }\n  }\n  restoreOverflowStyle() {\n    if (this.overflowStyles !== undefined && this.scrollEl !== undefined) {\n      const {\n        overflow,\n        overflowX,\n        overflowY\n      } = this.overflowStyles;\n      this.scrollEl.style.overflow = overflow;\n      this.scrollEl.style.overflowX = overflowX;\n      this.scrollEl.style.overflowY = overflowY;\n      this.overflowStyles = undefined;\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '8c7a5cc32da02a9cbeaa954258148683f60a6d1b',\n      slot: \"fixed\",\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`refresher-${mode}`]: true,\n        'refresher-native': this.nativeRefresher,\n        'refresher-active': this.state !== 1 /* RefresherState.Inactive */,\n        'refresher-pulling': this.state === 2 /* RefresherState.Pulling */,\n        'refresher-ready': this.state === 4 /* RefresherState.Ready */,\n        'refresher-refreshing': this.state === 8 /* RefresherState.Refreshing */,\n        'refresher-cancelling': this.state === 16 /* RefresherState.Cancelling */,\n        'refresher-completing': this.state === 32 /* RefresherState.Completing */\n      }\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"disabled\": [\"disabledChanged\"]\n    };\n  }\n};\nRefresher.style = {\n  ios: refresherIosCss,\n  md: refresherMdCss\n};\nconst RefresherContent = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n  }\n  componentWillLoad() {\n    if (this.pullingIcon === undefined) {\n      /**\n       * The native iOS refresher uses a spinner instead of\n       * an icon, so we need to see if this device supports\n       * the native iOS refresher.\n       */\n      const hasRubberBandScrolling = supportsRubberBandScrolling();\n      const mode = getIonMode(this);\n      const overflowRefresher = hasRubberBandScrolling ? 'lines' : arrowDown;\n      this.pullingIcon = config.get('refreshingIcon', mode === 'ios' && hasRubberBandScrolling ? config.get('spinner', overflowRefresher) : 'circular');\n    }\n    if (this.refreshingSpinner === undefined) {\n      const mode = getIonMode(this);\n      this.refreshingSpinner = config.get('refreshingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'circular'));\n    }\n  }\n  renderPullingText() {\n    const {\n      customHTMLEnabled,\n      pullingText\n    } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", {\n        class: \"refresher-pulling-text\",\n        innerHTML: sanitizeDOMString(pullingText)\n      });\n    }\n    return h(\"div\", {\n      class: \"refresher-pulling-text\"\n    }, pullingText);\n  }\n  renderRefreshingText() {\n    const {\n      customHTMLEnabled,\n      refreshingText\n    } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", {\n        class: \"refresher-refreshing-text\",\n        innerHTML: sanitizeDOMString(refreshingText)\n      });\n    }\n    return h(\"div\", {\n      class: \"refresher-refreshing-text\"\n    }, refreshingText);\n  }\n  render() {\n    const pullingIcon = this.pullingIcon;\n    const hasSpinner = pullingIcon != null && SPINNERS[pullingIcon] !== undefined;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'e235f8a9a84070ece2e2066ced234a64663bfa1d',\n      class: mode\n    }, h(\"div\", {\n      key: '9121691818ddaa35801a5f442e144ac27686cf19',\n      class: \"refresher-pulling\"\n    }, this.pullingIcon && hasSpinner && h(\"div\", {\n      key: 'c8d65d740f1575041bd3b752c789077927397fe4',\n      class: \"refresher-pulling-icon\"\n    }, h(\"div\", {\n      key: '309dd904977eaa788b09ea95b7fa4996a73bec5b',\n      class: \"spinner-arrow-container\"\n    }, h(\"ion-spinner\", {\n      key: 'a2a1480f67775d56ca7822e76be1e9f983bca2f9',\n      name: this.pullingIcon,\n      paused: true\n    }), mode === 'md' && this.pullingIcon === 'circular' && h(\"div\", {\n      key: '811d7e06d324bf4b6a18a31427a43e5177f3ae3a',\n      class: \"arrow-container\"\n    }, h(\"ion-icon\", {\n      key: '86cc48e2e8dc054ff6ff1299094da35b524be63d',\n      icon: caretBackSharp,\n      \"aria-hidden\": \"true\"\n    })))), this.pullingIcon && !hasSpinner && h(\"div\", {\n      key: '464ae097dbc95c18a2dd7dfd03f8489153dab719',\n      class: \"refresher-pulling-icon\"\n    }, h(\"ion-icon\", {\n      key: 'ed6875978b9035add562caa743a68353743d978f',\n      icon: this.pullingIcon,\n      lazy: false,\n      \"aria-hidden\": \"true\"\n    })), this.pullingText !== undefined && this.renderPullingText()), h(\"div\", {\n      key: 'aff891924e44354543fec484e5cde1ca92e69904',\n      class: \"refresher-refreshing\"\n    }, this.refreshingSpinner && h(\"div\", {\n      key: '842d7ac4ff10a1058775493d62f31cbdcd34f7a0',\n      class: \"refresher-refreshing-icon\"\n    }, h(\"ion-spinner\", {\n      key: '8c3e6195501e7e78d5cde1e3ad1fef90fd4a953f',\n      name: this.refreshingSpinner\n    })), this.refreshingText !== undefined && this.renderRefreshingText()));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nexport { Refresher as ion_refresher, RefresherContent as ion_refresher_content };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,IAAM,4BAA4B,eAAa;AAC7C,QAAM,kBAAkB,UAAU;AAClC,QAAM,YAAY,oBAAoB,QAAQ,gBAAgB,YAAY;AAC1E,SAAO,YAAY,cAAc;AACnC;AACA,IAAM,yBAAyB,CAAC,MAAM,gBAAgB,gBAAgB;AACpE,SAAO,SAAS,UAAU,qBAAqB,gBAAgB,WAAW,IAAI,yBAAyB,gBAAgB,WAAW;AACpI;AACA,IAAM,sBAAsB,0BAAwB;AAClD,QAAM,UAAU,qBAAqB,cAAc,aAAa;AAChE,QAAM,SAAS,QAAQ,WAAW,cAAc,QAAQ;AACxD,QAAM,wBAAwB,qBAAqB,cAAc,0BAA0B;AAC3F,QAAM,iBAAiB,qBAAqB,cAAc,kBAAkB;AAC5E,QAAM,QAAQ,iBAAiB,eAAe,cAAc,UAAU,IAAI;AAC1E,QAAM,gBAAgB,gBAAgB,EAAE,SAAS,GAAI,EAAE,OAAO,UAAU;AACxE,QAAM,iCAAiC,gBAAgB,EAAE,WAAW,qBAAqB,EAAE,UAAU,CAAC;AAAA,IACpG,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,CAAC,CAAC;AACF,QAAM,uBAAuB,gBAAgB,EAAE,WAAW,MAAM,EAAE,UAAU,CAAC;AAAA,IAC3E,QAAQ;AAAA,IACR,iBAAiB;AAAA,EACnB,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,iBAAiB;AAAA,EACnB,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,iBAAiB;AAAA,EACnB,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,iBAAiB;AAAA,EACnB,CAAC,CAAC;AACF,QAAM,uBAAuB,gBAAgB,EAAE,WAAW,OAAO,EAAE,UAAU,CAAC;AAAA,IAC5E,QAAQ;AAAA,IACR,WAAW;AAAA,EACb,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,WAAW;AAAA,EACb,CAAC,CAAC;AAMF,MAAI,kBAAkB,OAAO;AAC3B,UAAM,0BAA0B,gBAAgB,EAAE,WAAW,cAAc,EAAE,UAAU,CAAC;AAAA,MACtF,QAAQ;AAAA,MACR,WAAW;AAAA,IACb,GAAG;AAAA,MACD,QAAQ;AAAA,MACR,WAAW;AAAA,IACb,GAAG;AAAA,MACD,QAAQ;AAAA,MACR,WAAW;AAAA,IACb,GAAG;AAAA,MACD,QAAQ;AAAA,MACR,WAAW;AAAA,IACb,CAAC,CAAC;AACF,UAAM,iBAAiB,gBAAgB,EAAE,WAAW,KAAK,EAAE,UAAU,CAAC;AAAA,MACpE,QAAQ;AAAA,MACR,WAAW;AAAA,IACb,GAAG;AAAA,MACD,QAAQ;AAAA,MACR,WAAW;AAAA,IACb,GAAG;AAAA,MACD,QAAQ;AAAA,MACR,WAAW;AAAA,IACb,GAAG;AAAA,MACD,QAAQ;AAAA,MACR,WAAW;AAAA,IACb,CAAC,CAAC;AACF,kBAAc,aAAa,CAAC,yBAAyB,cAAc,CAAC;AAAA,EACtE;AACA,SAAO,cAAc,aAAa,CAAC,gCAAgC,sBAAsB,oBAAoB,CAAC;AAChH;AACA,IAAM,uBAAuB,CAAC,sBAAsB,gBAAgB;AAUlE,QAAM,SAAS,YAAY;AAC3B,QAAM,mBAAmB,gBAAgB,EAAE,WAAW,oBAAoB,EAAE,UAAU,CAAC;AAAA,IACrF,QAAQ;AAAA,IACR,WAAW,wBAAwB,MAAM;AAAA,EAC3C,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO,oBAAoB,oBAAoB,EAAE,aAAa,CAAC,gBAAgB,CAAC;AAClF;AACA,IAAM,2BAA2B,CAAC,sBAAsB,gBAAgB;AAUtE,QAAM,SAAS,YAAY;AAC3B,QAAM,mBAAmB,gBAAgB,EAAE,WAAW,oBAAoB,EAAE,UAAU,CAAC;AAAA,IACrF,QAAQ;AAAA,IACR,WAAW,eAAe,MAAM;AAAA,EAClC,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO,oBAAoB,oBAAoB,EAAE,aAAa,CAAC,gBAAgB,CAAC;AAClF;AACA,IAAM,0BAA0B,0BAAwB;AACtD,SAAO,gBAAgB,EAAE,SAAS,GAAG,EAAE,WAAW,oBAAoB,EAAE,OAAO,aAAa,6DAA6D,iBAAiB;AAC5K;AAGA,IAAM,oBAAoB,CAAC,SAAS,YAAY;AAC9C,UAAQ,MAAM,YAAY,WAAW,QAAQ,SAAS,CAAC;AACzD;AACA,IAAM,2BAA2B,CAAC,OAAO,UAAU,eAAe;AAChE,QAAM,MAAM;AACZ,YAAU,MAAM;AACd,UAAM,QAAQ,CAAC,IAAI,MAAM;AAOvB,YAAM,MAAM,KAAK,MAAM;AACvB,YAAM,QAAQ,MAAM;AACpB,YAAM,QAAQ,aAAa;AAC3B,YAAM,cAAc,MAAM,GAAG,QAAQ,OAAO,CAAC;AAC7C,SAAG,MAAM,YAAY,WAAW,YAAY,SAAS,CAAC;AAAA,IACxD,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAM,8BAA8B,CAAC,SAAS,kBAAkB;AAC9D,YAAU,MAAM;AAEd,YAAQ,MAAM,YAAY,kCAAkC,iBAAiB,IAAM,SAAS,IAAI;AAChG,YAAQ,MAAM,YAAY,WAAW,GAAG;AAAA,EAC1C,CAAC;AACH;AACA,IAAM,mBAAmB,CAAC,IAAI,OAAO,WAAW,QAAQ;AACtD,MAAI,CAAC,IAAI;AACP,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,QAAM,QAAQ,mBAAmB,IAAI,QAAQ;AAC7C,YAAU,MAAM;AACd,OAAG,MAAM,YAAY,cAAc,GAAG,QAAQ,iBAAiB;AAC/D,QAAI,UAAU,QAAW;AACvB,SAAG,MAAM,eAAe,WAAW;AAAA,IACrC,OAAO;AACL,SAAG,MAAM,YAAY,aAAa,oBAAoB,KAAK,QAAQ;AAAA,IACrE;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAkBA,IAAM,8BAA8B,MAAM;AACxC,SAAO,UAAU,iBAAiB,KAAK,IAAI,SAAS,uDAAuD;AAC7G;AACA,IAAM,2BAA2B,CAAO,aAAa,SAAS;AAC5D,QAAM,mBAAmB,YAAY,cAAc,uBAAuB;AAC1E,MAAI,CAAC,kBAAkB;AACrB,WAAO,QAAQ,QAAQ,KAAK;AAAA,EAC9B;AACA,QAAM,IAAI,QAAQ,aAAW,iBAAiB,kBAAkB,OAAO,CAAC;AACxE,QAAM,iBAAiB,YAAY,cAAc,sDAAsD;AACvG,QAAM,oBAAoB,YAAY,cAAc,yDAAyD;AAC7G,SAAO,mBAAmB,QAAQ,sBAAsB,SAAS,SAAS,SAAS,4BAA4B,KAAK,SAAS;AAC/H;AACA,IAAM,kBAAkB;AACxB,IAAM,iBAAiB;AACvB,IAAM,YAAY,MAAM;AAAA,EACtB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,aAAa,YAAY,MAAM,cAAc,CAAC;AACnD,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAClB,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AACrB,SAAK,aAAa,CAAC;AACnB,SAAK,kBAAkB;AAWvB,SAAK,QAAQ;AAOb,SAAK,UAAU;AAQf,SAAK,UAAU,KAAK,UAAU;AAM9B,SAAK,gBAAgB;AAMrB,SAAK,mBAAmB;AAexB,SAAK,aAAa;AAIlB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,OAAO,CAAC,KAAK,QAAQ;AAAA,IACpC;AAAA,EACF;AAAA,EACM,uBAAuB;AAAA;AAC3B,YAAM,qBAAqB,MAAM,yBAAyB,KAAK,IAAI,WAAW,IAAI,CAAC;AACnF,UAAI,sBAAsB,CAAC,KAAK,iBAAiB;AAC/C,cAAM,YAAY,KAAK,GAAG,QAAQ,aAAa;AAC/C,aAAK,qBAAqB,SAAS;AAAA,MACrC,WAAW,CAAC,oBAAoB;AAC9B,aAAK,uBAAuB;AAAA,MAC9B;AAAA,IACF;AAAA;AAAA,EACA,yBAAyB;AACvB,QAAI,KAAK,YAAY,KAAK,wBAAwB;AAChD,WAAK,SAAS,oBAAoB,UAAU,KAAK,sBAAsB;AACvE,WAAK,yBAAyB;AAAA,IAChC;AACA,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACM,qBAAqB,IAAI,OAAO;AAAA;AACpC,WAAK,QAAQ;AACb,UAAI,WAAW,IAAI,MAAM,OAAO;AAC9B,cAAM,iBAAiB,IAAI,QAAW,GAAG;AAAA,MAC3C,OAAO;AACL,cAAM,mBAAmB,KAAK,GAAG,cAAc,4BAA4B,GAAG,GAAG;AAAA,MACnF;AACA,WAAK,aAAa;AAClB,WAAK,kBAAkB;AACvB,WAAK,cAAc;AACnB,WAAK,WAAW,QAAQ,SAAO,IAAI,QAAQ,CAAC;AAC5C,WAAK,aAAa,CAAC;AACnB,WAAK,WAAW;AAChB,WAAK,QAAQ;AAAA,IACf;AAAA;AAAA,EACM,wBAAwB,gBAAgB,mBAAmB;AAAA;AAC/D,WAAK,qBAAqB,KAAK;AAC/B,YAAM,QAAQ,eAAe,WAAW,iBAAiB,KAAK;AAC9D,UAAI,WAAW,KAAK,SAAS,eAAe;AAC5C,YAAM,YAAY,MAAM;AACxB,gBAAU,MAAM,MAAM,QAAQ,QAAM,GAAG,MAAM,YAAY,aAAa,MAAM,CAAC,CAAC;AAC9E,WAAK,yBAAyB,MAAM;AAElC,YAAI,CAAC,KAAK,eAAe,KAAK,UAAU,GAAiC;AACvE;AAAA,QACF;AACA,iBAAS,MAAM;AAEb,gBAAM,YAAY,KAAK,SAAS;AAChC,gBAAM,kBAAkB,KAAK,GAAG;AAChC,cAAI,YAAY,GAAG;AAKjB,gBAAI,KAAK,UAAU,GAAmC;AACpD,oBAAM,QAAQ,MAAM,GAAG,aAAa,kBAAkB,MAAM,CAAC;AAC7D,wBAAU,MAAM,kBAAkB,mBAAmB,IAAI,KAAK,CAAC;AAC/D;AAAA,YACF;AACA;AAAA,UACF;AACA,cAAI,KAAK,aAAa;AACpB,gBAAI,CAAC,KAAK,UAAU;AAClB,mBAAK,WAAW;AAChB,mBAAK,SAAS,KAAK;AAAA,YACrB;AAEA,gBAAI,KAAK,aAAa;AACpB,mBAAK,QAAQ,KAAK;AAAA,YACpB;AAAA,UACF;AAQA,gBAAM,SAAS,KAAK,WAAW,KAAK;AACpC,gBAAM,aAAa,KAAK,WAAW,MAAM,IAAI,KAAK,IAAI,SAAS,IAAI,UAAU,UAAU,CAAC;AACxF,gBAAM,8BAA8B,KAAK,UAAU,KAAqC,eAAe;AACvG,cAAI,6BAA6B;AAC/B,gBAAI,KAAK,aAAa;AACpB,0CAA4B,mBAAmB,KAAK,aAAa;AAAA,YACnE;AACA,gBAAI,CAAC,KAAK,YAAY;AACpB,mBAAK,aAAa;AAClB,mBAAK,aAAa;AAClB,2BAAa;AAAA,gBACX,OAAO,YAAY;AAAA,cACrB,CAAC;AAKD,kBAAI,CAAC,KAAK,aAAa;AACrB,iCAAiB,KAAK,oBAAoB,GAAG,eAAe,IAAI;AAAA,cAClE;AAAA,YACF;AAAA,UACF,OAAO;AACL,iBAAK,QAAQ;AACb,qCAAyB,OAAO,WAAW,UAAU;AAAA,UACvD;AAAA,QACF,CAAC;AAAA,MACH;AACA,WAAK,SAAS,iBAAiB,UAAU,KAAK,sBAAsB;AACpE,WAAK,WAAW,MAAM,OAAO,8BAAqB,GAAG,cAAc;AAAA,QACjE,IAAI,KAAK;AAAA,QACT,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,SAAS,MAAM;AACb,eAAK,cAAc;AACnB,cAAI,CAAC,KAAK,YAAY;AACpB,6BAAiB,KAAK,oBAAoB,KAAK;AAAA,UACjD;AASA,cAAI,aAAa,GAAG;AAClB,uBAAW,KAAK,SAAS,eAAe;AAAA,UAC1C;AAAA,QACF;AAAA,QACA,QAAQ,QAAM;AACZ,eAAK,gBAAgB,GAAG;AAAA,QAC1B;AAAA,QACA,OAAO,MAAM;AACX,eAAK,cAAc;AACnB,eAAK,WAAW;AAChB,cAAI,KAAK,iBAAiB;AACxB,iBAAK;AAAA,cAAqB,KAAK;AAAA,cAAoB;AAAA;AAAA,YAAkC;AACrF,iBAAK,kBAAkB;AAAA,UACzB,WAAW,KAAK,YAAY;AAC1B,qBAAS,MAAM,iBAAiB,KAAK,oBAAoB,GAAG,KAAK,GAAG,YAAY,IAAI,CAAC;AAAA,UACvF;AAAA,QACF;AAAA,MACF,CAAC;AACD,WAAK,gBAAgB;AAAA,IACvB;AAAA;AAAA,EACM,uBAAuB,WAAW,gBAAgB,mBAAmB;AAAA;AACzE,YAAM,SAAS,eAAe,cAAc,EAAE,cAAc,QAAQ;AACpE,YAAM,uBAAuB,KAAK,GAAG,cAAc,+CAA+C;AAClG,YAAM,mBAAmB,eAAe,iBAAiB,EAAE,cAAc,QAAQ;AACjF,UAAI,WAAW,QAAQ,qBAAqB,MAAM;AAChD,kBAAU,MAAM;AACd,iBAAO,MAAM,YAAY,aAAa,MAAM;AAE5C,4BAAkB,MAAM,YAAY,mBAAmB,QAAQ;AAC/D,2BAAiB,MAAM,YAAY,mBAAmB,QAAQ;AAAA,QAChE,CAAC;AAAA,MACH;AACA,WAAK,WAAW,MAAM,OAAO,8BAAqB,GAAG,cAAc;AAAA,QACjE,IAAI,KAAK;AAAA,QACT,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU,MAAM,KAAK,UAAU,KAAqC,KAAK,UAAU,MAAsC,KAAK,SAAS,cAAc;AAAA,QACrJ,SAAS,QAAM;AACb,eAAK,WAAW;AAChB,aAAG,OAAO;AAAA,YACR,WAAW;AAAA,YACX,UAAU;AAAA,YACV,WAAW;AAAA,UACb;AAAA,QACF;AAAA,QACA,QAAQ,QAAM;AACZ,cAAI,GAAG,YAAY,KAAK,KAAK,aAAa,KAAK,CAAC,GAAG,KAAK,YAAY,GAAG,KAAK,WAAW;AACrF,eAAG,KAAK,YAAY;AACpB;AAAA,UACF;AACA,cAAI,CAAC,GAAG,KAAK,UAAU;AACrB,eAAG,KAAK,WAAW;AACnB,iBAAK,QAAQ;AAEb,kBAAM;AAAA,cACJ;AAAA,YACF,IAAI;AACJ,kBAAM,mBAAmB,SAAS,QAAQ,0BAA0B,IAAI,aAAa;AACrF,sBAAU,MAAM,SAAS,MAAM,YAAY,kBAAkB,QAAQ,CAAC;AACtE,kBAAM,gBAAgB,0BAA0B,SAAS;AACzD,kBAAM,YAAY,uBAAuB,eAAe,sBAAsB,KAAK,EAAE;AACrF,eAAG,KAAK,YAAY;AACpB,sBAAU,cAAc,OAAO,CAAC;AAChC,iBAAK,SAAS,KAAK;AACnB,iBAAK,WAAW,KAAK,SAAS;AAC9B;AAAA,UACF;AAEA,eAAK,WAAW,MAAM,GAAG,GAAG,SAAS,MAAM,KAAK,CAAC;AACjD,aAAG,KAAK,UAAU,aAAa,KAAK,QAAQ;AAC5C,eAAK,QAAQ,KAAK;AAAA,QACpB;AAAA,QACA,OAAO,QAAM;AACX,cAAI,CAAC,GAAG,KAAK,UAAU;AACrB;AAAA,UACF;AACA,eAAK,QAAQ,OAAO,KAAK;AACzB,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,gBAAM,mBAAmB,SAAS,QAAQ,0BAA0B,IAAI,aAAa;AACrF,oBAAU,MAAM,SAAS,MAAM,eAAe,gBAAgB,CAAC;AAC/D,cAAI,KAAK,YAAY,KAAK;AACxB,eAAG,KAAK,UAAU,YAAY,GAAG,KAAK,UAAU,GAAG,EAAE,SAAS,MAAM;AAClE,mBAAK,WAAW,QAAQ,SAAO,IAAI,QAAQ,CAAC;AAC5C,mBAAK,aAAa,CAAC;AACnB,mBAAK,QAAQ,OAAO,IAAI;AACxB,mBAAK,QAAQ;AAAA,YACf,CAAC;AACD;AAAA,UACF;AACA,gBAAM,WAAW,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;AACzF,gBAAM,oBAAoB,wBAAwB,oBAAoB;AACtE,eAAK,WAAW,KAAK,iBAAiB;AACtC,oBAAU,MAAY;AACpB,iCAAqB,MAAM,YAAY,qCAAqC,GAAG,WAAW,GAAG,IAAI;AACjG,eAAG,KAAK,UAAU,YAAY;AAC9B,kBAAM,kBAAkB,KAAK;AAC7B,iBAAK,aAAa;AAClB,eAAG,KAAK,UAAU,QAAQ;AAC1B,iBAAK,QAAQ,OAAO,IAAI;AAAA,UAC1B,EAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,WAAK,gBAAgB;AAAA,IACvB;AAAA;AAAA,EACM,qBAAqB,WAAW;AAAA;AACpC,UAAI,KAAK,0BAA0B,CAAC,aAAa,KAAK,mBAAmB,CAAC,KAAK,UAAU;AACvF;AAAA,MACF;AAQA,WAAK,OAAO,GAAG,IAAI,OAAO,EAAE;AAC5B,WAAK,kBAAkB;AACvB,YAAM,iBAAiB,KAAK,GAAG,cAAc,sDAAsD;AACnG,YAAM,oBAAoB,KAAK,GAAG,cAAc,yDAAyD;AACzG,UAAI,WAAW,IAAI,MAAM,OAAO;AAC9B,aAAK,wBAAwB,gBAAgB,iBAAiB;AAAA,MAChE,OAAO;AACL,aAAK,uBAAuB,WAAW,gBAAgB,iBAAiB;AAAA,MAC1E;AAAA,IACF;AAAA;AAAA,EACA,qBAAqB;AACnB,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACM,oBAAoB;AAAA;AACxB,UAAI,KAAK,GAAG,aAAa,MAAM,MAAM,SAAS;AAC5C,sBAAc,mEAAmE;AACjF;AAAA,MACF;AACA,YAAM,YAAY,KAAK,GAAG,QAAQ,4BAA4B;AAC9D,UAAI,CAAC,WAAW;AACd,gCAAwB,KAAK,EAAE;AAC/B;AAAA,MACF;AAKA,uBAAiB,WAAW,MAAY;AACtC,cAAM,qBAAqB,UAAU,cAAc,0BAA0B;AAO7E,aAAK,WAAW,MAAM,iBAAiB,uBAAuB,QAAQ,uBAAuB,SAAS,qBAAqB,SAAS;AAIpI,aAAK,sBAAsB,MAAM,UAAU,qBAAqB;AAMhE,aAAK,oBAAoB,UAAU;AACnC,YAAI,MAAM,yBAAyB,KAAK,IAAI,WAAW,IAAI,CAAC,GAAG;AAC7D,eAAK,qBAAqB,SAAS;AAAA,QACrC,OAAO;AACL,eAAK,WAAW,MAAM,OAAO,8BAAqB,GAAG,cAAc;AAAA,YACjE,IAAI;AAAA,YACJ,aAAa;AAAA,YACb,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,WAAW;AAAA,YACX,SAAS;AAAA,YACT,UAAU,MAAM,KAAK,SAAS;AAAA,YAC9B,SAAS,MAAM,KAAK,QAAQ;AAAA,YAC5B,QAAQ,QAAM,KAAK,OAAO,EAAE;AAAA,YAC5B,OAAO,MAAM,KAAK,MAAM;AAAA,UAC1B,CAAC;AACD,eAAK,gBAAgB;AAAA,QACvB;AAAA,MACF,EAAC;AAAA,IACH;AAAA;AAAA,EACA,uBAAuB;AACrB,SAAK,uBAAuB;AAC5B,SAAK,WAAW;AAChB,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,QAAQ;AACrB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUM,WAAW;AAAA;AACf,UAAI,KAAK,iBAAiB;AACxB,aAAK,kBAAkB;AAEvB,YAAI,CAAC,KAAK,aAAa;AACrB,cAAI,MAAM,IAAI,MAAM,KAAK;AAAA,YAAqB,KAAK;AAAA,YAAoB;AAAA;AAAA,UAAkC,CAAC,CAAC;AAAA,QAC7G;AAAA,MACF,OAAO;AACL,aAAK,MAAM,IAAoC,OAAO;AAAA,MACxD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,SAAS;AAAA;AACb,UAAI,KAAK,iBAAiB;AAExB,YAAI,CAAC,KAAK,aAAa;AACrB,cAAI,MAAM,IAAI,MAAM,KAAK;AAAA,YAAqB,KAAK;AAAA,YAAoB;AAAA;AAAA,UAAkC,CAAC,CAAC;AAAA,QAC7G;AAAA,MACF,OAAO;AACL,aAAK,MAAM,IAAoC,EAAE;AAAA,MACnD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,cAAc;AACZ,WAAO,QAAQ,QAAQ,KAAK,QAAQ;AAAA,EACtC;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,UAAU,GAAiC;AAClD,aAAO;AAAA,IACT;AAGA,QAAI,KAAK,SAAS,YAAY,GAAG;AAC/B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,qBAAqB;AAM1B,QAAI,KAAK,qBAAqB,KAAK,qBAAqB;AACtD,WAAK,oBAAoB,MAAM,YAAY,gBAAgB,KAAK;AAAA,IAClE;AAAA,EACF;AAAA,EACA,OAAO,QAAQ;AACb,QAAI,CAAC,KAAK,UAAU;AAClB;AAAA,IACF;AAKA,UAAM,KAAK,OAAO;AAClB,QAAI,GAAG,YAAY,UAAa,GAAG,QAAQ,SAAS,GAAG;AACrD;AAAA,IACF;AAIA,SAAK,KAAK,QAAQ,QAAoC,GAAG;AACvD;AAAA,IACF;AACA,UAAM,aAAa,OAAO,MAAM,KAAK,UAAU,KAAK,KAAK,aAAa,IAAI,IAAI,KAAK;AACnF,UAAM,SAAS,OAAO,SAAS;AAG/B,QAAI,UAAU,GAAG;AAGf,WAAK,WAAW;AAChB,WAAK,QAAQ;AACb,UAAI,KAAK,eAAe;AAEtB,aAAK,OAAO,GAAG,IAAI,OAAO,EAAE;AAC5B;AAAA,MACF;AACA;AAAA,IACF;AACA,QAAI,KAAK,UAAU,GAAiC;AAGlD,YAAM,sBAAsB,KAAK,SAAS;AAG1C,UAAI,sBAAsB,GAAG;AAC3B,aAAK,WAAW;AAChB;AAAA,MACF;AAEA,WAAK,QAAQ;AAAA,IACf;AAEA,QAAI,GAAG,YAAY;AACjB,SAAG,eAAe;AAAA,IACpB;AAGA,SAAK,OAAO,QAAQ,OAAO,MAAM,EAAE;AACnC,QAAI,WAAW,GAAG;AAEhB,WAAK,WAAW;AAChB;AAAA,IACF;AACA,UAAM,UAAU,KAAK;AAErB,SAAK,WAAW,SAAS;AAEzB,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW;AAChB,WAAK,SAAS,KAAK;AAAA,IACrB;AAEA,SAAK,QAAQ,KAAK;AAElB,QAAI,SAAS,SAAS;AAEpB,WAAK,QAAQ;AACb;AAAA,IACF;AACA,QAAI,SAAS,KAAK,SAAS;AAEzB,WAAK,aAAa;AAClB;AAAA,IACF;AAIA,SAAK,QAAQ;AACb;AAAA,EACF;AAAA,EACA,QAAQ;AAEN,QAAI,KAAK,UAAU,GAA8B;AAE/C,WAAK,aAAa;AAAA,IACpB,WAAW,KAAK,UAAU,GAAgC;AAKxD,WAAK,OAAO;AAAA,IACd,WAAW,KAAK,UAAU,GAAiC;AASzD,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,eAAe;AAGb,SAAK,QAAQ;AAEb,SAAK,OAAO,KAAK,SAAS,KAAK,kBAAkB,MAAM,EAAE;AAGzD,SAAK,WAAW,KAAK;AAAA,MACnB,UAAU,KAAK,SAAS,KAAK,IAAI;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,MAAM,OAAO,OAAO;AAElB,eAAW,MAAM;AACf,UAAI;AACJ,WAAK,QAAQ;AACb,WAAK,WAAW;AAChB,WAAK,WAAW;AAKhB,WAAK,OAAO,GAAG,OAAO,OAAO,IAAI,IAAI;AASrC,UAAI,KAAK,qBAAqB,KAAK,qBAAqB;AACtD,SAAC,KAAK,KAAK,yBAAyB,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,eAAe,cAAc;AAAA,MAC7G;AAAA,IACF,GAAG,GAAG;AAGN,SAAK,QAAQ;AACb,SAAK,OAAO,GAAG,KAAK,eAAe,MAAM,KAAK;AAAA,EAChD;AAAA,EACA,OAAO,GAAG,UAAU,iBAAiB,OAAO,6BAA6B,OAAO;AAC9E,QAAI,KAAK,iBAAiB;AACxB;AAAA,IACF;AACA,SAAK,gBAAgB,IAAI;AACzB,cAAU,MAAM;AACd,UAAI,KAAK,YAAY,KAAK,qBAAqB;AAC7C,cAAM,cAAc,KAAK,SAAS;AAClC,cAAM,kBAAkB,KAAK,oBAAoB;AACjD,oBAAY,YAAY,gBAAgB,YAAY,IAAI,IAAI,cAAc,CAAC,wBAAwB;AACnG,oBAAY,qBAAqB,gBAAgB,qBAAqB;AACtE,oBAAY,kBAAkB,gBAAgB,kBAAkB;AAChE,oBAAY,WAAW,kBAAkB,WAAW;AAAA,MACtD;AAQA,UAAI,4BAA4B;AAC9B,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,UAAU;AACjB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK,SAAS;AAClB,WAAK,iBAAiB;AAAA,QACpB,UAAU,aAAa,QAAQ,aAAa,SAAS,WAAW;AAAA,QAChE,WAAW,cAAc,QAAQ,cAAc,SAAS,YAAY;AAAA,QACpE,WAAW,cAAc,QAAQ,cAAc,SAAS,YAAY;AAAA,MACtE;AAAA,IACF;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,mBAAmB,UAAa,KAAK,aAAa,QAAW;AACpE,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,WAAK,SAAS,MAAM,WAAW;AAC/B,WAAK,SAAS,MAAM,YAAY;AAChC,WAAK,SAAS,MAAM,YAAY;AAChC,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,QACL,CAAC,IAAI,GAAG;AAAA;AAAA,QAER,CAAC,aAAa,IAAI,EAAE,GAAG;AAAA,QACvB,oBAAoB,KAAK;AAAA,QACzB,oBAAoB,KAAK,UAAU;AAAA,QACnC,qBAAqB,KAAK,UAAU;AAAA,QACpC,mBAAmB,KAAK,UAAU;AAAA,QAClC,wBAAwB,KAAK,UAAU;AAAA,QACvC,wBAAwB,KAAK,UAAU;AAAA,QACvC,wBAAwB,KAAK,UAAU;AAAA;AAAA,MACzC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,YAAY,CAAC,iBAAiB;AAAA,IAChC;AAAA,EACF;AACF;AACA,UAAU,QAAQ;AAAA,EAChB,KAAK;AAAA,EACL,IAAI;AACN;AACA,IAAM,mBAAmB,MAAM;AAAA,EAC7B,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,oBAAoB,OAAO,IAAI,6BAA6B,2BAA2B;AAAA,EAC9F;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,gBAAgB,QAAW;AAMlC,YAAM,yBAAyB,4BAA4B;AAC3D,YAAM,OAAO,WAAW,IAAI;AAC5B,YAAM,oBAAoB,yBAAyB,UAAU;AAC7D,WAAK,cAAc,OAAO,IAAI,kBAAkB,SAAS,SAAS,yBAAyB,OAAO,IAAI,WAAW,iBAAiB,IAAI,UAAU;AAAA,IAClJ;AACA,QAAI,KAAK,sBAAsB,QAAW;AACxC,YAAM,OAAO,WAAW,IAAI;AAC5B,WAAK,oBAAoB,OAAO,IAAI,qBAAqB,OAAO,IAAI,WAAW,SAAS,QAAQ,UAAU,UAAU,CAAC;AAAA,IACvH;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,mBAAmB;AACrB,aAAO,EAAE,OAAO;AAAA,QACd,OAAO;AAAA,QACP,WAAW,kBAAkB,WAAW;AAAA,MAC1C,CAAC;AAAA,IACH;AACA,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,IACT,GAAG,WAAW;AAAA,EAChB;AAAA,EACA,uBAAuB;AACrB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,mBAAmB;AACrB,aAAO,EAAE,OAAO;AAAA,QACd,OAAO;AAAA,QACP,WAAW,kBAAkB,cAAc;AAAA,MAC7C,CAAC;AAAA,IACH;AACA,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,IACT,GAAG,cAAc;AAAA,EACnB;AAAA,EACA,SAAS;AACP,UAAM,cAAc,KAAK;AACzB,UAAM,aAAa,eAAe,QAAQ,SAAS,WAAW,MAAM;AACpE,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,KAAK,eAAe,cAAc,EAAE,OAAO;AAAA,MAC5C,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,eAAe;AAAA,MAClB,KAAK;AAAA,MACL,MAAM,KAAK;AAAA,MACX,QAAQ;AAAA,IACV,CAAC,GAAG,SAAS,QAAQ,KAAK,gBAAgB,cAAc,EAAE,OAAO;AAAA,MAC/D,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,YAAY;AAAA,MACf,KAAK;AAAA,MACL,MAAM;AAAA,MACN,eAAe;AAAA,IACjB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,eAAe,CAAC,cAAc,EAAE,OAAO;AAAA,MACjD,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,YAAY;AAAA,MACf,KAAK;AAAA,MACL,MAAM,KAAK;AAAA,MACX,MAAM;AAAA,MACN,eAAe;AAAA,IACjB,CAAC,CAAC,GAAG,KAAK,gBAAgB,UAAa,KAAK,kBAAkB,CAAC,GAAG,EAAE,OAAO;AAAA,MACzE,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,KAAK,qBAAqB,EAAE,OAAO;AAAA,MACpC,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,eAAe;AAAA,MAClB,KAAK;AAAA,MACL,MAAM,KAAK;AAAA,IACb,CAAC,CAAC,GAAG,KAAK,mBAAmB,UAAa,KAAK,qBAAqB,CAAC,CAAC;AAAA,EACxE;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;", "names": []}