{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-infinite-scroll_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, f as readTask, e as getIonMode, h, j as Host, k as getElement, l as config } from './index-B_U9CtaY.js';\nimport { f as findClosestIonContent, p as printIonContentErrorMsg, g as getScrollElement } from './index-BlJTBdxG.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-AaTyISnm.js';\nimport './helpers-1O4D2b7y.js';\nconst infiniteScrollCss = \"ion-infinite-scroll{display:none;width:100%}.infinite-scroll-enabled{display:block}\";\nconst InfiniteScroll = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionInfinite = createEvent(this, \"ionInfinite\", 7);\n    this.thrPx = 0;\n    this.thrPc = 0;\n    /**\n     * didFire exists so that ionInfinite\n     * does not fire multiple times if\n     * users continue to scroll after\n     * scrolling into the infinite\n     * scroll threshold.\n     */\n    this.didFire = false;\n    this.isBusy = false;\n    this.isLoading = false;\n    /**\n     * The threshold distance from the bottom\n     * of the content to call the `infinite` output event when scrolled.\n     * The threshold value can be either a percent, or\n     * in pixels. For example, use the value of `10%` for the `infinite`\n     * output event to get called when the user has scrolled 10%\n     * from the bottom of the page. Use the value `100px` when the\n     * scroll is within 100 pixels from the bottom of the page.\n     */\n    this.threshold = '15%';\n    /**\n     * If `true`, the infinite scroll will be hidden and scroll event listeners\n     * will be removed.\n     *\n     * Set this to true to disable the infinite scroll from actively\n     * trying to receive new data while scrolling. This is useful\n     * when it is known that there is no more data that can be added, and\n     * the infinite scroll is no longer needed.\n     */\n    this.disabled = false;\n    /**\n     * The position of the infinite scroll element.\n     * The value can be either `top` or `bottom`.\n     */\n    this.position = 'bottom';\n    this.onScroll = () => {\n      const scrollEl = this.scrollEl;\n      if (!scrollEl || !this.canStart()) {\n        return 1;\n      }\n      const infiniteHeight = this.el.offsetHeight;\n      if (infiniteHeight === 0) {\n        // if there is no height of this element then do nothing\n        return 2;\n      }\n      const scrollTop = scrollEl.scrollTop;\n      const scrollHeight = scrollEl.scrollHeight;\n      const height = scrollEl.offsetHeight;\n      const threshold = this.thrPc !== 0 ? height * this.thrPc : this.thrPx;\n      const distanceFromInfinite = this.position === 'bottom' ? scrollHeight - infiniteHeight - scrollTop - threshold - height : scrollTop - infiniteHeight - threshold;\n      if (distanceFromInfinite < 0) {\n        if (!this.didFire) {\n          this.isLoading = true;\n          this.didFire = true;\n          this.ionInfinite.emit();\n          return 3;\n        }\n      }\n      return 4;\n    };\n  }\n  thresholdChanged() {\n    const val = this.threshold;\n    if (val.lastIndexOf('%') > -1) {\n      this.thrPx = 0;\n      this.thrPc = parseFloat(val) / 100;\n    } else {\n      this.thrPx = parseFloat(val);\n      this.thrPc = 0;\n    }\n  }\n  disabledChanged() {\n    const disabled = this.disabled;\n    if (disabled) {\n      this.isLoading = false;\n      this.isBusy = false;\n    }\n    this.enableScrollEvents(!disabled);\n  }\n  async connectedCallback() {\n    const contentEl = findClosestIonContent(this.el);\n    if (!contentEl) {\n      printIonContentErrorMsg(this.el);\n      return;\n    }\n    this.scrollEl = await getScrollElement(contentEl);\n    this.thresholdChanged();\n    this.disabledChanged();\n    if (this.position === 'top') {\n      writeTask(() => {\n        if (this.scrollEl) {\n          this.scrollEl.scrollTop = this.scrollEl.scrollHeight - this.scrollEl.clientHeight;\n        }\n      });\n    }\n  }\n  disconnectedCallback() {\n    this.enableScrollEvents(false);\n    this.scrollEl = undefined;\n  }\n  /**\n   * Call `complete()` within the `ionInfinite` output event handler when\n   * your async operation has completed. For example, the `loading`\n   * state is while the app is performing an asynchronous operation,\n   * such as receiving more data from an AJAX request to add more items\n   * to a data list. Once the data has been received and UI updated, you\n   * then call this method to signify that the loading has completed.\n   * This method will change the infinite scroll's state from `loading`\n   * to `enabled`.\n   */\n  async complete() {\n    const scrollEl = this.scrollEl;\n    if (!this.isLoading || !scrollEl) {\n      return;\n    }\n    this.isLoading = false;\n    if (this.position === 'top') {\n      /**\n       * New content is being added at the top, but the scrollTop position stays the same,\n       * which causes a scroll jump visually. This algorithm makes sure to prevent this.\n       * (Frame 1)\n       *    - complete() is called, but the UI hasn't had time to update yet.\n       *    - Save the current content dimensions.\n       *    - Wait for the next frame using _dom.read, so the UI will be updated.\n       * (Frame 2)\n       *    - Read the new content dimensions.\n       *    - Calculate the height difference and the new scroll position.\n       *    - Delay the scroll position change until other possible dom reads are done using _dom.write to be performant.\n       * (Still frame 2, if I'm correct)\n       *    - Change the scroll position (= visually maintain the scroll position).\n       *    - Change the state to re-enable the InfiniteScroll.\n       *    - This should be after changing the scroll position, or it could\n       *    cause the InfiniteScroll to be triggered again immediately.\n       * (Frame 3)\n       *    Done.\n       */\n      this.isBusy = true;\n      // ******** DOM READ ****************\n      // Save the current content dimensions before the UI updates\n      const prev = scrollEl.scrollHeight - scrollEl.scrollTop;\n      // ******** DOM READ ****************\n      requestAnimationFrame(() => {\n        readTask(() => {\n          // UI has updated, save the new content dimensions\n          const scrollHeight = scrollEl.scrollHeight;\n          // New content was added on top, so the scroll position should be changed immediately to prevent it from jumping around\n          const newScrollTop = scrollHeight - prev;\n          // ******** DOM WRITE ****************\n          requestAnimationFrame(() => {\n            writeTask(() => {\n              scrollEl.scrollTop = newScrollTop;\n              this.isBusy = false;\n              this.didFire = false;\n            });\n          });\n        });\n      });\n    } else {\n      this.didFire = false;\n    }\n  }\n  canStart() {\n    return !this.disabled && !this.isBusy && !!this.scrollEl && !this.isLoading;\n  }\n  enableScrollEvents(shouldListen) {\n    if (this.scrollEl) {\n      if (shouldListen) {\n        this.scrollEl.addEventListener('scroll', this.onScroll);\n      } else {\n        this.scrollEl.removeEventListener('scroll', this.onScroll);\n      }\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    const disabled = this.disabled;\n    return h(Host, {\n      key: 'e844956795f69be33396ce4480aa7a54ad01b28c',\n      class: {\n        [mode]: true,\n        'infinite-scroll-loading': this.isLoading,\n        'infinite-scroll-enabled': !disabled\n      }\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"threshold\": [\"thresholdChanged\"],\n      \"disabled\": [\"disabledChanged\"]\n    };\n  }\n};\nInfiniteScroll.style = infiniteScrollCss;\nconst infiniteScrollContentIosCss = \"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-ios .infinite-loading-text{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-small-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}\";\nconst infiniteScrollContentMdCss = \"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-md .infinite-loading-text{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-small-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.infinite-scroll-content-md .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}\";\nconst InfiniteScrollContent = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n  }\n  componentDidLoad() {\n    if (this.loadingSpinner === undefined) {\n      const mode = getIonMode(this);\n      this.loadingSpinner = config.get('infiniteLoadingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'crescent'));\n    }\n  }\n  renderLoadingText() {\n    const {\n      customHTMLEnabled,\n      loadingText\n    } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", {\n        class: \"infinite-loading-text\",\n        innerHTML: sanitizeDOMString(loadingText)\n      });\n    }\n    return h(\"div\", {\n      class: \"infinite-loading-text\"\n    }, this.loadingText);\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '7c16060dcfe2a0b0fb3e2f8f4c449589a76f1baa',\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`infinite-scroll-content-${mode}`]: true\n      }\n    }, h(\"div\", {\n      key: 'a94f4d8746e053dc718f97520bd7e48cb316443a',\n      class: \"infinite-loading\"\n    }, this.loadingSpinner && h(\"div\", {\n      key: '10143d5d2a50a2a2bc5de1cee8e7ab51263bcf23',\n      class: \"infinite-loading-spinner\"\n    }, h(\"ion-spinner\", {\n      key: '8846e88191690d9c61a0b462889ed56fbfed8b0d',\n      name: this.loadingSpinner\n    })), this.loadingText !== undefined && this.renderLoadingText()));\n  }\n};\nInfiniteScrollContent.style = {\n  ios: infiniteScrollContentIosCss,\n  md: infiniteScrollContentMdCss\n};\nexport { InfiniteScroll as ion_infinite_scroll, InfiniteScrollContent as ion_infinite_scroll_content };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,oBAAoB;AAC1B,IAAM,iBAAiB,MAAM;AAAA,EAC3B,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,cAAc,YAAY,MAAM,eAAe,CAAC;AACrD,SAAK,QAAQ;AACb,SAAK,QAAQ;AAQb,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,YAAY;AAUjB,SAAK,YAAY;AAUjB,SAAK,WAAW;AAKhB,SAAK,WAAW;AAChB,SAAK,WAAW,MAAM;AACpB,YAAM,WAAW,KAAK;AACtB,UAAI,CAAC,YAAY,CAAC,KAAK,SAAS,GAAG;AACjC,eAAO;AAAA,MACT;AACA,YAAM,iBAAiB,KAAK,GAAG;AAC/B,UAAI,mBAAmB,GAAG;AAExB,eAAO;AAAA,MACT;AACA,YAAM,YAAY,SAAS;AAC3B,YAAM,eAAe,SAAS;AAC9B,YAAM,SAAS,SAAS;AACxB,YAAM,YAAY,KAAK,UAAU,IAAI,SAAS,KAAK,QAAQ,KAAK;AAChE,YAAM,uBAAuB,KAAK,aAAa,WAAW,eAAe,iBAAiB,YAAY,YAAY,SAAS,YAAY,iBAAiB;AACxJ,UAAI,uBAAuB,GAAG;AAC5B,YAAI,CAAC,KAAK,SAAS;AACjB,eAAK,YAAY;AACjB,eAAK,UAAU;AACf,eAAK,YAAY,KAAK;AACtB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,UAAM,MAAM,KAAK;AACjB,QAAI,IAAI,YAAY,GAAG,IAAI,IAAI;AAC7B,WAAK,QAAQ;AACb,WAAK,QAAQ,WAAW,GAAG,IAAI;AAAA,IACjC,OAAO;AACL,WAAK,QAAQ,WAAW,GAAG;AAC3B,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,UAAM,WAAW,KAAK;AACtB,QAAI,UAAU;AACZ,WAAK,YAAY;AACjB,WAAK,SAAS;AAAA,IAChB;AACA,SAAK,mBAAmB,CAAC,QAAQ;AAAA,EACnC;AAAA,EACM,oBAAoB;AAAA;AACxB,YAAM,YAAY,sBAAsB,KAAK,EAAE;AAC/C,UAAI,CAAC,WAAW;AACd,gCAAwB,KAAK,EAAE;AAC/B;AAAA,MACF;AACA,WAAK,WAAW,MAAM,iBAAiB,SAAS;AAChD,WAAK,iBAAiB;AACtB,WAAK,gBAAgB;AACrB,UAAI,KAAK,aAAa,OAAO;AAC3B,kBAAU,MAAM;AACd,cAAI,KAAK,UAAU;AACjB,iBAAK,SAAS,YAAY,KAAK,SAAS,eAAe,KAAK,SAAS;AAAA,UACvE;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA,EACA,uBAAuB;AACrB,SAAK,mBAAmB,KAAK;AAC7B,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWM,WAAW;AAAA;AACf,YAAM,WAAW,KAAK;AACtB,UAAI,CAAC,KAAK,aAAa,CAAC,UAAU;AAChC;AAAA,MACF;AACA,WAAK,YAAY;AACjB,UAAI,KAAK,aAAa,OAAO;AAoB3B,aAAK,SAAS;AAGd,cAAM,OAAO,SAAS,eAAe,SAAS;AAE9C,8BAAsB,MAAM;AAC1B,mBAAS,MAAM;AAEb,kBAAM,eAAe,SAAS;AAE9B,kBAAM,eAAe,eAAe;AAEpC,kCAAsB,MAAM;AAC1B,wBAAU,MAAM;AACd,yBAAS,YAAY;AACrB,qBAAK,SAAS;AACd,qBAAK,UAAU;AAAA,cACjB,CAAC;AAAA,YACH,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH,OAAO;AACL,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AAAA;AAAA,EACA,WAAW;AACT,WAAO,CAAC,KAAK,YAAY,CAAC,KAAK,UAAU,CAAC,CAAC,KAAK,YAAY,CAAC,KAAK;AAAA,EACpE;AAAA,EACA,mBAAmB,cAAc;AAC/B,QAAI,KAAK,UAAU;AACjB,UAAI,cAAc;AAChB,aAAK,SAAS,iBAAiB,UAAU,KAAK,QAAQ;AAAA,MACxD,OAAO;AACL,aAAK,SAAS,oBAAoB,UAAU,KAAK,QAAQ;AAAA,MAC3D;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,WAAW,KAAK;AACtB,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO;AAAA,QACL,CAAC,IAAI,GAAG;AAAA,QACR,2BAA2B,KAAK;AAAA,QAChC,2BAA2B,CAAC;AAAA,MAC9B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,aAAa,CAAC,kBAAkB;AAAA,MAChC,YAAY,CAAC,iBAAiB;AAAA,IAChC;AAAA,EACF;AACF;AACA,eAAe,QAAQ;AACvB,IAAM,8BAA8B;AACpC,IAAM,6BAA6B;AACnC,IAAM,wBAAwB,MAAM;AAAA,EAClC,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,oBAAoB,OAAO,IAAI,6BAA6B,2BAA2B;AAAA,EAC9F;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,mBAAmB,QAAW;AACrC,YAAM,OAAO,WAAW,IAAI;AAC5B,WAAK,iBAAiB,OAAO,IAAI,0BAA0B,OAAO,IAAI,WAAW,SAAS,QAAQ,UAAU,UAAU,CAAC;AAAA,IACzH;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,mBAAmB;AACrB,aAAO,EAAE,OAAO;AAAA,QACd,OAAO;AAAA,QACP,WAAW,kBAAkB,WAAW;AAAA,MAC1C,CAAC;AAAA,IACH;AACA,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,IACT,GAAG,KAAK,WAAW;AAAA,EACrB;AAAA,EACA,SAAS;AACP,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO;AAAA,QACL,CAAC,IAAI,GAAG;AAAA;AAAA,QAER,CAAC,2BAA2B,IAAI,EAAE,GAAG;AAAA,MACvC;AAAA,IACF,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,KAAK,kBAAkB,EAAE,OAAO;AAAA,MACjC,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,eAAe;AAAA,MAClB,KAAK;AAAA,MACL,MAAM,KAAK;AAAA,IACb,CAAC,CAAC,GAAG,KAAK,gBAAgB,UAAa,KAAK,kBAAkB,CAAC,CAAC;AAAA,EAClE;AACF;AACA,sBAAsB,QAAQ;AAAA,EAC5B,KAAK;AAAA,EACL,IAAI;AACN;", "names": []}