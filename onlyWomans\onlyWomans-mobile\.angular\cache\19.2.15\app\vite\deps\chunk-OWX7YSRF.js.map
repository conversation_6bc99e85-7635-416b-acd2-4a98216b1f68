{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/gesture-controller-BTEOs1at.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nclass GestureController {\n  constructor() {\n    this.gestureId = 0;\n    this.requestedStart = new Map();\n    this.disabledGestures = new Map();\n    this.disabledScroll = new Set();\n  }\n  /**\n   * Creates a gesture delegate based on the GestureConfig passed\n   */\n  createGesture(config) {\n    var _a;\n    return new GestureDelegate(this, this.newID(), config.name, (_a = config.priority) !== null && _a !== void 0 ? _a : 0, !!config.disableScroll);\n  }\n  /**\n   * Creates a blocker that will block any other gesture events from firing. Set in the ion-gesture component.\n   */\n  createBlocker(opts = {}) {\n    return new BlockerDelegate(this, this.newID(), opts.disable, !!opts.disableScroll);\n  }\n  start(gestureName, id, priority) {\n    if (!this.canStart(gestureName)) {\n      this.requestedStart.delete(id);\n      return false;\n    }\n    this.requestedStart.set(id, priority);\n    return true;\n  }\n  capture(gestureName, id, priority) {\n    if (!this.start(gestureName, id, priority)) {\n      return false;\n    }\n    const requestedStart = this.requestedStart;\n    let maxPriority = -1e4;\n    requestedStart.forEach(value => {\n      maxPriority = Math.max(maxPriority, value);\n    });\n    if (maxPriority === priority) {\n      this.capturedId = id;\n      requestedStart.clear();\n      const event = new CustomEvent('ionGestureCaptured', {\n        detail: {\n          gestureName\n        }\n      });\n      document.dispatchEvent(event);\n      return true;\n    }\n    requestedStart.delete(id);\n    return false;\n  }\n  release(id) {\n    this.requestedStart.delete(id);\n    if (this.capturedId === id) {\n      this.capturedId = undefined;\n    }\n  }\n  disableGesture(gestureName, id) {\n    let set = this.disabledGestures.get(gestureName);\n    if (set === undefined) {\n      set = new Set();\n      this.disabledGestures.set(gestureName, set);\n    }\n    set.add(id);\n  }\n  enableGesture(gestureName, id) {\n    const set = this.disabledGestures.get(gestureName);\n    if (set !== undefined) {\n      set.delete(id);\n    }\n  }\n  disableScroll(id) {\n    this.disabledScroll.add(id);\n    if (this.disabledScroll.size === 1) {\n      document.body.classList.add(BACKDROP_NO_SCROLL);\n    }\n  }\n  enableScroll(id) {\n    this.disabledScroll.delete(id);\n    if (this.disabledScroll.size === 0) {\n      document.body.classList.remove(BACKDROP_NO_SCROLL);\n    }\n  }\n  canStart(gestureName) {\n    if (this.capturedId !== undefined) {\n      // a gesture already captured\n      return false;\n    }\n    if (this.isDisabled(gestureName)) {\n      return false;\n    }\n    return true;\n  }\n  isCaptured() {\n    return this.capturedId !== undefined;\n  }\n  isScrollDisabled() {\n    return this.disabledScroll.size > 0;\n  }\n  isDisabled(gestureName) {\n    const disabled = this.disabledGestures.get(gestureName);\n    if (disabled && disabled.size > 0) {\n      return true;\n    }\n    return false;\n  }\n  newID() {\n    this.gestureId++;\n    return this.gestureId;\n  }\n}\nclass GestureDelegate {\n  constructor(ctrl, id, name, priority, disableScroll) {\n    this.id = id;\n    this.name = name;\n    this.disableScroll = disableScroll;\n    this.priority = priority * 1000000 + id;\n    this.ctrl = ctrl;\n  }\n  canStart() {\n    if (!this.ctrl) {\n      return false;\n    }\n    return this.ctrl.canStart(this.name);\n  }\n  start() {\n    if (!this.ctrl) {\n      return false;\n    }\n    return this.ctrl.start(this.name, this.id, this.priority);\n  }\n  capture() {\n    if (!this.ctrl) {\n      return false;\n    }\n    const captured = this.ctrl.capture(this.name, this.id, this.priority);\n    if (captured && this.disableScroll) {\n      this.ctrl.disableScroll(this.id);\n    }\n    return captured;\n  }\n  release() {\n    if (this.ctrl) {\n      this.ctrl.release(this.id);\n      if (this.disableScroll) {\n        this.ctrl.enableScroll(this.id);\n      }\n    }\n  }\n  destroy() {\n    this.release();\n    this.ctrl = undefined;\n  }\n}\nclass BlockerDelegate {\n  constructor(ctrl, id, disable, disableScroll) {\n    this.id = id;\n    this.disable = disable;\n    this.disableScroll = disableScroll;\n    this.ctrl = ctrl;\n  }\n  block() {\n    if (!this.ctrl) {\n      return;\n    }\n    if (this.disable) {\n      for (const gesture of this.disable) {\n        this.ctrl.disableGesture(gesture, this.id);\n      }\n    }\n    if (this.disableScroll) {\n      this.ctrl.disableScroll(this.id);\n    }\n  }\n  unblock() {\n    if (!this.ctrl) {\n      return;\n    }\n    if (this.disable) {\n      for (const gesture of this.disable) {\n        this.ctrl.enableGesture(gesture, this.id);\n      }\n    }\n    if (this.disableScroll) {\n      this.ctrl.enableScroll(this.id);\n    }\n  }\n  destroy() {\n    this.unblock();\n    this.ctrl = undefined;\n  }\n}\nconst BACKDROP_NO_SCROLL = 'backdrop-no-scroll';\nconst GESTURE_CONTROLLER = new GestureController();\nexport { BACKDROP_NO_SCROLL as B, GESTURE_CONTROLLER as G };"], "mappings": ";AAGA,IAAM,oBAAN,MAAwB;AAAA,EACtB,cAAc;AACZ,SAAK,YAAY;AACjB,SAAK,iBAAiB,oBAAI,IAAI;AAC9B,SAAK,mBAAmB,oBAAI,IAAI;AAChC,SAAK,iBAAiB,oBAAI,IAAI;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,QAAQ;AACpB,QAAI;AACJ,WAAO,IAAI,gBAAgB,MAAM,KAAK,MAAM,GAAG,OAAO,OAAO,KAAK,OAAO,cAAc,QAAQ,OAAO,SAAS,KAAK,GAAG,CAAC,CAAC,OAAO,aAAa;AAAA,EAC/I;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,OAAO,CAAC,GAAG;AACvB,WAAO,IAAI,gBAAgB,MAAM,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,KAAK,aAAa;AAAA,EACnF;AAAA,EACA,MAAM,aAAa,IAAI,UAAU;AAC/B,QAAI,CAAC,KAAK,SAAS,WAAW,GAAG;AAC/B,WAAK,eAAe,OAAO,EAAE;AAC7B,aAAO;AAAA,IACT;AACA,SAAK,eAAe,IAAI,IAAI,QAAQ;AACpC,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,aAAa,IAAI,UAAU;AACjC,QAAI,CAAC,KAAK,MAAM,aAAa,IAAI,QAAQ,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,KAAK;AAC5B,QAAI,cAAc;AAClB,mBAAe,QAAQ,WAAS;AAC9B,oBAAc,KAAK,IAAI,aAAa,KAAK;AAAA,IAC3C,CAAC;AACD,QAAI,gBAAgB,UAAU;AAC5B,WAAK,aAAa;AAClB,qBAAe,MAAM;AACrB,YAAM,QAAQ,IAAI,YAAY,sBAAsB;AAAA,QAClD,QAAQ;AAAA,UACN;AAAA,QACF;AAAA,MACF,CAAC;AACD,eAAS,cAAc,KAAK;AAC5B,aAAO;AAAA,IACT;AACA,mBAAe,OAAO,EAAE;AACxB,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,IAAI;AACV,SAAK,eAAe,OAAO,EAAE;AAC7B,QAAI,KAAK,eAAe,IAAI;AAC1B,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,eAAe,aAAa,IAAI;AAC9B,QAAI,MAAM,KAAK,iBAAiB,IAAI,WAAW;AAC/C,QAAI,QAAQ,QAAW;AACrB,YAAM,oBAAI,IAAI;AACd,WAAK,iBAAiB,IAAI,aAAa,GAAG;AAAA,IAC5C;AACA,QAAI,IAAI,EAAE;AAAA,EACZ;AAAA,EACA,cAAc,aAAa,IAAI;AAC7B,UAAM,MAAM,KAAK,iBAAiB,IAAI,WAAW;AACjD,QAAI,QAAQ,QAAW;AACrB,UAAI,OAAO,EAAE;AAAA,IACf;AAAA,EACF;AAAA,EACA,cAAc,IAAI;AAChB,SAAK,eAAe,IAAI,EAAE;AAC1B,QAAI,KAAK,eAAe,SAAS,GAAG;AAClC,eAAS,KAAK,UAAU,IAAI,kBAAkB;AAAA,IAChD;AAAA,EACF;AAAA,EACA,aAAa,IAAI;AACf,SAAK,eAAe,OAAO,EAAE;AAC7B,QAAI,KAAK,eAAe,SAAS,GAAG;AAClC,eAAS,KAAK,UAAU,OAAO,kBAAkB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,SAAS,aAAa;AACpB,QAAI,KAAK,eAAe,QAAW;AAEjC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,WAAW,WAAW,GAAG;AAChC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AACX,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,eAAe,OAAO;AAAA,EACpC;AAAA,EACA,WAAW,aAAa;AACtB,UAAM,WAAW,KAAK,iBAAiB,IAAI,WAAW;AACtD,QAAI,YAAY,SAAS,OAAO,GAAG;AACjC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,SAAK;AACL,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,MAAM,IAAI,MAAM,UAAU,eAAe;AACnD,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,gBAAgB;AACrB,SAAK,WAAW,WAAW,MAAU;AACrC,SAAK,OAAO;AAAA,EACd;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,MAAM;AACd,aAAO;AAAA,IACT;AACA,WAAO,KAAK,KAAK,SAAS,KAAK,IAAI;AAAA,EACrC;AAAA,EACA,QAAQ;AACN,QAAI,CAAC,KAAK,MAAM;AACd,aAAO;AAAA,IACT;AACA,WAAO,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,QAAQ;AAAA,EAC1D;AAAA,EACA,UAAU;AACR,QAAI,CAAC,KAAK,MAAM;AACd,aAAO;AAAA,IACT;AACA,UAAM,WAAW,KAAK,KAAK,QAAQ,KAAK,MAAM,KAAK,IAAI,KAAK,QAAQ;AACpE,QAAI,YAAY,KAAK,eAAe;AAClC,WAAK,KAAK,cAAc,KAAK,EAAE;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,QAAQ,KAAK,EAAE;AACzB,UAAI,KAAK,eAAe;AACtB,aAAK,KAAK,aAAa,KAAK,EAAE;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,MAAM,IAAI,SAAS,eAAe;AAC5C,SAAK,KAAK;AACV,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,QAAQ;AACN,QAAI,CAAC,KAAK,MAAM;AACd;AAAA,IACF;AACA,QAAI,KAAK,SAAS;AAChB,iBAAW,WAAW,KAAK,SAAS;AAClC,aAAK,KAAK,eAAe,SAAS,KAAK,EAAE;AAAA,MAC3C;AAAA,IACF;AACA,QAAI,KAAK,eAAe;AACtB,WAAK,KAAK,cAAc,KAAK,EAAE;AAAA,IACjC;AAAA,EACF;AAAA,EACA,UAAU;AACR,QAAI,CAAC,KAAK,MAAM;AACd;AAAA,IACF;AACA,QAAI,KAAK,SAAS;AAChB,iBAAW,WAAW,KAAK,SAAS;AAClC,aAAK,KAAK,cAAc,SAAS,KAAK,EAAE;AAAA,MAC1C;AAAA,IACF;AACA,QAAI,KAAK,eAAe;AACtB,WAAK,KAAK,aAAa,KAAK,EAAE;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,qBAAqB;AAC3B,IAAM,qBAAqB,IAAI,kBAAkB;", "names": []}