{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/components/cubic-bezier.js", "../../../../../../node_modules/@ionic/core/components/ionic-global.js", "../../../../../../node_modules/@ionic/core/components/config.js", "../../../../../../node_modules/@ionic/core/components/index5.js", "../../../../../../node_modules/@ionic/core/components/framework-delegate.js", "../../../../../../node_modules/@ionic/core/components/overlays.js", "../../../../../../node_modules/@ionic/core/components/theme.js", "../../../../../../node_modules/@ionic/core/components/index.js", "../../../../../../node_modules/@ionic/angular/fesm2022/ionic-angular-common.mjs"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Based on:\n * https://stackoverflow.com/questions/7348009/y-coordinate-for-a-given-x-cubic-bezier\n * https://math.stackexchange.com/questions/26846/is-there-an-explicit-form-for-cubic-b%C3%A9zier-curves\n */\n/**\n * EXPERIMENTAL\n * Given a cubic-bezier curve, get the x value (time) given\n * the y value (progression).\n * Ex: cubic-bezier(0.32, 0.72, 0, 1);\n * P0: (0, 0)\n * P1: (0.32, 0.72)\n * P2: (0, 1)\n * P3: (1, 1)\n *\n * If you give a cubic bezier curve that never reaches the\n * provided progression, this function will return an empty array.\n */\nconst getTimeGivenProgression = (p0, p1, p2, p3, progression) => {\n  return solveCubicBezier(p0[1], p1[1], p2[1], p3[1], progression).map(tValue => {\n    return solveCubicParametricEquation(p0[0], p1[0], p2[0], p3[0], tValue);\n  });\n};\n/**\n * Solve a cubic equation in one dimension (time)\n */\nconst solveCubicParametricEquation = (p0, p1, p2, p3, t) => {\n  const partA = 3 * p1 * Math.pow(t - 1, 2);\n  const partB = -3 * p2 * t + 3 * p2 + p3 * t;\n  const partC = p0 * Math.pow(t - 1, 3);\n  return t * (partA + t * partB) - partC;\n};\n/**\n * Find the `t` value for a cubic bezier using Cardano's formula\n */\nconst solveCubicBezier = (p0, p1, p2, p3, refPoint) => {\n  p0 -= refPoint;\n  p1 -= refPoint;\n  p2 -= refPoint;\n  p3 -= refPoint;\n  const roots = solveCubicEquation(p3 - 3 * p2 + 3 * p1 - p0, 3 * p2 - 6 * p1 + 3 * p0, 3 * p1 - 3 * p0, p0);\n  return roots.filter(root => root >= 0 && root <= 1);\n};\nconst solveQuadraticEquation = (a, b, c) => {\n  const discriminant = b * b - 4 * a * c;\n  if (discriminant < 0) {\n    return [];\n  } else {\n    return [(-b + Math.sqrt(discriminant)) / (2 * a), (-b - Math.sqrt(discriminant)) / (2 * a)];\n  }\n};\nconst solveCubicEquation = (a, b, c, d) => {\n  if (a === 0) {\n    return solveQuadraticEquation(b, c, d);\n  }\n  b /= a;\n  c /= a;\n  d /= a;\n  const p = (3 * c - b * b) / 3;\n  const q = (2 * b * b * b - 9 * b * c + 27 * d) / 27;\n  if (p === 0) {\n    return [Math.pow(-q, 1 / 3)];\n  } else if (q === 0) {\n    return [Math.sqrt(-p), -Math.sqrt(-p)];\n  }\n  const discriminant = Math.pow(q / 2, 2) + Math.pow(p / 3, 3);\n  if (discriminant === 0) {\n    return [Math.pow(q / 2, 1 / 2) - b / 3];\n  } else if (discriminant > 0) {\n    return [Math.pow(-(q / 2) + Math.sqrt(discriminant), 1 / 3) - Math.pow(q / 2 + Math.sqrt(discriminant), 1 / 3) - b / 3];\n  }\n  const r = Math.sqrt(Math.pow(-(p / 3), 3));\n  const phi = Math.acos(-(q / (2 * Math.sqrt(Math.pow(-(p / 3), 3)))));\n  const s = 2 * Math.pow(r, 1 / 3);\n  return [s * Math.cos(phi / 3) - b / 3, s * Math.cos((phi + 2 * Math.PI) / 3) - b / 3, s * Math.cos((phi + 4 * Math.PI) / 3) - b / 3];\n};\nexport { getTimeGivenProgression as g };", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { setMode, getMode } from '@stencil/core/internal/client';\nimport { c as config, b as configFromSession, d as configFromURL, s as saveConfig, p as printIonWarning } from './index4.js';\nconst getPlatforms = win => setupPlatforms(win);\nconst isPlatform = (winOrPlatform, platform) => {\n  if (typeof winOrPlatform === 'string') {\n    platform = winOrPlatform;\n    winOrPlatform = undefined;\n  }\n  return getPlatforms(winOrPlatform).includes(platform);\n};\nconst setupPlatforms = (win = window) => {\n  if (typeof win === 'undefined') {\n    return [];\n  }\n  win.Ionic = win.Ionic || {};\n  let platforms = win.Ionic.platforms;\n  if (platforms == null) {\n    platforms = win.Ionic.platforms = detectPlatforms(win);\n    platforms.forEach(p => win.document.documentElement.classList.add(`plt-${p}`));\n  }\n  return platforms;\n};\nconst detectPlatforms = win => {\n  const customPlatformMethods = config.get('platform');\n  return Object.keys(PLATFORMS_MAP).filter(p => {\n    const customMethod = customPlatformMethods === null || customPlatformMethods === void 0 ? void 0 : customPlatformMethods[p];\n    return typeof customMethod === 'function' ? customMethod(win) : PLATFORMS_MAP[p](win);\n  });\n};\nconst isMobileWeb = win => isMobile(win) && !isHybrid(win);\nconst isIpad = win => {\n  // iOS 12 and below\n  if (testUserAgent(win, /iPad/i)) {\n    return true;\n  }\n  // iOS 13+\n  if (testUserAgent(win, /Macintosh/i) && isMobile(win)) {\n    return true;\n  }\n  return false;\n};\nconst isIphone = win => testUserAgent(win, /iPhone/i);\nconst isIOS = win => testUserAgent(win, /iPhone|iPod/i) || isIpad(win);\nconst isAndroid = win => testUserAgent(win, /android|sink/i);\nconst isAndroidTablet = win => {\n  return isAndroid(win) && !testUserAgent(win, /mobile/i);\n};\nconst isPhablet = win => {\n  const width = win.innerWidth;\n  const height = win.innerHeight;\n  const smallest = Math.min(width, height);\n  const largest = Math.max(width, height);\n  return smallest > 390 && smallest < 520 && largest > 620 && largest < 800;\n};\nconst isTablet = win => {\n  const width = win.innerWidth;\n  const height = win.innerHeight;\n  const smallest = Math.min(width, height);\n  const largest = Math.max(width, height);\n  return isIpad(win) || isAndroidTablet(win) || smallest > 460 && smallest < 820 && largest > 780 && largest < 1400;\n};\nconst isMobile = win => matchMedia(win, '(any-pointer:coarse)');\nconst isDesktop = win => !isMobile(win);\nconst isHybrid = win => isCordova(win) || isCapacitorNative(win);\nconst isCordova = win => !!(win['cordova'] || win['phonegap'] || win['PhoneGap']);\nconst isCapacitorNative = win => {\n  const capacitor = win['Capacitor'];\n  // TODO(ROU-11693): Remove when we no longer support Capacitor 2, which does not have isNativePlatform\n  return !!((capacitor === null || capacitor === void 0 ? void 0 : capacitor.isNative) || (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isNativePlatform) && !!capacitor.isNativePlatform());\n};\nconst isElectron = win => testUserAgent(win, /electron/i);\nconst isPWA = win => {\n  var _a;\n  return !!(((_a = win.matchMedia) === null || _a === void 0 ? void 0 : _a.call(win, '(display-mode: standalone)').matches) || win.navigator.standalone);\n};\nconst testUserAgent = (win, expr) => expr.test(win.navigator.userAgent);\nconst matchMedia = (win, query) => {\n  var _a;\n  return (_a = win.matchMedia) === null || _a === void 0 ? void 0 : _a.call(win, query).matches;\n};\nconst PLATFORMS_MAP = {\n  ipad: isIpad,\n  iphone: isIphone,\n  ios: isIOS,\n  android: isAndroid,\n  phablet: isPhablet,\n  tablet: isTablet,\n  cordova: isCordova,\n  capacitor: isCapacitorNative,\n  electron: isElectron,\n  pwa: isPWA,\n  mobile: isMobile,\n  mobileweb: isMobileWeb,\n  desktop: isDesktop,\n  hybrid: isHybrid\n};\n\n// TODO(FW-2832): types\nlet defaultMode;\nconst getIonMode = ref => {\n  return ref && getMode(ref) || defaultMode;\n};\nconst initialize = (userConfig = {}) => {\n  if (typeof window === 'undefined') {\n    return;\n  }\n  const doc = window.document;\n  const win = window;\n  const Ionic = win.Ionic = win.Ionic || {};\n  // create the Ionic.config from raw config object (if it exists)\n  // and convert Ionic.config into a ConfigApi that has a get() fn\n  const configObj = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, configFromSession(win)), {\n    persistConfig: false\n  }), Ionic.config), configFromURL(win)), userConfig);\n  config.reset(configObj);\n  if (config.getBoolean('persistConfig')) {\n    saveConfig(win, configObj);\n  }\n  // Setup platforms\n  setupPlatforms(win);\n  // first see if the mode was set as an attribute on <html>\n  // which could have been set by the user, or by pre-rendering\n  // otherwise get the mode via config settings, and fallback to md\n  Ionic.config = config;\n  Ionic.mode = defaultMode = config.get('mode', doc.documentElement.getAttribute('mode') || (isPlatform(win, 'ios') ? 'ios' : 'md'));\n  config.set('mode', defaultMode);\n  doc.documentElement.setAttribute('mode', defaultMode);\n  doc.documentElement.classList.add(defaultMode);\n  if (config.getBoolean('_testing')) {\n    config.set('animated', false);\n  }\n  const isIonicElement = elm => {\n    var _a;\n    return (_a = elm.tagName) === null || _a === void 0 ? void 0 : _a.startsWith('ION-');\n  };\n  const isAllowedIonicModeValue = elmMode => ['ios', 'md'].includes(elmMode);\n  setMode(elm => {\n    while (elm) {\n      const elmMode = elm.mode || elm.getAttribute('mode');\n      if (elmMode) {\n        if (isAllowedIonicModeValue(elmMode)) {\n          return elmMode;\n        } else if (isIonicElement(elm)) {\n          printIonWarning('Invalid ionic mode: \"' + elmMode + '\", expected: \"ios\" or \"md\"');\n        }\n      }\n      elm = elm.parentElement;\n    }\n    return defaultMode;\n  });\n};\nexport { isPlatform as a, getIonMode as b, getPlatforms as g, initialize as i };", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { a as printIonError } from './index4.js';\n\n/**\n * Does a simple sanitization of all elements\n * in an untrusted string\n */\nconst sanitizeDOMString = untrustedString => {\n  try {\n    if (untrustedString instanceof IonicSafeString) {\n      return untrustedString.value;\n    }\n    if (!isSanitizerEnabled() || typeof untrustedString !== 'string' || untrustedString === '') {\n      return untrustedString;\n    }\n    /**\n     * onload is fired when appending to a document\n     * fragment in Chrome. If a string\n     * contains onload then we should not\n     * attempt to add this to the fragment.\n     */\n    if (untrustedString.includes('onload=')) {\n      return '';\n    }\n    /**\n     * Create a document fragment\n     * separate from the main DOM,\n     * create a div to do our work in\n     */\n    const documentFragment = document.createDocumentFragment();\n    const workingDiv = document.createElement('div');\n    documentFragment.appendChild(workingDiv);\n    workingDiv.innerHTML = untrustedString;\n    /**\n     * Remove any elements\n     * that are blocked\n     */\n    blockedTags.forEach(blockedTag => {\n      const getElementsToRemove = documentFragment.querySelectorAll(blockedTag);\n      for (let elementIndex = getElementsToRemove.length - 1; elementIndex >= 0; elementIndex--) {\n        const element = getElementsToRemove[elementIndex];\n        if (element.parentNode) {\n          element.parentNode.removeChild(element);\n        } else {\n          documentFragment.removeChild(element);\n        }\n        /**\n         * We still need to sanitize\n         * the children of this element\n         * as they are left behind\n         */\n        const childElements = getElementChildren(element);\n        /* eslint-disable-next-line */\n        for (let childIndex = 0; childIndex < childElements.length; childIndex++) {\n          sanitizeElement(childElements[childIndex]);\n        }\n      }\n    });\n    /**\n     * Go through remaining elements and remove\n     * non-allowed attribs\n     */\n    // IE does not support .children on document fragments, only .childNodes\n    const dfChildren = getElementChildren(documentFragment);\n    /* eslint-disable-next-line */\n    for (let childIndex = 0; childIndex < dfChildren.length; childIndex++) {\n      sanitizeElement(dfChildren[childIndex]);\n    }\n    // Append document fragment to div\n    const fragmentDiv = document.createElement('div');\n    fragmentDiv.appendChild(documentFragment);\n    // First child is always the div we did our work in\n    const getInnerDiv = fragmentDiv.querySelector('div');\n    return getInnerDiv !== null ? getInnerDiv.innerHTML : fragmentDiv.innerHTML;\n  } catch (err) {\n    printIonError('sanitizeDOMString', err);\n    return '';\n  }\n};\n/**\n * Clean up current element based on allowed attributes\n * and then recursively dig down into any child elements to\n * clean those up as well\n */\n// TODO(FW-2832): type (using Element triggers other type errors as well)\nconst sanitizeElement = element => {\n  // IE uses childNodes, so ignore nodes that are not elements\n  if (element.nodeType && element.nodeType !== 1) {\n    return;\n  }\n  /**\n   * If attributes is not a NamedNodeMap\n   * then we should remove the element entirely.\n   * This helps avoid DOM Clobbering attacks where\n   * attributes is overridden.\n   */\n  if (typeof NamedNodeMap !== 'undefined' && !(element.attributes instanceof NamedNodeMap)) {\n    element.remove();\n    return;\n  }\n  for (let i = element.attributes.length - 1; i >= 0; i--) {\n    const attribute = element.attributes.item(i);\n    const attributeName = attribute.name;\n    // remove non-allowed attribs\n    if (!allowedAttributes.includes(attributeName.toLowerCase())) {\n      element.removeAttribute(attributeName);\n      continue;\n    }\n    // clean up any allowed attribs\n    // that attempt to do any JS funny-business\n    const attributeValue = attribute.value;\n    /**\n     * We also need to check the property value\n     * as javascript: can allow special characters\n     * such as &Tab; and still be valid (i.e. java&Tab;script)\n     */\n    const propertyValue = element[attributeName];\n    /* eslint-disable */\n    if (attributeValue != null && attributeValue.toLowerCase().includes('javascript:') || propertyValue != null && propertyValue.toLowerCase().includes('javascript:')) {\n      element.removeAttribute(attributeName);\n    }\n    /* eslint-enable */\n  }\n  /**\n   * Sanitize any nested children\n   */\n  const childElements = getElementChildren(element);\n  /* eslint-disable-next-line */\n  for (let i = 0; i < childElements.length; i++) {\n    sanitizeElement(childElements[i]);\n  }\n};\n/**\n * IE doesn't always support .children\n * so we revert to .childNodes instead\n */\n// TODO(FW-2832): type\nconst getElementChildren = el => {\n  return el.children != null ? el.children : el.childNodes;\n};\nconst isSanitizerEnabled = () => {\n  var _a;\n  const win = window;\n  const config = (_a = win === null || win === void 0 ? void 0 : win.Ionic) === null || _a === void 0 ? void 0 : _a.config;\n  if (config) {\n    if (config.get) {\n      return config.get('sanitizerEnabled', true);\n    } else {\n      return config.sanitizerEnabled === true || config.sanitizerEnabled === undefined;\n    }\n  }\n  return true;\n};\nconst allowedAttributes = ['class', 'id', 'href', 'src', 'name', 'slot'];\nconst blockedTags = ['script', 'style', 'iframe', 'meta', 'link', 'object', 'embed'];\nclass IonicSafeString {\n  constructor(value) {\n    this.value = value;\n  }\n}\nconst setupConfig = config => {\n  const win = window;\n  const Ionic = win.Ionic;\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n  if (Ionic && Ionic.config && Ionic.config.constructor.name !== 'Object') {\n    return;\n  }\n  win.Ionic = win.Ionic || {};\n  win.Ionic.config = Object.assign(Object.assign({}, win.Ionic.config), config);\n  return win.Ionic.config;\n};\nconst getMode = () => {\n  var _a;\n  const win = window;\n  const config = (_a = win === null || win === void 0 ? void 0 : win.Ionic) === null || _a === void 0 ? void 0 : _a.config;\n  if (config) {\n    if (config.mode) {\n      return config.mode;\n    } else {\n      return config.get('mode');\n    }\n  }\n  return 'md';\n};\nconst ENABLE_HTML_CONTENT_DEFAULT = false;\nexport { ENABLE_HTML_CONTENT_DEFAULT as E, IonicSafeString as I, sanitizeDOMString as a, getMode as g, setupConfig as s };", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index9.js';\nimport { MENU_BACK_BUTTON_PRIORITY } from './hardware-back-button.js';\nimport { p as printIonWarning } from './index4.js';\nimport { c as componentOnReady } from './helpers.js';\nimport { b as getIonMode } from './ionic-global.js';\nimport { c as createAnimation } from './animation.js';\n\n/**\n * baseAnimation\n * Base class which is extended by the various types. Each\n * type will provide their own animations for open and close\n * and registers itself with Menu.\n */\nconst baseAnimation = isIos => {\n  // https://material.io/guidelines/motion/movement.html#movement-movement-in-out-of-screen-bounds\n  // https://material.io/guidelines/motion/duration-easing.html#duration-easing-natural-easing-curves\n  /**\n   * \"Apply the sharp curve to items temporarily leaving the screen that may return\n   * from the same exit point. When they return, use the deceleration curve. On mobile,\n   * this transition typically occurs over 300ms\" -- MD Motion Guide\n   */\n  return createAnimation().duration(isIos ? 400 : 300);\n};\n\n/**\n * Menu Overlay Type\n * The menu slides over the content. The content\n * itself, which is under the menu, does not move.\n */\nconst menuOverlayAnimation = menu => {\n  let closedX;\n  let openedX;\n  const width = menu.width + 8;\n  const menuAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  if (menu.isEndSide) {\n    // right side\n    closedX = width + 'px';\n    openedX = '0px';\n  } else {\n    // left side\n    closedX = -width + 'px';\n    openedX = '0px';\n  }\n  menuAnimation.addElement(menu.menuInnerEl).fromTo('transform', `translateX(${closedX})`, `translateX(${openedX})`);\n  const mode = getIonMode(menu);\n  const isIos = mode === 'ios';\n  const opacity = isIos ? 0.2 : 0.25;\n  backdropAnimation.addElement(menu.backdropEl).fromTo('opacity', 0.01, opacity);\n  return baseAnimation(isIos).addAnimation([menuAnimation, backdropAnimation]);\n};\n\n/**\n * Menu Push Type\n * The content slides over to reveal the menu underneath.\n * The menu itself also slides over to reveal its bad self.\n */\nconst menuPushAnimation = menu => {\n  let contentOpenedX;\n  let menuClosedX;\n  const mode = getIonMode(menu);\n  const width = menu.width;\n  if (menu.isEndSide) {\n    contentOpenedX = -width + 'px';\n    menuClosedX = width + 'px';\n  } else {\n    contentOpenedX = width + 'px';\n    menuClosedX = -width + 'px';\n  }\n  const menuAnimation = createAnimation().addElement(menu.menuInnerEl).fromTo('transform', `translateX(${menuClosedX})`, 'translateX(0px)');\n  const contentAnimation = createAnimation().addElement(menu.contentEl).fromTo('transform', 'translateX(0px)', `translateX(${contentOpenedX})`);\n  const backdropAnimation = createAnimation().addElement(menu.backdropEl).fromTo('opacity', 0.01, 0.32);\n  return baseAnimation(mode === 'ios').addAnimation([menuAnimation, contentAnimation, backdropAnimation]);\n};\n\n/**\n * Menu Reveal Type\n * The content slides over to reveal the menu underneath.\n * The menu itself, which is under the content, does not move.\n */\nconst menuRevealAnimation = menu => {\n  const mode = getIonMode(menu);\n  const openedX = menu.width * (menu.isEndSide ? -1 : 1) + 'px';\n  const contentOpen = createAnimation().addElement(menu.contentEl) // REVIEW\n  .fromTo('transform', 'translateX(0px)', `translateX(${openedX})`);\n  return baseAnimation(mode === 'ios').addAnimation(contentOpen);\n};\nconst createMenuController = () => {\n  const menuAnimations = new Map();\n  const menus = [];\n  const open = async menu => {\n    const menuEl = await get(menu, true);\n    if (menuEl) {\n      return menuEl.open();\n    }\n    return false;\n  };\n  const close = async menu => {\n    const menuEl = await (menu !== undefined ? get(menu, true) : getOpen());\n    if (menuEl !== undefined) {\n      return menuEl.close();\n    }\n    return false;\n  };\n  const toggle = async menu => {\n    const menuEl = await get(menu, true);\n    if (menuEl) {\n      return menuEl.toggle();\n    }\n    return false;\n  };\n  const enable = async (shouldEnable, menu) => {\n    const menuEl = await get(menu);\n    if (menuEl) {\n      menuEl.disabled = !shouldEnable;\n    }\n    return menuEl;\n  };\n  const swipeGesture = async (shouldEnable, menu) => {\n    const menuEl = await get(menu);\n    if (menuEl) {\n      menuEl.swipeGesture = shouldEnable;\n    }\n    return menuEl;\n  };\n  const isOpen = async menu => {\n    if (menu != null) {\n      const menuEl = await get(menu);\n      // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n      return menuEl !== undefined && menuEl.isOpen();\n    } else {\n      const menuEl = await getOpen();\n      return menuEl !== undefined;\n    }\n  };\n  const isEnabled = async menu => {\n    const menuEl = await get(menu);\n    if (menuEl) {\n      return !menuEl.disabled;\n    }\n    return false;\n  };\n  /**\n   * Finds and returns the menu specified by \"menu\" if registered.\n   * @param menu - The side or ID of the desired menu\n   * @param logOnMultipleSideMenus - If true, this function will log a warning\n   * if \"menu\" is a side but multiple menus on the same side were found. Since this function\n   * is used in multiple places, we default this log to false so that the calling\n   * functions can choose whether or not it is appropriate to log this warning.\n   */\n  const get = async (menu, logOnMultipleSideMenus = false) => {\n    await waitUntilReady();\n    if (menu === 'start' || menu === 'end') {\n      // there could be more than one menu on the same side\n      // so first try to get the enabled one\n      const menuRefs = menus.filter(m => m.side === menu && !m.disabled);\n      if (menuRefs.length >= 1) {\n        if (menuRefs.length > 1 && logOnMultipleSideMenus) {\n          printIonWarning(`menuController queried for a menu on the \"${menu}\" side, but ${menuRefs.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`, menuRefs.map(m => m.el));\n        }\n        return menuRefs[0].el;\n      }\n      // didn't find a menu side that is enabled\n      // so try to get the first menu side found\n      const sideMenuRefs = menus.filter(m => m.side === menu);\n      if (sideMenuRefs.length >= 1) {\n        if (sideMenuRefs.length > 1 && logOnMultipleSideMenus) {\n          printIonWarning(`menuController queried for a menu on the \"${menu}\" side, but ${sideMenuRefs.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`, sideMenuRefs.map(m => m.el));\n        }\n        return sideMenuRefs[0].el;\n      }\n    } else if (menu != null) {\n      // the menuId was not left or right\n      // so try to get the menu by its \"id\"\n      return find(m => m.menuId === menu);\n    }\n    // return the first enabled menu\n    const menuEl = find(m => !m.disabled);\n    if (menuEl) {\n      return menuEl;\n    }\n    // get the first menu in the array, if one exists\n    return menus.length > 0 ? menus[0].el : undefined;\n  };\n  /**\n   * Get the instance of the opened menu. Returns `null` if a menu is not found.\n   */\n  const getOpen = async () => {\n    await waitUntilReady();\n    return _getOpenSync();\n  };\n  /**\n   * Get all menu instances.\n   */\n  const getMenus = async () => {\n    await waitUntilReady();\n    return getMenusSync();\n  };\n  /**\n   * Get whether or not a menu is animating. Returns `true` if any\n   * menu is currently animating.\n   */\n  const isAnimating = async () => {\n    await waitUntilReady();\n    return isAnimatingSync();\n  };\n  const registerAnimation = (name, animation) => {\n    menuAnimations.set(name, animation);\n  };\n  const _register = menu => {\n    if (menus.indexOf(menu) < 0) {\n      menus.push(menu);\n    }\n  };\n  const _unregister = menu => {\n    const index = menus.indexOf(menu);\n    if (index > -1) {\n      menus.splice(index, 1);\n    }\n  };\n  const _setOpen = async (menu, shouldOpen, animated, role) => {\n    if (isAnimatingSync()) {\n      return false;\n    }\n    if (shouldOpen) {\n      const openedMenu = await getOpen();\n      if (openedMenu && menu.el !== openedMenu) {\n        await openedMenu.setOpen(false, false);\n      }\n    }\n    return menu._setOpen(shouldOpen, animated, role);\n  };\n  const _createAnimation = (type, menuCmp) => {\n    const animationBuilder = menuAnimations.get(type); // TODO(FW-2832): type\n    if (!animationBuilder) {\n      throw new Error('animation not registered');\n    }\n    const animation = animationBuilder(menuCmp);\n    return animation;\n  };\n  const _getOpenSync = () => {\n    return find(m => m._isOpen);\n  };\n  const getMenusSync = () => {\n    return menus.map(menu => menu.el);\n  };\n  const isAnimatingSync = () => {\n    return menus.some(menu => menu.isAnimating);\n  };\n  const find = predicate => {\n    const instance = menus.find(predicate);\n    if (instance !== undefined) {\n      return instance.el;\n    }\n    return undefined;\n  };\n  const waitUntilReady = () => {\n    return Promise.all(Array.from(document.querySelectorAll('ion-menu')).map(menu => new Promise(resolve => componentOnReady(menu, resolve))));\n  };\n  registerAnimation('reveal', menuRevealAnimation);\n  registerAnimation('push', menuPushAnimation);\n  registerAnimation('overlay', menuOverlayAnimation);\n  doc === null || doc === void 0 ? void 0 : doc.addEventListener('ionBackButton', ev => {\n    const openMenu = _getOpenSync();\n    if (openMenu) {\n      ev.detail.register(MENU_BACK_BUTTON_PRIORITY, () => {\n        return openMenu.close();\n      });\n    }\n  });\n  return {\n    registerAnimation,\n    get,\n    getMenus,\n    getOpen,\n    isEnabled,\n    swipeGesture,\n    isAnimating,\n    isOpen,\n    enable,\n    toggle,\n    close,\n    open,\n    _getOpenSync,\n    _createAnimation,\n    _register,\n    _unregister,\n    _setOpen\n  };\n};\nconst menuController = /*@__PURE__*/createMenuController();\nexport { menuController as m };", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as componentOnReady } from './helpers.js';\n\n// TODO(FW-2832): types\nconst attachComponent = async (delegate, container, component, cssClasses, componentProps, inline) => {\n  var _a;\n  if (delegate) {\n    return delegate.attachViewToDom(container, component, componentProps, cssClasses);\n  }\n  if (!inline && typeof component !== 'string' && !(component instanceof HTMLElement)) {\n    throw new Error('framework delegate is missing');\n  }\n  const el = typeof component === 'string' ? (_a = container.ownerDocument) === null || _a === void 0 ? void 0 : _a.createElement(component) : component;\n  if (cssClasses) {\n    cssClasses.forEach(c => el.classList.add(c));\n  }\n  if (componentProps) {\n    Object.assign(el, componentProps);\n  }\n  container.appendChild(el);\n  await new Promise(resolve => componentOnReady(el, resolve));\n  return el;\n};\nconst detachComponent = (delegate, element) => {\n  if (element) {\n    if (delegate) {\n      const container = element.parentElement;\n      return delegate.removeViewFromDom(container, element);\n    }\n    element.remove();\n  }\n  return Promise.resolve();\n};\nconst CoreDelegate = () => {\n  let BaseComponent;\n  let Reference;\n  const attachViewToDom = async (parentElement, userComponent, userComponentProps = {}, cssClasses = []) => {\n    var _a, _b;\n    BaseComponent = parentElement;\n    let ChildComponent;\n    /**\n     * If passing in a component via the `component` props\n     * we need to append it inside of our overlay component.\n     */\n    if (userComponent) {\n      /**\n       * If passing in the tag name, create\n       * the element otherwise just get a reference\n       * to the component.\n       */\n      const el = typeof userComponent === 'string' ? (_a = BaseComponent.ownerDocument) === null || _a === void 0 ? void 0 : _a.createElement(userComponent) : userComponent;\n      /**\n       * Add any css classes passed in\n       * via the cssClasses prop on the overlay.\n       */\n      cssClasses.forEach(c => el.classList.add(c));\n      /**\n       * Add any props passed in\n       * via the componentProps prop on the overlay.\n       */\n      Object.assign(el, userComponentProps);\n      /**\n       * Finally, append the component\n       * inside of the overlay component.\n       */\n      BaseComponent.appendChild(el);\n      ChildComponent = el;\n      await new Promise(resolve => componentOnReady(el, resolve));\n    } else if (BaseComponent.children.length > 0 && (BaseComponent.tagName === 'ION-MODAL' || BaseComponent.tagName === 'ION-POPOVER')) {\n      /**\n       * The delegate host wrapper el is only needed for modals and popovers\n       * because they allow the dev to provide custom content to the overlay.\n       */\n      const root = ChildComponent = BaseComponent.children[0];\n      if (!root.classList.contains('ion-delegate-host')) {\n        /**\n         * If the root element is not a delegate host, it means\n         * that the overlay has not been presented yet and we need\n         * to create the containing element with the specified classes.\n         */\n        const el = (_b = BaseComponent.ownerDocument) === null || _b === void 0 ? void 0 : _b.createElement('div');\n        // Add a class to track if the root element was created by the delegate.\n        el.classList.add('ion-delegate-host');\n        cssClasses.forEach(c => el.classList.add(c));\n        // Move each child from the original template to the new parent element.\n        el.append(...BaseComponent.children);\n        // Append the new parent element to the original parent element.\n        BaseComponent.appendChild(el);\n        /**\n         * Update the ChildComponent to be the\n         * newly created div in the event that one\n         * does not already exist.\n         */\n        ChildComponent = el;\n      }\n    }\n    /**\n     * Get the root of the app and\n     * add the overlay there.\n     */\n    const app = document.querySelector('ion-app') || document.body;\n    /**\n     * Create a placeholder comment so that\n     * we can return this component to where\n     * it was previously.\n     */\n    Reference = document.createComment('ionic teleport');\n    BaseComponent.parentNode.insertBefore(Reference, BaseComponent);\n    app.appendChild(BaseComponent);\n    /**\n     * We return the child component rather than the overlay\n     * reference itself since modal and popover will\n     * use this to wait for any Ionic components in the child view\n     * to be ready (i.e. componentOnReady) when using the\n     * lazy loaded component bundle.\n     *\n     * However, we fall back to returning BaseComponent\n     * in the event that a modal or popover is presented\n     * with no child content.\n     */\n    return ChildComponent !== null && ChildComponent !== void 0 ? ChildComponent : BaseComponent;\n  };\n  const removeViewFromDom = () => {\n    /**\n     * Return component to where it was previously in the DOM.\n     */\n    if (BaseComponent && Reference) {\n      Reference.parentNode.insertBefore(BaseComponent, Reference);\n      Reference.remove();\n    }\n    return Promise.resolve();\n  };\n  return {\n    attachViewToDom,\n    removeViewFromDom\n  };\n};\nexport { CoreDelegate as C, attachComponent as a, detachComponent as d };", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index9.js';\nimport { h as focusVisibleElement, c as componentOnReady, a as addEventListener, b as removeEventListener, g as getElementRoot } from './helpers.js';\nimport { OVERLAY_BACK_BUTTON_PRIORITY, shouldUseCloseWatcher } from './hardware-back-button.js';\nimport { c as config, a as printIonError, p as printIonWarning } from './index4.js';\nimport { b as getIonMode, a as isPlatform } from './ionic-global.js';\nimport { C as CoreDelegate } from './framework-delegate.js';\nimport { B as BACKDROP_NO_SCROLL } from './gesture-controller.js';\n\n/**\n * This query string selects elements that\n * are eligible to receive focus. We select\n * interactive elements that meet the following\n * criteria:\n * 1. Element does not have a negative tabindex\n * 2. Element does not have `hidden`\n * 3. Element does not have `disabled` for non-Ionic components.\n * 4. <PERSON><PERSON> does not have `disabled` or `disabled=\"true\"` for Ionic components.\n * Note: We need this distinction because `disabled=\"false\"` is\n * valid usage for the disabled property on ion-button.\n */\nconst focusableQueryString = '[tabindex]:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^=\"-\"]):not([hidden]):not([disabled]), textarea:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), button:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), select:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), .ion-focusable[disabled=\"false\"]:not([tabindex^=\"-\"]):not([hidden])';\n/**\n * Focuses the first descendant in a context\n * that can receive focus. If none exists,\n * a fallback element will be focused.\n * This fallback is typically an ancestor\n * container such as a menu or overlay so focus does not\n * leave the container we are trying to trap focus in.\n *\n * If no fallback is specified then we focus the container itself.\n */\nconst focusFirstDescendant = (ref, fallbackElement) => {\n  const firstInput = ref.querySelector(focusableQueryString);\n  focusElementInContext(firstInput, fallbackElement !== null && fallbackElement !== void 0 ? fallbackElement : ref);\n};\n/**\n * Focuses the last descendant in a context\n * that can receive focus. If none exists,\n * a fallback element will be focused.\n * This fallback is typically an ancestor\n * container such as a menu or overlay so focus does not\n * leave the container we are trying to trap focus in.\n *\n * If no fallback is specified then we focus the container itself.\n */\nconst focusLastDescendant = (ref, fallbackElement) => {\n  const inputs = Array.from(ref.querySelectorAll(focusableQueryString));\n  const lastInput = inputs.length > 0 ? inputs[inputs.length - 1] : null;\n  focusElementInContext(lastInput, fallbackElement !== null && fallbackElement !== void 0 ? fallbackElement : ref);\n};\n/**\n * Focuses a particular element in a context. If the element\n * doesn't have anything focusable associated with it then\n * a fallback element will be focused.\n *\n * This fallback is typically an ancestor\n * container such as a menu or overlay so focus does not\n * leave the container we are trying to trap focus in.\n * This should be used instead of the focus() method\n * on most elements because the focusable element\n * may not be the host element.\n *\n * For example, if an ion-button should be focused\n * then we should actually focus the native <button>\n * element inside of ion-button's shadow root, not\n * the host element itself.\n */\nconst focusElementInContext = (hostToFocus, fallbackElement) => {\n  let elementToFocus = hostToFocus;\n  const shadowRoot = hostToFocus === null || hostToFocus === void 0 ? void 0 : hostToFocus.shadowRoot;\n  if (shadowRoot) {\n    // If there are no inner focusable elements, just focus the host element.\n    elementToFocus = shadowRoot.querySelector(focusableQueryString) || hostToFocus;\n  }\n  if (elementToFocus) {\n    const radioGroup = elementToFocus.closest('ion-radio-group');\n    if (radioGroup) {\n      radioGroup.setFocus();\n    } else {\n      focusVisibleElement(elementToFocus);\n    }\n  } else {\n    // Focus fallback element instead of letting focus escape\n    fallbackElement.focus();\n  }\n};\nlet lastOverlayIndex = 0;\nlet lastId = 0;\nconst activeAnimations = new WeakMap();\nconst createController = tagName => {\n  return {\n    create(options) {\n      return createOverlay(tagName, options);\n    },\n    dismiss(data, role, id) {\n      return dismissOverlay(document, data, role, tagName, id);\n    },\n    async getTop() {\n      return getPresentedOverlay(document, tagName);\n    }\n  };\n};\nconst alertController = /*@__PURE__*/createController('ion-alert');\nconst actionSheetController = /*@__PURE__*/createController('ion-action-sheet');\nconst loadingController = /*@__PURE__*/createController('ion-loading');\nconst modalController = /*@__PURE__*/createController('ion-modal');\n/**\n * @deprecated Use the inline ion-picker component instead.\n */\nconst pickerController = /*@__PURE__*/createController('ion-picker-legacy');\nconst popoverController = /*@__PURE__*/createController('ion-popover');\nconst toastController = /*@__PURE__*/createController('ion-toast');\n/**\n * Prepares the overlay element to be presented.\n */\nconst prepareOverlay = el => {\n  if (typeof document !== 'undefined') {\n    /**\n     * Adds a single instance of event listeners for application behaviors:\n     *\n     * - Escape Key behavior to dismiss an overlay\n     * - Trapping focus within an overlay\n     * - Back button behavior to dismiss an overlay\n     *\n     * This only occurs when the first overlay is created.\n     */\n    connectListeners(document);\n  }\n  const overlayIndex = lastOverlayIndex++;\n  /**\n   * overlayIndex is used in the overlay components to set a zIndex.\n   * This ensures that the most recently presented overlay will be\n   * on top.\n   */\n  el.overlayIndex = overlayIndex;\n};\n/**\n * Assigns an incrementing id to an overlay element, that does not\n * already have an id assigned to it.\n *\n * Used to track unique instances of an overlay element.\n */\nconst setOverlayId = el => {\n  if (!el.hasAttribute('id')) {\n    el.id = `ion-overlay-${++lastId}`;\n  }\n  return el.id;\n};\nconst createOverlay = (tagName, opts) => {\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n  if (typeof window !== 'undefined' && typeof window.customElements !== 'undefined') {\n    return window.customElements.whenDefined(tagName).then(() => {\n      const element = document.createElement(tagName);\n      element.classList.add('overlay-hidden');\n      /**\n       * Convert the passed in overlay options into props\n       * that get passed down into the new overlay.\n       */\n      Object.assign(element, Object.assign(Object.assign({}, opts), {\n        hasController: true\n      }));\n      // append the overlay element to the document body\n      getAppRoot(document).appendChild(element);\n      return new Promise(resolve => componentOnReady(element, resolve));\n    });\n  }\n  return Promise.resolve();\n};\nconst isOverlayHidden = overlay => overlay.classList.contains('overlay-hidden');\n/**\n * Focuses a particular element in an overlay. If the element\n * doesn't have anything focusable associated with it then\n * the overlay itself will be focused.\n * This should be used instead of the focus() method\n * on most elements because the focusable element\n * may not be the host element.\n *\n * For example, if an ion-button should be focused\n * then we should actually focus the native <button>\n * element inside of ion-button's shadow root, not\n * the host element itself.\n */\nconst focusElementInOverlay = (hostToFocus, overlay) => {\n  let elementToFocus = hostToFocus;\n  const shadowRoot = hostToFocus === null || hostToFocus === void 0 ? void 0 : hostToFocus.shadowRoot;\n  if (shadowRoot) {\n    // If there are no inner focusable elements, just focus the host element.\n    elementToFocus = shadowRoot.querySelector(focusableQueryString) || hostToFocus;\n  }\n  if (elementToFocus) {\n    focusVisibleElement(elementToFocus);\n  } else {\n    // Focus overlay instead of letting focus escape\n    overlay.focus();\n  }\n};\n/**\n * Traps keyboard focus inside of overlay components.\n * Based on https://w3c.github.io/aria-practices/examples/dialog-modal/alertdialog.html\n * This includes the following components: Action Sheet, Alert, Loading, Modal,\n * Picker, and Popover.\n * Should NOT include: Toast\n */\nconst trapKeyboardFocus = (ev, doc) => {\n  const lastOverlay = getPresentedOverlay(doc, 'ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover');\n  const target = ev.target;\n  /**\n   * If no active overlay, ignore this event.\n   *\n   * If this component uses the shadow dom,\n   * this global listener is pointless\n   * since it will not catch the focus\n   * traps as they are inside the shadow root.\n   * We need to add a listener to the shadow root\n   * itself to ensure the focus trap works.\n   */\n  if (!lastOverlay || !target) {\n    return;\n  }\n  /**\n   * If the ion-disable-focus-trap class\n   * is present on an overlay, then this component\n   * instance has opted out of focus trapping.\n   * An example of this is when the sheet modal\n   * has a backdrop that is disabled. The content\n   * behind the sheet should be focusable until\n   * the backdrop is enabled.\n   */\n  if (lastOverlay.classList.contains(FOCUS_TRAP_DISABLE_CLASS)) {\n    return;\n  }\n  const trapScopedFocus = () => {\n    /**\n     * If we are focusing the overlay, clear\n     * the last focused element so that hitting\n     * tab activates the first focusable element\n     * in the overlay wrapper.\n     */\n    if (lastOverlay === target) {\n      lastOverlay.lastFocus = undefined;\n      /**\n       * Toasts can be presented from an overlay.\n       * However, focus should still be returned to\n       * the overlay when clicking a toast. Normally,\n       * focus would be returned to the last focusable\n       * descendant in the overlay which may not always be\n       * the button that the toast was presented from. In this case,\n       * the focus may be returned to an unexpected element.\n       * To account for this, we make sure to return focus to the\n       * last focused element in the overlay if focus is\n       * moved to the toast.\n       */\n    } else if (target.tagName === 'ION-TOAST') {\n      focusElementInOverlay(lastOverlay.lastFocus, lastOverlay);\n      /**\n       * Otherwise, we must be focusing an element\n       * inside of the overlay. The two possible options\n       * here are an input/button/etc or the ion-focus-trap\n       * element. The focus trap element is used to prevent\n       * the keyboard focus from leaving the overlay when\n       * using Tab or screen assistants.\n       */\n    } else {\n      /**\n       * We do not want to focus the traps, so get the overlay\n       * wrapper element as the traps live outside of the wrapper.\n       */\n      const overlayRoot = getElementRoot(lastOverlay);\n      if (!overlayRoot.contains(target)) {\n        return;\n      }\n      const overlayWrapper = overlayRoot.querySelector('.ion-overlay-wrapper');\n      if (!overlayWrapper) {\n        return;\n      }\n      /**\n       * If the target is inside the wrapper, let the browser\n       * focus as normal and keep a log of the last focused element.\n       * Additionally, if the backdrop was tapped we should not\n       * move focus back inside the wrapper as that could cause\n       * an interactive elements focus state to activate.\n       */\n      if (overlayWrapper.contains(target) || target === overlayRoot.querySelector('ion-backdrop')) {\n        lastOverlay.lastFocus = target;\n      } else {\n        /**\n         * Otherwise, we must have focused one of the focus traps.\n         * We need to wrap the focus to either the first element\n         * or the last element.\n         */\n        /**\n         * Once we call `focusFirstDescendant` and focus the first\n         * descendant, another focus event will fire which will\n         * cause `lastOverlay.lastFocus` to be updated before\n         * we can run the code after that. We will cache the value\n         * here to avoid that.\n         */\n        const lastFocus = lastOverlay.lastFocus;\n        // Focus the first element in the overlay wrapper\n        focusFirstDescendant(overlayWrapper, lastOverlay);\n        /**\n         * If the cached last focused element is the\n         * same as the active element, then we need\n         * to wrap focus to the last descendant. This happens\n         * when the first descendant is focused, and the user\n         * presses Shift + Tab. The previous line will focus\n         * the same descendant again (the first one), causing\n         * last focus to equal the active element.\n         */\n        if (lastFocus === doc.activeElement) {\n          focusLastDescendant(overlayWrapper, lastOverlay);\n        }\n        lastOverlay.lastFocus = doc.activeElement;\n      }\n    }\n  };\n  const trapShadowFocus = () => {\n    /**\n     * If the target is inside the wrapper, let the browser\n     * focus as normal and keep a log of the last focused element.\n     */\n    if (lastOverlay.contains(target)) {\n      lastOverlay.lastFocus = target;\n      /**\n       * Toasts can be presented from an overlay.\n       * However, focus should still be returned to\n       * the overlay when clicking a toast. Normally,\n       * focus would be returned to the last focusable\n       * descendant in the overlay which may not always be\n       * the button that the toast was presented from. In this case,\n       * the focus may be returned to an unexpected element.\n       * To account for this, we make sure to return focus to the\n       * last focused element in the overlay if focus is\n       * moved to the toast.\n       */\n    } else if (target.tagName === 'ION-TOAST') {\n      focusElementInOverlay(lastOverlay.lastFocus, lastOverlay);\n    } else {\n      /**\n       * Otherwise, we are about to have focus\n       * go out of the overlay. We need to wrap\n       * the focus to either the first element\n       * or the last element.\n       */\n      /**\n       * Once we call `focusFirstDescendant` and focus the first\n       * descendant, another focus event will fire which will\n       * cause `lastOverlay.lastFocus` to be updated before\n       * we can run the code after that. We will cache the value\n       * here to avoid that.\n       */\n      const lastFocus = lastOverlay.lastFocus;\n      // Focus the first element in the overlay wrapper\n      focusFirstDescendant(lastOverlay);\n      /**\n       * If the cached last focused element is the\n       * same as the active element, then we need\n       * to wrap focus to the last descendant. This happens\n       * when the first descendant is focused, and the user\n       * presses Shift + Tab. The previous line will focus\n       * the same descendant again (the first one), causing\n       * last focus to equal the active element.\n       */\n      if (lastFocus === doc.activeElement) {\n        focusLastDescendant(lastOverlay);\n      }\n      lastOverlay.lastFocus = doc.activeElement;\n    }\n  };\n  if (lastOverlay.shadowRoot) {\n    trapShadowFocus();\n  } else {\n    trapScopedFocus();\n  }\n};\nconst connectListeners = doc => {\n  if (lastOverlayIndex === 0) {\n    lastOverlayIndex = 1;\n    doc.addEventListener('focus', ev => {\n      trapKeyboardFocus(ev, doc);\n    }, true);\n    // handle back-button click\n    doc.addEventListener('ionBackButton', ev => {\n      const lastOverlay = getPresentedOverlay(doc);\n      if (lastOverlay === null || lastOverlay === void 0 ? void 0 : lastOverlay.backdropDismiss) {\n        ev.detail.register(OVERLAY_BACK_BUTTON_PRIORITY, () => {\n          /**\n           * Do not return this promise otherwise\n           * the hardware back button utility will\n           * be blocked until the overlay dismisses.\n           * This is important for a modal with canDismiss.\n           * If the application presents a confirmation alert\n           * in the \"canDismiss\" callback, then it will be impossible\n           * to use the hardware back button to dismiss the alert\n           * dialog because the hardware back button utility\n           * is blocked on waiting for the modal to dismiss.\n           */\n          lastOverlay.dismiss(undefined, BACKDROP);\n        });\n      }\n    });\n    /**\n     * Handle ESC to close overlay.\n     * CloseWatcher also handles pressing the Esc\n     * key, so if a browser supports CloseWatcher then\n     * this behavior will be handled via the ionBackButton\n     * event.\n     */\n    if (!shouldUseCloseWatcher()) {\n      doc.addEventListener('keydown', ev => {\n        if (ev.key === 'Escape') {\n          const lastOverlay = getPresentedOverlay(doc);\n          if (lastOverlay === null || lastOverlay === void 0 ? void 0 : lastOverlay.backdropDismiss) {\n            lastOverlay.dismiss(undefined, BACKDROP);\n          }\n        }\n      });\n    }\n  }\n};\nconst dismissOverlay = (doc, data, role, overlayTag, id) => {\n  const overlay = getPresentedOverlay(doc, overlayTag, id);\n  if (!overlay) {\n    return Promise.reject('overlay does not exist');\n  }\n  return overlay.dismiss(data, role);\n};\n/**\n * Returns a list of all overlays in the DOM even if they are not presented.\n */\nconst getOverlays = (doc, selector) => {\n  if (selector === undefined) {\n    selector = 'ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast';\n  }\n  return Array.from(doc.querySelectorAll(selector)).filter(c => c.overlayIndex > 0);\n};\n/**\n * Returns a list of all presented overlays.\n * Inline overlays can exist in the DOM but not be presented,\n * so there are times when we want to exclude those.\n * @param doc The document to find the element within.\n * @param overlayTag The selector for the overlay, defaults to Ionic overlay components.\n */\nconst getPresentedOverlays = (doc, overlayTag) => {\n  return getOverlays(doc, overlayTag).filter(o => !isOverlayHidden(o));\n};\n/**\n * Returns a presented overlay element.\n * @param doc The document to find the element within.\n * @param overlayTag The selector for the overlay, defaults to Ionic overlay components.\n * @param id The unique identifier for the overlay instance.\n * @returns The overlay element or `undefined` if no overlay element is found.\n */\nconst getPresentedOverlay = (doc, overlayTag, id) => {\n  const overlays = getPresentedOverlays(doc, overlayTag);\n  return id === undefined ? overlays[overlays.length - 1] : overlays.find(o => o.id === id);\n};\n/**\n * When an overlay is presented, the main\n * focus is the overlay not the page content.\n * We need to remove the page content from the\n * accessibility tree otherwise when\n * users use \"read screen from top\" gestures with\n * TalkBack and VoiceOver, the screen reader will begin\n * to read the content underneath the overlay.\n *\n * We need a container where all page components\n * exist that is separate from where the overlays\n * are added in the DOM. For most apps, this element\n * is the top most ion-router-outlet. In the event\n * that devs are not using a router,\n * they will need to add the \"ion-view-container-root\"\n * id to the element that contains all of their views.\n *\n * TODO: If Framework supports having multiple top\n * level router outlets we would need to update this.\n * Example: One outlet for side menu and one outlet\n * for main content.\n */\nconst setRootAriaHidden = (hidden = false) => {\n  const root = getAppRoot(document);\n  const viewContainer = root.querySelector('ion-router-outlet, ion-nav, #ion-view-container-root');\n  if (!viewContainer) {\n    return;\n  }\n  if (hidden) {\n    viewContainer.setAttribute('aria-hidden', 'true');\n  } else {\n    viewContainer.removeAttribute('aria-hidden');\n  }\n};\nconst present = async (overlay, name, iosEnterAnimation, mdEnterAnimation, opts) => {\n  var _a, _b;\n  if (overlay.presented) {\n    return;\n  }\n  /**\n   * Due to accessibility guidelines, toasts do not have\n   * focus traps.\n   *\n   * All other overlays should have focus traps to prevent\n   * the keyboard focus from leaving the overlay.\n   */\n  if (overlay.el.tagName !== 'ION-TOAST') {\n    setRootAriaHidden(true);\n    document.body.classList.add(BACKDROP_NO_SCROLL);\n  }\n  hideUnderlyingOverlaysFromScreenReaders(overlay.el);\n  hideAnimatingOverlayFromScreenReaders(overlay.el);\n  overlay.presented = true;\n  overlay.willPresent.emit();\n  (_a = overlay.willPresentShorthand) === null || _a === void 0 ? void 0 : _a.emit();\n  const mode = getIonMode(overlay);\n  // get the user's animation fn if one was provided\n  const animationBuilder = overlay.enterAnimation ? overlay.enterAnimation : config.get(name, mode === 'ios' ? iosEnterAnimation : mdEnterAnimation);\n  const completed = await overlayAnimation(overlay, animationBuilder, overlay.el, opts);\n  if (completed) {\n    overlay.didPresent.emit();\n    (_b = overlay.didPresentShorthand) === null || _b === void 0 ? void 0 : _b.emit();\n  }\n  /**\n   * When an overlay that steals focus\n   * is dismissed, focus should be returned\n   * to the element that was focused\n   * prior to the overlay opening. Toast\n   * does not steal focus and is excluded\n   * from returning focus as a result.\n   */\n  if (overlay.el.tagName !== 'ION-TOAST') {\n    restoreElementFocus(overlay.el);\n  }\n  /**\n   * If the focused element is already\n   * inside the overlay component then\n   * focus should not be moved from that\n   * to the overlay container.\n   */\n  if (overlay.keyboardClose && (document.activeElement === null || !overlay.el.contains(document.activeElement))) {\n    overlay.el.focus();\n  }\n  /**\n   * If this overlay was previously dismissed without being\n   * the topmost one (such as by manually calling dismiss()),\n   * it would still have aria-hidden on being presented again.\n   * Removing it here ensures the overlay is visible to screen\n   * readers.\n   *\n   * If this overlay was being presented, then it was hidden\n   * from screen readers during the animation. Now that the\n   * animation is complete, we can reveal the overlay to\n   * screen readers.\n   */\n  overlay.el.removeAttribute('aria-hidden');\n};\n/**\n * When an overlay component is dismissed,\n * focus should be returned to the element\n * that presented the overlay. Otherwise\n * focus will be set on the body which\n * means that people using screen readers\n * or tabbing will need to re-navigate\n * to where they were before they\n * opened the overlay.\n */\nconst restoreElementFocus = async overlayEl => {\n  let previousElement = document.activeElement;\n  if (!previousElement) {\n    return;\n  }\n  const shadowRoot = previousElement === null || previousElement === void 0 ? void 0 : previousElement.shadowRoot;\n  if (shadowRoot) {\n    // If there are no inner focusable elements, just focus the host element.\n    previousElement = shadowRoot.querySelector(focusableQueryString) || previousElement;\n  }\n  await overlayEl.onDidDismiss();\n  /**\n   * After onDidDismiss, the overlay loses focus\n   * because it is removed from the document\n   *\n   * > An element will also lose focus [...]\n   * > if the element is removed from the document)\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/Element/blur_event\n   *\n   * Additionally, `document.activeElement` returns:\n   *\n   * > The Element which currently has focus,\n   * > `<body>` or null if there is\n   * > no focused element.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/Document/activeElement#value\n   *\n   * However, if the user has already focused\n   * an element sometime between onWillDismiss\n   * and onDidDismiss (for example, focusing a\n   * text box after tapping a button in an\n   * action sheet) then don't restore focus to\n   * previous element\n   */\n  if (document.activeElement === null || document.activeElement === document.body) {\n    previousElement.focus();\n  }\n};\nconst dismiss = async (overlay, data, role, name, iosLeaveAnimation, mdLeaveAnimation, opts) => {\n  var _a, _b;\n  if (!overlay.presented) {\n    return false;\n  }\n  const presentedOverlays = doc !== undefined ? getPresentedOverlays(doc) : [];\n  /**\n   * For accessibility, toasts lack focus traps and don't receive\n   * `aria-hidden` on the root element when presented.\n   *\n   * All other overlays use focus traps to keep keyboard focus\n   * within the overlay, setting `aria-hidden` on the root element\n   * to enhance accessibility.\n   *\n   * Therefore, we must remove `aria-hidden` from the root element\n   * when the last non-toast overlay is dismissed.\n   */\n  const overlaysNotToast = presentedOverlays.filter(o => o.tagName !== 'ION-TOAST');\n  const lastOverlayNotToast = overlaysNotToast.length === 1 && overlaysNotToast[0].id === overlay.el.id;\n  /**\n   * If this is the last visible overlay that is not a toast\n   * then we want to re-add the root to the accessibility tree.\n   */\n  if (lastOverlayNotToast) {\n    setRootAriaHidden(false);\n    document.body.classList.remove(BACKDROP_NO_SCROLL);\n  }\n  overlay.presented = false;\n  try {\n    /**\n     * There is no need to show the overlay to screen readers during\n     * the dismiss animation. This is because the overlay will be removed\n     * from the DOM after the animation is complete.\n     */\n    hideAnimatingOverlayFromScreenReaders(overlay.el);\n    // Overlay contents should not be clickable during dismiss\n    overlay.el.style.setProperty('pointer-events', 'none');\n    overlay.willDismiss.emit({\n      data,\n      role\n    });\n    (_a = overlay.willDismissShorthand) === null || _a === void 0 ? void 0 : _a.emit({\n      data,\n      role\n    });\n    const mode = getIonMode(overlay);\n    const animationBuilder = overlay.leaveAnimation ? overlay.leaveAnimation : config.get(name, mode === 'ios' ? iosLeaveAnimation : mdLeaveAnimation);\n    // If dismissed via gesture, no need to play leaving animation again\n    if (role !== GESTURE) {\n      await overlayAnimation(overlay, animationBuilder, overlay.el, opts);\n    }\n    overlay.didDismiss.emit({\n      data,\n      role\n    });\n    (_b = overlay.didDismissShorthand) === null || _b === void 0 ? void 0 : _b.emit({\n      data,\n      role\n    });\n    // Get a reference to all animations currently assigned to this overlay\n    // Then tear them down to return the overlay to its initial visual state\n    const animations = activeAnimations.get(overlay) || [];\n    animations.forEach(ani => ani.destroy());\n    activeAnimations.delete(overlay);\n    /**\n     * Make overlay hidden again in case it is being reused.\n     * We can safely remove pointer-events: none as\n     * overlay-hidden will set display: none.\n     */\n    overlay.el.classList.add('overlay-hidden');\n    overlay.el.style.removeProperty('pointer-events');\n    /**\n     * Clear any focus trapping references\n     * when the overlay is dismissed.\n     */\n    if (overlay.el.lastFocus !== undefined) {\n      overlay.el.lastFocus = undefined;\n    }\n  } catch (err) {\n    printIonError(`[${overlay.el.tagName.toLowerCase()}] - `, err);\n  }\n  overlay.el.remove();\n  revealOverlaysToScreenReaders();\n  return true;\n};\nconst getAppRoot = doc => {\n  return doc.querySelector('ion-app') || doc.body;\n};\nconst overlayAnimation = async (overlay, animationBuilder, baseEl, opts) => {\n  // Make overlay visible in case it's hidden\n  baseEl.classList.remove('overlay-hidden');\n  const aniRoot = overlay.el;\n  const animation = animationBuilder(aniRoot, opts);\n  if (!overlay.animated || !config.getBoolean('animated', true)) {\n    animation.duration(0);\n  }\n  if (overlay.keyboardClose) {\n    animation.beforeAddWrite(() => {\n      const activeElement = baseEl.ownerDocument.activeElement;\n      if (activeElement === null || activeElement === void 0 ? void 0 : activeElement.matches('input,ion-input, ion-textarea')) {\n        activeElement.blur();\n      }\n    });\n  }\n  const activeAni = activeAnimations.get(overlay) || [];\n  activeAnimations.set(overlay, [...activeAni, animation]);\n  await animation.play();\n  return true;\n};\nconst eventMethod = (element, eventName) => {\n  let resolve;\n  const promise = new Promise(r => resolve = r);\n  onceEvent(element, eventName, event => {\n    resolve(event.detail);\n  });\n  return promise;\n};\nconst onceEvent = (element, eventName, callback) => {\n  const handler = ev => {\n    removeEventListener(element, eventName, handler);\n    callback(ev);\n  };\n  addEventListener(element, eventName, handler);\n};\nconst isCancel = role => {\n  return role === 'cancel' || role === BACKDROP;\n};\nconst defaultGate = h => h();\n/**\n * Calls a developer provided method while avoiding\n * Angular Zones. Since the handler is provided by\n * the developer, we should throw any errors\n * received so that developer-provided bug\n * tracking software can log it.\n */\nconst safeCall = (handler, arg) => {\n  if (typeof handler === 'function') {\n    const jmp = config.get('_zoneGate', defaultGate);\n    return jmp(() => {\n      try {\n        return handler(arg);\n      } catch (e) {\n        throw e;\n      }\n    });\n  }\n  return undefined;\n};\nconst BACKDROP = 'backdrop';\nconst GESTURE = 'gesture';\nconst OVERLAY_GESTURE_PRIORITY = 39;\n/**\n * Creates a delegate controller.\n *\n * Requires that the component has the following properties:\n * - `el: HTMLElement`\n * - `hasController: boolean`\n * - `delegate?: FrameworkDelegate`\n *\n * @param ref The component class instance.\n */\nconst createDelegateController = ref => {\n  let inline = false;\n  let workingDelegate;\n  const coreDelegate = CoreDelegate();\n  /**\n   * Determines whether or not an overlay is being used\n   * inline or via a controller/JS and returns the correct delegate.\n   * By default, subsequent calls to getDelegate will use\n   * a cached version of the delegate.\n   * This is useful for calling dismiss after present,\n   * so that the correct delegate is given.\n   * @param force `true` to force the non-cached version of the delegate.\n   * @returns The delegate to use and whether or not the overlay is inline.\n   */\n  const getDelegate = (force = false) => {\n    if (workingDelegate && !force) {\n      return {\n        delegate: workingDelegate,\n        inline\n      };\n    }\n    const {\n      el,\n      hasController,\n      delegate\n    } = ref;\n    /**\n     * If using overlay inline\n     * we potentially need to use the coreDelegate\n     * so that this works in vanilla JS apps.\n     * If a developer has presented this component\n     * via a controller, then we can assume\n     * the component is already in the\n     * correct place.\n     */\n    const parentEl = el.parentNode;\n    inline = parentEl !== null && !hasController;\n    workingDelegate = inline ? delegate || coreDelegate : delegate;\n    return {\n      inline,\n      delegate: workingDelegate\n    };\n  };\n  /**\n   * Attaches a component in the DOM. Teleports the component\n   * to the root of the app.\n   * @param component The component to optionally construct and append to the element.\n   */\n  const attachViewToDom = async component => {\n    const {\n      delegate\n    } = getDelegate(true);\n    if (delegate) {\n      return await delegate.attachViewToDom(ref.el, component);\n    }\n    const {\n      hasController\n    } = ref;\n    if (hasController && component !== undefined) {\n      throw new Error('framework delegate is missing');\n    }\n    return null;\n  };\n  /**\n   * Moves a component back to its original location in the DOM.\n   */\n  const removeViewFromDom = () => {\n    const {\n      delegate\n    } = getDelegate();\n    if (delegate && ref.el !== undefined) {\n      delegate.removeViewFromDom(ref.el.parentElement, ref.el);\n    }\n  };\n  return {\n    attachViewToDom,\n    removeViewFromDom\n  };\n};\n/**\n * Constructs a trigger interaction for an overlay.\n * Presents an overlay when the trigger is clicked.\n *\n * Usage:\n * ```ts\n * triggerController = createTriggerController();\n * triggerController.addClickListener(el, trigger);\n * ```\n */\nconst createTriggerController = () => {\n  let destroyTriggerInteraction;\n  /**\n   * Removes the click listener from the trigger element.\n   */\n  const removeClickListener = () => {\n    if (destroyTriggerInteraction) {\n      destroyTriggerInteraction();\n      destroyTriggerInteraction = undefined;\n    }\n  };\n  /**\n   * Adds a click listener to the trigger element.\n   * Presents the overlay when the trigger is clicked.\n   * @param el The overlay element.\n   * @param trigger The ID of the element to add a click listener to.\n   */\n  const addClickListener = (el, trigger) => {\n    removeClickListener();\n    const triggerEl = trigger !== undefined ? document.getElementById(trigger) : null;\n    if (!triggerEl) {\n      printIonWarning(`[${el.tagName.toLowerCase()}] - A trigger element with the ID \"${trigger}\" was not found in the DOM. The trigger element must be in the DOM when the \"trigger\" property is set on an overlay component.`, el);\n      return;\n    }\n    const configureTriggerInteraction = (targetEl, overlayEl) => {\n      const openOverlay = () => {\n        overlayEl.present();\n      };\n      targetEl.addEventListener('click', openOverlay);\n      return () => {\n        targetEl.removeEventListener('click', openOverlay);\n      };\n    };\n    destroyTriggerInteraction = configureTriggerInteraction(triggerEl, el);\n  };\n  return {\n    addClickListener,\n    removeClickListener\n  };\n};\n/**\n * The overlay that is being animated also needs to hide from screen\n * readers during its animation. This ensures that assistive technologies\n * like TalkBack do not announce or interact with the content until the\n * animation is complete, avoiding confusion for users.\n *\n * When the overlay is presented on an Android device, TalkBack's focus rings\n * may appear in the wrong position due to the transition (specifically\n * `transform` styles). This occurs because the focus rings are initially\n * displayed at the starting position of the elements before the transition\n * begins. This workaround ensures the focus rings do not appear in the\n * incorrect location.\n *\n * If this solution is applied to iOS devices, then it leads to a bug where\n * the overlays cannot be accessed by screen readers. This is due to\n * VoiceOver not being able to update the accessibility tree when the\n * `aria-hidden` is removed.\n *\n * @param overlay - The overlay that is being animated.\n */\nconst hideAnimatingOverlayFromScreenReaders = overlay => {\n  if (doc === undefined) return;\n  if (isPlatform('android')) {\n    /**\n     * Once the animation is complete, this attribute will be removed.\n     * This is done at the end of the `present` method.\n     */\n    overlay.setAttribute('aria-hidden', 'true');\n  }\n};\n/**\n * Ensure that underlying overlays have aria-hidden if necessary so that screen readers\n * cannot move focus to these elements. Note that we cannot rely on focus/focusin/focusout\n * events here because those events do not fire when the screen readers moves to a non-focusable\n * element such as text.\n * Without this logic screen readers would be able to move focus outside of the top focus-trapped overlay.\n *\n * @param newTopMostOverlay - The overlay that is being presented. Since the overlay has not been\n * fully presented yet at the time this function is called it will not be included in the getPresentedOverlays result.\n */\nconst hideUnderlyingOverlaysFromScreenReaders = newTopMostOverlay => {\n  var _a;\n  if (doc === undefined) return;\n  const overlays = getPresentedOverlays(doc);\n  for (let i = overlays.length - 1; i >= 0; i--) {\n    const presentedOverlay = overlays[i];\n    const nextPresentedOverlay = (_a = overlays[i + 1]) !== null && _a !== void 0 ? _a : newTopMostOverlay;\n    /**\n     * If next overlay has aria-hidden then all remaining overlays will have it too.\n     * Or, if the next overlay is a Toast that does not have aria-hidden then current overlay\n     * should not have aria-hidden either so focus can remain in the current overlay.\n     */\n    if (nextPresentedOverlay.hasAttribute('aria-hidden') || nextPresentedOverlay.tagName !== 'ION-TOAST') {\n      presentedOverlay.setAttribute('aria-hidden', 'true');\n    }\n  }\n};\n/**\n * When dismissing an overlay we need to reveal the new top-most overlay to screen readers.\n * If the top-most overlay is a Toast we potentially need to reveal more overlays since\n * focus is never automatically moved to the Toast.\n */\nconst revealOverlaysToScreenReaders = () => {\n  if (doc === undefined) return;\n  const overlays = getPresentedOverlays(doc);\n  for (let i = overlays.length - 1; i >= 0; i--) {\n    const currentOverlay = overlays[i];\n    /**\n     * If the current we are looking at is a Toast then we can remove aria-hidden.\n     * However, we potentially need to keep looking at the overlay stack because there\n     * could be more Toasts underneath. Additionally, we need to unhide the closest non-Toast\n     * overlay too so focus can move there since focus is never automatically moved to the Toast.\n     */\n    currentOverlay.removeAttribute('aria-hidden');\n    /**\n     * If we found a non-Toast element then we can just remove aria-hidden and stop searching entirely\n     * since this overlay should always receive focus. As a result, all underlying overlays should still\n     * be hidden from screen readers.\n     */\n    if (currentOverlay.tagName !== 'ION-TOAST') {\n      break;\n    }\n  }\n};\nconst FOCUS_TRAP_DISABLE_CLASS = 'ion-disable-focus-trap';\nexport { BACKDROP as B, FOCUS_TRAP_DISABLE_CLASS as F, GESTURE as G, OVERLAY_GESTURE_PRIORITY as O, alertController as a, actionSheetController as b, popoverController as c, createDelegateController as d, createTriggerController as e, present as f, dismiss as g, eventMethod as h, isCancel as i, prepareOverlay as j, setOverlayId as k, loadingController as l, modalController as m, focusFirstDescendant as n, getPresentedOverlay as o, pickerController as p, focusLastDescendant as q, safeCall as s, toastController as t };", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n  return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n  return typeof color === 'string' && color.length > 0 ? Object.assign({\n    'ion-color': true,\n    [`ion-color-${color}`]: true\n  }, cssClassMap) : cssClassMap;\n};\nconst getClassList = classes => {\n  if (classes !== undefined) {\n    const array = Array.isArray(classes) ? classes : classes.split(' ');\n    return array.filter(c => c != null).map(c => c.trim()).filter(c => c !== '');\n  }\n  return [];\n};\nconst getClassMap = classes => {\n  const map = {};\n  getClassList(classes).forEach(c => map[c] = true);\n  return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n  if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n    const router = document.querySelector('ion-router');\n    if (router) {\n      if (ev != null) {\n        ev.preventDefault();\n      }\n      return router.push(url, direction, animation);\n    }\n  }\n  return false;\n};\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nexport { getAssetPath, render, setAssetPath, setNonce, setPlatformOptions } from '@stencil/core/internal/client';\nexport { c as createAnimation } from './animation.js';\nexport { a as LIFECYCLE_DID_ENTER, c as LIFECYCLE_DID_LEAVE, L as LIFECYCLE_WILL_ENTER, b as LIFECYCLE_WILL_LEAVE, d as LIFECYCLE_WILL_UNLOAD, g as getIonPageElement } from './index2.js';\nexport { iosTransitionAnimation } from './ios.transition.js';\nexport { mdTransitionAnimation } from './md.transition.js';\nexport { g as getTimeGivenProgression } from './cubic-bezier.js';\nexport { createGesture } from './index3.js';\nexport { g as getPlatforms, i as initialize, a as isPlatform } from './ionic-global.js';\nexport { c as componentOnReady } from './helpers.js';\nexport { L as LogLevel } from './index4.js';\nexport { I as IonicSafeString, g as getMode, s as setupConfig } from './config.js';\nexport { o as openURL } from './theme.js';\nexport { m as menuController } from './index5.js';\nexport { b as actionSheetController, a as alertController, l as loadingController, m as modalController, p as pickerController, c as popoverController, t as toastController } from './overlays.js';\nconst IonicSlides = opts => {\n  const {\n    swiper,\n    extendParams\n  } = opts;\n  const slidesParams = {\n    effect: undefined,\n    direction: 'horizontal',\n    initialSlide: 0,\n    loop: false,\n    parallax: false,\n    slidesPerView: 1,\n    spaceBetween: 0,\n    speed: 300,\n    slidesPerColumn: 1,\n    slidesPerColumnFill: 'column',\n    slidesPerGroup: 1,\n    centeredSlides: false,\n    slidesOffsetBefore: 0,\n    slidesOffsetAfter: 0,\n    touchEventsTarget: 'container',\n    freeMode: false,\n    freeModeMomentum: true,\n    freeModeMomentumRatio: 1,\n    freeModeMomentumBounce: true,\n    freeModeMomentumBounceRatio: 1,\n    freeModeMomentumVelocityRatio: 1,\n    freeModeSticky: false,\n    freeModeMinimumVelocity: 0.02,\n    autoHeight: false,\n    setWrapperSize: false,\n    zoom: {\n      maxRatio: 3,\n      minRatio: 1,\n      toggle: false\n    },\n    touchRatio: 1,\n    touchAngle: 45,\n    simulateTouch: true,\n    touchStartPreventDefault: false,\n    shortSwipes: true,\n    longSwipes: true,\n    longSwipesRatio: 0.5,\n    longSwipesMs: 300,\n    followFinger: true,\n    threshold: 0,\n    touchMoveStopPropagation: true,\n    touchReleaseOnEdges: false,\n    iOSEdgeSwipeDetection: false,\n    iOSEdgeSwipeThreshold: 20,\n    resistance: true,\n    resistanceRatio: 0.85,\n    watchSlidesProgress: false,\n    watchSlidesVisibility: false,\n    preventClicks: true,\n    preventClicksPropagation: true,\n    slideToClickedSlide: false,\n    loopAdditionalSlides: 0,\n    noSwiping: true,\n    runCallbacksOnInit: true,\n    coverflowEffect: {\n      rotate: 50,\n      stretch: 0,\n      depth: 100,\n      modifier: 1,\n      slideShadows: true\n    },\n    flipEffect: {\n      slideShadows: true,\n      limitRotation: true\n    },\n    cubeEffect: {\n      slideShadows: true,\n      shadow: true,\n      shadowOffset: 20,\n      shadowScale: 0.94\n    },\n    fadeEffect: {\n      crossFade: false\n    },\n    a11y: {\n      prevSlideMessage: 'Previous slide',\n      nextSlideMessage: 'Next slide',\n      firstSlideMessage: 'This is the first slide',\n      lastSlideMessage: 'This is the last slide'\n    }\n  };\n  if (swiper.pagination) {\n    slidesParams.pagination = {\n      type: 'bullets',\n      clickable: false,\n      hideOnClick: false\n    };\n  }\n  if (swiper.scrollbar) {\n    slidesParams.scrollbar = {\n      hide: true\n    };\n  }\n  extendParams(slidesParams);\n};\nexport { IonicSlides };", "import * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, InjectionToken, inject, NgZone, ApplicationRef, Injector, createComponent, TemplateRef, Directive, ContentChild, EventEmitter, ViewContainerRef, EnvironmentInjector, Attribute, SkipSelf, Input, Output, reflectComponentType, HostListener, ElementRef, ViewChild } from '@angular/core';\nimport * as i3 from '@angular/router';\nimport { NavigationStart, PRIMARY_OUTLET, ChildrenOutletContexts, ActivatedRoute, Router } from '@angular/router';\nimport * as i1 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport { isPlatform, getPlatforms, LIFECYCLE_WILL_ENTER, LIFECYCLE_DID_ENTER, LIFECYCLE_WILL_LEAVE, LIFECYCLE_DID_LEAVE, LIFECYCLE_WILL_UNLOAD, componentOnReady } from '@ionic/core/components';\nimport { Subject, fromEvent, BehaviorSubject, combineLatest, of } from 'rxjs';\nimport { __decorate } from 'tslib';\nimport { filter, switchMap, distinctUntilChanged } from 'rxjs/operators';\nimport { NgControl } from '@angular/forms';\nconst _c0 = [\"tabsInner\"];\nclass MenuController {\n  menuController;\n  constructor(menuController) {\n    this.menuController = menuController;\n  }\n  /**\n   * Programmatically open the Menu.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu is fully opened\n   */\n  open(menuId) {\n    return this.menuController.open(menuId);\n  }\n  /**\n   * Programmatically close the Menu. If no `menuId` is given as the first\n   * argument then it'll close any menu which is open. If a `menuId`\n   * is given then it'll close that exact menu.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu is fully closed\n   */\n  close(menuId) {\n    return this.menuController.close(menuId);\n  }\n  /**\n   * Toggle the menu. If it's closed, it will open, and if opened, it\n   * will close.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu has been toggled\n   */\n  toggle(menuId) {\n    return this.menuController.toggle(menuId);\n  }\n  /**\n   * Used to enable or disable a menu. For example, there could be multiple\n   * left menus, but only one of them should be able to be opened at the same\n   * time. If there are multiple menus on the same side, then enabling one menu\n   * will also automatically disable all the others that are on the same side.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu, which is useful for chaining.\n   */\n  enable(shouldEnable, menuId) {\n    return this.menuController.enable(shouldEnable, menuId);\n  }\n  /**\n   * Used to enable or disable the ability to swipe open the menu.\n   * @param shouldEnable  True if it should be swipe-able, false if not.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu, which is useful for chaining.\n   */\n  swipeGesture(shouldEnable, menuId) {\n    return this.menuController.swipeGesture(shouldEnable, menuId);\n  }\n  /**\n   * @param [menuId] Optionally get the menu by its id, or side.\n   * @return Returns true if the specified menu is currently open, otherwise false.\n   * If the menuId is not specified, it returns true if ANY menu is currenly open.\n   */\n  isOpen(menuId) {\n    return this.menuController.isOpen(menuId);\n  }\n  /**\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns true if the menu is currently enabled, otherwise false.\n   */\n  isEnabled(menuId) {\n    return this.menuController.isEnabled(menuId);\n  }\n  /**\n   * Used to get a menu instance. If a `menuId` is not provided then it'll\n   * return the first menu found. If a `menuId` is `left` or `right`, then\n   * it'll return the enabled menu on that side. Otherwise, if a `menuId` is\n   * provided, then it'll try to find the menu using the menu's `id`\n   * property. If a menu is not found then it'll return `null`.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu if found, otherwise `null`.\n   */\n  get(menuId) {\n    return this.menuController.get(menuId);\n  }\n  /**\n   * @return Returns the instance of the menu already opened, otherwise `null`.\n   */\n  getOpen() {\n    return this.menuController.getOpen();\n  }\n  /**\n   * @return Returns an array of all menu instances.\n   */\n  getMenus() {\n    return this.menuController.getMenus();\n  }\n  registerAnimation(name, animation) {\n    return this.menuController.registerAnimation(name, animation);\n  }\n  isAnimating() {\n    return this.menuController.isAnimating();\n  }\n  _getOpenSync() {\n    return this.menuController._getOpenSync();\n  }\n  _createAnimation(type, menuCmp) {\n    return this.menuController._createAnimation(type, menuCmp);\n  }\n  _register(menu) {\n    return this.menuController._register(menu);\n  }\n  _unregister(menu) {\n    return this.menuController._unregister(menu);\n  }\n  _setOpen(menu, shouldOpen, animated) {\n    return this.menuController._setOpen(menu, shouldOpen, animated);\n  }\n}\nclass DomController {\n  /**\n   * Schedules a task to run during the READ phase of the next frame.\n   * This task should only read the DOM, but never modify it.\n   */\n  read(cb) {\n    getQueue().read(cb);\n  }\n  /**\n   * Schedules a task to run during the WRITE phase of the next frame.\n   * This task should write the DOM, but never READ it.\n   */\n  write(cb) {\n    getQueue().write(cb);\n  }\n  /** @nocollapse */\n  static ɵfac = function DomController_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomController)();\n  };\n  /** @nocollapse */\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomController,\n    factory: DomController.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomController, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst getQueue = () => {\n  const win = typeof window !== 'undefined' ? window : null;\n  if (win != null) {\n    const Ionic = win.Ionic;\n    if (Ionic?.queue) {\n      return Ionic.queue;\n    }\n    return {\n      read: cb => win.requestAnimationFrame(cb),\n      write: cb => win.requestAnimationFrame(cb)\n    };\n  }\n  return {\n    read: cb => cb(),\n    write: cb => cb()\n  };\n};\nclass Platform {\n  doc;\n  _readyPromise;\n  win;\n  /**\n   * @hidden\n   */\n  backButton = new Subject();\n  /**\n   * The keyboardDidShow event emits when the\n   * on-screen keyboard is presented.\n   */\n  keyboardDidShow = new Subject();\n  /**\n   * The keyboardDidHide event emits when the\n   * on-screen keyboard is hidden.\n   */\n  keyboardDidHide = new Subject();\n  /**\n   * The pause event emits when the native platform puts the application\n   * into the background, typically when the user switches to a different\n   * application. This event would emit when a Cordova app is put into\n   * the background, however, it would not fire on a standard web browser.\n   */\n  pause = new Subject();\n  /**\n   * The resume event emits when the native platform pulls the application\n   * out from the background. This event would emit when a Cordova app comes\n   * out from the background, however, it would not fire on a standard web browser.\n   */\n  resume = new Subject();\n  /**\n   * The resize event emits when the browser window has changed dimensions. This\n   * could be from a browser window being physically resized, or from a device\n   * changing orientation.\n   */\n  resize = new Subject();\n  constructor(doc, zone) {\n    this.doc = doc;\n    zone.run(() => {\n      this.win = doc.defaultView;\n      this.backButton.subscribeWithPriority = function (priority, callback) {\n        return this.subscribe(ev => {\n          return ev.register(priority, processNextHandler => zone.run(() => callback(processNextHandler)));\n        });\n      };\n      proxyEvent(this.pause, doc, 'pause', zone);\n      proxyEvent(this.resume, doc, 'resume', zone);\n      proxyEvent(this.backButton, doc, 'ionBackButton', zone);\n      proxyEvent(this.resize, this.win, 'resize', zone);\n      proxyEvent(this.keyboardDidShow, this.win, 'ionKeyboardDidShow', zone);\n      proxyEvent(this.keyboardDidHide, this.win, 'ionKeyboardDidHide', zone);\n      let readyResolve;\n      this._readyPromise = new Promise(res => {\n        readyResolve = res;\n      });\n      if (this.win?.['cordova']) {\n        doc.addEventListener('deviceready', () => {\n          readyResolve('cordova');\n        }, {\n          once: true\n        });\n      } else {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        readyResolve('dom');\n      }\n    });\n  }\n  /**\n   * @returns returns true/false based on platform.\n   * @description\n   * Depending on the platform the user is on, `is(platformName)` will\n   * return `true` or `false`. Note that the same app can return `true`\n   * for more than one platform name. For example, an app running from\n   * an iPad would return `true` for the platform names: `mobile`,\n   * `ios`, `ipad`, and `tablet`. Additionally, if the app was running\n   * from Cordova then `cordova` would be true, and if it was running\n   * from a web browser on the iPad then `mobileweb` would be `true`.\n   *\n   * ```\n   * import { Platform } from 'ionic-angular';\n   *\n   * @Component({...})\n   * export MyPage {\n   *   constructor(public platform: Platform) {\n   *     if (this.platform.is('ios')) {\n   *       // This will only print when on iOS\n   *       console.log('I am an iOS device!');\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * | Platform Name   | Description                        |\n   * |-----------------|------------------------------------|\n   * | android         | on a device running Android.       |\n   * | capacitor       | on a device running Capacitor.     |\n   * | cordova         | on a device running Cordova.       |\n   * | ios             | on a device running iOS.           |\n   * | ipad            | on an iPad device.                 |\n   * | iphone          | on an iPhone device.               |\n   * | phablet         | on a phablet device.               |\n   * | tablet          | on a tablet device.                |\n   * | electron        | in Electron on a desktop device.   |\n   * | pwa             | as a PWA app.                      |\n   * | mobile          | on a mobile device.                |\n   * | mobileweb       | on a mobile device in a browser.   |\n   * | desktop         | on a desktop device.               |\n   * | hybrid          | is a cordova or capacitor app.     |\n   *\n   */\n  is(platformName) {\n    return isPlatform(this.win, platformName);\n  }\n  /**\n   * @returns the array of platforms\n   * @description\n   * Depending on what device you are on, `platforms` can return multiple values.\n   * Each possible value is a hierarchy of platforms. For example, on an iPhone,\n   * it would return `mobile`, `ios`, and `iphone`.\n   *\n   * ```\n   * import { Platform } from 'ionic-angular';\n   *\n   * @Component({...})\n   * export MyPage {\n   *   constructor(public platform: Platform) {\n   *     // This will print an array of the current platforms\n   *     console.log(this.platform.platforms());\n   *   }\n   * }\n   * ```\n   */\n  platforms() {\n    return getPlatforms(this.win);\n  }\n  /**\n   * Returns a promise when the platform is ready and native functionality\n   * can be called. If the app is running from within a web browser, then\n   * the promise will resolve when the DOM is ready. When the app is running\n   * from an application engine such as Cordova, then the promise will\n   * resolve when Cordova triggers the `deviceready` event.\n   *\n   * The resolved value is the `readySource`, which states which platform\n   * ready was used. For example, when Cordova is ready, the resolved ready\n   * source is `cordova`. The default ready source value will be `dom`. The\n   * `readySource` is useful if different logic should run depending on the\n   * platform the app is running from. For example, only Cordova can execute\n   * the status bar plugin, so the web should not run status bar plugin logic.\n   *\n   * ```\n   * import { Component } from '@angular/core';\n   * import { Platform } from 'ionic-angular';\n   *\n   * @Component({...})\n   * export MyApp {\n   *   constructor(public platform: Platform) {\n   *     this.platform.ready().then((readySource) => {\n   *       console.log('Platform ready from', readySource);\n   *       // Platform now ready, execute any required native code\n   *     });\n   *   }\n   * }\n   * ```\n   */\n  ready() {\n    return this._readyPromise;\n  }\n  /**\n   * Returns if this app is using right-to-left language direction or not.\n   * We recommend the app's `index.html` file already has the correct `dir`\n   * attribute value set, such as `<html dir=\"ltr\">` or `<html dir=\"rtl\">`.\n   * [W3C: Structural markup and right-to-left text in HTML](http://www.w3.org/International/questions/qa-html-dir)\n   */\n  get isRTL() {\n    return this.doc.dir === 'rtl';\n  }\n  /**\n   * Get the query string parameter\n   */\n  getQueryParam(key) {\n    return readQueryParam(this.win.location.href, key);\n  }\n  /**\n   * Returns `true` if the app is in landscape mode.\n   */\n  isLandscape() {\n    return !this.isPortrait();\n  }\n  /**\n   * Returns `true` if the app is in portrait mode.\n   */\n  isPortrait() {\n    return this.win.matchMedia?.('(orientation: portrait)').matches;\n  }\n  testUserAgent(expression) {\n    const nav = this.win.navigator;\n    return !!(nav?.userAgent && nav.userAgent.indexOf(expression) >= 0);\n  }\n  /**\n   * Get the current url.\n   */\n  url() {\n    return this.win.location.href;\n  }\n  /**\n   * Gets the width of the platform's viewport using `window.innerWidth`.\n   */\n  width() {\n    return this.win.innerWidth;\n  }\n  /**\n   * Gets the height of the platform's viewport using `window.innerHeight`.\n   */\n  height() {\n    return this.win.innerHeight;\n  }\n  /** @nocollapse */\n  static ɵfac = function Platform_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Platform)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone));\n  };\n  /** @nocollapse */\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Platform,\n    factory: Platform.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Platform, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\nconst readQueryParam = (url, key) => {\n  key = key.replace(/[[\\]\\\\]/g, '\\\\$&');\n  const regex = new RegExp('[\\\\?&]' + key + '=([^&#]*)');\n  const results = regex.exec(url);\n  return results ? decodeURIComponent(results[1].replace(/\\+/g, ' ')) : null;\n};\nconst proxyEvent = (emitter, el, eventName, zone) => {\n  if (el) {\n    el.addEventListener(eventName, ev => {\n      /**\n       * `zone.run` is required to make sure that we are running inside the Angular zone\n       * at all times. This is necessary since an app that has Capacitor will\n       * override the `document.addEventListener` with its own implementation.\n       * The override causes the event to no longer be in the Angular zone.\n       */\n      zone.run(() => {\n        // ?? cordova might emit \"null\" events\n        const value = ev != null ? ev.detail : undefined;\n        emitter.next(value);\n      });\n    });\n  }\n};\nclass NavController {\n  location;\n  serializer;\n  router;\n  topOutlet;\n  direction = DEFAULT_DIRECTION;\n  animated = DEFAULT_ANIMATED;\n  animationBuilder;\n  guessDirection = 'forward';\n  guessAnimation;\n  lastNavId = -1;\n  constructor(platform, location, serializer, router) {\n    this.location = location;\n    this.serializer = serializer;\n    this.router = router;\n    // Subscribe to router events to detect direction\n    if (router) {\n      router.events.subscribe(ev => {\n        if (ev instanceof NavigationStart) {\n          // restoredState is set if the browser back/forward button is used\n          const id = ev.restoredState ? ev.restoredState.navigationId : ev.id;\n          this.guessDirection = this.guessAnimation = id < this.lastNavId ? 'back' : 'forward';\n          this.lastNavId = this.guessDirection === 'forward' ? ev.id : id;\n        }\n      });\n    }\n    // Subscribe to backButton events\n    platform.backButton.subscribeWithPriority(0, processNextHandler => {\n      this.pop();\n      processNextHandler();\n    });\n  }\n  /**\n   * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n   * it's equivalent to calling `this.router.navigateByUrl()`, but it's explicit about the **direction** of the transition.\n   *\n   * Going **forward** means that a new page is going to be pushed to the stack of the outlet (ion-router-outlet),\n   * and that it will show a \"forward\" animation by default.\n   *\n   * Navigating forward can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n   *\n   * ```html\n   * <a routerLink=\"/path/to/page\" routerDirection=\"forward\">Link</a>\n   * ```\n   */\n  navigateForward(url, options = {}) {\n    this.setDirection('forward', options.animated, options.animationDirection, options.animation);\n    return this.navigate(url, options);\n  }\n  /**\n   * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n   * it's equivalent to calling:\n   *\n   * ```ts\n   * this.navController.setDirection('back');\n   * this.router.navigateByUrl(path);\n   * ```\n   *\n   * Going **back** means that all the pages in the stack until the navigated page is found will be popped,\n   * and that it will show a \"back\" animation by default.\n   *\n   * Navigating back can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n   *\n   * ```html\n   * <a routerLink=\"/path/to/page\" routerDirection=\"back\">Link</a>\n   * ```\n   */\n  navigateBack(url, options = {}) {\n    this.setDirection('back', options.animated, options.animationDirection, options.animation);\n    return this.navigate(url, options);\n  }\n  /**\n   * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n   * it's equivalent to calling:\n   *\n   * ```ts\n   * this.navController.setDirection('root');\n   * this.router.navigateByUrl(path);\n   * ```\n   *\n   * Going **root** means that all existing pages in the stack will be removed,\n   * and the navigated page will become the single page in the stack.\n   *\n   * Navigating root can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n   *\n   * ```html\n   * <a routerLink=\"/path/to/page\" routerDirection=\"root\">Link</a>\n   * ```\n   */\n  navigateRoot(url, options = {}) {\n    this.setDirection('root', options.animated, options.animationDirection, options.animation);\n    return this.navigate(url, options);\n  }\n  /**\n   * Same as [Location](https://angular.io/api/common/Location)'s back() method.\n   * It will use the standard `window.history.back()` under the hood, but featuring a `back` animation\n   * by default.\n   */\n  back(options = {\n    animated: true,\n    animationDirection: 'back'\n  }) {\n    this.setDirection('back', options.animated, options.animationDirection, options.animation);\n    return this.location.back();\n  }\n  /**\n   * This methods goes back in the context of Ionic's stack navigation.\n   *\n   * It recursively finds the top active `ion-router-outlet` and calls `pop()`.\n   * This is the recommended way to go back when you are using `ion-router-outlet`.\n   *\n   * Resolves to `true` if it was able to pop.\n   */\n  async pop() {\n    let outlet = this.topOutlet;\n    while (outlet) {\n      if (await outlet.pop()) {\n        return true;\n      } else {\n        outlet = outlet.parentOutlet;\n      }\n    }\n    return false;\n  }\n  /**\n   * This methods specifies the direction of the next navigation performed by the Angular router.\n   *\n   * `setDirection()` does not trigger any transition, it just sets some flags to be consumed by `ion-router-outlet`.\n   *\n   * It's recommended to use `navigateForward()`, `navigateBack()` and `navigateRoot()` instead of `setDirection()`.\n   */\n  setDirection(direction, animated, animationDirection, animationBuilder) {\n    this.direction = direction;\n    this.animated = getAnimation(direction, animated, animationDirection);\n    this.animationBuilder = animationBuilder;\n  }\n  /**\n   * @internal\n   */\n  setTopOutlet(outlet) {\n    this.topOutlet = outlet;\n  }\n  /**\n   * @internal\n   */\n  consumeTransition() {\n    let direction = 'root';\n    let animation;\n    const animationBuilder = this.animationBuilder;\n    if (this.direction === 'auto') {\n      direction = this.guessDirection;\n      animation = this.guessAnimation;\n    } else {\n      animation = this.animated;\n      direction = this.direction;\n    }\n    this.direction = DEFAULT_DIRECTION;\n    this.animated = DEFAULT_ANIMATED;\n    this.animationBuilder = undefined;\n    return {\n      direction,\n      animation,\n      animationBuilder\n    };\n  }\n  navigate(url, options) {\n    if (Array.isArray(url)) {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      return this.router.navigate(url, options);\n    } else {\n      /**\n       * navigateByUrl ignores any properties that\n       * would change the url, so things like queryParams\n       * would be ignored unless we create a url tree\n       * More Info: https://github.com/angular/angular/issues/18798\n       */\n      const urlTree = this.serializer.parse(url.toString());\n      if (options.queryParams !== undefined) {\n        urlTree.queryParams = {\n          ...options.queryParams\n        };\n      }\n      if (options.fragment !== undefined) {\n        urlTree.fragment = options.fragment;\n      }\n      /**\n       * `navigateByUrl` will still apply `NavigationExtras` properties\n       * that do not modify the url, such as `replaceUrl` which is why\n       * `options` is passed in here.\n       */\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      return this.router.navigateByUrl(urlTree, options);\n    }\n  }\n  /** @nocollapse */\n  static ɵfac = function NavController_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NavController)(i0.ɵɵinject(Platform), i0.ɵɵinject(i1.Location), i0.ɵɵinject(i3.UrlSerializer), i0.ɵɵinject(i3.Router, 8));\n  };\n  /** @nocollapse */\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NavController,\n    factory: NavController.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NavController, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: Platform\n    }, {\n      type: i1.Location\n    }, {\n      type: i3.UrlSerializer\n    }, {\n      type: i3.Router,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\nconst getAnimation = (direction, animated, animationDirection) => {\n  if (animated === false) {\n    return undefined;\n  }\n  if (animationDirection !== undefined) {\n    return animationDirection;\n  }\n  if (direction === 'forward' || direction === 'back') {\n    return direction;\n  } else if (direction === 'root' && animated === true) {\n    return 'forward';\n  }\n  return undefined;\n};\nconst DEFAULT_DIRECTION = 'auto';\nconst DEFAULT_ANIMATED = undefined;\nclass Config {\n  get(key, fallback) {\n    const c = getConfig();\n    if (c) {\n      return c.get(key, fallback);\n    }\n    return null;\n  }\n  getBoolean(key, fallback) {\n    const c = getConfig();\n    if (c) {\n      return c.getBoolean(key, fallback);\n    }\n    return false;\n  }\n  getNumber(key, fallback) {\n    const c = getConfig();\n    if (c) {\n      return c.getNumber(key, fallback);\n    }\n    return 0;\n  }\n  /** @nocollapse */\n  static ɵfac = function Config_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Config)();\n  };\n  /** @nocollapse */\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Config,\n    factory: Config.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Config, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst ConfigToken = new InjectionToken('USERCONFIG');\nconst getConfig = () => {\n  if (typeof window !== 'undefined') {\n    const Ionic = window.Ionic;\n    if (Ionic?.config) {\n      return Ionic.config;\n    }\n  }\n  return null;\n};\n\n/**\n * @description\n * NavParams are an object that exists on a page and can contain data for that particular view.\n * Similar to how data was pass to a view in V1 with `$stateParams`, NavParams offer a much more flexible\n * option with a simple `get` method.\n *\n * @usage\n * ```ts\n * import { NavParams } from '@ionic/angular';\n *\n * export class MyClass{\n *\n *  constructor(navParams: NavParams){\n *    // userParams is an object we have in our nav-parameters\n *    navParams.get('userParams');\n *  }\n *\n * }\n * ```\n */\nclass NavParams {\n  data;\n  constructor(data = {}) {\n    this.data = data;\n    console.warn(`[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.`);\n  }\n  /**\n   * Get the value of a nav-parameter for the current view\n   *\n   * ```ts\n   * import { NavParams } from 'ionic-angular';\n   *\n   * export class MyClass{\n   *  constructor(public navParams: NavParams){\n   *    // userParams is an object we have in our nav-parameters\n   *    this.navParams.get('userParams');\n   *  }\n   * }\n   * ```\n   *\n   * @param param Which param you want to look up\n   */\n  get(param) {\n    return this.data[param];\n  }\n}\n\n// TODO(FW-2827): types\nclass AngularDelegate {\n  zone = inject(NgZone);\n  applicationRef = inject(ApplicationRef);\n  config = inject(ConfigToken);\n  create(environmentInjector, injector, elementReferenceKey) {\n    return new AngularFrameworkDelegate(environmentInjector, injector, this.applicationRef, this.zone, elementReferenceKey, this.config.useSetInputAPI ?? false);\n  }\n  /** @nocollapse */\n  static ɵfac = function AngularDelegate_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AngularDelegate)();\n  };\n  /** @nocollapse */\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AngularDelegate,\n    factory: AngularDelegate.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularDelegate, [{\n    type: Injectable\n  }], null, null);\n})();\nclass AngularFrameworkDelegate {\n  environmentInjector;\n  injector;\n  applicationRef;\n  zone;\n  elementReferenceKey;\n  enableSignalsSupport;\n  elRefMap = new WeakMap();\n  elEventsMap = new WeakMap();\n  constructor(environmentInjector, injector, applicationRef, zone, elementReferenceKey, enableSignalsSupport) {\n    this.environmentInjector = environmentInjector;\n    this.injector = injector;\n    this.applicationRef = applicationRef;\n    this.zone = zone;\n    this.elementReferenceKey = elementReferenceKey;\n    this.enableSignalsSupport = enableSignalsSupport;\n  }\n  attachViewToDom(container, component, params, cssClasses) {\n    return this.zone.run(() => {\n      return new Promise(resolve => {\n        const componentProps = {\n          ...params\n        };\n        /**\n         * Ionic Angular passes a reference to a modal\n         * or popover that can be accessed using a\n         * variable in the overlay component. If\n         * elementReferenceKey is defined, then we should\n         * pass a reference to the component using\n         * elementReferenceKey as the key.\n         */\n        if (this.elementReferenceKey !== undefined) {\n          componentProps[this.elementReferenceKey] = container;\n        }\n        const el = attachView(this.zone, this.environmentInjector, this.injector, this.applicationRef, this.elRefMap, this.elEventsMap, container, component, componentProps, cssClasses, this.elementReferenceKey, this.enableSignalsSupport);\n        resolve(el);\n      });\n    });\n  }\n  removeViewFromDom(_container, component) {\n    return this.zone.run(() => {\n      return new Promise(resolve => {\n        const componentRef = this.elRefMap.get(component);\n        if (componentRef) {\n          componentRef.destroy();\n          this.elRefMap.delete(component);\n          const unbindEvents = this.elEventsMap.get(component);\n          if (unbindEvents) {\n            unbindEvents();\n            this.elEventsMap.delete(component);\n          }\n        }\n        resolve();\n      });\n    });\n  }\n}\nconst attachView = (zone, environmentInjector, injector, applicationRef, elRefMap, elEventsMap, container, component, params, cssClasses, elementReferenceKey, enableSignalsSupport) => {\n  /**\n   * Wraps the injector with a custom injector that\n   * provides NavParams to the component.\n   *\n   * NavParams is a legacy feature from Ionic v3 that allows\n   * Angular developers to provide data to a component\n   * and access it by providing NavParams as a dependency\n   * in the constructor.\n   *\n   * The modern approach is to access the data directly\n   * from the component's class instance.\n   */\n  const childInjector = Injector.create({\n    providers: getProviders(params),\n    parent: injector\n  });\n  const componentRef = createComponent(component, {\n    environmentInjector,\n    elementInjector: childInjector\n  });\n  const instance = componentRef.instance;\n  const hostElement = componentRef.location.nativeElement;\n  if (params) {\n    /**\n     * For modals and popovers, a reference to the component is\n     * added to `params` during the call to attachViewToDom. If\n     * a reference using this name is already set, this means\n     * the app is trying to use the name as a component prop,\n     * which will cause collisions.\n     */\n    if (elementReferenceKey && instance[elementReferenceKey] !== undefined) {\n      console.error(`[Ionic Error]: ${elementReferenceKey} is a reserved property when using ${container.tagName.toLowerCase()}. Rename or remove the \"${elementReferenceKey}\" property from ${component.name}.`);\n    }\n    /**\n     * Angular 14.1 added support for setInput\n     * so we need to fall back to Object.assign\n     * for Angular 14.0.\n     */\n    if (enableSignalsSupport === true && componentRef.setInput !== undefined) {\n      const {\n        modal,\n        popover,\n        ...otherParams\n      } = params;\n      /**\n       * Any key/value pairs set in componentProps\n       * must be set as inputs on the component instance.\n       */\n      for (const key in otherParams) {\n        componentRef.setInput(key, otherParams[key]);\n      }\n      /**\n       * Using setInput will cause an error when\n       * setting modal/popover on a component that\n       * does not define them as an input. For backwards\n       * compatibility purposes we fall back to using\n       * Object.assign for these properties.\n       */\n      if (modal !== undefined) {\n        Object.assign(instance, {\n          modal\n        });\n      }\n      if (popover !== undefined) {\n        Object.assign(instance, {\n          popover\n        });\n      }\n    } else {\n      Object.assign(instance, params);\n    }\n  }\n  if (cssClasses) {\n    for (const cssClass of cssClasses) {\n      hostElement.classList.add(cssClass);\n    }\n  }\n  const unbindEvents = bindLifecycleEvents(zone, instance, hostElement);\n  container.appendChild(hostElement);\n  applicationRef.attachView(componentRef.hostView);\n  elRefMap.set(hostElement, componentRef);\n  elEventsMap.set(hostElement, unbindEvents);\n  return hostElement;\n};\nconst LIFECYCLES = [LIFECYCLE_WILL_ENTER, LIFECYCLE_DID_ENTER, LIFECYCLE_WILL_LEAVE, LIFECYCLE_DID_LEAVE, LIFECYCLE_WILL_UNLOAD];\nconst bindLifecycleEvents = (zone, instance, element) => {\n  return zone.run(() => {\n    const unregisters = LIFECYCLES.filter(eventName => typeof instance[eventName] === 'function').map(eventName => {\n      const handler = ev => instance[eventName](ev.detail);\n      element.addEventListener(eventName, handler);\n      return () => element.removeEventListener(eventName, handler);\n    });\n    return () => unregisters.forEach(fn => fn());\n  });\n};\nconst NavParamsToken = new InjectionToken('NavParamsToken');\nconst getProviders = params => {\n  return [{\n    provide: NavParamsToken,\n    useValue: params\n  }, {\n    provide: NavParams,\n    useFactory: provideNavParamsInjectable,\n    deps: [NavParamsToken]\n  }];\n};\nconst provideNavParamsInjectable = params => {\n  return new NavParams(params);\n};\n\n// TODO: Is there a way we can grab this from angular-component-lib instead?\n/* eslint-disable */\n/* tslint:disable */\nconst proxyInputs = (Cmp, inputs) => {\n  const Prototype = Cmp.prototype;\n  inputs.forEach(item => {\n    Object.defineProperty(Prototype, item, {\n      get() {\n        return this.el[item];\n      },\n      set(val) {\n        this.z.runOutsideAngular(() => this.el[item] = val);\n      }\n    });\n  });\n};\nconst proxyMethods = (Cmp, methods) => {\n  const Prototype = Cmp.prototype;\n  methods.forEach(methodName => {\n    Prototype[methodName] = function () {\n      const args = arguments;\n      return this.z.runOutsideAngular(() => this.el[methodName].apply(this.el, args));\n    };\n  });\n};\nconst proxyOutputs = (instance, el, events) => {\n  events.forEach(eventName => instance[eventName] = fromEvent(el, eventName));\n};\n// tslint:disable-next-line: only-arrow-functions\nfunction ProxyCmp(opts) {\n  const decorator = function (cls) {\n    const {\n      defineCustomElementFn,\n      inputs,\n      methods\n    } = opts;\n    if (defineCustomElementFn !== undefined) {\n      defineCustomElementFn();\n    }\n    if (inputs) {\n      proxyInputs(cls, inputs);\n    }\n    if (methods) {\n      proxyMethods(cls, methods);\n    }\n    return cls;\n  };\n  return decorator;\n}\nconst POPOVER_INPUTS = ['alignment', 'animated', 'arrow', 'keepContentsMounted', 'backdropDismiss', 'cssClass', 'dismissOnSelect', 'enterAnimation', 'event', 'focusTrap', 'isOpen', 'keyboardClose', 'leaveAnimation', 'mode', 'showBackdrop', 'translucent', 'trigger', 'triggerAction', 'reference', 'size', 'side'];\nconst POPOVER_METHODS = ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss'];\nlet IonPopover = class IonPopover {\n  z;\n  // TODO(FW-2827): type\n  template;\n  isCmpOpen = false;\n  el;\n  constructor(c, r, z) {\n    this.z = z;\n    this.el = r.nativeElement;\n    this.el.addEventListener('ionMount', () => {\n      this.isCmpOpen = true;\n      c.detectChanges();\n    });\n    this.el.addEventListener('didDismiss', () => {\n      this.isCmpOpen = false;\n      c.detectChanges();\n    });\n    proxyOutputs(this, this.el, ['ionPopoverDidPresent', 'ionPopoverWillPresent', 'ionPopoverWillDismiss', 'ionPopoverDidDismiss', 'didPresent', 'willPresent', 'willDismiss', 'didDismiss']);\n  }\n  /** @nocollapse */\n  static ɵfac = function IonPopover_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || IonPopover)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonPopover,\n    selectors: [[\"ion-popover\"]],\n    contentQueries: function IonPopover_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, TemplateRef, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n      }\n    },\n    inputs: {\n      alignment: \"alignment\",\n      animated: \"animated\",\n      arrow: \"arrow\",\n      keepContentsMounted: \"keepContentsMounted\",\n      backdropDismiss: \"backdropDismiss\",\n      cssClass: \"cssClass\",\n      dismissOnSelect: \"dismissOnSelect\",\n      enterAnimation: \"enterAnimation\",\n      event: \"event\",\n      focusTrap: \"focusTrap\",\n      isOpen: \"isOpen\",\n      keyboardClose: \"keyboardClose\",\n      leaveAnimation: \"leaveAnimation\",\n      mode: \"mode\",\n      showBackdrop: \"showBackdrop\",\n      translucent: \"translucent\",\n      trigger: \"trigger\",\n      triggerAction: \"triggerAction\",\n      reference: \"reference\",\n      size: \"size\",\n      side: \"side\"\n    },\n    standalone: false\n  });\n};\nIonPopover = __decorate([ProxyCmp({\n  inputs: POPOVER_INPUTS,\n  methods: POPOVER_METHODS\n})\n/**\n * @Component extends from @Directive\n * so by defining the inputs here we\n * do not need to re-define them for the\n * lazy loaded popover.\n */], IonPopover);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonPopover, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-popover',\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: POPOVER_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    template: [{\n      type: ContentChild,\n      args: [TemplateRef, {\n        static: false\n      }]\n    }]\n  });\n})();\nconst MODAL_INPUTS = ['animated', 'keepContentsMounted', 'backdropBreakpoint', 'backdropDismiss', 'breakpoints', 'canDismiss', 'cssClass', 'enterAnimation', 'expandToScroll', 'event', 'focusTrap', 'handle', 'handleBehavior', 'initialBreakpoint', 'isOpen', 'keyboardClose', 'leaveAnimation', 'mode', 'presentingElement', 'showBackdrop', 'translucent', 'trigger'];\nconst MODAL_METHODS = ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss', 'setCurrentBreakpoint', 'getCurrentBreakpoint'];\nlet IonModal = class IonModal {\n  z;\n  // TODO(FW-2827): type\n  template;\n  isCmpOpen = false;\n  el;\n  constructor(c, r, z) {\n    this.z = z;\n    this.el = r.nativeElement;\n    this.el.addEventListener('ionMount', () => {\n      this.isCmpOpen = true;\n      c.detectChanges();\n    });\n    this.el.addEventListener('didDismiss', () => {\n      this.isCmpOpen = false;\n      c.detectChanges();\n    });\n    proxyOutputs(this, this.el, ['ionModalDidPresent', 'ionModalWillPresent', 'ionModalWillDismiss', 'ionModalDidDismiss', 'ionBreakpointDidChange', 'didPresent', 'willPresent', 'willDismiss', 'didDismiss']);\n  }\n  /** @nocollapse */\n  static ɵfac = function IonModal_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || IonModal)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonModal,\n    selectors: [[\"ion-modal\"]],\n    contentQueries: function IonModal_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, TemplateRef, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n      }\n    },\n    inputs: {\n      animated: \"animated\",\n      keepContentsMounted: \"keepContentsMounted\",\n      backdropBreakpoint: \"backdropBreakpoint\",\n      backdropDismiss: \"backdropDismiss\",\n      breakpoints: \"breakpoints\",\n      canDismiss: \"canDismiss\",\n      cssClass: \"cssClass\",\n      enterAnimation: \"enterAnimation\",\n      expandToScroll: \"expandToScroll\",\n      event: \"event\",\n      focusTrap: \"focusTrap\",\n      handle: \"handle\",\n      handleBehavior: \"handleBehavior\",\n      initialBreakpoint: \"initialBreakpoint\",\n      isOpen: \"isOpen\",\n      keyboardClose: \"keyboardClose\",\n      leaveAnimation: \"leaveAnimation\",\n      mode: \"mode\",\n      presentingElement: \"presentingElement\",\n      showBackdrop: \"showBackdrop\",\n      translucent: \"translucent\",\n      trigger: \"trigger\"\n    },\n    standalone: false\n  });\n};\nIonModal = __decorate([ProxyCmp({\n  inputs: MODAL_INPUTS,\n  methods: MODAL_METHODS\n})\n/**\n * @Component extends from @Directive\n * so by defining the inputs here we\n * do not need to re-define them for the\n * lazy loaded popover.\n */], IonModal);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonModal, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-modal',\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: MODAL_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    template: [{\n      type: ContentChild,\n      args: [TemplateRef, {\n        static: false\n      }]\n    }]\n  });\n})();\nconst insertView = (views, view, direction) => {\n  if (direction === 'root') {\n    return setRoot(views, view);\n  } else if (direction === 'forward') {\n    return setForward(views, view);\n  } else {\n    return setBack(views, view);\n  }\n};\nconst setRoot = (views, view) => {\n  views = views.filter(v => v.stackId !== view.stackId);\n  views.push(view);\n  return views;\n};\nconst setForward = (views, view) => {\n  const index = views.indexOf(view);\n  if (index >= 0) {\n    views = views.filter(v => v.stackId !== view.stackId || v.id <= view.id);\n  } else {\n    views.push(view);\n  }\n  return views;\n};\nconst setBack = (views, view) => {\n  const index = views.indexOf(view);\n  if (index >= 0) {\n    return views.filter(v => v.stackId !== view.stackId || v.id <= view.id);\n  } else {\n    return setRoot(views, view);\n  }\n};\nconst getUrl = (router, activatedRoute) => {\n  const urlTree = router.createUrlTree(['.'], {\n    relativeTo: activatedRoute\n  });\n  return router.serializeUrl(urlTree);\n};\nconst isTabSwitch = (enteringView, leavingView) => {\n  if (!leavingView) {\n    return true;\n  }\n  return enteringView.stackId !== leavingView.stackId;\n};\nconst computeStackId = (prefixUrl, url) => {\n  if (!prefixUrl) {\n    return undefined;\n  }\n  const segments = toSegments(url);\n  for (let i = 0; i < segments.length; i++) {\n    if (i >= prefixUrl.length) {\n      return segments[i];\n    }\n    if (segments[i] !== prefixUrl[i]) {\n      return undefined;\n    }\n  }\n  return undefined;\n};\nconst toSegments = path => {\n  return path.split('/').map(s => s.trim()).filter(s => s !== '');\n};\nconst destroyView = view => {\n  if (view) {\n    view.ref.destroy();\n    view.unlistenEvents();\n  }\n};\n\n// TODO(FW-2827): types\nclass StackController {\n  containerEl;\n  router;\n  navCtrl;\n  zone;\n  location;\n  views = [];\n  runningTask;\n  skipTransition = false;\n  tabsPrefix;\n  activeView;\n  nextId = 0;\n  constructor(tabsPrefix, containerEl, router, navCtrl, zone, location) {\n    this.containerEl = containerEl;\n    this.router = router;\n    this.navCtrl = navCtrl;\n    this.zone = zone;\n    this.location = location;\n    this.tabsPrefix = tabsPrefix !== undefined ? toSegments(tabsPrefix) : undefined;\n  }\n  createView(ref, activatedRoute) {\n    const url = getUrl(this.router, activatedRoute);\n    const element = ref?.location?.nativeElement;\n    const unlistenEvents = bindLifecycleEvents(this.zone, ref.instance, element);\n    return {\n      id: this.nextId++,\n      stackId: computeStackId(this.tabsPrefix, url),\n      unlistenEvents,\n      element,\n      ref,\n      url\n    };\n  }\n  getExistingView(activatedRoute) {\n    const activatedUrlKey = getUrl(this.router, activatedRoute);\n    const view = this.views.find(vw => vw.url === activatedUrlKey);\n    if (view) {\n      view.ref.changeDetectorRef.reattach();\n    }\n    return view;\n  }\n  setActive(enteringView) {\n    const consumeResult = this.navCtrl.consumeTransition();\n    let {\n      direction,\n      animation,\n      animationBuilder\n    } = consumeResult;\n    const leavingView = this.activeView;\n    const tabSwitch = isTabSwitch(enteringView, leavingView);\n    if (tabSwitch) {\n      direction = 'back';\n      animation = undefined;\n    }\n    const viewsSnapshot = this.views.slice();\n    let currentNavigation;\n    const router = this.router;\n    // Angular >= 7.2.0\n    if (router.getCurrentNavigation) {\n      currentNavigation = router.getCurrentNavigation();\n      // Angular < 7.2.0\n    } else if (router.navigations?.value) {\n      currentNavigation = router.navigations.value;\n    }\n    /**\n     * If the navigation action\n     * sets `replaceUrl: true`\n     * then we need to make sure\n     * we remove the last item\n     * from our views stack\n     */\n    if (currentNavigation?.extras?.replaceUrl) {\n      if (this.views.length > 0) {\n        this.views.splice(-1, 1);\n      }\n    }\n    const reused = this.views.includes(enteringView);\n    const views = this.insertView(enteringView, direction);\n    // Trigger change detection before transition starts\n    // This will call ngOnInit() the first time too, just after the view\n    // was attached to the dom, but BEFORE the transition starts\n    if (!reused) {\n      enteringView.ref.changeDetectorRef.detectChanges();\n    }\n    /**\n     * If we are going back from a page that\n     * was presented using a custom animation\n     * we should default to using that\n     * unless the developer explicitly\n     * provided another animation.\n     */\n    const customAnimation = enteringView.animationBuilder;\n    if (animationBuilder === undefined && direction === 'back' && !tabSwitch && customAnimation !== undefined) {\n      animationBuilder = customAnimation;\n    }\n    /**\n     * Save any custom animation so that navigating\n     * back will use this custom animation by default.\n     */\n    if (leavingView) {\n      leavingView.animationBuilder = animationBuilder;\n    }\n    // Wait until previous transitions finish\n    return this.zone.runOutsideAngular(() => {\n      return this.wait(() => {\n        // disconnect leaving page from change detection to\n        // reduce jank during the page transition\n        if (leavingView) {\n          leavingView.ref.changeDetectorRef.detach();\n        }\n        // In case the enteringView is the same as the leavingPage we need to reattach()\n        enteringView.ref.changeDetectorRef.reattach();\n        return this.transition(enteringView, leavingView, animation, this.canGoBack(1), false, animationBuilder).then(() => cleanupAsync(enteringView, views, viewsSnapshot, this.location, this.zone)).then(() => ({\n          enteringView,\n          direction,\n          animation,\n          tabSwitch\n        }));\n      });\n    });\n  }\n  canGoBack(deep, stackId = this.getActiveStackId()) {\n    return this.getStack(stackId).length > deep;\n  }\n  pop(deep, stackId = this.getActiveStackId()) {\n    return this.zone.run(() => {\n      const views = this.getStack(stackId);\n      if (views.length <= deep) {\n        return Promise.resolve(false);\n      }\n      const view = views[views.length - deep - 1];\n      let url = view.url;\n      const viewSavedData = view.savedData;\n      if (viewSavedData) {\n        const primaryOutlet = viewSavedData.get('primary');\n        if (primaryOutlet?.route?._routerState?.snapshot.url) {\n          url = primaryOutlet.route._routerState.snapshot.url;\n        }\n      }\n      const {\n        animationBuilder\n      } = this.navCtrl.consumeTransition();\n      return this.navCtrl.navigateBack(url, {\n        ...view.savedExtras,\n        animation: animationBuilder\n      }).then(() => true);\n    });\n  }\n  startBackTransition() {\n    const leavingView = this.activeView;\n    if (leavingView) {\n      const views = this.getStack(leavingView.stackId);\n      const enteringView = views[views.length - 2];\n      const customAnimation = enteringView.animationBuilder;\n      return this.wait(() => {\n        return this.transition(enteringView,\n        // entering view\n        leavingView,\n        // leaving view\n        'back', this.canGoBack(2), true, customAnimation);\n      });\n    }\n    return Promise.resolve();\n  }\n  endBackTransition(shouldComplete) {\n    if (shouldComplete) {\n      this.skipTransition = true;\n      this.pop(1);\n    } else if (this.activeView) {\n      cleanup(this.activeView, this.views, this.views, this.location, this.zone);\n    }\n  }\n  getLastUrl(stackId) {\n    const views = this.getStack(stackId);\n    return views.length > 0 ? views[views.length - 1] : undefined;\n  }\n  /**\n   * @internal\n   */\n  getRootUrl(stackId) {\n    const views = this.getStack(stackId);\n    return views.length > 0 ? views[0] : undefined;\n  }\n  getActiveStackId() {\n    return this.activeView ? this.activeView.stackId : undefined;\n  }\n  /**\n   * @internal\n   */\n  getActiveView() {\n    return this.activeView;\n  }\n  hasRunningTask() {\n    return this.runningTask !== undefined;\n  }\n  destroy() {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.containerEl = undefined;\n    this.views.forEach(destroyView);\n    this.activeView = undefined;\n    this.views = [];\n  }\n  getStack(stackId) {\n    return this.views.filter(v => v.stackId === stackId);\n  }\n  insertView(enteringView, direction) {\n    this.activeView = enteringView;\n    this.views = insertView(this.views, enteringView, direction);\n    return this.views.slice();\n  }\n  transition(enteringView, leavingView, direction, showGoBack, progressAnimation, animationBuilder) {\n    if (this.skipTransition) {\n      this.skipTransition = false;\n      return Promise.resolve(false);\n    }\n    if (leavingView === enteringView) {\n      return Promise.resolve(false);\n    }\n    const enteringEl = enteringView ? enteringView.element : undefined;\n    const leavingEl = leavingView ? leavingView.element : undefined;\n    const containerEl = this.containerEl;\n    if (enteringEl && enteringEl !== leavingEl) {\n      enteringEl.classList.add('ion-page');\n      enteringEl.classList.add('ion-page-invisible');\n      if (containerEl.commit) {\n        return containerEl.commit(enteringEl, leavingEl, {\n          duration: direction === undefined ? 0 : undefined,\n          direction,\n          showGoBack,\n          progressAnimation,\n          animationBuilder\n        });\n      }\n    }\n    return Promise.resolve(false);\n  }\n  async wait(task) {\n    if (this.runningTask !== undefined) {\n      await this.runningTask;\n      this.runningTask = undefined;\n    }\n    const promise = this.runningTask = task();\n    promise.finally(() => this.runningTask = undefined);\n    return promise;\n  }\n}\nconst cleanupAsync = (activeRoute, views, viewsSnapshot, location, zone) => {\n  if (typeof requestAnimationFrame === 'function') {\n    return new Promise(resolve => {\n      requestAnimationFrame(() => {\n        cleanup(activeRoute, views, viewsSnapshot, location, zone);\n        resolve();\n      });\n    });\n  }\n  return Promise.resolve();\n};\nconst cleanup = (activeRoute, views, viewsSnapshot, location, zone) => {\n  /**\n   * Re-enter the Angular zone when destroying page components. This will allow\n   * lifecycle events (`ngOnDestroy`) to be run inside the Angular zone.\n   */\n  zone.run(() => viewsSnapshot.filter(view => !views.includes(view)).forEach(destroyView));\n  views.forEach(view => {\n    /**\n     * In the event that a user navigated multiple\n     * times in rapid succession, we want to make sure\n     * we don't pre-emptively detach a view while\n     * it is in mid-transition.\n     *\n     * In this instance we also do not care about query\n     * params or fragments as it will be the same view regardless\n     */\n    const locationWithoutParams = location.path().split('?')[0];\n    const locationWithoutFragment = locationWithoutParams.split('#')[0];\n    if (view !== activeRoute && view.url !== locationWithoutFragment) {\n      const element = view.element;\n      element.setAttribute('aria-hidden', 'true');\n      element.classList.add('ion-page-hidden');\n      view.ref.changeDetectorRef.detach();\n    }\n  });\n};\n\n// TODO(FW-2827): types\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass IonRouterOutlet {\n  parentOutlet;\n  nativeEl;\n  activatedView = null;\n  tabsPrefix;\n  _swipeGesture;\n  stackCtrl;\n  // Maintain map of activated route proxies for each component instance\n  proxyMap = new WeakMap();\n  // Keep the latest activated route in a subject for the proxy routes to switch map to\n  currentActivatedRoute$ = new BehaviorSubject(null);\n  activated = null;\n  /** @internal */\n  get activatedComponentRef() {\n    return this.activated;\n  }\n  _activatedRoute = null;\n  /**\n   * The name of the outlet\n   */\n  name = PRIMARY_OUTLET;\n  /** @internal */\n  stackWillChange = new EventEmitter();\n  /** @internal */\n  stackDidChange = new EventEmitter();\n  // eslint-disable-next-line @angular-eslint/no-output-rename\n  activateEvents = new EventEmitter();\n  // eslint-disable-next-line @angular-eslint/no-output-rename\n  deactivateEvents = new EventEmitter();\n  parentContexts = inject(ChildrenOutletContexts);\n  location = inject(ViewContainerRef);\n  environmentInjector = inject(EnvironmentInjector);\n  inputBinder = inject(INPUT_BINDER, {\n    optional: true\n  });\n  /** @nodoc */\n  supportsBindingToComponentInputs = true;\n  // Ionic providers\n  config = inject(Config);\n  navCtrl = inject(NavController);\n  set animation(animation) {\n    this.nativeEl.animation = animation;\n  }\n  set animated(animated) {\n    this.nativeEl.animated = animated;\n  }\n  set swipeGesture(swipe) {\n    this._swipeGesture = swipe;\n    this.nativeEl.swipeHandler = swipe ? {\n      canStart: () => this.stackCtrl.canGoBack(1) && !this.stackCtrl.hasRunningTask(),\n      onStart: () => this.stackCtrl.startBackTransition(),\n      onEnd: shouldContinue => this.stackCtrl.endBackTransition(shouldContinue)\n    } : undefined;\n  }\n  constructor(name, tabs, commonLocation, elementRef, router, zone, activatedRoute, parentOutlet) {\n    this.parentOutlet = parentOutlet;\n    this.nativeEl = elementRef.nativeElement;\n    this.name = name || PRIMARY_OUTLET;\n    this.tabsPrefix = tabs === 'true' ? getUrl(router, activatedRoute) : undefined;\n    this.stackCtrl = new StackController(this.tabsPrefix, this.nativeEl, router, this.navCtrl, zone, commonLocation);\n    this.parentContexts.onChildOutletCreated(this.name, this);\n  }\n  ngOnDestroy() {\n    this.stackCtrl.destroy();\n    this.inputBinder?.unsubscribeFromRouteData(this);\n  }\n  getContext() {\n    return this.parentContexts.getContext(this.name);\n  }\n  ngOnInit() {\n    this.initializeOutletWithName();\n  }\n  // Note: Ionic deviates from the Angular Router implementation here\n  initializeOutletWithName() {\n    if (!this.activated) {\n      // If the outlet was not instantiated at the time the route got activated we need to populate\n      // the outlet when it is initialized (ie inside a NgIf)\n      const context = this.getContext();\n      if (context?.route) {\n        this.activateWith(context.route, context.injector);\n      }\n    }\n    new Promise(resolve => componentOnReady(this.nativeEl, resolve)).then(() => {\n      if (this._swipeGesture === undefined) {\n        this.swipeGesture = this.config.getBoolean('swipeBackEnabled', this.nativeEl.mode === 'ios');\n      }\n    });\n  }\n  get isActivated() {\n    return !!this.activated;\n  }\n  get component() {\n    if (!this.activated) {\n      throw new Error('Outlet is not activated');\n    }\n    return this.activated.instance;\n  }\n  get activatedRoute() {\n    if (!this.activated) {\n      throw new Error('Outlet is not activated');\n    }\n    return this._activatedRoute;\n  }\n  get activatedRouteData() {\n    if (this._activatedRoute) {\n      return this._activatedRoute.snapshot.data;\n    }\n    return {};\n  }\n  /**\n   * Called when the `RouteReuseStrategy` instructs to detach the subtree\n   */\n  detach() {\n    throw new Error('incompatible reuse strategy');\n  }\n  /**\n   * Called when the `RouteReuseStrategy` instructs to re-attach a previously detached subtree\n   */\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  attach(_ref, _activatedRoute) {\n    throw new Error('incompatible reuse strategy');\n  }\n  deactivate() {\n    if (this.activated) {\n      if (this.activatedView) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const context = this.getContext();\n        this.activatedView.savedData = new Map(context.children['contexts']);\n        /**\n         * Angular v11.2.10 introduced a change\n         * where this route context is cleared out when\n         * a router-outlet is deactivated, However,\n         * we need this route information in order to\n         * return a user back to the correct tab when\n         * leaving and then going back to the tab context.\n         */\n        const primaryOutlet = this.activatedView.savedData.get('primary');\n        if (primaryOutlet && context.route) {\n          primaryOutlet.route = {\n            ...context.route\n          };\n        }\n        /**\n         * Ensure we are saving the NavigationExtras\n         * data otherwise it will be lost\n         */\n        this.activatedView.savedExtras = {};\n        if (context.route) {\n          const contextSnapshot = context.route.snapshot;\n          this.activatedView.savedExtras.queryParams = contextSnapshot.queryParams;\n          this.activatedView.savedExtras.fragment = contextSnapshot.fragment;\n        }\n      }\n      const c = this.component;\n      this.activatedView = null;\n      this.activated = null;\n      this._activatedRoute = null;\n      this.deactivateEvents.emit(c);\n    }\n  }\n  activateWith(activatedRoute, environmentInjector) {\n    if (this.isActivated) {\n      throw new Error('Cannot activate an already activated outlet');\n    }\n    this._activatedRoute = activatedRoute;\n    let cmpRef;\n    let enteringView = this.stackCtrl.getExistingView(activatedRoute);\n    if (enteringView) {\n      cmpRef = this.activated = enteringView.ref;\n      const saved = enteringView.savedData;\n      if (saved) {\n        // self-restore\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const context = this.getContext();\n        context.children['contexts'] = saved;\n      }\n      // Updated activated route proxy for this component\n      this.updateActivatedRouteProxy(cmpRef.instance, activatedRoute);\n    } else {\n      const snapshot = activatedRoute._futureSnapshot;\n      /**\n       * Angular 14 introduces a new `loadComponent` property to the route config.\n       * This function will assign a `component` property to the route snapshot.\n       * We check for the presence of this property to determine if the route is\n       * using standalone components.\n       */\n      const childContexts = this.parentContexts.getOrCreateContext(this.name).children;\n      // We create an activated route proxy object that will maintain future updates for this component\n      // over its lifecycle in the stack.\n      const component$ = new BehaviorSubject(null);\n      const activatedRouteProxy = this.createActivatedRouteProxy(component$, activatedRoute);\n      const injector = new OutletInjector(activatedRouteProxy, childContexts, this.location.injector);\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const component = snapshot.routeConfig.component ?? snapshot.component;\n      /**\n       * View components need to be added as a child of ion-router-outlet\n       * for page transitions and swipe to go back.\n       * However, createComponent mounts components as siblings of the\n       * ViewContainerRef. As a result, outletContent must reference\n       * an ng-container inside of ion-router-outlet and not\n       * ion-router-outlet itself.\n       */\n      cmpRef = this.activated = this.outletContent.createComponent(component, {\n        index: this.outletContent.length,\n        injector,\n        environmentInjector: environmentInjector ?? this.environmentInjector\n      });\n      // Once the component is created we can push it to our local subject supplied to the proxy\n      component$.next(cmpRef.instance);\n      // Calling `markForCheck` to make sure we will run the change detection when the\n      // `RouterOutlet` is inside a `ChangeDetectionStrategy.OnPush` component.\n      /**\n       * At this point this.activated has been set earlier\n       * in this function, so it is guaranteed to be non-null.\n       */\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      enteringView = this.stackCtrl.createView(this.activated, activatedRoute);\n      // Store references to the proxy by component\n      this.proxyMap.set(cmpRef.instance, activatedRouteProxy);\n      this.currentActivatedRoute$.next({\n        component: cmpRef.instance,\n        activatedRoute\n      });\n    }\n    this.inputBinder?.bindActivatedRouteToOutletComponent(this);\n    this.activatedView = enteringView;\n    /**\n     * The top outlet is set prior to the entering view's transition completing,\n     * so that when we have nested outlets (e.g. ion-tabs inside an ion-router-outlet),\n     * the tabs outlet will be assigned as the top outlet when a view inside tabs is\n     * activated.\n     *\n     * In this scenario, activeWith is called for both the tabs and the root router outlet.\n     * To avoid a race condition, we assign the top outlet synchronously.\n     */\n    this.navCtrl.setTopOutlet(this);\n    const leavingView = this.stackCtrl.getActiveView();\n    this.stackWillChange.emit({\n      enteringView,\n      tabSwitch: isTabSwitch(enteringView, leavingView)\n    });\n    this.stackCtrl.setActive(enteringView).then(data => {\n      this.activateEvents.emit(cmpRef.instance);\n      this.stackDidChange.emit(data);\n    });\n  }\n  /**\n   * Returns `true` if there are pages in the stack to go back.\n   */\n  canGoBack(deep = 1, stackId) {\n    return this.stackCtrl.canGoBack(deep, stackId);\n  }\n  /**\n   * Resolves to `true` if it the outlet was able to sucessfully pop the last N pages.\n   */\n  pop(deep = 1, stackId) {\n    return this.stackCtrl.pop(deep, stackId);\n  }\n  /**\n   * Returns the URL of the active page of each stack.\n   */\n  getLastUrl(stackId) {\n    const active = this.stackCtrl.getLastUrl(stackId);\n    return active ? active.url : undefined;\n  }\n  /**\n   * Returns the RouteView of the active page of each stack.\n   * @internal\n   */\n  getLastRouteView(stackId) {\n    return this.stackCtrl.getLastUrl(stackId);\n  }\n  /**\n   * Returns the root view in the tab stack.\n   * @internal\n   */\n  getRootView(stackId) {\n    return this.stackCtrl.getRootUrl(stackId);\n  }\n  /**\n   * Returns the active stack ID. In the context of ion-tabs, it means the active tab.\n   */\n  getActiveStackId() {\n    return this.stackCtrl.getActiveStackId();\n  }\n  /**\n   * Since the activated route can change over the life time of a component in an ion router outlet, we create\n   * a proxy so that we can update the values over time as a user navigates back to components already in the stack.\n   */\n  createActivatedRouteProxy(component$, activatedRoute) {\n    const proxy = new ActivatedRoute();\n    proxy._futureSnapshot = activatedRoute._futureSnapshot;\n    proxy._routerState = activatedRoute._routerState;\n    proxy.snapshot = activatedRoute.snapshot;\n    proxy.outlet = activatedRoute.outlet;\n    proxy.component = activatedRoute.component;\n    // Setup wrappers for the observables so consumers don't have to worry about switching to new observables as the state updates\n    proxy._paramMap = this.proxyObservable(component$, 'paramMap');\n    proxy._queryParamMap = this.proxyObservable(component$, 'queryParamMap');\n    proxy.url = this.proxyObservable(component$, 'url');\n    proxy.params = this.proxyObservable(component$, 'params');\n    proxy.queryParams = this.proxyObservable(component$, 'queryParams');\n    proxy.fragment = this.proxyObservable(component$, 'fragment');\n    proxy.data = this.proxyObservable(component$, 'data');\n    return proxy;\n  }\n  /**\n   * Create a wrapped observable that will switch to the latest activated route matched by the given component\n   */\n  proxyObservable(component$, path) {\n    return component$.pipe(\n    // First wait until the component instance is pushed\n    filter(component => !!component), switchMap(component => this.currentActivatedRoute$.pipe(filter(current => current !== null && current.component === component), switchMap(current => current && current.activatedRoute[path]), distinctUntilChanged())));\n  }\n  /**\n   * Updates the activated route proxy for the given component to the new incoming router state\n   */\n  updateActivatedRouteProxy(component, activatedRoute) {\n    const proxy = this.proxyMap.get(component);\n    if (!proxy) {\n      throw new Error(`Could not find activated route proxy for view`);\n    }\n    proxy._futureSnapshot = activatedRoute._futureSnapshot;\n    proxy._routerState = activatedRoute._routerState;\n    proxy.snapshot = activatedRoute.snapshot;\n    proxy.outlet = activatedRoute.outlet;\n    proxy.component = activatedRoute.component;\n    this.currentActivatedRoute$.next({\n      component,\n      activatedRoute\n    });\n  }\n  /** @nocollapse */\n  static ɵfac = function IonRouterOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || IonRouterOutlet)(i0.ɵɵinjectAttribute('name'), i0.ɵɵinjectAttribute('tabs'), i0.ɵɵdirectiveInject(i1.Location), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(IonRouterOutlet, 12));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonRouterOutlet,\n    selectors: [[\"ion-router-outlet\"]],\n    inputs: {\n      animated: \"animated\",\n      animation: \"animation\",\n      mode: \"mode\",\n      swipeGesture: \"swipeGesture\",\n      name: \"name\"\n    },\n    outputs: {\n      stackWillChange: \"stackWillChange\",\n      stackDidChange: \"stackDidChange\",\n      activateEvents: \"activate\",\n      deactivateEvents: \"deactivate\"\n    },\n    exportAs: [\"outlet\"],\n    standalone: false\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonRouterOutlet, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-router-outlet',\n      exportAs: 'outlet',\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: ['animated', 'animation', 'mode', 'swipeGesture']\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['name']\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Attribute,\n        args: ['tabs']\n      }]\n    }, {\n      type: i1.Location\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.Router\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.ActivatedRoute\n    }, {\n      type: IonRouterOutlet,\n      decorators: [{\n        type: SkipSelf\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    name: [{\n      type: Input\n    }],\n    stackWillChange: [{\n      type: Output\n    }],\n    stackDidChange: [{\n      type: Output\n    }],\n    activateEvents: [{\n      type: Output,\n      args: ['activate']\n    }],\n    deactivateEvents: [{\n      type: Output,\n      args: ['deactivate']\n    }]\n  });\n})();\nclass OutletInjector {\n  route;\n  childContexts;\n  parent;\n  constructor(route, childContexts, parent) {\n    this.route = route;\n    this.childContexts = childContexts;\n    this.parent = parent;\n  }\n  get(token, notFoundValue) {\n    if (token === ActivatedRoute) {\n      return this.route;\n    }\n    if (token === ChildrenOutletContexts) {\n      return this.childContexts;\n    }\n    return this.parent.get(token, notFoundValue);\n  }\n}\n// TODO: FW-4785 - Remove this once Angular 15 support is dropped\nconst INPUT_BINDER = new InjectionToken('');\n/**\n * Injectable used as a tree-shakable provider for opting in to binding router data to component\n * inputs.\n *\n * The RouterOutlet registers itself with this service when an `ActivatedRoute` is attached or\n * activated. When this happens, the service subscribes to the `ActivatedRoute` observables (params,\n * queryParams, data) and sets the inputs of the component using `ComponentRef.setInput`.\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. If it were not done this way, the previous information would be\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n *\n * The `RouterOutlet` should unregister itself when destroyed via `unsubscribeFromRouteData` so that\n * the subscriptions are cleaned up.\n */\nclass RoutedComponentInputBinder {\n  outletDataSubscriptions = new Map();\n  bindActivatedRouteToOutletComponent(outlet) {\n    this.unsubscribeFromRouteData(outlet);\n    this.subscribeToRouteData(outlet);\n  }\n  unsubscribeFromRouteData(outlet) {\n    this.outletDataSubscriptions.get(outlet)?.unsubscribe();\n    this.outletDataSubscriptions.delete(outlet);\n  }\n  subscribeToRouteData(outlet) {\n    const {\n      activatedRoute\n    } = outlet;\n    const dataSubscription = combineLatest([activatedRoute.queryParams, activatedRoute.params, activatedRoute.data]).pipe(switchMap(([queryParams, params, data], index) => {\n      data = {\n        ...queryParams,\n        ...params,\n        ...data\n      };\n      // Get the first result from the data subscription synchronously so it's available to\n      // the component as soon as possible (and doesn't require a second change detection).\n      if (index === 0) {\n        return of(data);\n      }\n      // Promise.resolve is used to avoid synchronously writing the wrong data when\n      // two of the Observables in the `combineLatest` stream emit one after\n      // another.\n      return Promise.resolve(data);\n    })).subscribe(data => {\n      // Outlet may have been deactivated or changed names to be associated with a different\n      // route\n      if (!outlet.isActivated || !outlet.activatedComponentRef || outlet.activatedRoute !== activatedRoute || activatedRoute.component === null) {\n        this.unsubscribeFromRouteData(outlet);\n        return;\n      }\n      const mirror = reflectComponentType(activatedRoute.component);\n      if (!mirror) {\n        this.unsubscribeFromRouteData(outlet);\n        return;\n      }\n      for (const {\n        templateName\n      } of mirror.inputs) {\n        outlet.activatedComponentRef.setInput(templateName, data[templateName]);\n      }\n    });\n    this.outletDataSubscriptions.set(outlet, dataSubscription);\n  }\n  /** @nocollapse */\n  static ɵfac = function RoutedComponentInputBinder_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RoutedComponentInputBinder)();\n  };\n  /** @nocollapse */\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RoutedComponentInputBinder,\n    factory: RoutedComponentInputBinder.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RoutedComponentInputBinder, [{\n    type: Injectable\n  }], null, null);\n})();\nconst provideComponentInputBinding = () => {\n  return {\n    provide: INPUT_BINDER,\n    useFactory: componentInputBindingFactory,\n    deps: [Router]\n  };\n};\nfunction componentInputBindingFactory(router) {\n  /**\n   * We cast the router to any here, since the componentInputBindingEnabled\n   * property is not available until Angular v16.\n   */\n  if (router?.componentInputBindingEnabled) {\n    return new RoutedComponentInputBinder();\n  }\n  return null;\n}\nconst BACK_BUTTON_INPUTS = ['color', 'defaultHref', 'disabled', 'icon', 'mode', 'routerAnimation', 'text', 'type'];\nlet IonBackButton = class IonBackButton {\n  routerOutlet;\n  navCtrl;\n  config;\n  r;\n  z;\n  el;\n  constructor(routerOutlet, navCtrl, config, r, z, c) {\n    this.routerOutlet = routerOutlet;\n    this.navCtrl = navCtrl;\n    this.config = config;\n    this.r = r;\n    this.z = z;\n    c.detach();\n    this.el = this.r.nativeElement;\n  }\n  /**\n   * @internal\n   */\n  onClick(ev) {\n    const defaultHref = this.defaultHref || this.config.get('backButtonDefaultHref');\n    if (this.routerOutlet?.canGoBack()) {\n      this.navCtrl.setDirection('back', undefined, undefined, this.routerAnimation);\n      this.routerOutlet.pop();\n      ev.preventDefault();\n    } else if (defaultHref != null) {\n      this.navCtrl.navigateBack(defaultHref, {\n        animation: this.routerAnimation\n      });\n      ev.preventDefault();\n    }\n  }\n  /** @nocollapse */\n  static ɵfac = function IonBackButton_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || IonBackButton)(i0.ɵɵdirectiveInject(IonRouterOutlet, 8), i0.ɵɵdirectiveInject(NavController), i0.ɵɵdirectiveInject(Config), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonBackButton,\n    hostBindings: function IonBackButton_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function IonBackButton_click_HostBindingHandler($event) {\n          return ctx.onClick($event);\n        });\n      }\n    },\n    inputs: {\n      color: \"color\",\n      defaultHref: \"defaultHref\",\n      disabled: \"disabled\",\n      icon: \"icon\",\n      mode: \"mode\",\n      routerAnimation: \"routerAnimation\",\n      text: \"text\",\n      type: \"type\"\n    },\n    standalone: false\n  });\n};\nIonBackButton = __decorate([ProxyCmp({\n  inputs: BACK_BUTTON_INPUTS\n})], IonBackButton);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonBackButton, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: BACK_BUTTON_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: IonRouterOutlet,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: NavController\n    }, {\n      type: Config\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }]\n  });\n})();\n\n/**\n * Adds support for Ionic routing directions and animations to the base Angular router link directive.\n *\n * When the router link is clicked, the directive will assign the direction and\n * animation so that the routing integration will transition correctly.\n */\nclass RouterLinkDelegateDirective {\n  locationStrategy;\n  navCtrl;\n  elementRef;\n  router;\n  routerLink;\n  routerDirection = 'forward';\n  routerAnimation;\n  constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n    this.locationStrategy = locationStrategy;\n    this.navCtrl = navCtrl;\n    this.elementRef = elementRef;\n    this.router = router;\n    this.routerLink = routerLink;\n  }\n  ngOnInit() {\n    this.updateTargetUrlAndHref();\n    this.updateTabindex();\n  }\n  ngOnChanges() {\n    this.updateTargetUrlAndHref();\n  }\n  /**\n   * The `tabindex` is set to `0` by default on the host element when\n   * the `routerLink` directive is used. This causes issues with Ionic\n   * components that wrap an `a` or `button` element, such as `ion-item`.\n   * See issue https://github.com/angular/angular/issues/28345\n   *\n   * This method removes the `tabindex` attribute from the host element\n   * to allow the Ionic component to manage the focus state correctly.\n   */\n  updateTabindex() {\n    // Ionic components that render a native anchor or button element\n    const ionicComponents = ['ION-BACK-BUTTON', 'ION-BREADCRUMB', 'ION-BUTTON', 'ION-CARD', 'ION-FAB-BUTTON', 'ION-ITEM', 'ION-ITEM-OPTION', 'ION-MENU-BUTTON', 'ION-SEGMENT-BUTTON', 'ION-TAB-BUTTON'];\n    const hostElement = this.elementRef.nativeElement;\n    if (ionicComponents.includes(hostElement.tagName)) {\n      if (hostElement.getAttribute('tabindex') === '0') {\n        hostElement.removeAttribute('tabindex');\n      }\n    }\n  }\n  updateTargetUrlAndHref() {\n    if (this.routerLink?.urlTree) {\n      const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n      this.elementRef.nativeElement.href = href;\n    }\n  }\n  /**\n   * @internal\n   */\n  onClick(ev) {\n    this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n    /**\n     * This prevents the browser from\n     * performing a page reload when pressing\n     * an Ionic component with routerLink.\n     * The page reload interferes with routing\n     * and causes ion-back-button to disappear\n     * since the local history is wiped on reload.\n     */\n    ev.preventDefault();\n  }\n  /** @nocollapse */\n  static ɵfac = function RouterLinkDelegateDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RouterLinkDelegateDirective)(i0.ɵɵdirectiveInject(i1.LocationStrategy), i0.ɵɵdirectiveInject(NavController), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.RouterLink, 8));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterLinkDelegateDirective,\n    selectors: [[\"\", \"routerLink\", \"\", 5, \"a\", 5, \"area\"]],\n    hostBindings: function RouterLinkDelegateDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function RouterLinkDelegateDirective_click_HostBindingHandler($event) {\n          return ctx.onClick($event);\n        });\n      }\n    },\n    inputs: {\n      routerDirection: \"routerDirection\",\n      routerAnimation: \"routerAnimation\"\n    },\n    standalone: false,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterLinkDelegateDirective, [{\n    type: Directive,\n    args: [{\n      selector: ':not(a):not(area)[routerLink]'\n    }]\n  }], function () {\n    return [{\n      type: i1.LocationStrategy\n    }, {\n      type: NavController\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.Router\n    }, {\n      type: i3.RouterLink,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    routerDirection: [{\n      type: Input\n    }],\n    routerAnimation: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }]\n  });\n})();\nclass RouterLinkWithHrefDelegateDirective {\n  locationStrategy;\n  navCtrl;\n  elementRef;\n  router;\n  routerLink;\n  routerDirection = 'forward';\n  routerAnimation;\n  constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n    this.locationStrategy = locationStrategy;\n    this.navCtrl = navCtrl;\n    this.elementRef = elementRef;\n    this.router = router;\n    this.routerLink = routerLink;\n  }\n  ngOnInit() {\n    this.updateTargetUrlAndHref();\n  }\n  ngOnChanges() {\n    this.updateTargetUrlAndHref();\n  }\n  updateTargetUrlAndHref() {\n    if (this.routerLink?.urlTree) {\n      const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n      this.elementRef.nativeElement.href = href;\n    }\n  }\n  /**\n   * @internal\n   */\n  onClick() {\n    this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n  }\n  /** @nocollapse */\n  static ɵfac = function RouterLinkWithHrefDelegateDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RouterLinkWithHrefDelegateDirective)(i0.ɵɵdirectiveInject(i1.LocationStrategy), i0.ɵɵdirectiveInject(NavController), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.RouterLink, 8));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterLinkWithHrefDelegateDirective,\n    selectors: [[\"a\", \"routerLink\", \"\"], [\"area\", \"routerLink\", \"\"]],\n    hostBindings: function RouterLinkWithHrefDelegateDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function RouterLinkWithHrefDelegateDirective_click_HostBindingHandler() {\n          return ctx.onClick();\n        });\n      }\n    },\n    inputs: {\n      routerDirection: \"routerDirection\",\n      routerAnimation: \"routerAnimation\"\n    },\n    standalone: false,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterLinkWithHrefDelegateDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'a[routerLink],area[routerLink]'\n    }]\n  }], function () {\n    return [{\n      type: i1.LocationStrategy\n    }, {\n      type: NavController\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.Router\n    }, {\n      type: i3.RouterLink,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    routerDirection: [{\n      type: Input\n    }],\n    routerAnimation: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click']\n    }]\n  });\n})();\nconst NAV_INPUTS = ['animated', 'animation', 'root', 'rootParams', 'swipeGesture'];\nconst NAV_METHODS = ['push', 'insert', 'insertPages', 'pop', 'popTo', 'popToRoot', 'removeIndex', 'setRoot', 'setPages', 'getActive', 'getByIndex', 'canGoBack', 'getPrevious'];\nlet IonNav = class IonNav {\n  z;\n  el;\n  constructor(ref, environmentInjector, injector, angularDelegate, z, c) {\n    this.z = z;\n    c.detach();\n    this.el = ref.nativeElement;\n    ref.nativeElement.delegate = angularDelegate.create(environmentInjector, injector);\n    proxyOutputs(this, this.el, ['ionNavDidChange', 'ionNavWillChange']);\n  }\n  /** @nocollapse */\n  static ɵfac = function IonNav_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || IonNav)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.EnvironmentInjector), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(AngularDelegate), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonNav,\n    inputs: {\n      animated: \"animated\",\n      animation: \"animation\",\n      root: \"root\",\n      rootParams: \"rootParams\",\n      swipeGesture: \"swipeGesture\"\n    },\n    standalone: false\n  });\n};\nIonNav = __decorate([ProxyCmp({\n  inputs: NAV_INPUTS,\n  methods: NAV_METHODS\n})], IonNav);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonNav, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: NAV_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.EnvironmentInjector\n    }, {\n      type: i0.Injector\n    }, {\n      type: AngularDelegate\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, null);\n})();\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass IonTabs {\n  navCtrl;\n  tabsInner;\n  /**\n   * Emitted before the tab view is changed.\n   */\n  ionTabsWillChange = new EventEmitter();\n  /**\n   * Emitted after the tab view is changed.\n   */\n  ionTabsDidChange = new EventEmitter();\n  tabBarSlot = 'bottom';\n  hasTab = false;\n  selectedTab;\n  leavingTab;\n  constructor(navCtrl) {\n    this.navCtrl = navCtrl;\n  }\n  ngAfterViewInit() {\n    /**\n     * Developers must pass at least one ion-tab\n     * inside of ion-tabs if they want to use a\n     * basic tab-based navigation without the\n     * history stack or URL updates associated\n     * with the router.\n     */\n    const firstTab = this.tabs.length > 0 ? this.tabs.first : undefined;\n    if (firstTab) {\n      this.hasTab = true;\n      this.setActiveTab(firstTab.tab);\n      this.tabSwitch();\n    }\n  }\n  ngAfterContentInit() {\n    this.detectSlotChanges();\n  }\n  ngAfterContentChecked() {\n    this.detectSlotChanges();\n  }\n  /**\n   * @internal\n   */\n  onStackWillChange({\n    enteringView,\n    tabSwitch\n  }) {\n    const stackId = enteringView.stackId;\n    if (tabSwitch && stackId !== undefined) {\n      this.ionTabsWillChange.emit({\n        tab: stackId\n      });\n    }\n  }\n  /**\n   * @internal\n   */\n  onStackDidChange({\n    enteringView,\n    tabSwitch\n  }) {\n    const stackId = enteringView.stackId;\n    if (tabSwitch && stackId !== undefined) {\n      if (this.tabBar) {\n        this.tabBar.selectedTab = stackId;\n      }\n      this.ionTabsDidChange.emit({\n        tab: stackId\n      });\n    }\n  }\n  /**\n   * When a tab button is clicked, there are several scenarios:\n   * 1. If the selected tab is currently active (the tab button has been clicked\n   *    again), then it should go to the root view for that tab.\n   *\n   *   a. Get the saved root view from the router outlet. If the saved root view\n   *      matches the tabRootUrl, set the route view to this view including the\n   *      navigation extras.\n   *   b. If the saved root view from the router outlet does\n   *      not match, navigate to the tabRootUrl. No navigation extras are\n   *      included.\n   *\n   * 2. If the current tab tab is not currently selected, get the last route\n   *    view from the router outlet.\n   *\n   *   a. If the last route view exists, navigate to that view including any\n   *      navigation extras\n   *   b. If the last route view doesn't exist, then navigate\n   *      to the default tabRootUrl\n   */\n  select(tabOrEvent) {\n    const isTabString = typeof tabOrEvent === 'string';\n    const tab = isTabString ? tabOrEvent : tabOrEvent.detail.tab;\n    /**\n     * If the tabs are not using the router, then\n     * the tab switch logic is handled by the tabs\n     * component itself.\n     */\n    if (this.hasTab) {\n      this.setActiveTab(tab);\n      this.tabSwitch();\n      return;\n    }\n    const alreadySelected = this.outlet.getActiveStackId() === tab;\n    const tabRootUrl = `${this.outlet.tabsPrefix}/${tab}`;\n    /**\n     * If this is a nested tab, prevent the event\n     * from bubbling otherwise the outer tabs\n     * will respond to this event too, causing\n     * the app to get directed to the wrong place.\n     */\n    if (!isTabString) {\n      tabOrEvent.stopPropagation();\n    }\n    if (alreadySelected) {\n      const activeStackId = this.outlet.getActiveStackId();\n      const activeView = this.outlet.getLastRouteView(activeStackId);\n      // If on root tab, do not navigate to root tab again\n      if (activeView?.url === tabRootUrl) {\n        return;\n      }\n      const rootView = this.outlet.getRootView(tab);\n      const navigationExtras = rootView && tabRootUrl === rootView.url && rootView.savedExtras;\n      return this.navCtrl.navigateRoot(tabRootUrl, {\n        ...navigationExtras,\n        animated: true,\n        animationDirection: 'back'\n      });\n    } else {\n      const lastRoute = this.outlet.getLastRouteView(tab);\n      /**\n       * If there is a lastRoute, goto that, otherwise goto the fallback url of the\n       * selected tab\n       */\n      const url = lastRoute?.url || tabRootUrl;\n      const navigationExtras = lastRoute?.savedExtras;\n      return this.navCtrl.navigateRoot(url, {\n        ...navigationExtras,\n        animated: true,\n        animationDirection: 'back'\n      });\n    }\n  }\n  setActiveTab(tab) {\n    const tabs = this.tabs;\n    const selectedTab = tabs.find(t => t.tab === tab);\n    if (!selectedTab) {\n      console.error(`[Ionic Error]: Tab with id: \"${tab}\" does not exist`);\n      return;\n    }\n    this.leavingTab = this.selectedTab;\n    this.selectedTab = selectedTab;\n    this.ionTabsWillChange.emit({\n      tab\n    });\n    selectedTab.el.active = true;\n  }\n  tabSwitch() {\n    const {\n      selectedTab,\n      leavingTab\n    } = this;\n    if (this.tabBar && selectedTab) {\n      this.tabBar.selectedTab = selectedTab.tab;\n    }\n    if (leavingTab?.tab !== selectedTab?.tab) {\n      if (leavingTab?.el) {\n        leavingTab.el.active = false;\n      }\n    }\n    if (selectedTab) {\n      this.ionTabsDidChange.emit({\n        tab: selectedTab.tab\n      });\n    }\n  }\n  getSelected() {\n    if (this.hasTab) {\n      return this.selectedTab?.tab;\n    }\n    return this.outlet.getActiveStackId();\n  }\n  /**\n   * Detects changes to the slot attribute of the tab bar.\n   *\n   * If the slot attribute has changed, then the tab bar\n   * should be relocated to the new slot position.\n   */\n  detectSlotChanges() {\n    this.tabBars.forEach(tabBar => {\n      // el is a protected attribute from the generated component wrapper\n      const currentSlot = tabBar.el.getAttribute('slot');\n      if (currentSlot !== this.tabBarSlot) {\n        this.tabBarSlot = currentSlot;\n        this.relocateTabBar();\n      }\n    });\n  }\n  /**\n   * Relocates the tab bar to the new slot position.\n   */\n  relocateTabBar() {\n    /**\n     * `el` is a protected attribute from the generated component wrapper.\n     * To avoid having to manually create the wrapper for tab bar, we\n     * cast the tab bar to any and access the protected attribute.\n     */\n    const tabBar = this.tabBar.el;\n    if (this.tabBarSlot === 'top') {\n      /**\n       * A tab bar with a slot of \"top\" should be inserted\n       * at the top of the container.\n       */\n      this.tabsInner.nativeElement.before(tabBar);\n    } else {\n      /**\n       * A tab bar with a slot of \"bottom\" or without a slot\n       * should be inserted at the end of the container.\n       */\n      this.tabsInner.nativeElement.after(tabBar);\n    }\n  }\n  /** @nocollapse */\n  static ɵfac = function IonTabs_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || IonTabs)(i0.ɵɵdirectiveInject(NavController));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonTabs,\n    selectors: [[\"ion-tabs\"]],\n    viewQuery: function IonTabs_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7, ElementRef);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabsInner = _t.first);\n      }\n    },\n    hostBindings: function IonTabs_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"ionTabButtonClick\", function IonTabs_ionTabButtonClick_HostBindingHandler($event) {\n          return ctx.select($event);\n        });\n      }\n    },\n    outputs: {\n      ionTabsWillChange: \"ionTabsWillChange\",\n      ionTabsDidChange: \"ionTabsDidChange\"\n    },\n    standalone: false\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonTabs, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-tabs'\n    }]\n  }], function () {\n    return [{\n      type: NavController\n    }];\n  }, {\n    tabsInner: [{\n      type: ViewChild,\n      args: ['tabsInner', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    ionTabsWillChange: [{\n      type: Output\n    }],\n    ionTabsDidChange: [{\n      type: Output\n    }],\n    select: [{\n      type: HostListener,\n      args: ['ionTabButtonClick', ['$event']]\n    }]\n  });\n})();\nconst raf = h => {\n  if (typeof __zone_symbol__requestAnimationFrame === 'function') {\n    return __zone_symbol__requestAnimationFrame(h);\n  }\n  if (typeof requestAnimationFrame === 'function') {\n    return requestAnimationFrame(h);\n  }\n  return setTimeout(h);\n};\n\n// TODO(FW-2827): types\nclass ValueAccessor {\n  injector;\n  elementRef;\n  onChange = () => {\n    /**/\n  };\n  onTouched = () => {\n    /**/\n  };\n  lastValue;\n  statusChanges;\n  constructor(injector, elementRef) {\n    this.injector = injector;\n    this.elementRef = elementRef;\n  }\n  writeValue(value) {\n    this.elementRef.nativeElement.value = this.lastValue = value;\n    setIonicClasses(this.elementRef);\n  }\n  /**\n   * Notifies the ControlValueAccessor of a change in the value of the control.\n   *\n   * This is called by each of the ValueAccessor directives when we want to update\n   * the status and validity of the form control. For example with text components this\n   * is called when the ionInput event is fired. For select components this is called\n   * when the ionChange event is fired.\n   *\n   * This also updates the Ionic form status classes on the element.\n   *\n   * @param el The component element.\n   * @param value The new value of the control.\n   */\n  handleValueChange(el, value) {\n    if (el === this.elementRef.nativeElement) {\n      if (value !== this.lastValue) {\n        this.lastValue = value;\n        this.onChange(value);\n      }\n      setIonicClasses(this.elementRef);\n    }\n  }\n  _handleBlurEvent(el) {\n    if (el === this.elementRef.nativeElement) {\n      this.onTouched();\n      setIonicClasses(this.elementRef);\n      // When ion-radio is blurred, el and this.elementRef.nativeElement are\n      // different so we need to check if the closest ion-radio-group is the same\n      // as this.elementRef.nativeElement and if so, we need to mark the radio group\n      // as touched\n    } else if (el.closest('ion-radio-group') === this.elementRef.nativeElement) {\n      this.onTouched();\n    }\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.elementRef.nativeElement.disabled = isDisabled;\n  }\n  ngOnDestroy() {\n    if (this.statusChanges) {\n      this.statusChanges.unsubscribe();\n    }\n  }\n  ngAfterViewInit() {\n    let ngControl;\n    try {\n      ngControl = this.injector.get(NgControl);\n    } catch {\n      /* No FormControl or ngModel binding */\n    }\n    if (!ngControl) {\n      return;\n    }\n    // Listen for changes in validity, disabled, or pending states\n    if (ngControl.statusChanges) {\n      this.statusChanges = ngControl.statusChanges.subscribe(() => setIonicClasses(this.elementRef));\n    }\n    /**\n     * TODO FW-2787: Remove this in favor of https://github.com/angular/angular/issues/10887\n     * whenever it is implemented.\n     */\n    const formControl = ngControl.control;\n    if (formControl) {\n      const methodsToPatch = ['markAsTouched', 'markAllAsTouched', 'markAsUntouched', 'markAsDirty', 'markAsPristine'];\n      methodsToPatch.forEach(method => {\n        if (typeof formControl[method] !== 'undefined') {\n          const oldFn = formControl[method].bind(formControl);\n          formControl[method] = (...params) => {\n            oldFn(...params);\n            setIonicClasses(this.elementRef);\n          };\n        }\n      });\n    }\n  }\n  /** @nocollapse */\n  static ɵfac = function ValueAccessor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ValueAccessor)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ValueAccessor,\n    hostBindings: function ValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"ionBlur\", function ValueAccessor_ionBlur_HostBindingHandler($event) {\n          return ctx._handleBlurEvent($event.target);\n        });\n      }\n    },\n    standalone: false\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ValueAccessor, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.Injector\n    }, {\n      type: i0.ElementRef\n    }];\n  }, {\n    _handleBlurEvent: [{\n      type: HostListener,\n      args: ['ionBlur', ['$event.target']]\n    }]\n  });\n})();\nconst setIonicClasses = element => {\n  raf(() => {\n    const input = element.nativeElement;\n    const hasValue = input.value != null && input.value.toString().length > 0;\n    const classes = getClasses(input);\n    setClasses(input, classes);\n    const item = input.closest('ion-item');\n    if (item) {\n      if (hasValue) {\n        setClasses(item, [...classes, 'item-has-value']);\n      } else {\n        setClasses(item, classes);\n      }\n    }\n  });\n};\nconst getClasses = element => {\n  const classList = element.classList;\n  const classes = [];\n  for (let i = 0; i < classList.length; i++) {\n    const item = classList.item(i);\n    if (item !== null && startsWith(item, 'ng-')) {\n      classes.push(`ion-${item.substring(3)}`);\n    }\n  }\n  return classes;\n};\nconst setClasses = (element, classes) => {\n  const classList = element.classList;\n  classList.remove('ion-valid', 'ion-invalid', 'ion-touched', 'ion-untouched', 'ion-dirty', 'ion-pristine');\n  classList.add(...classes);\n};\nconst startsWith = (input, search) => {\n  return input.substring(0, search.length) === search;\n};\n\n/**\n * Provides a way to customize when activated routes get reused.\n */\nclass IonicRouteStrategy {\n  /**\n   * Whether the given route should detach for later reuse.\n   */\n  shouldDetach(_route) {\n    return false;\n  }\n  /**\n   * Returns `false`, meaning the route (and its subtree) is never reattached\n   */\n  shouldAttach(_route) {\n    return false;\n  }\n  /**\n   * A no-op; the route is never stored since this strategy never detaches routes for later re-use.\n   */\n  store(_route, _detachedTree) {\n    return;\n  }\n  /**\n   * Returns `null` because this strategy does not store routes for later re-use.\n   */\n  retrieve(_route) {\n    return null;\n  }\n  /**\n   * Determines if a route should be reused.\n   * This strategy returns `true` when the future route config and\n   * current route config are identical and all route parameters are identical.\n   */\n  shouldReuseRoute(future, curr) {\n    if (future.routeConfig !== curr.routeConfig) {\n      return false;\n    }\n    // checking router params\n    const futureParams = future.params;\n    const currentParams = curr.params;\n    const keysA = Object.keys(futureParams);\n    const keysB = Object.keys(currentParams);\n    if (keysA.length !== keysB.length) {\n      return false;\n    }\n    // Test for A's keys different from B.\n    for (const key of keysA) {\n      if (currentParams[key] !== futureParams[key]) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n\n// TODO(FW-2827): types\nclass OverlayBaseController {\n  ctrl;\n  constructor(ctrl) {\n    this.ctrl = ctrl;\n  }\n  /**\n   * Creates a new overlay\n   */\n  create(opts) {\n    return this.ctrl.create(opts || {});\n  }\n  /**\n   * When `id` is not provided, it dismisses the top overlay.\n   */\n  dismiss(data, role, id) {\n    return this.ctrl.dismiss(data, role, id);\n  }\n  /**\n   * Returns the top overlay.\n   */\n  getTop() {\n    return this.ctrl.getTop();\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularDelegate, Config, ConfigToken, DomController, IonBackButton, IonModal, IonNav, IonPopover, IonRouterOutlet, IonTabs, IonicRouteStrategy, MenuController, NavController, NavParams, OverlayBaseController, Platform, ProxyCmp, RouterLinkDelegateDirective, RouterLinkWithHrefDelegateDirective, ValueAccessor, bindLifecycleEvents, provideComponentInputBinding, raf, setIonicClasses };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAM,0BAA0B,CAAC,IAAI,IAAI,IAAI,IAAI,gBAAgB;AAC/D,SAAO,iBAAiB,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,WAAW,EAAE,IAAI,YAAU;AAC7E,WAAO,6BAA6B,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM;AAAA,EACxE,CAAC;AACH;AAIA,IAAM,+BAA+B,CAAC,IAAI,IAAI,IAAI,IAAI,MAAM;AAC1D,QAAM,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC;AACxC,QAAM,QAAQ,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK;AAC1C,QAAM,QAAQ,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC;AACpC,SAAO,KAAK,QAAQ,IAAI,SAAS;AACnC;AAIA,IAAM,mBAAmB,CAAC,IAAI,IAAI,IAAI,IAAI,aAAa;AACrD,QAAM;AACN,QAAM;AACN,QAAM;AACN,QAAM;AACN,QAAM,QAAQ,mBAAmB,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;AACzG,SAAO,MAAM,OAAO,UAAQ,QAAQ,KAAK,QAAQ,CAAC;AACpD;AACA,IAAM,yBAAyB,CAAC,GAAG,GAAG,MAAM;AAC1C,QAAM,eAAe,IAAI,IAAI,IAAI,IAAI;AACrC,MAAI,eAAe,GAAG;AACpB,WAAO,CAAC;AAAA,EACV,OAAO;AACL,WAAO,EAAE,CAAC,IAAI,KAAK,KAAK,YAAY,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,YAAY,MAAM,IAAI,EAAE;AAAA,EAC5F;AACF;AACA,IAAM,qBAAqB,CAAC,GAAG,GAAG,GAAG,MAAM;AACzC,MAAI,MAAM,GAAG;AACX,WAAO,uBAAuB,GAAG,GAAG,CAAC;AAAA,EACvC;AACA,OAAK;AACL,OAAK;AACL,OAAK;AACL,QAAM,KAAK,IAAI,IAAI,IAAI,KAAK;AAC5B,QAAM,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK;AACjD,MAAI,MAAM,GAAG;AACX,WAAO,CAAC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,EAC7B,WAAW,MAAM,GAAG;AAClB,WAAO,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,EACvC;AACA,QAAM,eAAe,KAAK,IAAI,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC;AAC3D,MAAI,iBAAiB,GAAG;AACtB,WAAO,CAAC,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC;AAAA,EACxC,WAAW,eAAe,GAAG;AAC3B,WAAO,CAAC,KAAK,IAAI,EAAE,IAAI,KAAK,KAAK,KAAK,YAAY,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,YAAY,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC;AAAA,EACxH;AACA,QAAM,IAAI,KAAK,KAAK,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC;AACzC,QAAM,MAAM,KAAK,KAAK,EAAE,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC,GAAG;AACnE,QAAM,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC;AAC/B,SAAO,CAAC,IAAI,KAAK,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC;AACrI;;;ACzEA,IAAM,eAAe,SAAO,eAAe,GAAG;AAC9C,IAAM,aAAa,CAAC,eAAe,aAAa;AAC9C,MAAI,OAAO,kBAAkB,UAAU;AACrC,eAAW;AACX,oBAAgB;AAAA,EAClB;AACA,SAAO,aAAa,aAAa,EAAE,SAAS,QAAQ;AACtD;AACA,IAAM,iBAAiB,CAAC,MAAM,WAAW;AACvC,MAAI,OAAO,QAAQ,aAAa;AAC9B,WAAO,CAAC;AAAA,EACV;AACA,MAAI,QAAQ,IAAI,SAAS,CAAC;AAC1B,MAAI,YAAY,IAAI,MAAM;AAC1B,MAAI,aAAa,MAAM;AACrB,gBAAY,IAAI,MAAM,YAAY,gBAAgB,GAAG;AACrD,cAAU,QAAQ,OAAK,IAAI,SAAS,gBAAgB,UAAU,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,EAC/E;AACA,SAAO;AACT;AACA,IAAM,kBAAkB,SAAO;AAC7B,QAAM,wBAAwB,OAAO,IAAI,UAAU;AACnD,SAAO,OAAO,KAAK,aAAa,EAAE,OAAO,OAAK;AAC5C,UAAM,eAAe,0BAA0B,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,CAAC;AAC1H,WAAO,OAAO,iBAAiB,aAAa,aAAa,GAAG,IAAI,cAAc,CAAC,EAAE,GAAG;AAAA,EACtF,CAAC;AACH;AACA,IAAM,cAAc,SAAO,SAAS,GAAG,KAAK,CAAC,SAAS,GAAG;AACzD,IAAM,SAAS,SAAO;AAEpB,MAAI,cAAc,KAAK,OAAO,GAAG;AAC/B,WAAO;AAAA,EACT;AAEA,MAAI,cAAc,KAAK,YAAY,KAAK,SAAS,GAAG,GAAG;AACrD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,WAAW,SAAO,cAAc,KAAK,SAAS;AACpD,IAAM,QAAQ,SAAO,cAAc,KAAK,cAAc,KAAK,OAAO,GAAG;AACrE,IAAM,YAAY,SAAO,cAAc,KAAK,eAAe;AAC3D,IAAM,kBAAkB,SAAO;AAC7B,SAAO,UAAU,GAAG,KAAK,CAAC,cAAc,KAAK,SAAS;AACxD;AACA,IAAM,YAAY,SAAO;AACvB,QAAM,QAAQ,IAAI;AAClB,QAAM,SAAS,IAAI;AACnB,QAAM,WAAW,KAAK,IAAI,OAAO,MAAM;AACvC,QAAM,UAAU,KAAK,IAAI,OAAO,MAAM;AACtC,SAAO,WAAW,OAAO,WAAW,OAAO,UAAU,OAAO,UAAU;AACxE;AACA,IAAM,WAAW,SAAO;AACtB,QAAM,QAAQ,IAAI;AAClB,QAAM,SAAS,IAAI;AACnB,QAAM,WAAW,KAAK,IAAI,OAAO,MAAM;AACvC,QAAM,UAAU,KAAK,IAAI,OAAO,MAAM;AACtC,SAAO,OAAO,GAAG,KAAK,gBAAgB,GAAG,KAAK,WAAW,OAAO,WAAW,OAAO,UAAU,OAAO,UAAU;AAC/G;AACA,IAAM,WAAW,SAAO,WAAW,KAAK,sBAAsB;AAC9D,IAAM,YAAY,SAAO,CAAC,SAAS,GAAG;AACtC,IAAM,WAAW,SAAO,UAAU,GAAG,KAAK,kBAAkB,GAAG;AAC/D,IAAM,YAAY,SAAO,CAAC,EAAE,IAAI,SAAS,KAAK,IAAI,UAAU,KAAK,IAAI,UAAU;AAC/E,IAAM,oBAAoB,SAAO;AAC/B,QAAM,YAAY,IAAI,WAAW;AAEjC,SAAO,CAAC,GAAG,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,cAAc,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,qBAAqB,CAAC,CAAC,UAAU,iBAAiB;AAC7M;AACA,IAAM,aAAa,SAAO,cAAc,KAAK,WAAW;AACxD,IAAM,QAAQ,SAAO;AACnB,MAAIA;AACJ,SAAO,CAAC,IAAIA,MAAK,IAAI,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,KAAK,4BAA4B,EAAE,YAAY,IAAI,UAAU;AAC7I;AACA,IAAM,gBAAgB,CAAC,KAAK,SAAS,KAAK,KAAK,IAAI,UAAU,SAAS;AACtE,IAAM,aAAa,CAAC,KAAK,UAAU;AACjC,MAAIA;AACJ,UAAQA,MAAK,IAAI,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,KAAK,KAAK,EAAE;AACxF;AACA,IAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AAAA,EACV,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,SAAS;AAAA,EACT,QAAQ;AACV;AAGA,IAAI;AACJ,IAAM,aAAa,SAAO;AACxB,SAAO,OAAO,QAAQ,GAAG,KAAK;AAChC;AACA,IAAM,aAAa,CAAC,aAAa,CAAC,MAAM;AACtC,MAAI,OAAO,WAAW,aAAa;AACjC;AAAA,EACF;AACA,QAAMC,OAAM,OAAO;AACnB,QAAM,MAAM;AACZ,QAAM,QAAQ,IAAI,QAAQ,IAAI,SAAS,CAAC;AAGxC,QAAM,YAAY,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG;AAAA,IACnH,eAAe;AAAA,EACjB,CAAC,GAAG,MAAM,MAAM,GAAG,cAAc,GAAG,CAAC,GAAG,UAAU;AAClD,SAAO,MAAM,SAAS;AACtB,MAAI,OAAO,WAAW,eAAe,GAAG;AACtC,eAAW,KAAK,SAAS;AAAA,EAC3B;AAEA,iBAAe,GAAG;AAIlB,QAAM,SAAS;AACf,QAAM,OAAO,cAAc,OAAO,IAAI,QAAQA,KAAI,gBAAgB,aAAa,MAAM,MAAM,WAAW,KAAK,KAAK,IAAI,QAAQ,KAAK;AACjI,SAAO,IAAI,QAAQ,WAAW;AAC9B,EAAAA,KAAI,gBAAgB,aAAa,QAAQ,WAAW;AACpD,EAAAA,KAAI,gBAAgB,UAAU,IAAI,WAAW;AAC7C,MAAI,OAAO,WAAW,UAAU,GAAG;AACjC,WAAO,IAAI,YAAY,KAAK;AAAA,EAC9B;AACA,QAAM,iBAAiB,SAAO;AAC5B,QAAID;AACJ,YAAQA,MAAK,IAAI,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,WAAW,MAAM;AAAA,EACrF;AACA,QAAM,0BAA0B,aAAW,CAAC,OAAO,IAAI,EAAE,SAAS,OAAO;AACzE,UAAQ,SAAO;AACb,WAAO,KAAK;AACV,YAAM,UAAU,IAAI,QAAQ,IAAI,aAAa,MAAM;AACnD,UAAI,SAAS;AACX,YAAI,wBAAwB,OAAO,GAAG;AACpC,iBAAO;AAAA,QACT,WAAW,eAAe,GAAG,GAAG;AAC9B,0BAAgB,0BAA0B,UAAU,4BAA4B;AAAA,QAClF;AAAA,MACF;AACA,YAAM,IAAI;AAAA,IACZ;AACA,WAAO;AAAA,EACT,CAAC;AACH;;;AChJA,IAAM,oBAAoB,qBAAmB;AAC3C,MAAI;AACF,QAAI,2BAA2B,iBAAiB;AAC9C,aAAO,gBAAgB;AAAA,IACzB;AACA,QAAI,CAAC,mBAAmB,KAAK,OAAO,oBAAoB,YAAY,oBAAoB,IAAI;AAC1F,aAAO;AAAA,IACT;AAOA,QAAI,gBAAgB,SAAS,SAAS,GAAG;AACvC,aAAO;AAAA,IACT;AAMA,UAAM,mBAAmB,SAAS,uBAAuB;AACzD,UAAM,aAAa,SAAS,cAAc,KAAK;AAC/C,qBAAiB,YAAY,UAAU;AACvC,eAAW,YAAY;AAKvB,gBAAY,QAAQ,gBAAc;AAChC,YAAM,sBAAsB,iBAAiB,iBAAiB,UAAU;AACxE,eAAS,eAAe,oBAAoB,SAAS,GAAG,gBAAgB,GAAG,gBAAgB;AACzF,cAAM,UAAU,oBAAoB,YAAY;AAChD,YAAI,QAAQ,YAAY;AACtB,kBAAQ,WAAW,YAAY,OAAO;AAAA,QACxC,OAAO;AACL,2BAAiB,YAAY,OAAO;AAAA,QACtC;AAMA,cAAM,gBAAgB,mBAAmB,OAAO;AAEhD,iBAAS,aAAa,GAAG,aAAa,cAAc,QAAQ,cAAc;AACxE,0BAAgB,cAAc,UAAU,CAAC;AAAA,QAC3C;AAAA,MACF;AAAA,IACF,CAAC;AAMD,UAAM,aAAa,mBAAmB,gBAAgB;AAEtD,aAAS,aAAa,GAAG,aAAa,WAAW,QAAQ,cAAc;AACrE,sBAAgB,WAAW,UAAU,CAAC;AAAA,IACxC;AAEA,UAAM,cAAc,SAAS,cAAc,KAAK;AAChD,gBAAY,YAAY,gBAAgB;AAExC,UAAM,cAAc,YAAY,cAAc,KAAK;AACnD,WAAO,gBAAgB,OAAO,YAAY,YAAY,YAAY;AAAA,EACpE,SAAS,KAAK;AACZ,kBAAc,qBAAqB,GAAG;AACtC,WAAO;AAAA,EACT;AACF;AAOA,IAAM,kBAAkB,aAAW;AAEjC,MAAI,QAAQ,YAAY,QAAQ,aAAa,GAAG;AAC9C;AAAA,EACF;AAOA,MAAI,OAAO,iBAAiB,eAAe,EAAE,QAAQ,sBAAsB,eAAe;AACxF,YAAQ,OAAO;AACf;AAAA,EACF;AACA,WAAS,IAAI,QAAQ,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AACvD,UAAM,YAAY,QAAQ,WAAW,KAAK,CAAC;AAC3C,UAAM,gBAAgB,UAAU;AAEhC,QAAI,CAAC,kBAAkB,SAAS,cAAc,YAAY,CAAC,GAAG;AAC5D,cAAQ,gBAAgB,aAAa;AACrC;AAAA,IACF;AAGA,UAAM,iBAAiB,UAAU;AAMjC,UAAM,gBAAgB,QAAQ,aAAa;AAE3C,QAAI,kBAAkB,QAAQ,eAAe,YAAY,EAAE,SAAS,aAAa,KAAK,iBAAiB,QAAQ,cAAc,YAAY,EAAE,SAAS,aAAa,GAAG;AAClK,cAAQ,gBAAgB,aAAa;AAAA,IACvC;AAAA,EAEF;AAIA,QAAM,gBAAgB,mBAAmB,OAAO;AAEhD,WAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,oBAAgB,cAAc,CAAC,CAAC;AAAA,EAClC;AACF;AAMA,IAAM,qBAAqB,QAAM;AAC/B,SAAO,GAAG,YAAY,OAAO,GAAG,WAAW,GAAG;AAChD;AACA,IAAM,qBAAqB,MAAM;AAC/B,MAAIE;AACJ,QAAM,MAAM;AACZ,QAAMC,WAAUD,MAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,WAAW,QAAQA,QAAO,SAAS,SAASA,IAAG;AAClH,MAAIC,SAAQ;AACV,QAAIA,QAAO,KAAK;AACd,aAAOA,QAAO,IAAI,oBAAoB,IAAI;AAAA,IAC5C,OAAO;AACL,aAAOA,QAAO,qBAAqB,QAAQA,QAAO,qBAAqB;AAAA,IACzE;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,oBAAoB,CAAC,SAAS,MAAM,QAAQ,OAAO,QAAQ,MAAM;AACvE,IAAM,cAAc,CAAC,UAAU,SAAS,UAAU,QAAQ,QAAQ,UAAU,OAAO;AACnF,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,OAAO;AACjB,SAAK,QAAQ;AAAA,EACf;AACF;AAyBA,IAAM,8BAA8B;;;AC1KpC,IAAM,gBAAgB,WAAS;AAQ7B,SAAO,gBAAgB,EAAE,SAAS,QAAQ,MAAM,GAAG;AACrD;AAOA,IAAM,uBAAuB,UAAQ;AACnC,MAAI;AACJ,MAAI;AACJ,QAAM,QAAQ,KAAK,QAAQ;AAC3B,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,MAAI,KAAK,WAAW;AAElB,cAAU,QAAQ;AAClB,cAAU;AAAA,EACZ,OAAO;AAEL,cAAU,CAAC,QAAQ;AACnB,cAAU;AAAA,EACZ;AACA,gBAAc,WAAW,KAAK,WAAW,EAAE,OAAO,aAAa,cAAc,OAAO,KAAK,cAAc,OAAO,GAAG;AACjH,QAAM,OAAO,WAAW,IAAI;AAC5B,QAAM,QAAQ,SAAS;AACvB,QAAM,UAAU,QAAQ,MAAM;AAC9B,oBAAkB,WAAW,KAAK,UAAU,EAAE,OAAO,WAAW,MAAM,OAAO;AAC7E,SAAO,cAAc,KAAK,EAAE,aAAa,CAAC,eAAe,iBAAiB,CAAC;AAC7E;AAOA,IAAM,oBAAoB,UAAQ;AAChC,MAAI;AACJ,MAAI;AACJ,QAAM,OAAO,WAAW,IAAI;AAC5B,QAAM,QAAQ,KAAK;AACnB,MAAI,KAAK,WAAW;AAClB,qBAAiB,CAAC,QAAQ;AAC1B,kBAAc,QAAQ;AAAA,EACxB,OAAO;AACL,qBAAiB,QAAQ;AACzB,kBAAc,CAAC,QAAQ;AAAA,EACzB;AACA,QAAM,gBAAgB,gBAAgB,EAAE,WAAW,KAAK,WAAW,EAAE,OAAO,aAAa,cAAc,WAAW,KAAK,iBAAiB;AACxI,QAAM,mBAAmB,gBAAgB,EAAE,WAAW,KAAK,SAAS,EAAE,OAAO,aAAa,mBAAmB,cAAc,cAAc,GAAG;AAC5I,QAAM,oBAAoB,gBAAgB,EAAE,WAAW,KAAK,UAAU,EAAE,OAAO,WAAW,MAAM,IAAI;AACpG,SAAO,cAAc,SAAS,KAAK,EAAE,aAAa,CAAC,eAAe,kBAAkB,iBAAiB,CAAC;AACxG;AAOA,IAAM,sBAAsB,UAAQ;AAClC,QAAM,OAAO,WAAW,IAAI;AAC5B,QAAM,UAAU,KAAK,SAAS,KAAK,YAAY,KAAK,KAAK;AACzD,QAAM,cAAc,gBAAgB,EAAE,WAAW,KAAK,SAAS,EAC9D,OAAO,aAAa,mBAAmB,cAAc,OAAO,GAAG;AAChE,SAAO,cAAc,SAAS,KAAK,EAAE,aAAa,WAAW;AAC/D;AACA,IAAM,uBAAuB,MAAM;AACjC,QAAM,iBAAiB,oBAAI,IAAI;AAC/B,QAAM,QAAQ,CAAC;AACf,QAAM,OAAO,CAAM,SAAQ;AACzB,UAAM,SAAS,MAAM,IAAI,MAAM,IAAI;AACnC,QAAI,QAAQ;AACV,aAAO,OAAO,KAAK;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,CAAM,SAAQ;AAC1B,UAAM,SAAS,MAAO,SAAS,SAAY,IAAI,MAAM,IAAI,IAAI,QAAQ;AACrE,QAAI,WAAW,QAAW;AACxB,aAAO,OAAO,MAAM;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAM,SAAQ;AAC3B,UAAM,SAAS,MAAM,IAAI,MAAM,IAAI;AACnC,QAAI,QAAQ;AACV,aAAO,OAAO,OAAO;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAO,cAAc,SAAS;AAC3C,UAAM,SAAS,MAAM,IAAI,IAAI;AAC7B,QAAI,QAAQ;AACV,aAAO,WAAW,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AACA,QAAM,eAAe,CAAO,cAAc,SAAS;AACjD,UAAM,SAAS,MAAM,IAAI,IAAI;AAC7B,QAAI,QAAQ;AACV,aAAO,eAAe;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAM,SAAQ;AAC3B,QAAI,QAAQ,MAAM;AAChB,YAAM,SAAS,MAAM,IAAI,IAAI;AAE7B,aAAO,WAAW,UAAa,OAAO,OAAO;AAAA,IAC/C,OAAO;AACL,YAAM,SAAS,MAAM,QAAQ;AAC7B,aAAO,WAAW;AAAA,IACpB;AAAA,EACF;AACA,QAAM,YAAY,CAAM,SAAQ;AAC9B,UAAM,SAAS,MAAM,IAAI,IAAI;AAC7B,QAAI,QAAQ;AACV,aAAO,CAAC,OAAO;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AASA,QAAM,MAAM,CAAO,MAAM,yBAAyB,UAAU;AAC1D,UAAM,eAAe;AACrB,QAAI,SAAS,WAAW,SAAS,OAAO;AAGtC,YAAM,WAAW,MAAM,OAAO,OAAK,EAAE,SAAS,QAAQ,CAAC,EAAE,QAAQ;AACjE,UAAI,SAAS,UAAU,GAAG;AACxB,YAAI,SAAS,SAAS,KAAK,wBAAwB;AACjD,0BAAgB,6CAA6C,IAAI,eAAe,SAAS,MAAM,oJAAoJ,SAAS,IAAI,OAAK,EAAE,EAAE,CAAC;AAAA,QAC5Q;AACA,eAAO,SAAS,CAAC,EAAE;AAAA,MACrB;AAGA,YAAM,eAAe,MAAM,OAAO,OAAK,EAAE,SAAS,IAAI;AACtD,UAAI,aAAa,UAAU,GAAG;AAC5B,YAAI,aAAa,SAAS,KAAK,wBAAwB;AACrD,0BAAgB,6CAA6C,IAAI,eAAe,aAAa,MAAM,oJAAoJ,aAAa,IAAI,OAAK,EAAE,EAAE,CAAC;AAAA,QACpR;AACA,eAAO,aAAa,CAAC,EAAE;AAAA,MACzB;AAAA,IACF,WAAW,QAAQ,MAAM;AAGvB,aAAO,KAAK,OAAK,EAAE,WAAW,IAAI;AAAA,IACpC;AAEA,UAAM,SAAS,KAAK,OAAK,CAAC,EAAE,QAAQ;AACpC,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AAEA,WAAO,MAAM,SAAS,IAAI,MAAM,CAAC,EAAE,KAAK;AAAA,EAC1C;AAIA,QAAM,UAAU,MAAY;AAC1B,UAAM,eAAe;AACrB,WAAO,aAAa;AAAA,EACtB;AAIA,QAAM,WAAW,MAAY;AAC3B,UAAM,eAAe;AACrB,WAAO,aAAa;AAAA,EACtB;AAKA,QAAM,cAAc,MAAY;AAC9B,UAAM,eAAe;AACrB,WAAO,gBAAgB;AAAA,EACzB;AACA,QAAM,oBAAoB,CAAC,MAAM,cAAc;AAC7C,mBAAe,IAAI,MAAM,SAAS;AAAA,EACpC;AACA,QAAM,YAAY,UAAQ;AACxB,QAAI,MAAM,QAAQ,IAAI,IAAI,GAAG;AAC3B,YAAM,KAAK,IAAI;AAAA,IACjB;AAAA,EACF;AACA,QAAM,cAAc,UAAQ;AAC1B,UAAM,QAAQ,MAAM,QAAQ,IAAI;AAChC,QAAI,QAAQ,IAAI;AACd,YAAM,OAAO,OAAO,CAAC;AAAA,IACvB;AAAA,EACF;AACA,QAAM,WAAW,CAAO,MAAM,YAAY,UAAU,SAAS;AAC3D,QAAI,gBAAgB,GAAG;AACrB,aAAO;AAAA,IACT;AACA,QAAI,YAAY;AACd,YAAM,aAAa,MAAM,QAAQ;AACjC,UAAI,cAAc,KAAK,OAAO,YAAY;AACxC,cAAM,WAAW,QAAQ,OAAO,KAAK;AAAA,MACvC;AAAA,IACF;AACA,WAAO,KAAK,SAAS,YAAY,UAAU,IAAI;AAAA,EACjD;AACA,QAAM,mBAAmB,CAAC,MAAM,YAAY;AAC1C,UAAM,mBAAmB,eAAe,IAAI,IAAI;AAChD,QAAI,CAAC,kBAAkB;AACrB,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC5C;AACA,UAAM,YAAY,iBAAiB,OAAO;AAC1C,WAAO;AAAA,EACT;AACA,QAAM,eAAe,MAAM;AACzB,WAAO,KAAK,OAAK,EAAE,OAAO;AAAA,EAC5B;AACA,QAAM,eAAe,MAAM;AACzB,WAAO,MAAM,IAAI,UAAQ,KAAK,EAAE;AAAA,EAClC;AACA,QAAM,kBAAkB,MAAM;AAC5B,WAAO,MAAM,KAAK,UAAQ,KAAK,WAAW;AAAA,EAC5C;AACA,QAAM,OAAO,eAAa;AACxB,UAAM,WAAW,MAAM,KAAK,SAAS;AACrC,QAAI,aAAa,QAAW;AAC1B,aAAO,SAAS;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AACA,QAAM,iBAAiB,MAAM;AAC3B,WAAO,QAAQ,IAAI,MAAM,KAAK,SAAS,iBAAiB,UAAU,CAAC,EAAE,IAAI,UAAQ,IAAI,QAAQ,aAAW,iBAAiB,MAAM,OAAO,CAAC,CAAC,CAAC;AAAA,EAC3I;AACA,oBAAkB,UAAU,mBAAmB;AAC/C,oBAAkB,QAAQ,iBAAiB;AAC3C,oBAAkB,WAAW,oBAAoB;AACjD,UAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,iBAAiB,iBAAiB,QAAM;AACpF,UAAM,WAAW,aAAa;AAC9B,QAAI,UAAU;AACZ,SAAG,OAAO,SAAS,2BAA2B,MAAM;AAClD,eAAO,SAAS,MAAM;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,iBAA8B,qBAAqB;;;AC/RzD,IAAM,kBAAkB,CAAO,UAAU,WAAW,WAAW,YAAY,gBAAgB,WAAW;AACpG,MAAIC;AACJ,MAAI,UAAU;AACZ,WAAO,SAAS,gBAAgB,WAAW,WAAW,gBAAgB,UAAU;AAAA,EAClF;AACA,MAAI,CAAC,UAAU,OAAO,cAAc,YAAY,EAAE,qBAAqB,cAAc;AACnF,UAAM,IAAI,MAAM,+BAA+B;AAAA,EACjD;AACA,QAAM,KAAK,OAAO,cAAc,YAAYA,MAAK,UAAU,mBAAmB,QAAQA,QAAO,SAAS,SAASA,IAAG,cAAc,SAAS,IAAI;AAC7I,MAAI,YAAY;AACd,eAAW,QAAQ,OAAK,GAAG,UAAU,IAAI,CAAC,CAAC;AAAA,EAC7C;AACA,MAAI,gBAAgB;AAClB,WAAO,OAAO,IAAI,cAAc;AAAA,EAClC;AACA,YAAU,YAAY,EAAE;AACxB,QAAM,IAAI,QAAQ,aAAW,iBAAiB,IAAI,OAAO,CAAC;AAC1D,SAAO;AACT;AACA,IAAM,kBAAkB,CAAC,UAAU,YAAY;AAC7C,MAAI,SAAS;AACX,QAAI,UAAU;AACZ,YAAM,YAAY,QAAQ;AAC1B,aAAO,SAAS,kBAAkB,WAAW,OAAO;AAAA,IACtD;AACA,YAAQ,OAAO;AAAA,EACjB;AACA,SAAO,QAAQ,QAAQ;AACzB;AACA,IAAM,eAAe,MAAM;AACzB,MAAI;AACJ,MAAI;AACJ,QAAM,kBAAkB,CAAO,IAAe,OAA4D,sBAA3E,IAAe,IAA4D,mBAA3E,eAAe,eAAe,qBAAqB,CAAC,GAAG,aAAa,CAAC,GAAM;AACxG,QAAIA,KAAI;AACR,oBAAgB;AAChB,QAAI;AAKJ,QAAI,eAAe;AAMjB,YAAM,KAAK,OAAO,kBAAkB,YAAYA,MAAK,cAAc,mBAAmB,QAAQA,QAAO,SAAS,SAASA,IAAG,cAAc,aAAa,IAAI;AAKzJ,iBAAW,QAAQ,OAAK,GAAG,UAAU,IAAI,CAAC,CAAC;AAK3C,aAAO,OAAO,IAAI,kBAAkB;AAKpC,oBAAc,YAAY,EAAE;AAC5B,uBAAiB;AACjB,YAAM,IAAI,QAAQ,aAAW,iBAAiB,IAAI,OAAO,CAAC;AAAA,IAC5D,WAAW,cAAc,SAAS,SAAS,MAAM,cAAc,YAAY,eAAe,cAAc,YAAY,gBAAgB;AAKlI,YAAM,OAAO,iBAAiB,cAAc,SAAS,CAAC;AACtD,UAAI,CAAC,KAAK,UAAU,SAAS,mBAAmB,GAAG;AAMjD,cAAM,MAAM,KAAK,cAAc,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,KAAK;AAEzG,WAAG,UAAU,IAAI,mBAAmB;AACpC,mBAAW,QAAQ,OAAK,GAAG,UAAU,IAAI,CAAC,CAAC;AAE3C,WAAG,OAAO,GAAG,cAAc,QAAQ;AAEnC,sBAAc,YAAY,EAAE;AAM5B,yBAAiB;AAAA,MACnB;AAAA,IACF;AAKA,UAAM,MAAM,SAAS,cAAc,SAAS,KAAK,SAAS;AAM1D,gBAAY,SAAS,cAAc,gBAAgB;AACnD,kBAAc,WAAW,aAAa,WAAW,aAAa;AAC9D,QAAI,YAAY,aAAa;AAY7B,WAAO,mBAAmB,QAAQ,mBAAmB,SAAS,iBAAiB;AAAA,EACjF;AACA,QAAM,oBAAoB,MAAM;AAI9B,QAAI,iBAAiB,WAAW;AAC9B,gBAAU,WAAW,aAAa,eAAe,SAAS;AAC1D,gBAAU,OAAO;AAAA,IACnB;AACA,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;;;ACnHA,IAAM,uBAAuB;AAW7B,IAAM,uBAAuB,CAAC,KAAK,oBAAoB;AACrD,QAAM,aAAa,IAAI,cAAc,oBAAoB;AACzD,wBAAsB,YAAY,oBAAoB,QAAQ,oBAAoB,SAAS,kBAAkB,GAAG;AAClH;AAWA,IAAM,sBAAsB,CAAC,KAAK,oBAAoB;AACpD,QAAM,SAAS,MAAM,KAAK,IAAI,iBAAiB,oBAAoB,CAAC;AACpE,QAAM,YAAY,OAAO,SAAS,IAAI,OAAO,OAAO,SAAS,CAAC,IAAI;AAClE,wBAAsB,WAAW,oBAAoB,QAAQ,oBAAoB,SAAS,kBAAkB,GAAG;AACjH;AAkBA,IAAM,wBAAwB,CAAC,aAAa,oBAAoB;AAC9D,MAAI,iBAAiB;AACrB,QAAM,aAAa,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AACzF,MAAI,YAAY;AAEd,qBAAiB,WAAW,cAAc,oBAAoB,KAAK;AAAA,EACrE;AACA,MAAI,gBAAgB;AAClB,UAAM,aAAa,eAAe,QAAQ,iBAAiB;AAC3D,QAAI,YAAY;AACd,iBAAW,SAAS;AAAA,IACtB,OAAO;AACL,0BAAoB,cAAc;AAAA,IACpC;AAAA,EACF,OAAO;AAEL,oBAAgB,MAAM;AAAA,EACxB;AACF;AACA,IAAI,mBAAmB;AACvB,IAAI,SAAS;AACb,IAAM,mBAAmB,oBAAI,QAAQ;AACrC,IAAM,mBAAmB,aAAW;AAClC,SAAO;AAAA,IACL,OAAO,SAAS;AACd,aAAO,cAAc,SAAS,OAAO;AAAA,IACvC;AAAA,IACA,QAAQ,MAAM,MAAM,IAAI;AACtB,aAAO,eAAe,UAAU,MAAM,MAAM,SAAS,EAAE;AAAA,IACzD;AAAA,IACM,SAAS;AAAA;AACb,eAAO,oBAAoB,UAAU,OAAO;AAAA,MAC9C;AAAA;AAAA,EACF;AACF;AACA,IAAM,kBAA+B,iBAAiB,WAAW;AACjE,IAAM,wBAAqC,iBAAiB,kBAAkB;AAC9E,IAAM,oBAAiC,iBAAiB,aAAa;AACrE,IAAM,kBAA+B,iBAAiB,WAAW;AAIjE,IAAM,mBAAgC,iBAAiB,mBAAmB;AAC1E,IAAM,oBAAiC,iBAAiB,aAAa;AACrE,IAAM,kBAA+B,iBAAiB,WAAW;AAIjE,IAAM,iBAAiB,QAAM;AAC3B,MAAI,OAAO,aAAa,aAAa;AAUnC,qBAAiB,QAAQ;AAAA,EAC3B;AACA,QAAM,eAAe;AAMrB,KAAG,eAAe;AACpB;AAOA,IAAM,eAAe,QAAM;AACzB,MAAI,CAAC,GAAG,aAAa,IAAI,GAAG;AAC1B,OAAG,KAAK,eAAe,EAAE,MAAM;AAAA,EACjC;AACA,SAAO,GAAG;AACZ;AACA,IAAM,gBAAgB,CAAC,SAAS,SAAS;AAEvC,MAAI,OAAO,WAAW,eAAe,OAAO,OAAO,mBAAmB,aAAa;AACjF,WAAO,OAAO,eAAe,YAAY,OAAO,EAAE,KAAK,MAAM;AAC3D,YAAM,UAAU,SAAS,cAAc,OAAO;AAC9C,cAAQ,UAAU,IAAI,gBAAgB;AAKtC,aAAO,OAAO,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG;AAAA,QAC5D,eAAe;AAAA,MACjB,CAAC,CAAC;AAEF,iBAAW,QAAQ,EAAE,YAAY,OAAO;AACxC,aAAO,IAAI,QAAQ,aAAW,iBAAiB,SAAS,OAAO,CAAC;AAAA,IAClE,CAAC;AAAA,EACH;AACA,SAAO,QAAQ,QAAQ;AACzB;AACA,IAAM,kBAAkB,aAAW,QAAQ,UAAU,SAAS,gBAAgB;AAc9E,IAAM,wBAAwB,CAAC,aAAa,YAAY;AACtD,MAAI,iBAAiB;AACrB,QAAM,aAAa,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AACzF,MAAI,YAAY;AAEd,qBAAiB,WAAW,cAAc,oBAAoB,KAAK;AAAA,EACrE;AACA,MAAI,gBAAgB;AAClB,wBAAoB,cAAc;AAAA,EACpC,OAAO;AAEL,YAAQ,MAAM;AAAA,EAChB;AACF;AAQA,IAAM,oBAAoB,CAAC,IAAIC,SAAQ;AACrC,QAAM,cAAc,oBAAoBA,MAAK,gFAAgF;AAC7H,QAAM,SAAS,GAAG;AAWlB,MAAI,CAAC,eAAe,CAAC,QAAQ;AAC3B;AAAA,EACF;AAUA,MAAI,YAAY,UAAU,SAAS,wBAAwB,GAAG;AAC5D;AAAA,EACF;AACA,QAAM,kBAAkB,MAAM;AAO5B,QAAI,gBAAgB,QAAQ;AAC1B,kBAAY,YAAY;AAAA,IAa1B,WAAW,OAAO,YAAY,aAAa;AACzC,4BAAsB,YAAY,WAAW,WAAW;AAAA,IAS1D,OAAO;AAKL,YAAM,cAAc,eAAe,WAAW;AAC9C,UAAI,CAAC,YAAY,SAAS,MAAM,GAAG;AACjC;AAAA,MACF;AACA,YAAM,iBAAiB,YAAY,cAAc,sBAAsB;AACvE,UAAI,CAAC,gBAAgB;AACnB;AAAA,MACF;AAQA,UAAI,eAAe,SAAS,MAAM,KAAK,WAAW,YAAY,cAAc,cAAc,GAAG;AAC3F,oBAAY,YAAY;AAAA,MAC1B,OAAO;AAaL,cAAM,YAAY,YAAY;AAE9B,6BAAqB,gBAAgB,WAAW;AAUhD,YAAI,cAAcA,KAAI,eAAe;AACnC,8BAAoB,gBAAgB,WAAW;AAAA,QACjD;AACA,oBAAY,YAAYA,KAAI;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AACA,QAAM,kBAAkB,MAAM;AAK5B,QAAI,YAAY,SAAS,MAAM,GAAG;AAChC,kBAAY,YAAY;AAAA,IAa1B,WAAW,OAAO,YAAY,aAAa;AACzC,4BAAsB,YAAY,WAAW,WAAW;AAAA,IAC1D,OAAO;AAcL,YAAM,YAAY,YAAY;AAE9B,2BAAqB,WAAW;AAUhC,UAAI,cAAcA,KAAI,eAAe;AACnC,4BAAoB,WAAW;AAAA,MACjC;AACA,kBAAY,YAAYA,KAAI;AAAA,IAC9B;AAAA,EACF;AACA,MAAI,YAAY,YAAY;AAC1B,oBAAgB;AAAA,EAClB,OAAO;AACL,oBAAgB;AAAA,EAClB;AACF;AACA,IAAM,mBAAmB,CAAAA,SAAO;AAC9B,MAAI,qBAAqB,GAAG;AAC1B,uBAAmB;AACnB,IAAAA,KAAI,iBAAiB,SAAS,QAAM;AAClC,wBAAkB,IAAIA,IAAG;AAAA,IAC3B,GAAG,IAAI;AAEP,IAAAA,KAAI,iBAAiB,iBAAiB,QAAM;AAC1C,YAAM,cAAc,oBAAoBA,IAAG;AAC3C,UAAI,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,iBAAiB;AACzF,WAAG,OAAO,SAAS,8BAA8B,MAAM;AAYrD,sBAAY,QAAQ,QAAW,QAAQ;AAAA,QACzC,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAQD,QAAI,CAAC,sBAAsB,GAAG;AAC5B,MAAAA,KAAI,iBAAiB,WAAW,QAAM;AACpC,YAAI,GAAG,QAAQ,UAAU;AACvB,gBAAM,cAAc,oBAAoBA,IAAG;AAC3C,cAAI,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,iBAAiB;AACzF,wBAAY,QAAQ,QAAW,QAAQ;AAAA,UACzC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AACA,IAAM,iBAAiB,CAACA,MAAK,MAAM,MAAM,YAAY,OAAO;AAC1D,QAAM,UAAU,oBAAoBA,MAAK,YAAY,EAAE;AACvD,MAAI,CAAC,SAAS;AACZ,WAAO,QAAQ,OAAO,wBAAwB;AAAA,EAChD;AACA,SAAO,QAAQ,QAAQ,MAAM,IAAI;AACnC;AAIA,IAAM,cAAc,CAACA,MAAK,aAAa;AACrC,MAAI,aAAa,QAAW;AAC1B,eAAW;AAAA,EACb;AACA,SAAO,MAAM,KAAKA,KAAI,iBAAiB,QAAQ,CAAC,EAAE,OAAO,OAAK,EAAE,eAAe,CAAC;AAClF;AAQA,IAAM,uBAAuB,CAACA,MAAK,eAAe;AAChD,SAAO,YAAYA,MAAK,UAAU,EAAE,OAAO,OAAK,CAAC,gBAAgB,CAAC,CAAC;AACrE;AAQA,IAAM,sBAAsB,CAACA,MAAK,YAAY,OAAO;AACnD,QAAM,WAAW,qBAAqBA,MAAK,UAAU;AACrD,SAAO,OAAO,SAAY,SAAS,SAAS,SAAS,CAAC,IAAI,SAAS,KAAK,OAAK,EAAE,OAAO,EAAE;AAC1F;AAuBA,IAAM,oBAAoB,CAAC,SAAS,UAAU;AAC5C,QAAM,OAAO,WAAW,QAAQ;AAChC,QAAM,gBAAgB,KAAK,cAAc,sDAAsD;AAC/F,MAAI,CAAC,eAAe;AAClB;AAAA,EACF;AACA,MAAI,QAAQ;AACV,kBAAc,aAAa,eAAe,MAAM;AAAA,EAClD,OAAO;AACL,kBAAc,gBAAgB,aAAa;AAAA,EAC7C;AACF;AACA,IAAM,UAAU,CAAO,SAAS,MAAM,mBAAmB,kBAAkB,SAAS;AAClF,MAAIC,KAAI;AACR,MAAI,QAAQ,WAAW;AACrB;AAAA,EACF;AAQA,MAAI,QAAQ,GAAG,YAAY,aAAa;AACtC,sBAAkB,IAAI;AACtB,aAAS,KAAK,UAAU,IAAI,kBAAkB;AAAA,EAChD;AACA,0CAAwC,QAAQ,EAAE;AAClD,wCAAsC,QAAQ,EAAE;AAChD,UAAQ,YAAY;AACpB,UAAQ,YAAY,KAAK;AACzB,GAACA,MAAK,QAAQ,0BAA0B,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK;AACjF,QAAM,OAAO,WAAW,OAAO;AAE/B,QAAM,mBAAmB,QAAQ,iBAAiB,QAAQ,iBAAiB,OAAO,IAAI,MAAM,SAAS,QAAQ,oBAAoB,gBAAgB;AACjJ,QAAM,YAAY,MAAM,iBAAiB,SAAS,kBAAkB,QAAQ,IAAI,IAAI;AACpF,MAAI,WAAW;AACb,YAAQ,WAAW,KAAK;AACxB,KAAC,KAAK,QAAQ,yBAAyB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAAA,EAClF;AASA,MAAI,QAAQ,GAAG,YAAY,aAAa;AACtC,wBAAoB,QAAQ,EAAE;AAAA,EAChC;AAOA,MAAI,QAAQ,kBAAkB,SAAS,kBAAkB,QAAQ,CAAC,QAAQ,GAAG,SAAS,SAAS,aAAa,IAAI;AAC9G,YAAQ,GAAG,MAAM;AAAA,EACnB;AAaA,UAAQ,GAAG,gBAAgB,aAAa;AAC1C;AAWA,IAAM,sBAAsB,CAAM,cAAa;AAC7C,MAAI,kBAAkB,SAAS;AAC/B,MAAI,CAAC,iBAAiB;AACpB;AAAA,EACF;AACA,QAAM,aAAa,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB;AACrG,MAAI,YAAY;AAEd,sBAAkB,WAAW,cAAc,oBAAoB,KAAK;AAAA,EACtE;AACA,QAAM,UAAU,aAAa;AAyB7B,MAAI,SAAS,kBAAkB,QAAQ,SAAS,kBAAkB,SAAS,MAAM;AAC/E,oBAAgB,MAAM;AAAA,EACxB;AACF;AACA,IAAM,UAAU,CAAO,SAAS,MAAM,MAAM,MAAM,mBAAmB,kBAAkB,SAAS;AAC9F,MAAIA,KAAI;AACR,MAAI,CAAC,QAAQ,WAAW;AACtB,WAAO;AAAA,EACT;AACA,QAAM,oBAAoB,QAAQ,SAAY,qBAAqB,GAAG,IAAI,CAAC;AAY3E,QAAM,mBAAmB,kBAAkB,OAAO,OAAK,EAAE,YAAY,WAAW;AAChF,QAAM,sBAAsB,iBAAiB,WAAW,KAAK,iBAAiB,CAAC,EAAE,OAAO,QAAQ,GAAG;AAKnG,MAAI,qBAAqB;AACvB,sBAAkB,KAAK;AACvB,aAAS,KAAK,UAAU,OAAO,kBAAkB;AAAA,EACnD;AACA,UAAQ,YAAY;AACpB,MAAI;AAMF,0CAAsC,QAAQ,EAAE;AAEhD,YAAQ,GAAG,MAAM,YAAY,kBAAkB,MAAM;AACrD,YAAQ,YAAY,KAAK;AAAA,MACvB;AAAA,MACA;AAAA,IACF,CAAC;AACD,KAACA,MAAK,QAAQ,0BAA0B,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK;AAAA,MAC/E;AAAA,MACA;AAAA,IACF,CAAC;AACD,UAAM,OAAO,WAAW,OAAO;AAC/B,UAAM,mBAAmB,QAAQ,iBAAiB,QAAQ,iBAAiB,OAAO,IAAI,MAAM,SAAS,QAAQ,oBAAoB,gBAAgB;AAEjJ,QAAI,SAAS,SAAS;AACpB,YAAM,iBAAiB,SAAS,kBAAkB,QAAQ,IAAI,IAAI;AAAA,IACpE;AACA,YAAQ,WAAW,KAAK;AAAA,MACtB;AAAA,MACA;AAAA,IACF,CAAC;AACD,KAAC,KAAK,QAAQ,yBAAyB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAAA,MAC9E;AAAA,MACA;AAAA,IACF,CAAC;AAGD,UAAM,aAAa,iBAAiB,IAAI,OAAO,KAAK,CAAC;AACrD,eAAW,QAAQ,SAAO,IAAI,QAAQ,CAAC;AACvC,qBAAiB,OAAO,OAAO;AAM/B,YAAQ,GAAG,UAAU,IAAI,gBAAgB;AACzC,YAAQ,GAAG,MAAM,eAAe,gBAAgB;AAKhD,QAAI,QAAQ,GAAG,cAAc,QAAW;AACtC,cAAQ,GAAG,YAAY;AAAA,IACzB;AAAA,EACF,SAAS,KAAK;AACZ,kBAAc,IAAI,QAAQ,GAAG,QAAQ,YAAY,CAAC,QAAQ,GAAG;AAAA,EAC/D;AACA,UAAQ,GAAG,OAAO;AAClB,gCAA8B;AAC9B,SAAO;AACT;AACA,IAAM,aAAa,CAAAD,SAAO;AACxB,SAAOA,KAAI,cAAc,SAAS,KAAKA,KAAI;AAC7C;AACA,IAAM,mBAAmB,CAAO,SAAS,kBAAkB,QAAQ,SAAS;AAE1E,SAAO,UAAU,OAAO,gBAAgB;AACxC,QAAM,UAAU,QAAQ;AACxB,QAAM,YAAY,iBAAiB,SAAS,IAAI;AAChD,MAAI,CAAC,QAAQ,YAAY,CAAC,OAAO,WAAW,YAAY,IAAI,GAAG;AAC7D,cAAU,SAAS,CAAC;AAAA,EACtB;AACA,MAAI,QAAQ,eAAe;AACzB,cAAU,eAAe,MAAM;AAC7B,YAAM,gBAAgB,OAAO,cAAc;AAC3C,UAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,QAAQ,+BAA+B,GAAG;AACxH,sBAAc,KAAK;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,YAAY,iBAAiB,IAAI,OAAO,KAAK,CAAC;AACpD,mBAAiB,IAAI,SAAS,CAAC,GAAG,WAAW,SAAS,CAAC;AACvD,QAAM,UAAU,KAAK;AACrB,SAAO;AACT;AACA,IAAM,cAAc,CAAC,SAAS,cAAc;AAC1C,MAAI;AACJ,QAAM,UAAU,IAAI,QAAQ,OAAK,UAAU,CAAC;AAC5C,YAAU,SAAS,WAAW,WAAS;AACrC,YAAQ,MAAM,MAAM;AAAA,EACtB,CAAC;AACD,SAAO;AACT;AACA,IAAM,YAAY,CAAC,SAAS,WAAW,aAAa;AAClD,QAAM,UAAU,QAAM;AACpB,wBAAoB,SAAS,WAAW,OAAO;AAC/C,aAAS,EAAE;AAAA,EACb;AACA,mBAAiB,SAAS,WAAW,OAAO;AAC9C;AACA,IAAM,WAAW,UAAQ;AACvB,SAAO,SAAS,YAAY,SAAS;AACvC;AACA,IAAM,cAAc,OAAK,EAAE;AAQ3B,IAAM,WAAW,CAAC,SAAS,QAAQ;AACjC,MAAI,OAAO,YAAY,YAAY;AACjC,UAAM,MAAM,OAAO,IAAI,aAAa,WAAW;AAC/C,WAAO,IAAI,MAAM;AACf,UAAI;AACF,eAAO,QAAQ,GAAG;AAAA,MACpB,SAAS,GAAG;AACV,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,IAAM,WAAW;AACjB,IAAM,UAAU;AAChB,IAAM,2BAA2B;AAWjC,IAAM,2BAA2B,SAAO;AACtC,MAAI,SAAS;AACb,MAAI;AACJ,QAAM,eAAe,aAAa;AAWlC,QAAM,cAAc,CAAC,QAAQ,UAAU;AACrC,QAAI,mBAAmB,CAAC,OAAO;AAC7B,aAAO;AAAA,QACL,UAAU;AAAA,QACV;AAAA,MACF;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAUJ,UAAM,WAAW,GAAG;AACpB,aAAS,aAAa,QAAQ,CAAC;AAC/B,sBAAkB,SAAS,YAAY,eAAe;AACtD,WAAO;AAAA,MACL;AAAA,MACA,UAAU;AAAA,IACZ;AAAA,EACF;AAMA,QAAM,kBAAkB,CAAM,cAAa;AACzC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,YAAY,IAAI;AACpB,QAAI,UAAU;AACZ,aAAO,MAAM,SAAS,gBAAgB,IAAI,IAAI,SAAS;AAAA,IACzD;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,iBAAiB,cAAc,QAAW;AAC5C,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AAIA,QAAM,oBAAoB,MAAM;AAC9B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,YAAY;AAChB,QAAI,YAAY,IAAI,OAAO,QAAW;AACpC,eAAS,kBAAkB,IAAI,GAAG,eAAe,IAAI,EAAE;AAAA,IACzD;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAWA,IAAM,0BAA0B,MAAM;AACpC,MAAI;AAIJ,QAAM,sBAAsB,MAAM;AAChC,QAAI,2BAA2B;AAC7B,gCAA0B;AAC1B,kCAA4B;AAAA,IAC9B;AAAA,EACF;AAOA,QAAM,mBAAmB,CAAC,IAAI,YAAY;AACxC,wBAAoB;AACpB,UAAM,YAAY,YAAY,SAAY,SAAS,eAAe,OAAO,IAAI;AAC7E,QAAI,CAAC,WAAW;AACd,sBAAgB,IAAI,GAAG,QAAQ,YAAY,CAAC,sCAAsC,OAAO,kIAAkI,EAAE;AAC7N;AAAA,IACF;AACA,UAAM,8BAA8B,CAAC,UAAU,cAAc;AAC3D,YAAM,cAAc,MAAM;AACxB,kBAAU,QAAQ;AAAA,MACpB;AACA,eAAS,iBAAiB,SAAS,WAAW;AAC9C,aAAO,MAAM;AACX,iBAAS,oBAAoB,SAAS,WAAW;AAAA,MACnD;AAAA,IACF;AACA,gCAA4B,4BAA4B,WAAW,EAAE;AAAA,EACvE;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAqBA,IAAM,wCAAwC,aAAW;AACvD,MAAI,QAAQ,OAAW;AACvB,MAAI,WAAW,SAAS,GAAG;AAKzB,YAAQ,aAAa,eAAe,MAAM;AAAA,EAC5C;AACF;AAWA,IAAM,0CAA0C,uBAAqB;AACnE,MAAIC;AACJ,MAAI,QAAQ,OAAW;AACvB,QAAM,WAAW,qBAAqB,GAAG;AACzC,WAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,UAAM,mBAAmB,SAAS,CAAC;AACnC,UAAM,wBAAwBA,MAAK,SAAS,IAAI,CAAC,OAAO,QAAQA,QAAO,SAASA,MAAK;AAMrF,QAAI,qBAAqB,aAAa,aAAa,KAAK,qBAAqB,YAAY,aAAa;AACpG,uBAAiB,aAAa,eAAe,MAAM;AAAA,IACrD;AAAA,EACF;AACF;AAMA,IAAM,gCAAgC,MAAM;AAC1C,MAAI,QAAQ,OAAW;AACvB,QAAM,WAAW,qBAAqB,GAAG;AACzC,WAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,UAAM,iBAAiB,SAAS,CAAC;AAOjC,mBAAe,gBAAgB,aAAa;AAM5C,QAAI,eAAe,YAAY,aAAa;AAC1C;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,2BAA2B;;;ACj9BjC,IAAM,cAAc,CAAC,UAAU,OAAO;AACpC,SAAO,GAAG,QAAQ,QAAQ,MAAM;AAClC;AAIA,IAAM,qBAAqB,CAAC,OAAO,gBAAgB;AACjD,SAAO,OAAO,UAAU,YAAY,MAAM,SAAS,IAAI,OAAO,OAAO;AAAA,IACnE,aAAa;AAAA,IACb,CAAC,aAAa,KAAK,EAAE,GAAG;AAAA,EAC1B,GAAG,WAAW,IAAI;AACpB;AACA,IAAM,eAAe,aAAW;AAC9B,MAAI,YAAY,QAAW;AACzB,UAAM,QAAQ,MAAM,QAAQ,OAAO,IAAI,UAAU,QAAQ,MAAM,GAAG;AAClE,WAAO,MAAM,OAAO,OAAK,KAAK,IAAI,EAAE,IAAI,OAAK,EAAE,KAAK,CAAC,EAAE,OAAO,OAAK,MAAM,EAAE;AAAA,EAC7E;AACA,SAAO,CAAC;AACV;AACA,IAAM,cAAc,aAAW;AAC7B,QAAM,MAAM,CAAC;AACb,eAAa,OAAO,EAAE,QAAQ,OAAK,IAAI,CAAC,IAAI,IAAI;AAChD,SAAO;AACT;AACA,IAAM,SAAS;AACf,IAAM,UAAU,CAAO,KAAK,IAAI,WAAW,cAAc;AACvD,MAAI,OAAO,QAAQ,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,KAAK,GAAG,GAAG;AACtD,UAAM,SAAS,SAAS,cAAc,YAAY;AAClD,QAAI,QAAQ;AACV,UAAI,MAAM,MAAM;AACd,WAAG,eAAe;AAAA,MACpB;AACA,aAAO,OAAO,KAAK,KAAK,WAAW,SAAS;AAAA,IAC9C;AAAA,EACF;AACA,SAAO;AACT;;;ACtBA,IAAM,cAAc,UAAQ;AAC1B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,eAAe;AAAA,IACnB,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,cAAc;AAAA,IACd,MAAM;AAAA,IACN,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,+BAA+B;AAAA,IAC/B,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,MAAM;AAAA,MACJ,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,IACA,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,0BAA0B;AAAA,IAC1B,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,0BAA0B;AAAA,IAC1B,qBAAqB;AAAA,IACrB,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,IACvB,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,uBAAuB;AAAA,IACvB,eAAe;AAAA,IACf,0BAA0B;AAAA,IAC1B,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,MACf,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,IACA,YAAY;AAAA,MACV,cAAc;AAAA,MACd,eAAe;AAAA,IACjB;AAAA,IACA,YAAY;AAAA,MACV,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,aAAa;AAAA,IACf;AAAA,IACA,YAAY;AAAA,MACV,WAAW;AAAA,IACb;AAAA,IACA,MAAM;AAAA,MACJ,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,IACpB;AAAA,EACF;AACA,MAAI,OAAO,YAAY;AACrB,iBAAa,aAAa;AAAA,MACxB,MAAM;AAAA,MACN,WAAW;AAAA,MACX,aAAa;AAAA,IACf;AAAA,EACF;AACA,MAAI,OAAO,WAAW;AACpB,iBAAa,YAAY;AAAA,MACvB,MAAM;AAAA,IACR;AAAA,EACF;AACA,eAAa,YAAY;AAC3B;;;AC1GA,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,iBAAN,MAAqB;AAAA,EACnB;AAAA,EACA,YAAYC,iBAAgB;AAC1B,SAAK,iBAAiBA;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,QAAQ;AACX,WAAO,KAAK,eAAe,KAAK,MAAM;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,QAAQ;AACZ,WAAO,KAAK,eAAe,MAAM,MAAM;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,QAAQ;AACb,WAAO,KAAK,eAAe,OAAO,MAAM;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,cAAc,QAAQ;AAC3B,WAAO,KAAK,eAAe,OAAO,cAAc,MAAM;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,cAAc,QAAQ;AACjC,WAAO,KAAK,eAAe,aAAa,cAAc,MAAM;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,QAAQ;AACb,WAAO,KAAK,eAAe,OAAO,MAAM;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,QAAQ;AAChB,WAAO,KAAK,eAAe,UAAU,MAAM;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAI,QAAQ;AACV,WAAO,KAAK,eAAe,IAAI,MAAM;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,WAAO,KAAK,eAAe,QAAQ;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,WAAO,KAAK,eAAe,SAAS;AAAA,EACtC;AAAA,EACA,kBAAkB,MAAM,WAAW;AACjC,WAAO,KAAK,eAAe,kBAAkB,MAAM,SAAS;AAAA,EAC9D;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,eAAe,YAAY;AAAA,EACzC;AAAA,EACA,eAAe;AACb,WAAO,KAAK,eAAe,aAAa;AAAA,EAC1C;AAAA,EACA,iBAAiB,MAAM,SAAS;AAC9B,WAAO,KAAK,eAAe,iBAAiB,MAAM,OAAO;AAAA,EAC3D;AAAA,EACA,UAAU,MAAM;AACd,WAAO,KAAK,eAAe,UAAU,IAAI;AAAA,EAC3C;AAAA,EACA,YAAY,MAAM;AAChB,WAAO,KAAK,eAAe,YAAY,IAAI;AAAA,EAC7C;AAAA,EACA,SAAS,MAAM,YAAY,UAAU;AACnC,WAAO,KAAK,eAAe,SAAS,MAAM,YAAY,QAAQ;AAAA,EAChE;AACF;AACA,IAAM,iBAAN,MAAM,eAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlB,KAAK,IAAI;AACP,aAAS,EAAE,KAAK,EAAE;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,IAAI;AACR,aAAS,EAAE,MAAM,EAAE;AAAA,EACrB;AAWF;AAAA;AATE,cAhBI,gBAgBG,QAAO,SAAS,sBAAsB,mBAAmB;AAC9D,SAAO,KAAK,qBAAqB,gBAAe;AAClD;AAAA;AAEA,cApBI,gBAoBG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,eAAc;AAAA,EACvB,YAAY;AACd,CAAC;AAxBH,IAAM,gBAAN;AAAA,CA0BC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,WAAW,MAAM;AACrB,QAAM,MAAM,OAAO,WAAW,cAAc,SAAS;AACrD,MAAI,OAAO,MAAM;AACf,UAAM,QAAQ,IAAI;AAClB,QAAI,+BAAO,OAAO;AAChB,aAAO,MAAM;AAAA,IACf;AACA,WAAO;AAAA,MACL,MAAM,QAAM,IAAI,sBAAsB,EAAE;AAAA,MACxC,OAAO,QAAM,IAAI,sBAAsB,EAAE;AAAA,IAC3C;AAAA,EACF;AACA,SAAO;AAAA,IACL,MAAM,QAAM,GAAG;AAAA,IACf,OAAO,QAAM,GAAG;AAAA,EAClB;AACF;AACA,IAAM,YAAN,MAAM,UAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,kBAAkB,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9B,kBAAkB,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO9B,QAAQ,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,SAAS,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,SAAS,IAAI,QAAQ;AAAA,EACrB,YAAYC,MAAK,MAAM;AACrB,SAAK,MAAMA;AACX,SAAK,IAAI,MAAM;AAvNnB,UAAAC;AAwNM,WAAK,MAAMD,KAAI;AACf,WAAK,WAAW,wBAAwB,SAAU,UAAU,UAAU;AACpE,eAAO,KAAK,UAAU,QAAM;AAC1B,iBAAO,GAAG,SAAS,UAAU,wBAAsB,KAAK,IAAI,MAAM,SAAS,kBAAkB,CAAC,CAAC;AAAA,QACjG,CAAC;AAAA,MACH;AACA,iBAAW,KAAK,OAAOA,MAAK,SAAS,IAAI;AACzC,iBAAW,KAAK,QAAQA,MAAK,UAAU,IAAI;AAC3C,iBAAW,KAAK,YAAYA,MAAK,iBAAiB,IAAI;AACtD,iBAAW,KAAK,QAAQ,KAAK,KAAK,UAAU,IAAI;AAChD,iBAAW,KAAK,iBAAiB,KAAK,KAAK,sBAAsB,IAAI;AACrE,iBAAW,KAAK,iBAAiB,KAAK,KAAK,sBAAsB,IAAI;AACrE,UAAI;AACJ,WAAK,gBAAgB,IAAI,QAAQ,SAAO;AACtC,uBAAe;AAAA,MACjB,CAAC;AACD,WAAIC,MAAA,KAAK,QAAL,gBAAAA,IAAW,YAAY;AACzB,QAAAD,KAAI,iBAAiB,eAAe,MAAM;AACxC,uBAAa,SAAS;AAAA,QACxB,GAAG;AAAA,UACD,MAAM;AAAA,QACR,CAAC;AAAA,MACH,OAAO;AAEL,qBAAa,KAAK;AAAA,MACpB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4CA,GAAG,cAAc;AACf,WAAO,WAAW,KAAK,KAAK,YAAY;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,YAAY;AACV,WAAO,aAAa,KAAK,GAAG;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8BA,QAAQ;AACN,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,QAAQ;AACV,WAAO,KAAK,IAAI,QAAQ;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,KAAK;AACjB,WAAO,eAAe,KAAK,IAAI,SAAS,MAAM,GAAG;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,CAAC,KAAK,WAAW;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AAhXf,QAAAC,KAAA;AAiXI,YAAO,MAAAA,MAAA,KAAK,KAAI,eAAT,wBAAAA,KAAsB,2BAA2B;AAAA,EAC1D;AAAA,EACA,cAAc,YAAY;AACxB,UAAM,MAAM,KAAK,IAAI;AACrB,WAAO,CAAC,GAAE,2BAAK,cAAa,IAAI,UAAU,QAAQ,UAAU,KAAK;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM;AACJ,WAAO,KAAK,IAAI,SAAS;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACN,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,WAAO,KAAK,IAAI;AAAA,EAClB;AAWF;AAAA;AATE,cA1NI,WA0NG,QAAO,SAAS,iBAAiB,mBAAmB;AACzD,SAAO,KAAK,qBAAqB,WAAa,SAAS,QAAQ,GAAM,SAAY,MAAM,CAAC;AAC1F;AAAA;AAEA,cA9NI,WA8NG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,UAAS;AAAA,EAClB,YAAY;AACd,CAAC;AAlOH,IAAM,WAAN;AAAA,CAoOC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,iBAAiB,CAAC,KAAK,QAAQ;AACnC,QAAM,IAAI,QAAQ,YAAY,MAAM;AACpC,QAAM,QAAQ,IAAI,OAAO,WAAW,MAAM,WAAW;AACrD,QAAM,UAAU,MAAM,KAAK,GAAG;AAC9B,SAAO,UAAU,mBAAmB,QAAQ,CAAC,EAAE,QAAQ,OAAO,GAAG,CAAC,IAAI;AACxE;AACA,IAAM,aAAa,CAAC,SAAS,IAAI,WAAW,SAAS;AACnD,MAAI,IAAI;AACN,OAAG,iBAAiB,WAAW,QAAM;AAOnC,WAAK,IAAI,MAAM;AAEb,cAAM,QAAQ,MAAM,OAAO,GAAG,SAAS;AACvC,gBAAQ,KAAK,KAAK;AAAA,MACpB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,IAAM,iBAAN,MAAM,eAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ,WAAW;AAAA,EACX;AAAA,EACA,iBAAiB;AAAA,EACjB;AAAA,EACA,YAAY;AAAA,EACZ,YAAY,UAAU,UAAU,YAAY,QAAQ;AAClD,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,SAAS;AAEd,QAAI,QAAQ;AACV,aAAO,OAAO,UAAU,QAAM;AAC5B,YAAI,cAAc,iBAAiB;AAEjC,gBAAM,KAAK,GAAG,gBAAgB,GAAG,cAAc,eAAe,GAAG;AACjE,eAAK,iBAAiB,KAAK,iBAAiB,KAAK,KAAK,YAAY,SAAS;AAC3E,eAAK,YAAY,KAAK,mBAAmB,YAAY,GAAG,KAAK;AAAA,QAC/D;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,WAAW,sBAAsB,GAAG,wBAAsB;AACjE,WAAK,IAAI;AACT,yBAAmB;AAAA,IACrB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,gBAAgB,KAAK,UAAU,CAAC,GAAG;AACjC,SAAK,aAAa,WAAW,QAAQ,UAAU,QAAQ,oBAAoB,QAAQ,SAAS;AAC5F,WAAO,KAAK,SAAS,KAAK,OAAO;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,aAAa,KAAK,UAAU,CAAC,GAAG;AAC9B,SAAK,aAAa,QAAQ,QAAQ,UAAU,QAAQ,oBAAoB,QAAQ,SAAS;AACzF,WAAO,KAAK,SAAS,KAAK,OAAO;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,aAAa,KAAK,UAAU,CAAC,GAAG;AAC9B,SAAK,aAAa,QAAQ,QAAQ,UAAU,QAAQ,oBAAoB,QAAQ,SAAS;AACzF,WAAO,KAAK,SAAS,KAAK,OAAO;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,UAAU;AAAA,IACb,UAAU;AAAA,IACV,oBAAoB;AAAA,EACtB,GAAG;AACD,SAAK,aAAa,QAAQ,QAAQ,UAAU,QAAQ,oBAAoB,QAAQ,SAAS;AACzF,WAAO,KAAK,SAAS,KAAK;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASM,MAAM;AAAA;AACV,UAAI,SAAS,KAAK;AAClB,aAAO,QAAQ;AACb,YAAI,MAAM,OAAO,IAAI,GAAG;AACtB,iBAAO;AAAA,QACT,OAAO;AACL,mBAAS,OAAO;AAAA,QAClB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,WAAW,UAAU,oBAAoB,kBAAkB;AACtE,SAAK,YAAY;AACjB,SAAK,WAAW,aAAa,WAAW,UAAU,kBAAkB;AACpE,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,QAAQ;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAClB,QAAI,YAAY;AAChB,QAAI;AACJ,UAAM,mBAAmB,KAAK;AAC9B,QAAI,KAAK,cAAc,QAAQ;AAC7B,kBAAY,KAAK;AACjB,kBAAY,KAAK;AAAA,IACnB,OAAO;AACL,kBAAY,KAAK;AACjB,kBAAY,KAAK;AAAA,IACnB;AACA,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,KAAK,SAAS;AACrB,QAAI,MAAM,QAAQ,GAAG,GAAG;AAEtB,aAAO,KAAK,OAAO,SAAS,KAAK,OAAO;AAAA,IAC1C,OAAO;AAOL,YAAM,UAAU,KAAK,WAAW,MAAM,IAAI,SAAS,CAAC;AACpD,UAAI,QAAQ,gBAAgB,QAAW;AACrC,gBAAQ,cAAc,mBACjB,QAAQ;AAAA,MAEf;AACA,UAAI,QAAQ,aAAa,QAAW;AAClC,gBAAQ,WAAW,QAAQ;AAAA,MAC7B;AAOA,aAAO,KAAK,OAAO,cAAc,SAAS,OAAO;AAAA,IACnD;AAAA,EACF;AAWF;AAAA;AATE,cAnMI,gBAmMG,QAAO,SAAS,sBAAsB,mBAAmB;AAC9D,SAAO,KAAK,qBAAqB,gBAAkB,SAAS,QAAQ,GAAM,SAAY,QAAQ,GAAM,SAAY,aAAa,GAAM,SAAY,QAAQ,CAAC,CAAC;AAC3J;AAAA;AAEA,cAvMI,gBAuMG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,eAAc;AAAA,EACvB,YAAY;AACd,CAAC;AA3MH,IAAM,gBAAN;AAAA,CA6MC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,MACT,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,eAAe,CAAC,WAAW,UAAU,uBAAuB;AAChE,MAAI,aAAa,OAAO;AACtB,WAAO;AAAA,EACT;AACA,MAAI,uBAAuB,QAAW;AACpC,WAAO;AAAA,EACT;AACA,MAAI,cAAc,aAAa,cAAc,QAAQ;AACnD,WAAO;AAAA,EACT,WAAW,cAAc,UAAU,aAAa,MAAM;AACpD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,oBAAoB;AAC1B,IAAM,mBAAmB;AACzB,IAAM,UAAN,MAAM,QAAO;AAAA,EACX,IAAI,KAAK,UAAU;AACjB,UAAM,IAAI,UAAU;AACpB,QAAI,GAAG;AACL,aAAO,EAAE,IAAI,KAAK,QAAQ;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,KAAK,UAAU;AACxB,UAAM,IAAI,UAAU;AACpB,QAAI,GAAG;AACL,aAAO,EAAE,WAAW,KAAK,QAAQ;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,KAAK,UAAU;AACvB,UAAM,IAAI,UAAU;AACpB,QAAI,GAAG;AACL,aAAO,EAAE,UAAU,KAAK,QAAQ;AAAA,IAClC;AACA,WAAO;AAAA,EACT;AAWF;AAAA;AATE,cAvBI,SAuBG,QAAO,SAAS,eAAe,mBAAmB;AACvD,SAAO,KAAK,qBAAqB,SAAQ;AAC3C;AAAA;AAEA,cA3BI,SA2BG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,QAAO;AAAA,EAChB,YAAY;AACd,CAAC;AA/BH,IAAM,SAAN;AAAA,CAiCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,cAAc,IAAI,eAAe,YAAY;AACnD,IAAM,YAAY,MAAM;AACtB,MAAI,OAAO,WAAW,aAAa;AACjC,UAAM,QAAQ,OAAO;AACrB,QAAI,+BAAO,QAAQ;AACjB,aAAO,MAAM;AAAA,IACf;AAAA,EACF;AACA,SAAO;AACT;AAsBA,IAAM,YAAN,MAAgB;AAAA,EACd;AAAA,EACA,YAAY,OAAO,CAAC,GAAG;AACrB,SAAK,OAAO;AACZ,YAAQ,KAAK,gLAAgL;AAAA,EAC/L;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,IAAI,OAAO;AACT,WAAO,KAAK,KAAK,KAAK;AAAA,EACxB;AACF;AAGA,IAAM,mBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,MAAM;AAAA,EACpB,iBAAiB,OAAO,cAAc;AAAA,EACtC,SAAS,OAAO,WAAW;AAAA,EAC3B,OAAO,qBAAqB,UAAU,qBAAqB;AAvxB7D,QAAAA;AAwxBI,WAAO,IAAI,yBAAyB,qBAAqB,UAAU,KAAK,gBAAgB,KAAK,MAAM,sBAAqBA,MAAA,KAAK,OAAO,mBAAZ,OAAAA,MAA8B,KAAK;AAAA,EAC7J;AAUF;AAAA;AARE,cARI,kBAQG,QAAO,SAAS,wBAAwB,mBAAmB;AAChE,SAAO,KAAK,qBAAqB,kBAAiB;AACpD;AAAA;AAEA,cAZI,kBAYG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,iBAAgB;AAC3B,CAAC;AAfH,IAAM,kBAAN;AAAA,CAiBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,2BAAN,MAA+B;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW,oBAAI,QAAQ;AAAA,EACvB,cAAc,oBAAI,QAAQ;AAAA,EAC1B,YAAY,qBAAqB,UAAU,gBAAgB,MAAM,qBAAqB,sBAAsB;AAC1G,SAAK,sBAAsB;AAC3B,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,OAAO;AACZ,SAAK,sBAAsB;AAC3B,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,gBAAgB,WAAW,WAAW,QAAQ,YAAY;AACxD,WAAO,KAAK,KAAK,IAAI,MAAM;AACzB,aAAO,IAAI,QAAQ,aAAW;AAC5B,cAAM,iBAAiB,mBAClB;AAUL,YAAI,KAAK,wBAAwB,QAAW;AAC1C,yBAAe,KAAK,mBAAmB,IAAI;AAAA,QAC7C;AACA,cAAM,KAAK,WAAW,KAAK,MAAM,KAAK,qBAAqB,KAAK,UAAU,KAAK,gBAAgB,KAAK,UAAU,KAAK,aAAa,WAAW,WAAW,gBAAgB,YAAY,KAAK,qBAAqB,KAAK,oBAAoB;AACrO,gBAAQ,EAAE;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,YAAY,WAAW;AACvC,WAAO,KAAK,KAAK,IAAI,MAAM;AACzB,aAAO,IAAI,QAAQ,aAAW;AAC5B,cAAM,eAAe,KAAK,SAAS,IAAI,SAAS;AAChD,YAAI,cAAc;AAChB,uBAAa,QAAQ;AACrB,eAAK,SAAS,OAAO,SAAS;AAC9B,gBAAM,eAAe,KAAK,YAAY,IAAI,SAAS;AACnD,cAAI,cAAc;AAChB,yBAAa;AACb,iBAAK,YAAY,OAAO,SAAS;AAAA,UACnC;AAAA,QACF;AACA,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,IAAM,aAAa,CAAC,MAAM,qBAAqB,UAAU,gBAAgB,UAAU,aAAa,WAAW,WAAW,QAAQ,YAAY,qBAAqB,yBAAyB;AAatL,QAAM,gBAAgB,SAAS,OAAO;AAAA,IACpC,WAAW,aAAa,MAAM;AAAA,IAC9B,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,eAAe,gBAAgB,WAAW;AAAA,IAC9C;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACD,QAAM,WAAW,aAAa;AAC9B,QAAM,cAAc,aAAa,SAAS;AAC1C,MAAI,QAAQ;AAQV,QAAI,uBAAuB,SAAS,mBAAmB,MAAM,QAAW;AACtE,cAAQ,MAAM,kBAAkB,mBAAmB,sCAAsC,UAAU,QAAQ,YAAY,CAAC,2BAA2B,mBAAmB,mBAAmB,UAAU,IAAI,GAAG;AAAA,IAC5M;AAMA,QAAI,yBAAyB,QAAQ,aAAa,aAAa,QAAW;AACxE,YAIIA,MAAA,QAHF;AAAA;AAAA,QACA;AAAA,MA54BR,IA84BUA,KADC,wBACDA,KADC;AAAA,QAFH;AAAA,QACA;AAAA;AAOF,iBAAW,OAAO,aAAa;AAC7B,qBAAa,SAAS,KAAK,YAAY,GAAG,CAAC;AAAA,MAC7C;AAQA,UAAI,UAAU,QAAW;AACvB,eAAO,OAAO,UAAU;AAAA,UACtB;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,YAAY,QAAW;AACzB,eAAO,OAAO,UAAU;AAAA,UACtB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,aAAO,OAAO,UAAU,MAAM;AAAA,IAChC;AAAA,EACF;AACA,MAAI,YAAY;AACd,eAAW,YAAY,YAAY;AACjC,kBAAY,UAAU,IAAI,QAAQ;AAAA,IACpC;AAAA,EACF;AACA,QAAM,eAAe,oBAAoB,MAAM,UAAU,WAAW;AACpE,YAAU,YAAY,WAAW;AACjC,iBAAe,WAAW,aAAa,QAAQ;AAC/C,WAAS,IAAI,aAAa,YAAY;AACtC,cAAY,IAAI,aAAa,YAAY;AACzC,SAAO;AACT;AACA,IAAM,aAAa,CAAC,sBAAsB,qBAAqB,sBAAsB,qBAAqB,qBAAqB;AAC/H,IAAM,sBAAsB,CAAC,MAAM,UAAU,YAAY;AACvD,SAAO,KAAK,IAAI,MAAM;AACpB,UAAM,cAAc,WAAW,OAAO,eAAa,OAAO,SAAS,SAAS,MAAM,UAAU,EAAE,IAAI,eAAa;AAC7G,YAAM,UAAU,QAAM,SAAS,SAAS,EAAE,GAAG,MAAM;AACnD,cAAQ,iBAAiB,WAAW,OAAO;AAC3C,aAAO,MAAM,QAAQ,oBAAoB,WAAW,OAAO;AAAA,IAC7D,CAAC;AACD,WAAO,MAAM,YAAY,QAAQ,QAAM,GAAG,CAAC;AAAA,EAC7C,CAAC;AACH;AACA,IAAM,iBAAiB,IAAI,eAAe,gBAAgB;AAC1D,IAAM,eAAe,YAAU;AAC7B,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,MAAM,CAAC,cAAc;AAAA,EACvB,CAAC;AACH;AACA,IAAM,6BAA6B,YAAU;AAC3C,SAAO,IAAI,UAAU,MAAM;AAC7B;AAKA,IAAM,cAAc,CAAC,KAAK,WAAW;AACnC,QAAM,YAAY,IAAI;AACtB,SAAO,QAAQ,UAAQ;AACrB,WAAO,eAAe,WAAW,MAAM;AAAA,MACrC,MAAM;AACJ,eAAO,KAAK,GAAG,IAAI;AAAA,MACrB;AAAA,MACA,IAAI,KAAK;AACP,aAAK,EAAE,kBAAkB,MAAM,KAAK,GAAG,IAAI,IAAI,GAAG;AAAA,MACpD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAM,eAAe,CAAC,KAAK,YAAY;AACrC,QAAM,YAAY,IAAI;AACtB,UAAQ,QAAQ,gBAAc;AAC5B,cAAU,UAAU,IAAI,WAAY;AAClC,YAAM,OAAO;AACb,aAAO,KAAK,EAAE,kBAAkB,MAAM,KAAK,GAAG,UAAU,EAAE,MAAM,KAAK,IAAI,IAAI,CAAC;AAAA,IAChF;AAAA,EACF,CAAC;AACH;AACA,IAAM,eAAe,CAAC,UAAU,IAAI,WAAW;AAC7C,SAAO,QAAQ,eAAa,SAAS,SAAS,IAAI,UAAU,IAAI,SAAS,CAAC;AAC5E;AAEA,SAAS,SAAS,MAAM;AACtB,QAAM,YAAY,SAAU,KAAK;AAC/B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,0BAA0B,QAAW;AACvC,4BAAsB;AAAA,IACxB;AACA,QAAI,QAAQ;AACV,kBAAY,KAAK,MAAM;AAAA,IACzB;AACA,QAAI,SAAS;AACX,mBAAa,KAAK,OAAO;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,iBAAiB,CAAC,aAAa,YAAY,SAAS,uBAAuB,mBAAmB,YAAY,mBAAmB,kBAAkB,SAAS,aAAa,UAAU,iBAAiB,kBAAkB,QAAQ,gBAAgB,eAAe,WAAW,iBAAiB,aAAa,QAAQ,MAAM;AACtT,IAAM,kBAAkB,CAAC,WAAW,WAAW,gBAAgB,eAAe;AAngC9E;AAogCA,IAAI,cAAa,WAAiB;AAAA,EAChC;AAAA;AAAA,EAEA;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA,YAAY,GAAG,GAAG,GAAG;AACnB,SAAK,IAAI;AACT,SAAK,KAAK,EAAE;AACZ,SAAK,GAAG,iBAAiB,YAAY,MAAM;AACzC,WAAK,YAAY;AACjB,QAAE,cAAc;AAAA,IAClB,CAAC;AACD,SAAK,GAAG,iBAAiB,cAAc,MAAM;AAC3C,WAAK,YAAY;AACjB,QAAE,cAAc;AAAA,IAClB,CAAC;AACD,iBAAa,MAAM,KAAK,IAAI,CAAC,wBAAwB,yBAAyB,yBAAyB,wBAAwB,cAAc,eAAe,eAAe,YAAY,CAAC;AAAA,EAC1L;AA2CF;AAzCE,cApBe,IAoBR,QAAO,SAAS,mBAAmB,mBAAmB;AAC3D,SAAO,KAAK,qBAAqB,IAAe,kBAAqB,iBAAiB,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,CAAC;AAC/J;AAEA,cAxBe,IAwBR,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,EAC3B,gBAAgB,SAAS,0BAA0B,IAAI,KAAK,UAAU;AACpE,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,UAAU,aAAa,CAAC;AAAA,IAC5C;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,IACjE;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,UAAU;AAAA,IACV,OAAO;AAAA,IACP,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,OAAO;AAAA,IACP,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,MAAM;AAAA,IACN,cAAc;AAAA,IACd,aAAa;AAAA,IACb,SAAS;AAAA,IACT,eAAe;AAAA,IACf,WAAW;AAAA,IACX,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA,YAAY;AACd,CAAC,IA5Dc;AA8DjB,aAAa,WAAW;AAAA,EAAC,SAAS;AAAA,IAChC,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAME,GAAG,UAAU;AAAA,CACf,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA;AAAA,MAEV,QAAQ;AAAA,IACV,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,eAAe,CAAC,YAAY,uBAAuB,sBAAsB,mBAAmB,eAAe,cAAc,YAAY,kBAAkB,kBAAkB,SAAS,aAAa,UAAU,kBAAkB,qBAAqB,UAAU,iBAAiB,kBAAkB,QAAQ,qBAAqB,gBAAgB,eAAe,SAAS;AACxW,IAAM,gBAAgB,CAAC,WAAW,WAAW,gBAAgB,iBAAiB,wBAAwB,sBAAsB;AAtmC5H,IAAAA;AAumCA,IAAI,YAAWA,MAAA,MAAe;AAAA,EAC5B;AAAA;AAAA,EAEA;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA,YAAY,GAAG,GAAG,GAAG;AACnB,SAAK,IAAI;AACT,SAAK,KAAK,EAAE;AACZ,SAAK,GAAG,iBAAiB,YAAY,MAAM;AACzC,WAAK,YAAY;AACjB,QAAE,cAAc;AAAA,IAClB,CAAC;AACD,SAAK,GAAG,iBAAiB,cAAc,MAAM;AAC3C,WAAK,YAAY;AACjB,QAAE,cAAc;AAAA,IAClB,CAAC;AACD,iBAAa,MAAM,KAAK,IAAI,CAAC,sBAAsB,uBAAuB,uBAAuB,sBAAsB,0BAA0B,cAAc,eAAe,eAAe,YAAY,CAAC;AAAA,EAC5M;AA4CF;AA1CE,cApBaA,KAoBN,QAAO,SAAS,iBAAiB,mBAAmB;AACzD,SAAO,KAAK,qBAAqBA,KAAa,kBAAqB,iBAAiB,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,CAAC;AAC7J;AAEA,cAxBaA,KAwBN,QAAyB,kBAAkB;AAAA,EAChD,MAAMA;AAAA,EACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,EACzB,gBAAgB,SAAS,wBAAwB,IAAI,KAAK,UAAU;AAClE,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,UAAU,aAAa,CAAC;AAAA,IAC5C;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,IACjE;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,UAAU;AAAA,IACV,qBAAqB;AAAA,IACrB,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,OAAO;AAAA,IACP,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,MAAM;AAAA,IACN,mBAAmB;AAAA,IACnB,cAAc;AAAA,IACd,aAAa;AAAA,IACb,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AACd,CAAC,IA7DYA;AA+Df,WAAW,WAAW;AAAA,EAAC,SAAS;AAAA,IAC9B,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAME,GAAG,QAAQ;AAAA,CACb,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA;AAAA,MAEV,QAAQ;AAAA,IACV,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,aAAa,CAAC,OAAO,MAAM,cAAc;AAC7C,MAAI,cAAc,QAAQ;AACxB,WAAO,QAAQ,OAAO,IAAI;AAAA,EAC5B,WAAW,cAAc,WAAW;AAClC,WAAO,WAAW,OAAO,IAAI;AAAA,EAC/B,OAAO;AACL,WAAO,QAAQ,OAAO,IAAI;AAAA,EAC5B;AACF;AACA,IAAM,UAAU,CAAC,OAAO,SAAS;AAC/B,UAAQ,MAAM,OAAO,OAAK,EAAE,YAAY,KAAK,OAAO;AACpD,QAAM,KAAK,IAAI;AACf,SAAO;AACT;AACA,IAAM,aAAa,CAAC,OAAO,SAAS;AAClC,QAAM,QAAQ,MAAM,QAAQ,IAAI;AAChC,MAAI,SAAS,GAAG;AACd,YAAQ,MAAM,OAAO,OAAK,EAAE,YAAY,KAAK,WAAW,EAAE,MAAM,KAAK,EAAE;AAAA,EACzE,OAAO;AACL,UAAM,KAAK,IAAI;AAAA,EACjB;AACA,SAAO;AACT;AACA,IAAM,UAAU,CAAC,OAAO,SAAS;AAC/B,QAAM,QAAQ,MAAM,QAAQ,IAAI;AAChC,MAAI,SAAS,GAAG;AACd,WAAO,MAAM,OAAO,OAAK,EAAE,YAAY,KAAK,WAAW,EAAE,MAAM,KAAK,EAAE;AAAA,EACxE,OAAO;AACL,WAAO,QAAQ,OAAO,IAAI;AAAA,EAC5B;AACF;AACA,IAAM,SAAS,CAAC,QAAQ,mBAAmB;AACzC,QAAM,UAAU,OAAO,cAAc,CAAC,GAAG,GAAG;AAAA,IAC1C,YAAY;AAAA,EACd,CAAC;AACD,SAAO,OAAO,aAAa,OAAO;AACpC;AACA,IAAM,cAAc,CAAC,cAAc,gBAAgB;AACjD,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,SAAO,aAAa,YAAY,YAAY;AAC9C;AACA,IAAM,iBAAiB,CAAC,WAAW,QAAQ;AACzC,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AACA,QAAM,WAAW,WAAW,GAAG;AAC/B,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,QAAI,KAAK,UAAU,QAAQ;AACzB,aAAO,SAAS,CAAC;AAAA,IACnB;AACA,QAAI,SAAS,CAAC,MAAM,UAAU,CAAC,GAAG;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,aAAa,UAAQ;AACzB,SAAO,KAAK,MAAM,GAAG,EAAE,IAAI,OAAK,EAAE,KAAK,CAAC,EAAE,OAAO,OAAK,MAAM,EAAE;AAChE;AACA,IAAM,cAAc,UAAQ;AAC1B,MAAI,MAAM;AACR,SAAK,IAAI,QAAQ;AACjB,SAAK,eAAe;AAAA,EACtB;AACF;AAGA,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ,CAAC;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,YAAY,YAAY,aAAa,QAAQ,SAAS,MAAM,UAAU;AACpE,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,aAAa,eAAe,SAAY,WAAW,UAAU,IAAI;AAAA,EACxE;AAAA,EACA,WAAW,KAAK,gBAAgB;AAlyClC,QAAAA;AAmyCI,UAAM,MAAM,OAAO,KAAK,QAAQ,cAAc;AAC9C,UAAM,WAAUA,MAAA,2BAAK,aAAL,gBAAAA,IAAe;AAC/B,UAAM,iBAAiB,oBAAoB,KAAK,MAAM,IAAI,UAAU,OAAO;AAC3E,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,SAAS,eAAe,KAAK,YAAY,GAAG;AAAA,MAC5C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB,gBAAgB;AAC9B,UAAM,kBAAkB,OAAO,KAAK,QAAQ,cAAc;AAC1D,UAAM,OAAO,KAAK,MAAM,KAAK,QAAM,GAAG,QAAQ,eAAe;AAC7D,QAAI,MAAM;AACR,WAAK,IAAI,kBAAkB,SAAS;AAAA,IACtC;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,cAAc;AAvzC1B,QAAAA,KAAA;AAwzCI,UAAM,gBAAgB,KAAK,QAAQ,kBAAkB;AACrD,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,cAAc,KAAK;AACzB,UAAM,YAAY,YAAY,cAAc,WAAW;AACvD,QAAI,WAAW;AACb,kBAAY;AACZ,kBAAY;AAAA,IACd;AACA,UAAM,gBAAgB,KAAK,MAAM,MAAM;AACvC,QAAI;AACJ,UAAM,SAAS,KAAK;AAEpB,QAAI,OAAO,sBAAsB;AAC/B,0BAAoB,OAAO,qBAAqB;AAAA,IAElD,YAAWA,MAAA,OAAO,gBAAP,gBAAAA,IAAoB,OAAO;AACpC,0BAAoB,OAAO,YAAY;AAAA,IACzC;AAQA,SAAI,4DAAmB,WAAnB,mBAA2B,YAAY;AACzC,UAAI,KAAK,MAAM,SAAS,GAAG;AACzB,aAAK,MAAM,OAAO,IAAI,CAAC;AAAA,MACzB;AAAA,IACF;AACA,UAAM,SAAS,KAAK,MAAM,SAAS,YAAY;AAC/C,UAAM,QAAQ,KAAK,WAAW,cAAc,SAAS;AAIrD,QAAI,CAAC,QAAQ;AACX,mBAAa,IAAI,kBAAkB,cAAc;AAAA,IACnD;AAQA,UAAM,kBAAkB,aAAa;AACrC,QAAI,qBAAqB,UAAa,cAAc,UAAU,CAAC,aAAa,oBAAoB,QAAW;AACzG,yBAAmB;AAAA,IACrB;AAKA,QAAI,aAAa;AACf,kBAAY,mBAAmB;AAAA,IACjC;AAEA,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,aAAO,KAAK,KAAK,MAAM;AAGrB,YAAI,aAAa;AACf,sBAAY,IAAI,kBAAkB,OAAO;AAAA,QAC3C;AAEA,qBAAa,IAAI,kBAAkB,SAAS;AAC5C,eAAO,KAAK,WAAW,cAAc,aAAa,WAAW,KAAK,UAAU,CAAC,GAAG,OAAO,gBAAgB,EAAE,KAAK,MAAM,aAAa,cAAc,OAAO,eAAe,KAAK,UAAU,KAAK,IAAI,CAAC,EAAE,KAAK,OAAO;AAAA,UAC1M;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,EAAE;AAAA,MACJ,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,UAAU,MAAM,UAAU,KAAK,iBAAiB,GAAG;AACjD,WAAO,KAAK,SAAS,OAAO,EAAE,SAAS;AAAA,EACzC;AAAA,EACA,IAAI,MAAM,UAAU,KAAK,iBAAiB,GAAG;AAC3C,WAAO,KAAK,KAAK,IAAI,MAAM;AA34C/B,UAAAA,KAAA;AA44CM,YAAM,QAAQ,KAAK,SAAS,OAAO;AACnC,UAAI,MAAM,UAAU,MAAM;AACxB,eAAO,QAAQ,QAAQ,KAAK;AAAA,MAC9B;AACA,YAAM,OAAO,MAAM,MAAM,SAAS,OAAO,CAAC;AAC1C,UAAI,MAAM,KAAK;AACf,YAAM,gBAAgB,KAAK;AAC3B,UAAI,eAAe;AACjB,cAAM,gBAAgB,cAAc,IAAI,SAAS;AACjD,aAAI,MAAAA,MAAA,+CAAe,UAAf,gBAAAA,IAAsB,iBAAtB,mBAAoC,SAAS,KAAK;AACpD,gBAAM,cAAc,MAAM,aAAa,SAAS;AAAA,QAClD;AAAA,MACF;AACA,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,KAAK,QAAQ,kBAAkB;AACnC,aAAO,KAAK,QAAQ,aAAa,KAAK,iCACjC,KAAK,cAD4B;AAAA,QAEpC,WAAW;AAAA,MACb,EAAC,EAAE,KAAK,MAAM,IAAI;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AACpB,UAAM,cAAc,KAAK;AACzB,QAAI,aAAa;AACf,YAAM,QAAQ,KAAK,SAAS,YAAY,OAAO;AAC/C,YAAM,eAAe,MAAM,MAAM,SAAS,CAAC;AAC3C,YAAM,kBAAkB,aAAa;AACrC,aAAO,KAAK,KAAK,MAAM;AACrB,eAAO,KAAK;AAAA,UAAW;AAAA;AAAA,UAEvB;AAAA;AAAA,UAEA;AAAA,UAAQ,KAAK,UAAU,CAAC;AAAA,UAAG;AAAA,UAAM;AAAA,QAAe;AAAA,MAClD,CAAC;AAAA,IACH;AACA,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA,EACA,kBAAkB,gBAAgB;AAChC,QAAI,gBAAgB;AAClB,WAAK,iBAAiB;AACtB,WAAK,IAAI,CAAC;AAAA,IACZ,WAAW,KAAK,YAAY;AAC1B,cAAQ,KAAK,YAAY,KAAK,OAAO,KAAK,OAAO,KAAK,UAAU,KAAK,IAAI;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,WAAW,SAAS;AAClB,UAAM,QAAQ,KAAK,SAAS,OAAO;AACnC,WAAO,MAAM,SAAS,IAAI,MAAM,MAAM,SAAS,CAAC,IAAI;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,SAAS;AAClB,UAAM,QAAQ,KAAK,SAAS,OAAO;AACnC,WAAO,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI;AAAA,EACvC;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,aAAa,KAAK,WAAW,UAAU;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA,EACA,UAAU;AAER,SAAK,cAAc;AACnB,SAAK,MAAM,QAAQ,WAAW;AAC9B,SAAK,aAAa;AAClB,SAAK,QAAQ,CAAC;AAAA,EAChB;AAAA,EACA,SAAS,SAAS;AAChB,WAAO,KAAK,MAAM,OAAO,OAAK,EAAE,YAAY,OAAO;AAAA,EACrD;AAAA,EACA,WAAW,cAAc,WAAW;AAClC,SAAK,aAAa;AAClB,SAAK,QAAQ,WAAW,KAAK,OAAO,cAAc,SAAS;AAC3D,WAAO,KAAK,MAAM,MAAM;AAAA,EAC1B;AAAA,EACA,WAAW,cAAc,aAAa,WAAW,YAAY,mBAAmB,kBAAkB;AAChG,QAAI,KAAK,gBAAgB;AACvB,WAAK,iBAAiB;AACtB,aAAO,QAAQ,QAAQ,KAAK;AAAA,IAC9B;AACA,QAAI,gBAAgB,cAAc;AAChC,aAAO,QAAQ,QAAQ,KAAK;AAAA,IAC9B;AACA,UAAM,aAAa,eAAe,aAAa,UAAU;AACzD,UAAM,YAAY,cAAc,YAAY,UAAU;AACtD,UAAM,cAAc,KAAK;AACzB,QAAI,cAAc,eAAe,WAAW;AAC1C,iBAAW,UAAU,IAAI,UAAU;AACnC,iBAAW,UAAU,IAAI,oBAAoB;AAC7C,UAAI,YAAY,QAAQ;AACtB,eAAO,YAAY,OAAO,YAAY,WAAW;AAAA,UAC/C,UAAU,cAAc,SAAY,IAAI;AAAA,UACxC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO,QAAQ,QAAQ,KAAK;AAAA,EAC9B;AAAA,EACM,KAAK,MAAM;AAAA;AACf,UAAI,KAAK,gBAAgB,QAAW;AAClC,cAAM,KAAK;AACX,aAAK,cAAc;AAAA,MACrB;AACA,YAAM,UAAU,KAAK,cAAc,KAAK;AACxC,cAAQ,QAAQ,MAAM,KAAK,cAAc,MAAS;AAClD,aAAO;AAAA,IACT;AAAA;AACF;AACA,IAAM,eAAe,CAAC,aAAa,OAAO,eAAe,UAAU,SAAS;AAC1E,MAAI,OAAO,0BAA0B,YAAY;AAC/C,WAAO,IAAI,QAAQ,aAAW;AAC5B,4BAAsB,MAAM;AAC1B,gBAAQ,aAAa,OAAO,eAAe,UAAU,IAAI;AACzD,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,SAAO,QAAQ,QAAQ;AACzB;AACA,IAAM,UAAU,CAAC,aAAa,OAAO,eAAe,UAAU,SAAS;AAKrE,OAAK,IAAI,MAAM,cAAc,OAAO,UAAQ,CAAC,MAAM,SAAS,IAAI,CAAC,EAAE,QAAQ,WAAW,CAAC;AACvF,QAAM,QAAQ,UAAQ;AAUpB,UAAM,wBAAwB,SAAS,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC;AAC1D,UAAM,0BAA0B,sBAAsB,MAAM,GAAG,EAAE,CAAC;AAClE,QAAI,SAAS,eAAe,KAAK,QAAQ,yBAAyB;AAChE,YAAM,UAAU,KAAK;AACrB,cAAQ,aAAa,eAAe,MAAM;AAC1C,cAAQ,UAAU,IAAI,iBAAiB;AACvC,WAAK,IAAI,kBAAkB,OAAO;AAAA,IACpC;AAAA,EACF,CAAC;AACH;AAIA,IAAM,mBAAN,MAAM,iBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,WAAW,oBAAI,QAAQ;AAAA;AAAA,EAEvB,yBAAyB,IAAI,gBAAgB,IAAI;AAAA,EACjD,YAAY;AAAA;AAAA,EAEZ,IAAI,wBAAwB;AAC1B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB;AAAA;AAAA;AAAA;AAAA,EAIlB,OAAO;AAAA;AAAA,EAEP,kBAAkB,IAAI,aAAa;AAAA;AAAA,EAEnC,iBAAiB,IAAI,aAAa;AAAA;AAAA,EAElC,iBAAiB,IAAI,aAAa;AAAA;AAAA,EAElC,mBAAmB,IAAI,aAAa;AAAA,EACpC,iBAAiB,OAAO,sBAAsB;AAAA,EAC9C,WAAW,OAAO,gBAAgB;AAAA,EAClC,sBAAsB,OAAO,mBAAmB;AAAA,EAChD,cAAc,OAAO,cAAc;AAAA,IACjC,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,mCAAmC;AAAA;AAAA,EAEnC,SAAS,OAAO,MAAM;AAAA,EACtB,UAAU,OAAO,aAAa;AAAA,EAC9B,IAAI,UAAU,WAAW;AACvB,SAAK,SAAS,YAAY;AAAA,EAC5B;AAAA,EACA,IAAI,SAAS,UAAU;AACrB,SAAK,SAAS,WAAW;AAAA,EAC3B;AAAA,EACA,IAAI,aAAa,OAAO;AACtB,SAAK,gBAAgB;AACrB,SAAK,SAAS,eAAe,QAAQ;AAAA,MACnC,UAAU,MAAM,KAAK,UAAU,UAAU,CAAC,KAAK,CAAC,KAAK,UAAU,eAAe;AAAA,MAC9E,SAAS,MAAM,KAAK,UAAU,oBAAoB;AAAA,MAClD,OAAO,oBAAkB,KAAK,UAAU,kBAAkB,cAAc;AAAA,IAC1E,IAAI;AAAA,EACN;AAAA,EACA,YAAY,MAAM,MAAM,gBAAgB,YAAY,QAAQ,MAAM,gBAAgB,cAAc;AAC9F,SAAK,eAAe;AACpB,SAAK,WAAW,WAAW;AAC3B,SAAK,OAAO,QAAQ;AACpB,SAAK,aAAa,SAAS,SAAS,OAAO,QAAQ,cAAc,IAAI;AACrE,SAAK,YAAY,IAAI,gBAAgB,KAAK,YAAY,KAAK,UAAU,QAAQ,KAAK,SAAS,MAAM,cAAc;AAC/G,SAAK,eAAe,qBAAqB,KAAK,MAAM,IAAI;AAAA,EAC1D;AAAA,EACA,cAAc;AA1mDhB,QAAAA;AA2mDI,SAAK,UAAU,QAAQ;AACvB,KAAAA,MAAA,KAAK,gBAAL,gBAAAA,IAAkB,yBAAyB;AAAA,EAC7C;AAAA,EACA,aAAa;AACX,WAAO,KAAK,eAAe,WAAW,KAAK,IAAI;AAAA,EACjD;AAAA,EACA,WAAW;AACT,SAAK,yBAAyB;AAAA,EAChC;AAAA;AAAA,EAEA,2BAA2B;AACzB,QAAI,CAAC,KAAK,WAAW;AAGnB,YAAM,UAAU,KAAK,WAAW;AAChC,UAAI,mCAAS,OAAO;AAClB,aAAK,aAAa,QAAQ,OAAO,QAAQ,QAAQ;AAAA,MACnD;AAAA,IACF;AACA,QAAI,QAAQ,aAAW,iBAAiB,KAAK,UAAU,OAAO,CAAC,EAAE,KAAK,MAAM;AAC1E,UAAI,KAAK,kBAAkB,QAAW;AACpC,aAAK,eAAe,KAAK,OAAO,WAAW,oBAAoB,KAAK,SAAS,SAAS,KAAK;AAAA,MAC7F;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,YAAY;AACd,QAAI,CAAC,KAAK,WAAW;AACnB,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC3C;AACA,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,iBAAiB;AACnB,QAAI,CAAC,KAAK,WAAW;AACnB,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC3C;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,qBAAqB;AACvB,QAAI,KAAK,iBAAiB;AACxB,aAAO,KAAK,gBAAgB,SAAS;AAAA,IACvC;AACA,WAAO,CAAC;AAAA,EACV;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,UAAM,IAAI,MAAM,6BAA6B;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,MAAM,iBAAiB;AAC5B,UAAM,IAAI,MAAM,6BAA6B;AAAA,EAC/C;AAAA,EACA,aAAa;AACX,QAAI,KAAK,WAAW;AAClB,UAAI,KAAK,eAAe;AAEtB,cAAM,UAAU,KAAK,WAAW;AAChC,aAAK,cAAc,YAAY,IAAI,IAAI,QAAQ,SAAS,UAAU,CAAC;AASnE,cAAM,gBAAgB,KAAK,cAAc,UAAU,IAAI,SAAS;AAChE,YAAI,iBAAiB,QAAQ,OAAO;AAClC,wBAAc,QAAQ,mBACjB,QAAQ;AAAA,QAEf;AAKA,aAAK,cAAc,cAAc,CAAC;AAClC,YAAI,QAAQ,OAAO;AACjB,gBAAM,kBAAkB,QAAQ,MAAM;AACtC,eAAK,cAAc,YAAY,cAAc,gBAAgB;AAC7D,eAAK,cAAc,YAAY,WAAW,gBAAgB;AAAA,QAC5D;AAAA,MACF;AACA,YAAM,IAAI,KAAK;AACf,WAAK,gBAAgB;AACrB,WAAK,YAAY;AACjB,WAAK,kBAAkB;AACvB,WAAK,iBAAiB,KAAK,CAAC;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,aAAa,gBAAgB,qBAAqB;AA5sDpD,QAAAA,KAAA;AA6sDI,QAAI,KAAK,aAAa;AACpB,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC/D;AACA,SAAK,kBAAkB;AACvB,QAAI;AACJ,QAAI,eAAe,KAAK,UAAU,gBAAgB,cAAc;AAChE,QAAI,cAAc;AAChB,eAAS,KAAK,YAAY,aAAa;AACvC,YAAM,QAAQ,aAAa;AAC3B,UAAI,OAAO;AAGT,cAAM,UAAU,KAAK,WAAW;AAChC,gBAAQ,SAAS,UAAU,IAAI;AAAA,MACjC;AAEA,WAAK,0BAA0B,OAAO,UAAU,cAAc;AAAA,IAChE,OAAO;AACL,YAAM,WAAW,eAAe;AAOhC,YAAM,gBAAgB,KAAK,eAAe,mBAAmB,KAAK,IAAI,EAAE;AAGxE,YAAM,aAAa,IAAI,gBAAgB,IAAI;AAC3C,YAAM,sBAAsB,KAAK,0BAA0B,YAAY,cAAc;AACrF,YAAM,WAAW,IAAI,eAAe,qBAAqB,eAAe,KAAK,SAAS,QAAQ;AAE9F,YAAM,aAAYA,MAAA,SAAS,YAAY,cAArB,OAAAA,MAAkC,SAAS;AAS7D,eAAS,KAAK,YAAY,KAAK,cAAc,gBAAgB,WAAW;AAAA,QACtE,OAAO,KAAK,cAAc;AAAA,QAC1B;AAAA,QACA,qBAAqB,oDAAuB,KAAK;AAAA,MACnD,CAAC;AAED,iBAAW,KAAK,OAAO,QAAQ;AAQ/B,qBAAe,KAAK,UAAU,WAAW,KAAK,WAAW,cAAc;AAEvE,WAAK,SAAS,IAAI,OAAO,UAAU,mBAAmB;AACtD,WAAK,uBAAuB,KAAK;AAAA,QAC/B,WAAW,OAAO;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH;AACA,eAAK,gBAAL,mBAAkB,oCAAoC;AACtD,SAAK,gBAAgB;AAUrB,SAAK,QAAQ,aAAa,IAAI;AAC9B,UAAM,cAAc,KAAK,UAAU,cAAc;AACjD,SAAK,gBAAgB,KAAK;AAAA,MACxB;AAAA,MACA,WAAW,YAAY,cAAc,WAAW;AAAA,IAClD,CAAC;AACD,SAAK,UAAU,UAAU,YAAY,EAAE,KAAK,UAAQ;AAClD,WAAK,eAAe,KAAK,OAAO,QAAQ;AACxC,WAAK,eAAe,KAAK,IAAI;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,OAAO,GAAG,SAAS;AAC3B,WAAO,KAAK,UAAU,UAAU,MAAM,OAAO;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO,GAAG,SAAS;AACrB,WAAO,KAAK,UAAU,IAAI,MAAM,OAAO;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,SAAS;AAClB,UAAM,SAAS,KAAK,UAAU,WAAW,OAAO;AAChD,WAAO,SAAS,OAAO,MAAM;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,SAAS;AACxB,WAAO,KAAK,UAAU,WAAW,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,SAAS;AACnB,WAAO,KAAK,UAAU,WAAW,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AACjB,WAAO,KAAK,UAAU,iBAAiB;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B,YAAY,gBAAgB;AACpD,UAAM,QAAQ,IAAI,eAAe;AACjC,UAAM,kBAAkB,eAAe;AACvC,UAAM,eAAe,eAAe;AACpC,UAAM,WAAW,eAAe;AAChC,UAAM,SAAS,eAAe;AAC9B,UAAM,YAAY,eAAe;AAEjC,UAAM,YAAY,KAAK,gBAAgB,YAAY,UAAU;AAC7D,UAAM,iBAAiB,KAAK,gBAAgB,YAAY,eAAe;AACvE,UAAM,MAAM,KAAK,gBAAgB,YAAY,KAAK;AAClD,UAAM,SAAS,KAAK,gBAAgB,YAAY,QAAQ;AACxD,UAAM,cAAc,KAAK,gBAAgB,YAAY,aAAa;AAClE,UAAM,WAAW,KAAK,gBAAgB,YAAY,UAAU;AAC5D,UAAM,OAAO,KAAK,gBAAgB,YAAY,MAAM;AACpD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,YAAY,MAAM;AAChC,WAAO,WAAW;AAAA;AAAA,MAElB,OAAO,eAAa,CAAC,CAAC,SAAS;AAAA,MAAG,UAAU,eAAa,KAAK,uBAAuB,KAAK,OAAO,aAAW,YAAY,QAAQ,QAAQ,cAAc,SAAS,GAAG,UAAU,aAAW,WAAW,QAAQ,eAAe,IAAI,CAAC,GAAG,qBAAqB,CAAC,CAAC;AAAA,IAAC;AAAA,EAC3P;AAAA;AAAA;AAAA;AAAA,EAIA,0BAA0B,WAAW,gBAAgB;AACnD,UAAM,QAAQ,KAAK,SAAS,IAAI,SAAS;AACzC,QAAI,CAAC,OAAO;AACV,YAAM,IAAI,MAAM,+CAA+C;AAAA,IACjE;AACA,UAAM,kBAAkB,eAAe;AACvC,UAAM,eAAe,eAAe;AACpC,UAAM,WAAW,eAAe;AAChC,UAAM,SAAS,eAAe;AAC9B,UAAM,YAAY,eAAe;AACjC,SAAK,uBAAuB,KAAK;AAAA,MAC/B;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAyBF;AAAA;AAvBE,cA7UI,kBA6UG,QAAO,SAAS,wBAAwB,mBAAmB;AAChE,SAAO,KAAK,qBAAqB,kBAAoB,kBAAkB,MAAM,GAAM,kBAAkB,MAAM,GAAM,kBAAqB,QAAQ,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,cAAc,GAAM,kBAAkB,kBAAiB,EAAE,CAAC;AAC5U;AAAA;AAEA,cAjVI,kBAiVG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,EACjC,QAAQ;AAAA,IACN,UAAU;AAAA,IACV,WAAW;AAAA,IACX,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,EACpB;AAAA,EACA,UAAU,CAAC,QAAQ;AAAA,EACnB,YAAY;AACd,CAAC;AAnWH,IAAM,kBAAN;AAAA,CAqWC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA,MAEV,QAAQ,CAAC,YAAY,aAAa,QAAQ,cAAc;AAAA,IAC1D,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,MAAM;AAAA,MACf,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,GAAG;AAAA,QACD,MAAM;AAAA,QACN,MAAM,CAAC,MAAM;AAAA,MACf,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,GAAG;AAAA,QACD,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAqB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,OAAO,eAAe,QAAQ;AACxC,SAAK,QAAQ;AACb,SAAK,gBAAgB;AACrB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,IAAI,OAAO,eAAe;AACxB,QAAI,UAAU,gBAAgB;AAC5B,aAAO,KAAK;AAAA,IACd;AACA,QAAI,UAAU,wBAAwB;AACpC,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK,OAAO,IAAI,OAAO,aAAa;AAAA,EAC7C;AACF;AAEA,IAAM,eAAe,IAAI,eAAe,EAAE;AAe1C,IAAM,8BAAN,MAAM,4BAA2B;AAAA,EAC/B,0BAA0B,oBAAI,IAAI;AAAA,EAClC,oCAAoC,QAAQ;AAC1C,SAAK,yBAAyB,MAAM;AACpC,SAAK,qBAAqB,MAAM;AAAA,EAClC;AAAA,EACA,yBAAyB,QAAQ;AAx/DnC,QAAAA;AAy/DI,KAAAA,MAAA,KAAK,wBAAwB,IAAI,MAAM,MAAvC,gBAAAA,IAA0C;AAC1C,SAAK,wBAAwB,OAAO,MAAM;AAAA,EAC5C;AAAA,EACA,qBAAqB,QAAQ;AAC3B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,mBAAmB,cAAc,CAAC,eAAe,aAAa,eAAe,QAAQ,eAAe,IAAI,CAAC,EAAE,KAAK,UAAU,CAAC,CAAC,aAAa,QAAQ,IAAI,GAAG,UAAU;AACtK,aAAO,iDACF,cACA,SACA;AAIL,UAAI,UAAU,GAAG;AACf,eAAO,GAAG,IAAI;AAAA,MAChB;AAIA,aAAO,QAAQ,QAAQ,IAAI;AAAA,IAC7B,CAAC,CAAC,EAAE,UAAU,UAAQ;AAGpB,UAAI,CAAC,OAAO,eAAe,CAAC,OAAO,yBAAyB,OAAO,mBAAmB,kBAAkB,eAAe,cAAc,MAAM;AACzI,aAAK,yBAAyB,MAAM;AACpC;AAAA,MACF;AACA,YAAM,SAAS,qBAAqB,eAAe,SAAS;AAC5D,UAAI,CAAC,QAAQ;AACX,aAAK,yBAAyB,MAAM;AACpC;AAAA,MACF;AACA,iBAAW;AAAA,QACT;AAAA,MACF,KAAK,OAAO,QAAQ;AAClB,eAAO,sBAAsB,SAAS,cAAc,KAAK,YAAY,CAAC;AAAA,MACxE;AAAA,IACF,CAAC;AACD,SAAK,wBAAwB,IAAI,QAAQ,gBAAgB;AAAA,EAC3D;AAUF;AAAA;AARE,cAlDI,6BAkDG,QAAO,SAAS,mCAAmC,mBAAmB;AAC3E,SAAO,KAAK,qBAAqB,6BAA4B;AAC/D;AAAA;AAEA,cAtDI,6BAsDG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,4BAA2B;AACtC,CAAC;AAzDH,IAAM,6BAAN;AAAA,CA2DC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,+BAA+B,MAAM;AACzC,SAAO;AAAA,IACL,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,EACf;AACF;AACA,SAAS,6BAA6B,QAAQ;AAK5C,MAAI,iCAAQ,8BAA8B;AACxC,WAAO,IAAI,2BAA2B;AAAA,EACxC;AACA,SAAO;AACT;AACA,IAAM,qBAAqB,CAAC,SAAS,eAAe,YAAY,QAAQ,QAAQ,mBAAmB,QAAQ,MAAM;AAnkEjH,IAAAA;AAokEA,IAAI,iBAAgBA,MAAA,MAAoB;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,cAAc,SAASC,SAAQ,GAAG,GAAG,GAAG;AAClD,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,SAAK,SAASA;AACd,SAAK,IAAI;AACT,SAAK,IAAI;AACT,MAAE,OAAO;AACT,SAAK,KAAK,KAAK,EAAE;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,IAAI;AAvlEd,QAAAD;AAwlEI,UAAM,cAAc,KAAK,eAAe,KAAK,OAAO,IAAI,uBAAuB;AAC/E,SAAIA,MAAA,KAAK,iBAAL,gBAAAA,IAAmB,aAAa;AAClC,WAAK,QAAQ,aAAa,QAAQ,QAAW,QAAW,KAAK,eAAe;AAC5E,WAAK,aAAa,IAAI;AACtB,SAAG,eAAe;AAAA,IACpB,WAAW,eAAe,MAAM;AAC9B,WAAK,QAAQ,aAAa,aAAa;AAAA,QACrC,WAAW,KAAK;AAAA,MAClB,CAAC;AACD,SAAG,eAAe;AAAA,IACpB;AAAA,EACF;AA2BF;AAzBE,cAjCkBA,KAiCX,QAAO,SAAS,sBAAsB,mBAAmB;AAC9D,SAAO,KAAK,qBAAqBA,KAAkB,kBAAkB,iBAAiB,CAAC,GAAM,kBAAkB,aAAa,GAAM,kBAAkB,MAAM,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,iBAAiB,CAAC;AAC/Q;AAEA,cArCkBA,KAqCX,QAAyB,kBAAkB;AAAA,EAChD,MAAMA;AAAA,EACN,cAAc,SAAS,2BAA2B,IAAI,KAAK;AACzD,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,SAAS,SAAS,uCAAuC,QAAQ;AAC7E,eAAO,IAAI,QAAQ,MAAM;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,IACb,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA,YAAY;AACd,CAAC,IAzDiBA;AA2DpB,gBAAgB,WAAW,CAAC,SAAS;AAAA,EACnC,QAAQ;AACV,CAAC,CAAC,GAAG,aAAa;AAAA,CACjB,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,QAAQ;AAAA,IACV,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,IAAM,+BAAN,MAAM,6BAA4B;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB;AAAA,EACA,YAAY,kBAAkB,SAAS,YAAY,QAAQ,YAAY;AACrE,SAAK,mBAAmB;AACxB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,WAAW;AACT,SAAK,uBAAuB;AAC5B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,cAAc;AACZ,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,iBAAiB;AAEf,UAAM,kBAAkB,CAAC,mBAAmB,kBAAkB,cAAc,YAAY,kBAAkB,YAAY,mBAAmB,mBAAmB,sBAAsB,gBAAgB;AAClM,UAAM,cAAc,KAAK,WAAW;AACpC,QAAI,gBAAgB,SAAS,YAAY,OAAO,GAAG;AACjD,UAAI,YAAY,aAAa,UAAU,MAAM,KAAK;AAChD,oBAAY,gBAAgB,UAAU;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAAA,EACA,yBAAyB;AAjtE3B,QAAAA;AAktEI,SAAIA,MAAA,KAAK,eAAL,gBAAAA,IAAiB,SAAS;AAC5B,YAAM,OAAO,KAAK,iBAAiB,mBAAmB,KAAK,OAAO,aAAa,KAAK,WAAW,OAAO,CAAC;AACvG,WAAK,WAAW,cAAc,OAAO;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,IAAI;AACV,SAAK,QAAQ,aAAa,KAAK,iBAAiB,QAAW,QAAW,KAAK,eAAe;AAS1F,OAAG,eAAe;AAAA,EACpB;AAuBF;AAAA;AArBE,cA/DI,8BA+DG,QAAO,SAAS,oCAAoC,mBAAmB;AAC5E,SAAO,KAAK,qBAAqB,8BAAgC,kBAAqB,gBAAgB,GAAM,kBAAkB,aAAa,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,YAAY,CAAC,CAAC;AAC5P;AAAA;AAEA,cAnEI,8BAmEG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,cAAc,IAAI,GAAG,KAAK,GAAG,MAAM,CAAC;AAAA,EACrD,cAAc,SAAS,yCAAyC,IAAI,KAAK;AACvE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,SAAS,SAAS,qDAAqD,QAAQ;AAC3F,eAAO,IAAI,QAAQ,MAAM;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,EACnB;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,oBAAoB;AACpC,CAAC;AAnFH,IAAM,8BAAN;AAAA,CAqFC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,MACT,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,uCAAN,MAAM,qCAAoC;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB;AAAA,EACA,YAAY,kBAAkB,SAAS,YAAY,QAAQ,YAAY;AACrE,SAAK,mBAAmB;AACxB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,WAAW;AACT,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,cAAc;AACZ,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,yBAAyB;AApzE3B,QAAAA;AAqzEI,SAAIA,MAAA,KAAK,eAAL,gBAAAA,IAAiB,SAAS;AAC5B,YAAM,OAAO,KAAK,iBAAiB,mBAAmB,KAAK,OAAO,aAAa,KAAK,WAAW,OAAO,CAAC;AACvG,WAAK,WAAW,cAAc,OAAO;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,SAAK,QAAQ,aAAa,KAAK,iBAAiB,QAAW,QAAW,KAAK,eAAe;AAAA,EAC5F;AAuBF;AAAA;AArBE,cAlCI,sCAkCG,QAAO,SAAS,4CAA4C,mBAAmB;AACpF,SAAO,KAAK,qBAAqB,sCAAwC,kBAAqB,gBAAgB,GAAM,kBAAkB,aAAa,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,YAAY,CAAC,CAAC;AACpQ;AAAA;AAEA,cAtCI,sCAsCG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,KAAK,cAAc,EAAE,GAAG,CAAC,QAAQ,cAAc,EAAE,CAAC;AAAA,EAC/D,cAAc,SAAS,iDAAiD,IAAI,KAAK;AAC/E,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,SAAS,SAAS,+DAA+D;AAC7F,eAAO,IAAI,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,EACnB;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,oBAAoB;AACpC,CAAC;AAtDH,IAAM,sCAAN;AAAA,CAwDC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qCAAqC,CAAC;AAAA,IAC5G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,MACT,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,aAAa,CAAC,YAAY,aAAa,QAAQ,cAAc,cAAc;AACjF,IAAM,cAAc,CAAC,QAAQ,UAAU,eAAe,OAAO,SAAS,aAAa,eAAe,WAAW,YAAY,aAAa,cAAc,aAAa,aAAa;AA13E9K,IAAAA;AA23EA,IAAI,UAASA,MAAA,MAAa;AAAA,EACxB;AAAA,EACA;AAAA,EACA,YAAY,KAAK,qBAAqB,UAAU,iBAAiB,GAAG,GAAG;AACrE,SAAK,IAAI;AACT,MAAE,OAAO;AACT,SAAK,KAAK,IAAI;AACd,QAAI,cAAc,WAAW,gBAAgB,OAAO,qBAAqB,QAAQ;AACjF,iBAAa,MAAM,KAAK,IAAI,CAAC,mBAAmB,kBAAkB,CAAC;AAAA,EACrE;AAiBF;AAfE,cAXWA,KAWJ,QAAO,SAAS,eAAe,mBAAmB;AACvD,SAAO,KAAK,qBAAqBA,KAAW,kBAAqB,UAAU,GAAM,kBAAqB,mBAAmB,GAAM,kBAAqB,QAAQ,GAAM,kBAAkB,eAAe,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,iBAAiB,CAAC;AACnR;AAEA,cAfWA,KAeJ,QAAyB,kBAAkB;AAAA,EAChD,MAAMA;AAAA,EACN,QAAQ;AAAA,IACN,UAAU;AAAA,IACV,WAAW;AAAA,IACX,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AACd,CAAC,IAzBUA;AA2Bb,SAAS,WAAW,CAAC,SAAS;AAAA,EAC5B,QAAQ;AAAA,EACR,SAAS;AACX,CAAC,CAAC,GAAG,MAAM;AAAA,CACV,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,QAAQ;AAAA,IACV,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAGH,IAAM,WAAN,MAAM,SAAQ;AAAA,EACZ;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA,EAIrC,mBAAmB,IAAI,aAAa;AAAA,EACpC,aAAa;AAAA,EACb,SAAS;AAAA,EACT;AAAA,EACA;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,kBAAkB;AAQhB,UAAM,WAAW,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,QAAQ;AAC1D,QAAI,UAAU;AACZ,WAAK,SAAS;AACd,WAAK,aAAa,SAAS,GAAG;AAC9B,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,wBAAwB;AACtB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAAA,IAChB;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,UAAU,aAAa;AAC7B,QAAI,aAAa,YAAY,QAAW;AACtC,WAAK,kBAAkB,KAAK;AAAA,QAC1B,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AAAA,IACf;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,UAAU,aAAa;AAC7B,QAAI,aAAa,YAAY,QAAW;AACtC,UAAI,KAAK,QAAQ;AACf,aAAK,OAAO,cAAc;AAAA,MAC5B;AACA,WAAK,iBAAiB,KAAK;AAAA,QACzB,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,OAAO,YAAY;AACjB,UAAM,cAAc,OAAO,eAAe;AAC1C,UAAM,MAAM,cAAc,aAAa,WAAW,OAAO;AAMzD,QAAI,KAAK,QAAQ;AACf,WAAK,aAAa,GAAG;AACrB,WAAK,UAAU;AACf;AAAA,IACF;AACA,UAAM,kBAAkB,KAAK,OAAO,iBAAiB,MAAM;AAC3D,UAAM,aAAa,GAAG,KAAK,OAAO,UAAU,IAAI,GAAG;AAOnD,QAAI,CAAC,aAAa;AAChB,iBAAW,gBAAgB;AAAA,IAC7B;AACA,QAAI,iBAAiB;AACnB,YAAM,gBAAgB,KAAK,OAAO,iBAAiB;AACnD,YAAM,aAAa,KAAK,OAAO,iBAAiB,aAAa;AAE7D,WAAI,yCAAY,SAAQ,YAAY;AAClC;AAAA,MACF;AACA,YAAM,WAAW,KAAK,OAAO,YAAY,GAAG;AAC5C,YAAM,mBAAmB,YAAY,eAAe,SAAS,OAAO,SAAS;AAC7E,aAAO,KAAK,QAAQ,aAAa,YAAY,iCACxC,mBADwC;AAAA,QAE3C,UAAU;AAAA,QACV,oBAAoB;AAAA,MACtB,EAAC;AAAA,IACH,OAAO;AACL,YAAM,YAAY,KAAK,OAAO,iBAAiB,GAAG;AAKlD,YAAM,OAAM,uCAAW,QAAO;AAC9B,YAAM,mBAAmB,uCAAW;AACpC,aAAO,KAAK,QAAQ,aAAa,KAAK,iCACjC,mBADiC;AAAA,QAEpC,UAAU;AAAA,QACV,oBAAoB;AAAA,MACtB,EAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,aAAa,KAAK;AAChB,UAAM,OAAO,KAAK;AAClB,UAAM,cAAc,KAAK,KAAK,OAAK,EAAE,QAAQ,GAAG;AAChD,QAAI,CAAC,aAAa;AAChB,cAAQ,MAAM,gCAAgC,GAAG,kBAAkB;AACnE;AAAA,IACF;AACA,SAAK,aAAa,KAAK;AACvB,SAAK,cAAc;AACnB,SAAK,kBAAkB,KAAK;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,gBAAY,GAAG,SAAS;AAAA,EAC1B;AAAA,EACA,YAAY;AACV,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,UAAU,aAAa;AAC9B,WAAK,OAAO,cAAc,YAAY;AAAA,IACxC;AACA,SAAI,yCAAY,UAAQ,2CAAa,MAAK;AACxC,UAAI,yCAAY,IAAI;AAClB,mBAAW,GAAG,SAAS;AAAA,MACzB;AAAA,IACF;AACA,QAAI,aAAa;AACf,WAAK,iBAAiB,KAAK;AAAA,QACzB,KAAK,YAAY;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AAnmFhB,QAAAA;AAomFI,QAAI,KAAK,QAAQ;AACf,cAAOA,MAAA,KAAK,gBAAL,gBAAAA,IAAkB;AAAA,IAC3B;AACA,WAAO,KAAK,OAAO,iBAAiB;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB;AAClB,SAAK,QAAQ,QAAQ,YAAU;AAE7B,YAAM,cAAc,OAAO,GAAG,aAAa,MAAM;AACjD,UAAI,gBAAgB,KAAK,YAAY;AACnC,aAAK,aAAa;AAClB,aAAK,eAAe;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AAMf,UAAM,SAAS,KAAK,OAAO;AAC3B,QAAI,KAAK,eAAe,OAAO;AAK7B,WAAK,UAAU,cAAc,OAAO,MAAM;AAAA,IAC5C,OAAO;AAKL,WAAK,UAAU,cAAc,MAAM,MAAM;AAAA,IAC3C;AAAA,EACF;AA+BF;AAAA;AA7BE,cA/NI,UA+NG,QAAO,SAAS,gBAAgB,mBAAmB;AACxD,SAAO,KAAK,qBAAqB,UAAY,kBAAkB,aAAa,CAAC;AAC/E;AAAA;AAEA,cAnOI,UAmOG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,EACxB,WAAW,SAAS,cAAc,IAAI,KAAK;AACzC,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,KAAK,GAAG,UAAU;AAAA,IACnC;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAAA,IAClE;AAAA,EACF;AAAA,EACA,cAAc,SAAS,qBAAqB,IAAI,KAAK;AACnD,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,qBAAqB,SAAS,6CAA6C,QAAQ;AAC/F,eAAO,IAAI,OAAO,MAAM;AAAA,MAC1B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,EACpB;AAAA,EACA,YAAY;AACd,CAAC;AA3PH,IAAM,UAAN;AAAA,CA6PC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB,CAAC,QAAQ,CAAC;AAAA,IACxC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,MAAM,OAAK;AACf,MAAI,OAAO,yCAAyC,YAAY;AAC9D,WAAO,qCAAqC,CAAC;AAAA,EAC/C;AACA,MAAI,OAAO,0BAA0B,YAAY;AAC/C,WAAO,sBAAsB,CAAC;AAAA,EAChC;AACA,SAAO,WAAW,CAAC;AACrB;AAGA,IAAM,iBAAN,MAAM,eAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA,WAAW,MAAM;AAAA,EAEjB;AAAA,EACA,YAAY,MAAM;AAAA,EAElB;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,UAAU,YAAY;AAChC,SAAK,WAAW;AAChB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,WAAW,cAAc,QAAQ,KAAK,YAAY;AACvD,oBAAgB,KAAK,UAAU;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,kBAAkB,IAAI,OAAO;AAC3B,QAAI,OAAO,KAAK,WAAW,eAAe;AACxC,UAAI,UAAU,KAAK,WAAW;AAC5B,aAAK,YAAY;AACjB,aAAK,SAAS,KAAK;AAAA,MACrB;AACA,sBAAgB,KAAK,UAAU;AAAA,IACjC;AAAA,EACF;AAAA,EACA,iBAAiB,IAAI;AACnB,QAAI,OAAO,KAAK,WAAW,eAAe;AACxC,WAAK,UAAU;AACf,sBAAgB,KAAK,UAAU;AAAA,IAKjC,WAAW,GAAG,QAAQ,iBAAiB,MAAM,KAAK,WAAW,eAAe;AAC1E,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,YAAY;AAC3B,SAAK,WAAW,cAAc,WAAW;AAAA,EAC3C;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,YAAY;AAAA,IACjC;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI;AACJ,QAAI;AACF,kBAAY,KAAK,SAAS,IAAI,SAAS;AAAA,IACzC,QAAQ;AAAA,IAER;AACA,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AAEA,QAAI,UAAU,eAAe;AAC3B,WAAK,gBAAgB,UAAU,cAAc,UAAU,MAAM,gBAAgB,KAAK,UAAU,CAAC;AAAA,IAC/F;AAKA,UAAM,cAAc,UAAU;AAC9B,QAAI,aAAa;AACf,YAAM,iBAAiB,CAAC,iBAAiB,oBAAoB,mBAAmB,eAAe,gBAAgB;AAC/G,qBAAe,QAAQ,YAAU;AAC/B,YAAI,OAAO,YAAY,MAAM,MAAM,aAAa;AAC9C,gBAAM,QAAQ,YAAY,MAAM,EAAE,KAAK,WAAW;AAClD,sBAAY,MAAM,IAAI,IAAI,WAAW;AACnC,kBAAM,GAAG,MAAM;AACf,4BAAgB,KAAK,UAAU;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAiBF;AAAA;AAfE,cApGI,gBAoGG,QAAO,SAAS,sBAAsB,mBAAmB;AAC9D,SAAO,KAAK,qBAAqB,gBAAkB,kBAAqB,QAAQ,GAAM,kBAAqB,UAAU,CAAC;AACxH;AAAA;AAEA,cAxGI,gBAwGG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,cAAc,SAAS,2BAA2B,IAAI,KAAK;AACzD,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,WAAW,SAAS,yCAAyC,QAAQ;AACjF,eAAO,IAAI,iBAAiB,OAAO,MAAM;AAAA,MAC3C,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY;AACd,CAAC;AAlHH,IAAM,gBAAN;AAAA,CAoHC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC;AAAA,IACrC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAkB,aAAW;AACjC,MAAI,MAAM;AACR,UAAM,QAAQ,QAAQ;AACtB,UAAM,WAAW,MAAM,SAAS,QAAQ,MAAM,MAAM,SAAS,EAAE,SAAS;AACxE,UAAM,UAAU,WAAW,KAAK;AAChC,eAAW,OAAO,OAAO;AACzB,UAAM,OAAO,MAAM,QAAQ,UAAU;AACrC,QAAI,MAAM;AACR,UAAI,UAAU;AACZ,mBAAW,MAAM,CAAC,GAAG,SAAS,gBAAgB,CAAC;AAAA,MACjD,OAAO;AACL,mBAAW,MAAM,OAAO;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,CAAC;AACH;AACA,IAAM,aAAa,aAAW;AAC5B,QAAM,YAAY,QAAQ;AAC1B,QAAM,UAAU,CAAC;AACjB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAM,OAAO,UAAU,KAAK,CAAC;AAC7B,QAAI,SAAS,QAAQ,WAAW,MAAM,KAAK,GAAG;AAC5C,cAAQ,KAAK,OAAO,KAAK,UAAU,CAAC,CAAC,EAAE;AAAA,IACzC;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,aAAa,CAAC,SAAS,YAAY;AACvC,QAAM,YAAY,QAAQ;AAC1B,YAAU,OAAO,aAAa,eAAe,eAAe,iBAAiB,aAAa,cAAc;AACxG,YAAU,IAAI,GAAG,OAAO;AAC1B;AACA,IAAM,aAAa,CAAC,OAAO,WAAW;AACpC,SAAO,MAAM,UAAU,GAAG,OAAO,MAAM,MAAM;AAC/C;AAKA,IAAM,qBAAN,MAAyB;AAAA;AAAA;AAAA;AAAA,EAIvB,aAAa,QAAQ;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,QAAQ;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,QAAQ,eAAe;AAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,QAAQ;AACf,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,QAAQ,MAAM;AAC7B,QAAI,OAAO,gBAAgB,KAAK,aAAa;AAC3C,aAAO;AAAA,IACT;AAEA,UAAM,eAAe,OAAO;AAC5B,UAAM,gBAAgB,KAAK;AAC3B,UAAM,QAAQ,OAAO,KAAK,YAAY;AACtC,UAAM,QAAQ,OAAO,KAAK,aAAa;AACvC,QAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,aAAO;AAAA,IACT;AAEA,eAAW,OAAO,OAAO;AACvB,UAAI,cAAc,GAAG,MAAM,aAAa,GAAG,GAAG;AAC5C,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAGA,IAAM,wBAAN,MAA4B;AAAA,EAC1B;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM;AACX,WAAO,KAAK,KAAK,OAAO,QAAQ,CAAC,CAAC;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,MAAM,MAAM,IAAI;AACtB,WAAO,KAAK,KAAK,QAAQ,MAAM,MAAM,EAAE;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,WAAO,KAAK,KAAK,OAAO;AAAA,EAC1B;AACF;", "names": ["_a", "doc", "_a", "config", "_a", "doc", "_a", "menuController", "doc", "_a", "config"]}