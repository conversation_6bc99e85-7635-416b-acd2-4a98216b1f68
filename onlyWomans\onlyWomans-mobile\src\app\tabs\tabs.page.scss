/* Instagram-Style Tab Bar */
.ow-tab-bar.instagram-style {
  --background: rgba(255, 255, 255, 0.98);
  --border-top: 1px solid rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  height: 60px;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.1);
}

.instagram-style .ow-tab-button {
  --color: #262626;
  --color-selected: #000000;
  --ripple-color: transparent;
  min-height: 50px;

  ion-icon {
    font-size: 24px;
    transition: all 0.2s ease;
  }

  &.tab-selected {
    ion-icon {
      color: #000000;
      transform: scale(1.1);
    }

    .profile-avatar {
      border: 2px solid #000000;
    }
  }

  &.camera-btn {
    .camera-icon {
      background: linear-gradient(135deg, var(--ow-rose-500) 0%, var(--ow-purple-500) 100%);
      color: white;
      border-radius: 8px;
      padding: 6px;
      font-size: 20px;
    }
  }

  .profile-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid transparent;
    transition: all 0.2s ease;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

/* Tab Content Styles */
ion-content {
  --background: linear-gradient(135deg, var(--ow-rose-50) 0%, var(--ow-purple-50) 100%);
}

/* Custom animations for tab transitions */
@keyframes tabSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.tab-content {
  animation: tabSlideIn 0.3s ease-out;
}