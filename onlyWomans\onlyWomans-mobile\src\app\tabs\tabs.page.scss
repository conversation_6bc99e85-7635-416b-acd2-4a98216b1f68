/* OnlyWomans Tab Bar Styles */
.ow-tab-bar {
  --background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(253, 242, 248, 0.95) 100%);
  --border: 1px solid var(--ow-rose-200);
  backdrop-filter: blur(10px);
  border-radius: var(--ow-radius-lg) var(--ow-radius-lg) 0 0;
  box-shadow: var(--ow-shadow-lg);
  margin: 0 var(--ow-space-sm);
  margin-bottom: env(safe-area-inset-bottom);
}

.ow-tab-button {
  --color: var(--ion-color-medium);
  --color-selected: var(--ion-color-primary);
  --ripple-color: var(--ow-rose-200);
  border-radius: var(--ow-radius-md);
  margin: var(--ow-space-xs);
  transition: all 0.3s ease;

  ion-label {
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 2px;
  }

  ion-icon {
    font-size: 1.5rem;
    transition: all 0.3s ease;
  }

  &.tab-selected {
    --color: var(--ion-color-primary);
    background: var(--ow-rose-100);

    ion-icon {
      transform: scale(1.1);
      color: var(--ion-color-primary);
    }

    ion-label {
      color: var(--ion-color-primary);
      font-weight: 600;
    }
  }

  &:hover {
    --color: var(--ion-color-primary);
    background: var(--ow-rose-50);
  }
}

/* Tab Content Styles */
ion-content {
  --background: linear-gradient(135deg, var(--ow-rose-50) 0%, var(--ow-purple-50) 100%);
}

/* Custom animations for tab transitions */
@keyframes tabSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.tab-content {
  animation: tabSlideIn 0.3s ease-out;
}