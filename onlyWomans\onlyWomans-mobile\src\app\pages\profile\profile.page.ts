import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.page.html',
  styleUrls: ['./profile.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class ProfilePage implements OnInit {

  user = {
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+91 9876543210',
    avatar: 'assets/avatars/user1.jpg',
    memberSince: 'January 2024'
  };

  menuItems = [
    { icon: 'bag', title: 'My Orders', subtitle: 'Track your orders', color: 'var(--ow-rose-500)' },
    { icon: 'location', title: 'Addresses', subtitle: 'Manage delivery addresses', color: 'var(--ow-purple-500)' },
    { icon: 'card', title: 'Payment Methods', subtitle: 'Saved cards & wallets', color: 'var(--ow-gold-500)' },
    { icon: 'notifications', title: 'Notifications', subtitle: 'Manage preferences', color: 'var(--ion-color-success)' },
    { icon: 'help-circle', title: 'Help & Support', subtitle: 'Get assistance', color: 'var(--ion-color-secondary)' },
    { icon: 'settings', title: 'Settings', subtitle: 'App preferences', color: 'var(--ion-color-medium)' }
  ];

  constructor() { }

  ngOnInit() {
  }

  onMenuItemClick(item: any) {
    console.log('Menu item clicked:', item);
  }

  onEditProfile() {
    console.log('Edit profile clicked');
  }

  onLogout() {
    console.log('Logout clicked');
  }
}
