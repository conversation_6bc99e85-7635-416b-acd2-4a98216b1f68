{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-select-modal.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, n as forceUpdate, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { s as safeCall } from './overlays-8Y2rA-ps.js';\nimport { g as getClassMap } from './theme-DiVJyqlX.js';\nimport './index-ZjP4CjeZ.js';\nimport './helpers-1O4D2b7y.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './framework-delegate-DxcnWic_.js';\nimport './gesture-controller-BTEOs1at.js';\nconst ionicSelectModalMdCss = \".sc-ion-select-modal-ionic-h{height:100%}ion-list.sc-ion-select-modal-ionic ion-radio.sc-ion-select-modal-ionic::part(container){display:none}ion-list.sc-ion-select-modal-ionic ion-radio.sc-ion-select-modal-ionic::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-modal-ionic{--inner-border-width:0}.item-radio-checked.sc-ion-select-modal-ionic{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-modal-ionic{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}\";\nconst selectModalIosCss = \".sc-ion-select-modal-ios-h{height:100%}ion-item.sc-ion-select-modal-ios{--inner-padding-end:0}ion-radio.sc-ion-select-modal-ios::after{bottom:0;position:absolute;width:calc(100% - 0.9375rem - 16px);border-width:0px 0px 0.55px 0px;border-style:solid;border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));content:\\\"\\\"}ion-radio.sc-ion-select-modal-ios::after{inset-inline-start:calc(0.9375rem + 16px)}\";\nconst selectModalMdCss = \".sc-ion-select-modal-md-h{height:100%}ion-list.sc-ion-select-modal-md ion-radio.sc-ion-select-modal-md::part(container){display:none}ion-list.sc-ion-select-modal-md ion-radio.sc-ion-select-modal-md::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-modal-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-modal-md{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-modal-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}\";\nconst SelectModal = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.options = [];\n  }\n  closeModal() {\n    const modal = this.el.closest('ion-modal');\n    if (modal) {\n      modal.dismiss();\n    }\n  }\n  findOptionFromEvent(ev) {\n    const {\n      options\n    } = this;\n    return options.find(o => o.value === ev.target.value);\n  }\n  getValues(ev) {\n    const {\n      multiple,\n      options\n    } = this;\n    if (multiple) {\n      // this is a modal with checkboxes (multiple value select)\n      // return an array of all the checked values\n      return options.filter(o => o.checked).map(o => o.value);\n    }\n    // this is a modal with radio buttons (single value select)\n    // return the value that was clicked, otherwise undefined\n    const option = ev ? this.findOptionFromEvent(ev) : null;\n    return option ? option.value : undefined;\n  }\n  callOptionHandler(ev) {\n    const option = this.findOptionFromEvent(ev);\n    const values = this.getValues(ev);\n    if (option === null || option === void 0 ? void 0 : option.handler) {\n      safeCall(option.handler, values);\n    }\n  }\n  setChecked(ev) {\n    const {\n      multiple\n    } = this;\n    const option = this.findOptionFromEvent(ev);\n    // this is a modal with checkboxes (multiple value select)\n    // we need to set the checked value for this option\n    if (multiple && option) {\n      option.checked = ev.detail.checked;\n    }\n  }\n  renderRadioOptions() {\n    const checked = this.options.filter(o => o.checked).map(o => o.value)[0];\n    return h(\"ion-radio-group\", {\n      value: checked,\n      onIonChange: ev => this.callOptionHandler(ev)\n    }, this.options.map(option => h(\"ion-item\", {\n      lines: \"none\",\n      class: Object.assign({\n        // TODO FW-4784\n        'item-radio-checked': option.value === checked\n      }, getClassMap(option.cssClass))\n    }, h(\"ion-radio\", {\n      value: option.value,\n      disabled: option.disabled,\n      justify: \"start\",\n      labelPlacement: \"end\",\n      onClick: () => this.closeModal(),\n      onKeyUp: ev => {\n        if (ev.key === ' ') {\n          /**\n           * Selecting a radio option with keyboard navigation,\n           * either through the Enter or Space keys, should\n           * dismiss the modal.\n           */\n          this.closeModal();\n        }\n      }\n    }, option.text))));\n  }\n  renderCheckboxOptions() {\n    return this.options.map(option => h(\"ion-item\", {\n      class: Object.assign({\n        // TODO FW-4784\n        'item-checkbox-checked': option.checked\n      }, getClassMap(option.cssClass))\n    }, h(\"ion-checkbox\", {\n      value: option.value,\n      disabled: option.disabled,\n      checked: option.checked,\n      justify: \"start\",\n      labelPlacement: \"end\",\n      onIonChange: ev => {\n        this.setChecked(ev);\n        this.callOptionHandler(ev);\n        // TODO FW-4784\n        forceUpdate(this);\n      }\n    }, option.text)));\n  }\n  render() {\n    return h(Host, {\n      key: 'b6c0dec240b2e41985b15fdf4e5a6d3a145c1567',\n      class: getIonMode(this)\n    }, h(\"ion-header\", {\n      key: 'cd177e85ee0f62a60a3a708342d6ab6eb19a44dc'\n    }, h(\"ion-toolbar\", {\n      key: 'aee8222a5a4daa540ad202b2e4cac1ef93d9558c'\n    }, this.header !== undefined && h(\"ion-title\", {\n      key: '5f8fecc764d97bf840d3d4cfddeeccd118ab4436'\n    }, this.header), h(\"ion-buttons\", {\n      key: '919033950d7c2b0101f96a9c9698219de9f568ea',\n      slot: \"end\"\n    }, h(\"ion-button\", {\n      key: '34b571cab6dced4bde555a077a21e91800829931',\n      onClick: () => this.closeModal()\n    }, \"Close\")))), h(\"ion-content\", {\n      key: '3c9153d26ba7a5a03d3b20fcd628d0c3031661a7'\n    }, h(\"ion-list\", {\n      key: 'e00b222c071bc97c82ad1bba4db95a8a5c43ed6d'\n    }, this.multiple === true ? this.renderCheckboxOptions() : this.renderRadioOptions())));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nSelectModal.style = {\n  ionic: ionicSelectModalMdCss,\n  ios: selectModalIosCss,\n  md: selectModalMdCss\n};\nexport { SelectModal as ion_select_modal };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAWA,IAAM,wBAAwB;AAC9B,IAAM,oBAAoB;AAC1B,IAAM,mBAAmB;AACzB,IAAM,cAAc,MAAM;AAAA,EACxB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,UAAU,CAAC;AAAA,EAClB;AAAA,EACA,aAAa;AACX,UAAM,QAAQ,KAAK,GAAG,QAAQ,WAAW;AACzC,QAAI,OAAO;AACT,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF;AAAA,EACA,oBAAoB,IAAI;AACtB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,QAAQ,KAAK,OAAK,EAAE,UAAU,GAAG,OAAO,KAAK;AAAA,EACtD;AAAA,EACA,UAAU,IAAI;AACZ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,UAAU;AAGZ,aAAO,QAAQ,OAAO,OAAK,EAAE,OAAO,EAAE,IAAI,OAAK,EAAE,KAAK;AAAA,IACxD;AAGA,UAAM,SAAS,KAAK,KAAK,oBAAoB,EAAE,IAAI;AACnD,WAAO,SAAS,OAAO,QAAQ;AAAA,EACjC;AAAA,EACA,kBAAkB,IAAI;AACpB,UAAM,SAAS,KAAK,oBAAoB,EAAE;AAC1C,UAAM,SAAS,KAAK,UAAU,EAAE;AAChC,QAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AAClE,eAAS,OAAO,SAAS,MAAM;AAAA,IACjC;AAAA,EACF;AAAA,EACA,WAAW,IAAI;AACb,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,KAAK,oBAAoB,EAAE;AAG1C,QAAI,YAAY,QAAQ;AACtB,aAAO,UAAU,GAAG,OAAO;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,UAAM,UAAU,KAAK,QAAQ,OAAO,OAAK,EAAE,OAAO,EAAE,IAAI,OAAK,EAAE,KAAK,EAAE,CAAC;AACvE,WAAO,EAAE,mBAAmB;AAAA,MAC1B,OAAO;AAAA,MACP,aAAa,QAAM,KAAK,kBAAkB,EAAE;AAAA,IAC9C,GAAG,KAAK,QAAQ,IAAI,YAAU,EAAE,YAAY;AAAA,MAC1C,OAAO;AAAA,MACP,OAAO,OAAO,OAAO;AAAA;AAAA,QAEnB,sBAAsB,OAAO,UAAU;AAAA,MACzC,GAAG,YAAY,OAAO,QAAQ,CAAC;AAAA,IACjC,GAAG,EAAE,aAAa;AAAA,MAChB,OAAO,OAAO;AAAA,MACd,UAAU,OAAO;AAAA,MACjB,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,SAAS,MAAM,KAAK,WAAW;AAAA,MAC/B,SAAS,QAAM;AACb,YAAI,GAAG,QAAQ,KAAK;AAMlB,eAAK,WAAW;AAAA,QAClB;AAAA,MACF;AAAA,IACF,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC;AAAA,EACnB;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,QAAQ,IAAI,YAAU,EAAE,YAAY;AAAA,MAC9C,OAAO,OAAO,OAAO;AAAA;AAAA,QAEnB,yBAAyB,OAAO;AAAA,MAClC,GAAG,YAAY,OAAO,QAAQ,CAAC;AAAA,IACjC,GAAG,EAAE,gBAAgB;AAAA,MACnB,OAAO,OAAO;AAAA,MACd,UAAU,OAAO;AAAA,MACjB,SAAS,OAAO;AAAA,MAChB,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,aAAa,QAAM;AACjB,aAAK,WAAW,EAAE;AAClB,aAAK,kBAAkB,EAAE;AAEzB,oBAAY,IAAI;AAAA,MAClB;AAAA,IACF,GAAG,OAAO,IAAI,CAAC,CAAC;AAAA,EAClB;AAAA,EACA,SAAS;AACP,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,WAAW,IAAI;AAAA,IACxB,GAAG,EAAE,cAAc;AAAA,MACjB,KAAK;AAAA,IACP,GAAG,EAAE,eAAe;AAAA,MAClB,KAAK;AAAA,IACP,GAAG,KAAK,WAAW,UAAa,EAAE,aAAa;AAAA,MAC7C,KAAK;AAAA,IACP,GAAG,KAAK,MAAM,GAAG,EAAE,eAAe;AAAA,MAChC,KAAK;AAAA,MACL,MAAM;AAAA,IACR,GAAG,EAAE,cAAc;AAAA,MACjB,KAAK;AAAA,MACL,SAAS,MAAM,KAAK,WAAW;AAAA,IACjC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,eAAe;AAAA,MAC/B,KAAK;AAAA,IACP,GAAG,EAAE,YAAY;AAAA,MACf,KAAK;AAAA,IACP,GAAG,KAAK,aAAa,OAAO,KAAK,sBAAsB,IAAI,KAAK,mBAAmB,CAAC,CAAC,CAAC;AAAA,EACxF;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;AACA,YAAY,QAAQ;AAAA,EAClB,OAAO;AAAA,EACP,KAAK;AAAA,EACL,IAAI;AACN;", "names": []}