/* OnlyWomans Beauty Page Styles */

.ow-header {
  --background: linear-gradient(135deg, var(--ow-gold-500) 0%, var(--ow-rose-500) 100%);
  --color: white;
  box-shadow: var(--ow-shadow-lg);
}

.ow-toolbar {
  --background: transparent;
  --color: white;
}

.ow-content {
  --background: linear-gradient(135deg, var(--ow-rose-50) 0%, var(--ow-gold-50) 100%);
}

.section-title {
  margin: 0 0 var(--ow-space-md) 0;
  color: var(--ion-color-dark);
  padding: 0 var(--ow-space-md);
}

/* Categories Section */
.categories-section {
  margin-bottom: var(--ow-space-xl);
  
  .categories-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--ow-space-md);
    padding: 0 var(--ow-space-md);
    
    .category-card {
      background: white;
      border-radius: var(--ow-radius-lg);
      padding: var(--ow-space-lg);
      text-align: center;
      box-shadow: var(--ow-shadow-sm);
      
      .category-icon {
        width: 60px;
        height: 60px;
        border-radius: var(--ow-radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--ow-space-md);
        
        .icon-emoji {
          font-size: 2rem;
        }
      }
      
      .category-name {
        margin: 0;
        color: var(--ion-color-dark);
      }
    }
  }
}

/* Tutorials Section */
.tutorials-section {
  margin-bottom: var(--ow-space-xl);
  
  .tutorials-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--ow-space-md);
    padding: 0 var(--ow-space-md);
    
    .tutorial-card {
      background: white;
      border-radius: var(--ow-radius-lg);
      overflow: hidden;
      box-shadow: var(--ow-shadow-sm);
      
      .tutorial-thumbnail {
        position: relative;
        height: 180px;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .play-button {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 60px;
          height: 60px;
          background: rgba(255, 255, 255, 0.9);
          border-radius: var(--ow-radius-full);
          display: flex;
          align-items: center;
          justify-content: center;
          
          .play-icon {
            font-size: 1.5rem;
            color: var(--ion-color-primary);
            margin-left: 4px;
          }
        }
        
        .duration-badge {
          position: absolute;
          bottom: var(--ow-space-sm);
          right: var(--ow-space-sm);
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 4px 8px;
          border-radius: var(--ow-radius-sm);
          font-size: 0.75rem;
        }
      }
      
      .tutorial-info {
        padding: var(--ow-space-md);
        
        .tutorial-title {
          margin: 0 0 var(--ow-space-xs) 0;
          color: var(--ion-color-dark);
        }
        
        .tutorial-views {
          color: var(--ion-color-medium);
          font-size: 0.85rem;
          margin: 0;
        }
      }
    }
  }
}

/* Products Section */
.products-section {
  margin-bottom: var(--ow-space-xl);
  
  .products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--ow-space-md);
    padding: 0 var(--ow-space-md);
    
    .product-card {
      background: white;
      border-radius: var(--ow-radius-lg);
      overflow: hidden;
      box-shadow: var(--ow-shadow-sm);
      
      .product-image-container {
        height: 140px;
        
        .product-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .product-info {
        padding: var(--ow-space-md);
        
        .product-name {
          margin: 0 0 var(--ow-space-xs) 0;
          color: var(--ion-color-dark);
          font-size: 0.9rem;
        }
        
        .product-rating {
          display: flex;
          align-items: center;
          margin-bottom: var(--ow-space-sm);
          
          .star-icon {
            color: var(--ow-gold-500);
            font-size: 0.8rem;
            margin-right: 2px;
          }
          
          .rating-value {
            font-size: 0.8rem;
            font-weight: 500;
            color: var(--ion-color-dark);
            margin-right: 4px;
          }
          
          .rating-count {
            font-size: 0.75rem;
            color: var(--ion-color-medium);
          }
        }
        
        .product-price {
          .current-price {
            font-size: 1rem;
            font-weight: 600;
            margin-right: var(--ow-space-sm);
          }
          
          .original-price {
            font-size: 0.85rem;
            color: var(--ion-color-medium);
            text-decoration: line-through;
          }
        }
      }
    }
  }
}

.bottom-spacing {
  height: var(--ow-space-xl);
}
