import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-shop',
  templateUrl: './shop.page.html',
  styleUrls: ['./shop.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class ShopPage implements OnInit {

  categories = [
    { id: 1, name: 'All', active: true },
    { id: 2, name: 'Ethnic Wear', active: false },
    { id: 3, name: 'Western Wear', active: false },
    { id: 4, name: 'Beauty', active: false },
    { id: 5, name: 'Accessories', active: false }
  ];

  products = [
    {
      id: 1,
      name: 'Floral Maxi Dress',
      price: 2499,
      originalPrice: 3499,
      image: 'assets/products/dress1.jpg',
      rating: 4.8,
      reviews: 124,
      category: 'Western Wear'
    },
    {
      id: 2,
      name: 'Silk Saree Collection',
      price: 4999,
      originalPrice: 6999,
      image: 'assets/products/saree1.jpg',
      rating: 4.9,
      reviews: 89,
      category: 'Ethnic Wear'
    },
    {
      id: 3,
      name: '<PERSON><PERSON>tick Set',
      price: 899,
      originalPrice: 1299,
      image: 'assets/products/lipstick1.jpg',
      rating: 4.7,
      reviews: 256,
      category: 'Beauty'
    },
    {
      id: 4,
      name: 'Designer Handbag',
      price: 1999,
      originalPrice: 2999,
      image: 'assets/products/handbag1.jpg',
      rating: 4.6,
      reviews: 78,
      category: 'Accessories'
    }
  ];

  filteredProducts = [...this.products];
  searchTerm = '';

  constructor() { }

  ngOnInit() {
  }

  onCategorySelect(category: any) {
    // Update active category
    this.categories.forEach(cat => cat.active = false);
    category.active = true;
    
    // Filter products
    if (category.name === 'All') {
      this.filteredProducts = [...this.products];
    } else {
      this.filteredProducts = this.products.filter(product => 
        product.category === category.name
      );
    }
  }

  onSearch(event: any) {
    const searchTerm = event.target.value.toLowerCase();
    this.searchTerm = searchTerm;
    
    if (searchTerm.trim() === '') {
      this.filteredProducts = [...this.products];
    } else {
      this.filteredProducts = this.products.filter(product =>
        product.name.toLowerCase().includes(searchTerm) ||
        product.category.toLowerCase().includes(searchTerm)
      );
    }
  }

  onProductClick(product: any) {
    console.log('Product clicked:', product);
  }

  addToWishlist(product: any, event: Event) {
    event.stopPropagation();
    console.log('Added to wishlist:', product);
  }

  addToCart(product: any, event: Event) {
    event.stopPropagation();
    console.log('Added to cart:', product);
  }
}
