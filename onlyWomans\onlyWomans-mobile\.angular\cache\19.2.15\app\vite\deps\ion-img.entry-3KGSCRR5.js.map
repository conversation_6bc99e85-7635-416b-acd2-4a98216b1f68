{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-img.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { b as inheritAttributes } from './helpers-1O4D2b7y.js';\nconst imgCss = \":host{display:block;-o-object-fit:contain;object-fit:contain}img{display:block;width:100%;height:100%;-o-object-fit:inherit;object-fit:inherit;-o-object-position:inherit;object-position:inherit}\";\nconst Img = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionImgWillLoad = createEvent(this, \"ionImgWillLoad\", 7);\n    this.ionImgDidLoad = createEvent(this, \"ionImgDidLoad\", 7);\n    this.ionError = createEvent(this, \"ionError\", 7);\n    this.inheritedAttributes = {};\n    this.onLoad = () => {\n      this.ionImgDidLoad.emit();\n    };\n    this.onError = () => {\n      this.ionError.emit();\n    };\n  }\n  srcChanged() {\n    this.addIO();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAttributes(this.el, ['draggable']);\n  }\n  componentDidLoad() {\n    this.addIO();\n  }\n  addIO() {\n    if (this.src === undefined) {\n      return;\n    }\n    if (typeof window !== 'undefined' && 'IntersectionObserver' in window && 'IntersectionObserverEntry' in window && 'isIntersecting' in window.IntersectionObserverEntry.prototype) {\n      this.removeIO();\n      this.io = new IntersectionObserver(data => {\n        /**\n         * On slower devices, it is possible for an intersection observer entry to contain multiple\n         * objects in the array. This happens when quickly scrolling an image into view and then out of\n         * view. In this case, the last object represents the current state of the component.\n         */\n        if (data[data.length - 1].isIntersecting) {\n          this.load();\n          this.removeIO();\n        }\n      });\n      this.io.observe(this.el);\n    } else {\n      // fall back to setTimeout for Safari and IE\n      setTimeout(() => this.load(), 200);\n    }\n  }\n  load() {\n    this.loadError = this.onError;\n    this.loadSrc = this.src;\n    this.ionImgWillLoad.emit();\n  }\n  removeIO() {\n    if (this.io) {\n      this.io.disconnect();\n      this.io = undefined;\n    }\n  }\n  render() {\n    const {\n      loadSrc,\n      alt,\n      onLoad,\n      loadError,\n      inheritedAttributes\n    } = this;\n    const {\n      draggable\n    } = inheritedAttributes;\n    return h(Host, {\n      key: 'da600442894427dee1974a28e545613afac69fca',\n      class: getIonMode(this)\n    }, h(\"img\", {\n      key: '16df0c7069af86c0fa7ce5af598bc0f63b4eb71a',\n      decoding: \"async\",\n      src: loadSrc,\n      alt: alt,\n      onLoad: onLoad,\n      onError: loadError,\n      part: \"image\",\n      draggable: isDraggable(draggable)\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"src\": [\"srcChanged\"]\n    };\n  }\n};\n/**\n * Enumerated strings must be set as booleans\n * as Stencil will not render 'false' in the DOM.\n * The need to explicitly render draggable=\"true\"\n * as only certain elements are draggable by default.\n * https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/draggable.\n */\nconst isDraggable = draggable => {\n  switch (draggable) {\n    case 'true':\n      return true;\n    case 'false':\n      return false;\n    default:\n      return undefined;\n  }\n};\nImg.style = imgCss;\nexport { Img as ion_img };"], "mappings": ";;;;;;;;;;;;;;AAKA,IAAM,SAAS;AACf,IAAM,MAAM,MAAM;AAAA,EAChB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,SAAK,gBAAgB,YAAY,MAAM,iBAAiB,CAAC;AACzD,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,sBAAsB,CAAC;AAC5B,SAAK,SAAS,MAAM;AAClB,WAAK,cAAc,KAAK;AAAA,IAC1B;AACA,SAAK,UAAU,MAAM;AACnB,WAAK,SAAS,KAAK;AAAA,IACrB;AAAA,EACF;AAAA,EACA,aAAa;AACX,SAAK,MAAM;AAAA,EACb;AAAA,EACA,oBAAoB;AAClB,SAAK,sBAAsB,kBAAkB,KAAK,IAAI,CAAC,WAAW,CAAC;AAAA,EACrE;AAAA,EACA,mBAAmB;AACjB,SAAK,MAAM;AAAA,EACb;AAAA,EACA,QAAQ;AACN,QAAI,KAAK,QAAQ,QAAW;AAC1B;AAAA,IACF;AACA,QAAI,OAAO,WAAW,eAAe,0BAA0B,UAAU,+BAA+B,UAAU,oBAAoB,OAAO,0BAA0B,WAAW;AAChL,WAAK,SAAS;AACd,WAAK,KAAK,IAAI,qBAAqB,UAAQ;AAMzC,YAAI,KAAK,KAAK,SAAS,CAAC,EAAE,gBAAgB;AACxC,eAAK,KAAK;AACV,eAAK,SAAS;AAAA,QAChB;AAAA,MACF,CAAC;AACD,WAAK,GAAG,QAAQ,KAAK,EAAE;AAAA,IACzB,OAAO;AAEL,iBAAW,MAAM,KAAK,KAAK,GAAG,GAAG;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,YAAY,KAAK;AACtB,SAAK,UAAU,KAAK;AACpB,SAAK,eAAe,KAAK;AAAA,EAC3B;AAAA,EACA,WAAW;AACT,QAAI,KAAK,IAAI;AACX,WAAK,GAAG,WAAW;AACnB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,WAAW,IAAI;AAAA,IACxB,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,UAAU;AAAA,MACV,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT,MAAM;AAAA,MACN,WAAW,YAAY,SAAS;AAAA,IAClC,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,OAAO,CAAC,YAAY;AAAA,IACtB;AAAA,EACF;AACF;AAQA,IAAM,cAAc,eAAa;AAC/B,UAAQ,WAAW;AAAA,IACjB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AACA,IAAI,QAAQ;", "names": []}