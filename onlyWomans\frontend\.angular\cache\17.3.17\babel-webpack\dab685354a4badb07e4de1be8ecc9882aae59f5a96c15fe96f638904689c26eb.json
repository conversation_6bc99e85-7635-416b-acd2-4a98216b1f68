{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nfunction AppComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9)(3, \"h1\", 10)(4, \"span\", 11);\n    i0.ɵɵtext(5, \"\\uD83C\\uDF38\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 12);\n    i0.ɵɵtext(7, \"OnlyWomans\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"p\", 13);\n    i0.ɵɵtext(9, \"Fashion for Every Woman\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 14);\n    i0.ɵɵelement(11, \"div\", 15)(12, \"div\", 15)(13, \"div\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\", 16);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r0.loadingText);\n  }\n}\nfunction AppComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class AppComponent {\n  constructor(router) {\n    this.router = router;\n    this.title = 'OnlyWomans - Fashion for Every Woman';\n    this.isLoading = true;\n    this.loadingText = 'Loading your fashion world...';\n    this.loadingTexts = ['Loading your fashion world...', 'Curating the latest trends...', 'Preparing your style journey...', 'Setting up your wardrobe...', 'Almost ready to shop...'];\n  }\n  ngOnInit() {\n    this.startLoadingSequence();\n    this.setupRouterEvents();\n  }\n  startLoadingSequence() {\n    let textIndex = 0;\n    // Change loading text every 800ms\n    const textInterval = setInterval(() => {\n      textIndex = (textIndex + 1) % this.loadingTexts.length;\n      this.loadingText = this.loadingTexts[textIndex];\n    }, 800);\n    // Hide loading screen after 3 seconds\n    setTimeout(() => {\n      clearInterval(textInterval);\n      this.isLoading = false;\n    }, 3000);\n  }\n  setupRouterEvents() {\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      // Add page-specific classes or analytics here\n      if (event instanceof NavigationEnd) {\n        this.updatePageMeta(event.url);\n      }\n    });\n  }\n  updatePageMeta(url) {\n    // Update page title and meta tags based on route\n    const pageTitles = {\n      '/': 'OnlyWomans - Fashion for Every Woman',\n      '/shop': 'Shop - OnlyWomans',\n      '/categories': 'Categories - OnlyWomans',\n      '/beauty': 'Beauty & Makeup - OnlyWomans',\n      '/accessories': 'Accessories - OnlyWomans',\n      '/ethnic': 'Ethnic Wear - OnlyWomans',\n      '/western': 'Western Wear - OnlyWomans',\n      '/cart': 'Shopping Cart - OnlyWomans',\n      '/wishlist': 'My Wishlist - OnlyWomans',\n      '/profile': 'My Profile - OnlyWomans'\n    };\n    const pageTitle = pageTitles[url] || this.title;\n    document.title = pageTitle;\n    // Update meta description\n    const metaDescription = document.querySelector('meta[name=\"description\"]');\n    if (metaDescription) {\n      metaDescription.setAttribute('content', 'Discover the latest in women\\'s fashion, beauty, and accessories. Shop ethnic wear, western outfits, makeup, jewelry and more at OnlyWomans.');\n    }\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"ow-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 4,\n      consts: [[1, \"app-container\"], [\"class\", \"loading-screen\", 4, \"ngIf\"], [\"class\", \"main-content fade-in-up\", 4, \"ngIf\"], [1, \"bg-decorations\"], [1, \"decoration\", \"decoration-1\"], [1, \"decoration\", \"decoration-2\"], [1, \"decoration\", \"decoration-3\"], [1, \"loading-screen\"], [1, \"loading-content\"], [1, \"logo-container\"], [1, \"app-logo\"], [1, \"logo-icon\"], [1, \"logo-text\"], [1, \"tagline\"], [1, \"loading-spinner\"], [1, \"spinner-ring\"], [1, \"loading-text\"], [1, \"main-content\", \"fade-in-up\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, AppComponent_div_1_Template, 16, 1, \"div\", 1)(2, AppComponent_div_2_Template, 2, 0, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵelement(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i2.NgIf, RouterOutlet],\n      styles: [\".app-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  position: relative;\\n  overflow-x: hidden;\\n  background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%);\\n}\\n\\n.loading-screen[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, var(--primary-100), var(--secondary-100));\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 9999;\\n}\\n\\n.loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  animation: fadeInUp 0.8s ease-out;\\n}\\n\\n.logo-container[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-2xl);\\n}\\n\\n.app-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: var(--space-md);\\n  font-size: var(--font-size-4xl);\\n  font-weight: var(--font-weight-bold);\\n  color: var(--primary-600);\\n  margin: 0 0 var(--space-md) 0;\\n}\\n\\n.logo-icon[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-5xl);\\n  animation: pulse 2s infinite;\\n}\\n\\n.logo-text[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.tagline[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-lg);\\n  color: var(--text-secondary);\\n  font-weight: var(--font-weight-medium);\\n  margin: 0;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: var(--space-sm);\\n  margin-bottom: var(--space-xl);\\n}\\n\\n.spinner-ring[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  background: var(--primary-500);\\n  animation: _ngcontent-%COMP%_bounce 1.4s ease-in-out infinite both;\\n}\\n\\n.spinner-ring[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: -0.32s;\\n}\\n\\n.spinner-ring[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: -0.16s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 80%, 100% {\\n    transform: scale(0);\\n    opacity: 0.5;\\n  }\\n  40% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n.loading-text[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-base);\\n  color: var(--text-secondary);\\n  margin: 0;\\n  animation: pulse 2s infinite;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.bg-decorations[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n.decoration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  border-radius: 50%;\\n  opacity: 0.1;\\n  animation: _ngcontent-%COMP%_float 6s ease-in-out infinite;\\n}\\n\\n.decoration-1[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  background: var(--primary-300);\\n  top: 10%;\\n  right: 10%;\\n  animation-delay: 0s;\\n}\\n\\n.decoration-2[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n  background: var(--secondary-300);\\n  bottom: 20%;\\n  left: 5%;\\n  animation-delay: 2s;\\n}\\n\\n.decoration-3[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  background: var(--accent-300);\\n  top: 50%;\\n  left: 50%;\\n  animation-delay: 4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px) rotate(0deg);\\n  }\\n  50% {\\n    transform: translateY(-20px) rotate(180deg);\\n  }\\n}\\n.fade-in-up[_ngcontent-%COMP%] {\\n  animation: fadeInUp 0.6s ease-out;\\n}\\n\\n@media (max-width: 768px) {\\n  .app-logo[_ngcontent-%COMP%] {\\n    font-size: var(--font-size-3xl);\\n    flex-direction: column;\\n    gap: var(--space-sm);\\n  }\\n  .logo-icon[_ngcontent-%COMP%] {\\n    font-size: var(--font-size-4xl);\\n  }\\n  .decoration[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterOutlet", "NavigationEnd", "filter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "loadingText", "AppComponent", "constructor", "router", "title", "isLoading", "loadingTexts", "ngOnInit", "startLoadingSequence", "setupRouterEvents", "textIndex", "textInterval", "setInterval", "length", "setTimeout", "clearInterval", "events", "pipe", "event", "subscribe", "updatePageMeta", "url", "pageTitles", "pageTitle", "document", "metaDescription", "querySelector", "setAttribute", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵtemplate", "AppComponent_div_1_Template", "AppComponent_div_2_Template", "ɵɵclassProp", "ɵɵproperty", "i2", "NgIf", "styles"], "sources": ["E:\\Fahion\\DFashion\\onlyWomans\\frontend\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet, Router, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\n\n@Component({\n  selector: 'ow-root',\n  standalone: true,\n  imports: [CommonModule, RouterOutlet],\n  template: `\n    <div class=\"app-container\" [class.loading]=\"isLoading\">\n      <!-- Loading Screen -->\n      <div *ngIf=\"isLoading\" class=\"loading-screen\">\n        <div class=\"loading-content\">\n          <div class=\"logo-container\">\n            <h1 class=\"app-logo\">\n              <span class=\"logo-icon\">🌸</span>\n              <span class=\"logo-text\">OnlyWomans</span>\n            </h1>\n            <p class=\"tagline\">Fashion for Every Woman</p>\n          </div>\n          \n          <div class=\"loading-spinner\">\n            <div class=\"spinner-ring\"></div>\n            <div class=\"spinner-ring\"></div>\n            <div class=\"spinner-ring\"></div>\n          </div>\n          \n          <p class=\"loading-text\">{{ loadingText }}</p>\n        </div>\n      </div>\n\n      <!-- Main App Content -->\n      <div *ngIf=\"!isLoading\" class=\"main-content fade-in-up\">\n        <router-outlet></router-outlet>\n      </div>\n\n      <!-- Background Decorations -->\n      <div class=\"bg-decorations\">\n        <div class=\"decoration decoration-1\"></div>\n        <div class=\"decoration decoration-2\"></div>\n        <div class=\"decoration decoration-3\"></div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .app-container {\n      min-height: 100vh;\n      position: relative;\n      overflow-x: hidden;\n      background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%);\n    }\n\n    .loading-screen {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background: linear-gradient(135deg, var(--primary-100), var(--secondary-100));\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      z-index: 9999;\n    }\n\n    .loading-content {\n      text-align: center;\n      animation: fadeInUp 0.8s ease-out;\n    }\n\n    .logo-container {\n      margin-bottom: var(--space-2xl);\n    }\n\n    .app-logo {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: var(--space-md);\n      font-size: var(--font-size-4xl);\n      font-weight: var(--font-weight-bold);\n      color: var(--primary-600);\n      margin: 0 0 var(--space-md) 0;\n    }\n\n    .logo-icon {\n      font-size: var(--font-size-5xl);\n      animation: pulse 2s infinite;\n    }\n\n    .logo-text {\n      background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n      background-clip: text;\n    }\n\n    .tagline {\n      font-size: var(--font-size-lg);\n      color: var(--text-secondary);\n      font-weight: var(--font-weight-medium);\n      margin: 0;\n    }\n\n    .loading-spinner {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      gap: var(--space-sm);\n      margin-bottom: var(--space-xl);\n    }\n\n    .spinner-ring {\n      width: 12px;\n      height: 12px;\n      border-radius: 50%;\n      background: var(--primary-500);\n      animation: bounce 1.4s ease-in-out infinite both;\n    }\n\n    .spinner-ring:nth-child(1) { animation-delay: -0.32s; }\n    .spinner-ring:nth-child(2) { animation-delay: -0.16s; }\n\n    @keyframes bounce {\n      0%, 80%, 100% {\n        transform: scale(0);\n        opacity: 0.5;\n      }\n      40% {\n        transform: scale(1);\n        opacity: 1;\n      }\n    }\n\n    .loading-text {\n      font-size: var(--font-size-base);\n      color: var(--text-secondary);\n      margin: 0;\n      animation: pulse 2s infinite;\n    }\n\n    .main-content {\n      position: relative;\n      z-index: 1;\n    }\n\n    .bg-decorations {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      pointer-events: none;\n      z-index: 0;\n    }\n\n    .decoration {\n      position: absolute;\n      border-radius: 50%;\n      opacity: 0.1;\n      animation: float 6s ease-in-out infinite;\n    }\n\n    .decoration-1 {\n      width: 200px;\n      height: 200px;\n      background: var(--primary-300);\n      top: 10%;\n      right: 10%;\n      animation-delay: 0s;\n    }\n\n    .decoration-2 {\n      width: 150px;\n      height: 150px;\n      background: var(--secondary-300);\n      bottom: 20%;\n      left: 5%;\n      animation-delay: 2s;\n    }\n\n    .decoration-3 {\n      width: 100px;\n      height: 100px;\n      background: var(--accent-300);\n      top: 50%;\n      left: 50%;\n      animation-delay: 4s;\n    }\n\n    @keyframes float {\n      0%, 100% {\n        transform: translateY(0px) rotate(0deg);\n      }\n      50% {\n        transform: translateY(-20px) rotate(180deg);\n      }\n    }\n\n    .fade-in-up {\n      animation: fadeInUp 0.6s ease-out;\n    }\n\n    @media (max-width: 768px) {\n      .app-logo {\n        font-size: var(--font-size-3xl);\n        flex-direction: column;\n        gap: var(--space-sm);\n      }\n\n      .logo-icon {\n        font-size: var(--font-size-4xl);\n      }\n\n      .decoration {\n        display: none;\n      }\n    }\n  `]\n})\nexport class AppComponent implements OnInit {\n  title = 'OnlyWomans - Fashion for Every Woman';\n  isLoading = true;\n  loadingText = 'Loading your fashion world...';\n\n  private loadingTexts = [\n    'Loading your fashion world...',\n    'Curating the latest trends...',\n    'Preparing your style journey...',\n    'Setting up your wardrobe...',\n    'Almost ready to shop...'\n  ];\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.startLoadingSequence();\n    this.setupRouterEvents();\n  }\n\n  private startLoadingSequence() {\n    let textIndex = 0;\n    \n    // Change loading text every 800ms\n    const textInterval = setInterval(() => {\n      textIndex = (textIndex + 1) % this.loadingTexts.length;\n      this.loadingText = this.loadingTexts[textIndex];\n    }, 800);\n\n    // Hide loading screen after 3 seconds\n    setTimeout(() => {\n      clearInterval(textInterval);\n      this.isLoading = false;\n    }, 3000);\n  }\n\n  private setupRouterEvents() {\n    this.router.events\n      .pipe(filter(event => event instanceof NavigationEnd))\n      .subscribe((event) => {\n        // Add page-specific classes or analytics here\n        if (event instanceof NavigationEnd) {\n          this.updatePageMeta(event.url);\n        }\n      });\n  }\n\n  private updatePageMeta(url: string) {\n    // Update page title and meta tags based on route\n    const pageTitles: { [key: string]: string } = {\n      '/': 'OnlyWomans - Fashion for Every Woman',\n      '/shop': 'Shop - OnlyWomans',\n      '/categories': 'Categories - OnlyWomans',\n      '/beauty': 'Beauty & Makeup - OnlyWomans',\n      '/accessories': 'Accessories - OnlyWomans',\n      '/ethnic': 'Ethnic Wear - OnlyWomans',\n      '/western': 'Western Wear - OnlyWomans',\n      '/cart': 'Shopping Cart - OnlyWomans',\n      '/wishlist': 'My Wishlist - OnlyWomans',\n      '/profile': 'My Profile - OnlyWomans'\n    };\n\n    const pageTitle = pageTitles[url] || this.title;\n    document.title = pageTitle;\n\n    // Update meta description\n    const metaDescription = document.querySelector('meta[name=\"description\"]');\n    if (metaDescription) {\n      metaDescription.setAttribute('content', \n        'Discover the latest in women\\'s fashion, beauty, and accessories. Shop ethnic wear, western outfits, makeup, jewelry and more at OnlyWomans.'\n      );\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,EAAUC,aAAa,QAAQ,iBAAiB;AACrE,SAASC,MAAM,QAAQ,gBAAgB;;;;;;IAazBC,EAJR,CAAAC,cAAA,aAA8C,aACf,aACC,aACL,eACK;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjCH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACpCF,EADoC,CAAAG,YAAA,EAAO,EACtC;IACLH,EAAA,CAAAC,cAAA,YAAmB;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAC5CF,EAD4C,CAAAG,YAAA,EAAI,EAC1C;IAENH,EAAA,CAAAC,cAAA,eAA6B;IAG3BD,EAFA,CAAAI,SAAA,eAAgC,eACA,eACA;IAClCJ,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IAE7CF,EAF6C,CAAAG,YAAA,EAAI,EACzC,EACF;;;;IAFsBH,EAAA,CAAAK,SAAA,IAAiB;IAAjBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAiB;;;;;IAK7CR,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAI,SAAA,oBAA+B;IACjCJ,EAAA,CAAAG,YAAA,EAAM;;;AA0LZ,OAAM,MAAOM,YAAY;EAavBC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAZ1B,KAAAC,KAAK,GAAG,sCAAsC;IAC9C,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAAL,WAAW,GAAG,+BAA+B;IAErC,KAAAM,YAAY,GAAG,CACrB,+BAA+B,EAC/B,+BAA+B,EAC/B,iCAAiC,EACjC,6BAA6B,EAC7B,yBAAyB,CAC1B;EAEoC;EAErCC,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEQD,oBAAoBA,CAAA;IAC1B,IAAIE,SAAS,GAAG,CAAC;IAEjB;IACA,MAAMC,YAAY,GAAGC,WAAW,CAAC,MAAK;MACpCF,SAAS,GAAG,CAACA,SAAS,GAAG,CAAC,IAAI,IAAI,CAACJ,YAAY,CAACO,MAAM;MACtD,IAAI,CAACb,WAAW,GAAG,IAAI,CAACM,YAAY,CAACI,SAAS,CAAC;IACjD,CAAC,EAAE,GAAG,CAAC;IAEP;IACAI,UAAU,CAAC,MAAK;MACdC,aAAa,CAACJ,YAAY,CAAC;MAC3B,IAAI,CAACN,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;EAEQI,iBAAiBA,CAAA;IACvB,IAAI,CAACN,MAAM,CAACa,MAAM,CACfC,IAAI,CAAC1B,MAAM,CAAC2B,KAAK,IAAIA,KAAK,YAAY5B,aAAa,CAAC,CAAC,CACrD6B,SAAS,CAAED,KAAK,IAAI;MACnB;MACA,IAAIA,KAAK,YAAY5B,aAAa,EAAE;QAClC,IAAI,CAAC8B,cAAc,CAACF,KAAK,CAACG,GAAG,CAAC;;IAElC,CAAC,CAAC;EACN;EAEQD,cAAcA,CAACC,GAAW;IAChC;IACA,MAAMC,UAAU,GAA8B;MAC5C,GAAG,EAAE,sCAAsC;MAC3C,OAAO,EAAE,mBAAmB;MAC5B,aAAa,EAAE,yBAAyB;MACxC,SAAS,EAAE,8BAA8B;MACzC,cAAc,EAAE,0BAA0B;MAC1C,SAAS,EAAE,0BAA0B;MACrC,UAAU,EAAE,2BAA2B;MACvC,OAAO,EAAE,4BAA4B;MACrC,WAAW,EAAE,0BAA0B;MACvC,UAAU,EAAE;KACb;IAED,MAAMC,SAAS,GAAGD,UAAU,CAACD,GAAG,CAAC,IAAI,IAAI,CAACjB,KAAK;IAC/CoB,QAAQ,CAACpB,KAAK,GAAGmB,SAAS;IAE1B;IACA,MAAME,eAAe,GAAGD,QAAQ,CAACE,aAAa,CAAC,0BAA0B,CAAC;IAC1E,IAAID,eAAe,EAAE;MACnBA,eAAe,CAACE,YAAY,CAAC,SAAS,EACpC,8IAA8I,CAC/I;;EAEL;;;uBAxEW1B,YAAY,EAAAT,EAAA,CAAAoC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAZ7B,YAAY;MAAA8B,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAzC,EAAA,CAAA0C,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnNrBhD,EAAA,CAAAC,cAAA,aAAuD;UAuBrDD,EArBA,CAAAkD,UAAA,IAAAC,2BAAA,kBAA8C,IAAAC,2BAAA,iBAqBU;UAKxDpD,EAAA,CAAAC,cAAA,aAA4B;UAG1BD,EAFA,CAAAI,SAAA,aAA2C,aACA,aACA;UAE/CJ,EADE,CAAAG,YAAA,EAAM,EACF;;;UAjCqBH,EAAA,CAAAqD,WAAA,YAAAJ,GAAA,CAAApC,SAAA,CAA2B;UAE9Cb,EAAA,CAAAK,SAAA,EAAe;UAAfL,EAAA,CAAAsD,UAAA,SAAAL,GAAA,CAAApC,SAAA,CAAe;UAqBfb,EAAA,CAAAK,SAAA,EAAgB;UAAhBL,EAAA,CAAAsD,UAAA,UAAAL,GAAA,CAAApC,SAAA,CAAgB;;;qBAzBhBjB,YAAY,EAAA2D,EAAA,CAAAC,IAAA,EAAE3D,YAAY;MAAA4D,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}