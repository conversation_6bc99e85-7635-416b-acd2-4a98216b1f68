<ion-header [translucent]="true" class="ow-header">
  <ion-toolbar class="ow-toolbar">
    <ion-title class="ow-heading">Profile</ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="onEditProfile()">
        <ion-icon name="create" class="ow-icon"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ow-content">
  <!-- User Info Section -->
  <div class="user-section ow-gradient-primary">
    <div class="user-info">
      <div class="user-avatar">
        <img [src]="user.avatar" [alt]="user.name">
      </div>
      <div class="user-details">
        <h2 class="user-name">{{ user.name }}</h2>
        <p class="user-email">{{ user.email }}</p>
        <p class="member-since">Member since {{ user.memberSince }}</p>
      </div>
    </div>
  </div>

  <!-- Stats Section -->
  <div class="stats-section">
    <div class="stats-grid">
      <div class="stat-item ow-card">
        <div class="stat-number ow-text-primary">12</div>
        <div class="stat-label">Orders</div>
      </div>
      <div class="stat-item ow-card">
        <div class="stat-number ow-text-secondary">5</div>
        <div class="stat-label">Wishlist</div>
      </div>
      <div class="stat-item ow-card">
        <div class="stat-number ow-text-primary">₹25K</div>
        <div class="stat-label">Saved</div>
      </div>
    </div>
  </div>

  <!-- Menu Section -->
  <div class="menu-section">
    <div class="menu-items">
      <div 
        *ngFor="let item of menuItems" 
        class="menu-item ow-card"
        (click)="onMenuItemClick(item)">
        <div class="menu-icon" [style.background]="item.color">
          <ion-icon [name]="item.icon" class="icon"></ion-icon>
        </div>
        <div class="menu-content">
          <h4 class="menu-title ow-heading-sm">{{ item.title }}</h4>
          <p class="menu-subtitle">{{ item.subtitle }}</p>
        </div>
        <ion-icon name="chevron-forward" class="chevron-icon ow-icon-muted"></ion-icon>
      </div>
    </div>
  </div>

  <!-- Logout Section -->
  <div class="logout-section">
    <ion-button 
      fill="outline" 
      color="danger" 
      class="logout-btn"
      (click)="onLogout()">
      <ion-icon name="log-out" slot="start"></ion-icon>
      Logout
    </ion-button>
  </div>

  <!-- Bottom Spacing -->
  <div class="bottom-spacing"></div>
</ion-content>
