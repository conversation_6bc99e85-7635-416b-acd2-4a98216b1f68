/*
 * Only<PERSON>omans Mobile App - Global CSS
 * ----------------------------------------------------------------------------
 * Feminine and elegant styles for the women's fashion e-commerce mobile app
 * Inspired by modern fashion apps with sophisticated design elements
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/* Import Google Fonts for elegant typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

/* OnlyWomans Global Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--ow-font-family);
  background: linear-gradient(135deg, var(--ow-rose-50) 0%, var(--ow-purple-50) 100%);
  color: var(--ion-color-dark);
}

/* Custom Typography Classes */
.ow-heading {
  font-family: var(--ow-font-family-display);
  font-weight: 600;
  color: var(--ion-color-dark);
  line-height: 1.2;
}

.ow-heading-xl {
  font-size: 2rem;
  font-weight: 700;
}

.ow-heading-lg {
  font-size: 1.5rem;
  font-weight: 600;
}

.ow-heading-md {
  font-size: 1.25rem;
  font-weight: 600;
}

.ow-heading-sm {
  font-size: 1rem;
  font-weight: 500;
}

.ow-text-primary {
  color: var(--ion-color-primary);
}

.ow-text-secondary {
  color: var(--ion-color-secondary);
}

.ow-text-muted {
  color: var(--ion-color-medium);
}

/* Custom Button Styles */
.ow-btn {
  --border-radius: var(--ow-radius-lg);
  --box-shadow: var(--ow-shadow-md);
  font-weight: 500;
  text-transform: none;
  letter-spacing: 0.025em;
}

.ow-btn-primary {
  --background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ow-rose-600) 100%);
  --color: white;
}

.ow-btn-secondary {
  --background: linear-gradient(135deg, var(--ion-color-secondary) 0%, var(--ow-purple-600) 100%);
  --color: white;
}

.ow-btn-outline {
  --border-color: var(--ion-color-primary);
  --color: var(--ion-color-primary);
  --background: transparent;
}

.ow-btn-soft {
  --background: var(--ow-rose-100);
  --color: var(--ow-rose-700);
}

/* Card Styles */
.ow-card {
  --background: white;
  --border-radius: var(--ow-radius-lg);
  --box-shadow: var(--ow-shadow-md);
  margin: var(--ow-space-md);
  overflow: hidden;
}

.ow-card-elegant {
  --background: linear-gradient(135deg, white 0%, var(--ow-rose-50) 100%);
  border: 1px solid var(--ow-rose-200);
}

/* Product Card Styles */
.ow-product-card {
  --background: white;
  --border-radius: var(--ow-radius-lg);
  --box-shadow: var(--ow-shadow-sm);
  margin: var(--ow-space-sm);
  overflow: hidden;
  transition: all 0.3s ease;
}

.ow-product-card:hover {
  --box-shadow: var(--ow-shadow-lg);
  transform: translateY(-2px);
}

/* Input Styles */
.ow-input {
  --border-radius: var(--ow-radius-md);
  --border-color: var(--ow-rose-200);
  --color: var(--ion-color-dark);
  --placeholder-color: var(--ion-color-medium);
}

.ow-input.ion-focused {
  --border-color: var(--ion-color-primary);
  --highlight-color: var(--ion-color-primary);
}

/* Gradient Backgrounds */
.ow-gradient-primary {
  background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ow-rose-600) 100%);
}

.ow-gradient-secondary {
  background: linear-gradient(135deg, var(--ion-color-secondary) 0%, var(--ow-purple-600) 100%);
}

.ow-gradient-soft {
  background: linear-gradient(135deg, var(--ow-rose-50) 0%, var(--ow-purple-50) 100%);
}

/* Icon Styles */
.ow-icon {
  color: var(--ion-color-primary);
}

.ow-icon-secondary {
  color: var(--ion-color-secondary);
}

.ow-icon-muted {
  color: var(--ion-color-medium);
}

/* Badge Styles */
.ow-badge {
  --background: var(--ion-color-primary);
  --color: white;
  --border-radius: var(--ow-radius-full);
  font-size: 0.75rem;
  font-weight: 500;
}

.ow-badge-secondary {
  --background: var(--ion-color-secondary);
}

.ow-badge-success {
  --background: var(--ion-color-success);
}

.ow-badge-warning {
  --background: var(--ion-color-warning);
}

/* Animation Classes */
.ow-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.ow-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */
@import '@ionic/angular/css/palettes/dark.system.css';
