import { bootstrapApplication } from '@angular/platform-browser';
import { AppComponent } from './app/app.component';
import { provideRouter } from '@angular/router';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';
import { importProvidersFrom } from '@angular/core';
import { CommonModule } from '@angular/common';

// Routes
const routes = [
  {
    path: '',
    loadComponent: () => import('./app/pages/home/<USER>').then(m => m.HomeComponent)
  },
  {
    path: '**',
    redirectTo: ''
  }
];

// HTTP Interceptors
const authInterceptor = (req: any, next: any) => {
  const token = localStorage.getItem('onlywomans_token');
  if (token) {
    req = req.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`
      }
    });
  }
  return next(req);
};

const errorInterceptor = (req: any, next: any) => {
  return next(req);
};

bootstrapApplication(AppComponent, {
  providers: [
    provideRouter(routes),
    provideHttpClient(withInterceptors([authInterceptor, errorInterceptor])),
    provideAnimations(),
    importProvidersFrom(CommonModule)
  ]
}).catch(err => console.error(err));
