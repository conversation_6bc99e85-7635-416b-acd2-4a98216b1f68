{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-input.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, n as forceUpdate, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { c as createNotchController } from './notch-controller-C5LPspO8.js';\nimport { d as debounceEvent, i as inheritAriaAttributes, b as inheritAttributes, c as componentOnReady } from './helpers-1O4D2b7y.js';\nimport { c as createSlotMutationController, g as getCounterText } from './input.utils-zWijNCrx.js';\nimport { h as hostContext, c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { b as closeCircle, d as closeSharp } from './index-BLV6ykCk.js';\nimport './index-ZjP4CjeZ.js';\nconst inputIosCss = \".sc-ion-input-ios-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}ion-item[slot=start].sc-ion-input-ios-h,ion-item [slot=start].sc-ion-input-ios-h,ion-item[slot=end].sc-ion-input-ios-h,ion-item [slot=end].sc-ion-input-ios-h{width:auto}.ion-color.sc-ion-input-ios-h{--highlight-color-focused:var(--ion-color-base)}.input-label-placement-floating.sc-ion-input-ios-h,.input-label-placement-stacked.sc-ion-input-ios-h{min-height:56px}.native-input.sc-ion-input-ios{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;height:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-ios::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-ios:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-ios::-ms-clear{display:none}.cloned-input.sc-ion-input-ios{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-input-ios{inset-inline-start:0}.cloned-input.sc-ion-input-ios:disabled{opacity:1}.input-clear-icon.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{color:inherit}.input-clear-icon.sc-ion-input-ios:focus{opacity:0.5}.has-value.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{visibility:visible}.input-wrapper.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}.has-focus.ion-valid.sc-ion-input-ios-h,.ion-touched.ion-invalid.sc-ion-input-ios-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:block}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:none}.input-bottom.sc-ion-input-ios .counter.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-ios-h input.sc-ion-input-ios{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-ios,.sc-ion-input-ios-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-ios,.input-outline-notch-hidden.sc-ion-input-ios{display:none}.input-wrapper.sc-ion-input-ios input.sc-ion-input-ios{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text.sc-ion-input-ios{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-ios-h input.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios,.has-value.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:1}.label-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-ios-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-ios-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-input-ios-h[disabled].sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[disabled] .sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[readonly].sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[readonly] .sc-ion-input-ios-s>ion-input-password-toggle{display:none}.sc-ion-input-ios-h{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--highlight-height:0px;font-size:inherit}.input-clear-icon.sc-ion-input-ios ion-icon.sc-ion-input-ios{width:18px;height:18px}.input-disabled.sc-ion-input-ios-h{opacity:0.3}.sc-ion-input-ios-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-ios-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst inputMdCss = \".sc-ion-input-md-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}ion-item[slot=start].sc-ion-input-md-h,ion-item [slot=start].sc-ion-input-md-h,ion-item[slot=end].sc-ion-input-md-h,ion-item [slot=end].sc-ion-input-md-h{width:auto}.ion-color.sc-ion-input-md-h{--highlight-color-focused:var(--ion-color-base)}.input-label-placement-floating.sc-ion-input-md-h,.input-label-placement-stacked.sc-ion-input-md-h{min-height:56px}.native-input.sc-ion-input-md{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;height:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-md::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-md:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-md::-ms-clear{display:none}.cloned-input.sc-ion-input-md{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-input-md{inset-inline-start:0}.cloned-input.sc-ion-input-md:disabled{opacity:1}.input-clear-icon.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{color:inherit}.input-clear-icon.sc-ion-input-md:focus{opacity:0.5}.has-value.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{visibility:visible}.input-wrapper.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:block}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:none}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-md-h input.sc-ion-input-md{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-md,.sc-ion-input-md-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-md,.input-outline-notch-hidden.sc-ion-input-md{display:none}.input-wrapper.sc-ion-input-md input.sc-ion-input-md{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text.sc-ion-input-md{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md,.has-value.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:1}.label-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-md-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-md-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-input-md-h[disabled].sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[disabled] .sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[readonly].sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[readonly] .sc-ion-input-md-s>ion-input-password-toggle{display:none}.input-fill-solid.sc-ion-input-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:var(--border-width) var(--border-style) var(--border-color)}.has-focus.input-fill-solid.ion-valid.sc-ion-input-md-h,.input-fill-solid.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-fill-solid.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}@media (any-hover: hover){.input-fill-solid.sc-ion-input-md-h:hover{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.input-fill-solid.has-focus.sc-ion-input-md-h{--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}.label-floating.input-fill-solid.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{max-width:calc(100% / 0.75)}.input-fill-outline.sc-ion-input-md-h{--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-outline.input-shape-round.sc-ion-input-md-h{--border-radius:28px;--padding-start:32px;--padding-end:32px}.has-focus.input-fill-outline.ion-valid.sc-ion-input-md-h,.input-fill-outline.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}@media (any-hover: hover){.input-fill-outline.sc-ion-input-md-h:hover{--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.input-fill-outline.has-focus.sc-ion-input-md-h{--border-width:var(--highlight-height);--border-color:var(--highlight-color)}.input-fill-outline.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}.input-fill-outline.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:none}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{position:relative}.label-floating.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}.input-fill-outline.sc-ion-input-md-h .input-outline-container.sc-ion-input-md{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{pointer-events:none}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color)}.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{max-width:calc(100% - var(--padding-start) - var(--padding-end))}.input-fill-outline.sc-ion-input-md-h .notch-spacer.sc-ion-input-md{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none;-webkit-box-sizing:content-box;box-sizing:content-box}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius);-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color);width:calc(var(--padding-start) - 4px)}.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color);border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px;-ms-flex-positive:1;flex-grow:1}.label-floating.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{border-top:none}.sc-ion-input-md-h{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px;font-size:inherit}.input-clear-icon.sc-ion-input-md ion-icon.sc-ion-input-md{width:22px;height:22px}.input-disabled.sc-ion-input-md-h{opacity:0.38}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{letter-spacing:0.0333333333em}.input-label-placement-floating.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.has-focus.input-label-placement-floating.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.has-focus.input-label-placement-stacked.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.input-highlight.sc-ion-input-md{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.input-highlight.sc-ion-input-md{inset-inline-start:0}.has-focus.sc-ion-input-md-h .input-highlight.sc-ion-input-md{-webkit-transform:scale(1);transform:scale(1)}.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{bottom:0}.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{inset-inline-start:0}.input-shape-round.sc-ion-input-md-h{--border-radius:16px}.sc-ion-input-md-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-md-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst Input = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionInput = createEvent(this, \"ionInput\", 7);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.inputId = `ion-input-${inputIds++}`;\n    this.helperTextId = `${this.inputId}-helper-text`;\n    this.errorTextId = `${this.inputId}-error-text`;\n    this.inheritedAttributes = {};\n    this.isComposing = false;\n    /**\n     * `true` if the input was cleared as a result of the user typing\n     * with `clearOnEdit` enabled.\n     *\n     * Resets when the input loses focus.\n     */\n    this.didInputClearOnEdit = false;\n    /**\n     * The `hasFocus` state ensures the focus class is\n     * added regardless of how the element is focused.\n     * The `ion-focused` class only applies when focused\n     * via tabbing, not by clicking.\n     * The `has-focus` logic was added to ensure the class\n     * is applied in both cases.\n     */\n    this.hasFocus = false;\n    /**\n     * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user.\n     * Available options: `\"off\"`, `\"none\"`, `\"on\"`, `\"sentences\"`, `\"words\"`, `\"characters\"`.\n     */\n    this.autocapitalize = 'off';\n    /**\n     * Indicates whether the value of the control can be automatically completed by the browser.\n     */\n    this.autocomplete = 'off';\n    /**\n     * Whether auto correction should be enabled when the user is entering/editing the text value.\n     */\n    this.autocorrect = 'off';\n    /**\n     * Sets the [`autofocus` attribute](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/autofocus) on the native input element.\n     *\n     * This may not be sufficient for the element to be focused on page load. See [managing focus](/docs/developing/managing-focus) for more information.\n     */\n    this.autofocus = false;\n    /**\n     * If `true`, a clear icon will appear in the input when there is a value. Clicking it clears the input.\n     */\n    this.clearInput = false;\n    /**\n     * If `true`, a character counter will display the ratio of characters used and the total character limit. Developers must also set the `maxlength` property for the counter to be calculated correctly.\n     */\n    this.counter = false;\n    /**\n     * If `true`, the user cannot interact with the input.\n     */\n    this.disabled = false;\n    /**\n     * Where to place the label relative to the input.\n     * `\"start\"`: The label will appear to the left of the input in LTR and to the right in RTL.\n     * `\"end\"`: The label will appear to the right of the input in LTR and to the left in RTL.\n     * `\"floating\"`: The label will appear smaller and above the input when the input is focused or it has a value. Otherwise it will appear on top of the input.\n     * `\"stacked\"`: The label will appear smaller and above the input regardless even when the input is blurred or has no value.\n     * `\"fixed\"`: The label has the same behavior as `\"start\"` except it also has a fixed width. Long text will be truncated with ellipses (\"...\").\n     */\n    this.labelPlacement = 'start';\n    /**\n     * The name of the control, which is submitted with the form data.\n     */\n    this.name = this.inputId;\n    /**\n     * If `true`, the user cannot modify the value.\n     */\n    this.readonly = false;\n    /**\n     * If `true`, the user must fill in a value before submitting a form.\n     */\n    this.required = false;\n    /**\n     * If `true`, the element will have its spelling and grammar checked.\n     */\n    this.spellcheck = false;\n    /**\n     * The type of control to display. The default type is text.\n     */\n    this.type = 'text';\n    /**\n     * The value of the input.\n     */\n    this.value = '';\n    this.onInput = ev => {\n      const input = ev.target;\n      if (input) {\n        this.value = input.value || '';\n      }\n      this.emitInputChange(ev);\n    };\n    this.onChange = ev => {\n      this.emitValueChange(ev);\n    };\n    this.onBlur = ev => {\n      this.hasFocus = false;\n      if (this.focusedValue !== this.value) {\n        /**\n         * Emits the `ionChange` event when the input value\n         * is different than the value when the input was focused.\n         */\n        this.emitValueChange(ev);\n      }\n      this.didInputClearOnEdit = false;\n      this.ionBlur.emit(ev);\n    };\n    this.onFocus = ev => {\n      this.hasFocus = true;\n      this.focusedValue = this.value;\n      this.ionFocus.emit(ev);\n    };\n    this.onKeydown = ev => {\n      this.checkClearOnEdit(ev);\n    };\n    this.onCompositionStart = () => {\n      this.isComposing = true;\n    };\n    this.onCompositionEnd = () => {\n      this.isComposing = false;\n    };\n    this.clearTextInput = ev => {\n      if (this.clearInput && !this.readonly && !this.disabled && ev) {\n        ev.preventDefault();\n        ev.stopPropagation();\n        // Attempt to focus input again after pressing clear button\n        this.setFocus();\n      }\n      this.value = '';\n      this.emitInputChange(ev);\n    };\n    /**\n     * Stops propagation when the label is clicked,\n     * otherwise, two clicks will be triggered.\n     */\n    this.onLabelClick = ev => {\n      // Only stop propagation if the click was directly on the label\n      // and not on the input or other child elements\n      if (ev.target === ev.currentTarget) {\n        ev.stopPropagation();\n      }\n    };\n  }\n  debounceChanged() {\n    const {\n      ionInput,\n      debounce,\n      originalIonInput\n    } = this;\n    /**\n     * If debounce is undefined, we have to manually revert the ionInput emitter in case\n     * debounce used to be set to a number. Otherwise, the event would stay debounced.\n     */\n    this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n  }\n  /**\n   * Whenever the type on the input changes we need\n   * to update the internal type prop on the password\n   * toggle so that that correct icon is shown.\n   */\n  onTypeChange() {\n    const passwordToggle = this.el.querySelector('ion-input-password-toggle');\n    if (passwordToggle) {\n      passwordToggle.type = this.type;\n    }\n  }\n  /**\n   * Update the native input element when the value changes\n   */\n  valueChanged() {\n    const nativeInput = this.nativeInput;\n    const value = this.getValue();\n    if (nativeInput && nativeInput.value !== value && !this.isComposing) {\n      /**\n       * Assigning the native input's value on attribute\n       * value change, allows `ionInput` implementations\n       * to override the control's value.\n       *\n       * Used for patterns such as input trimming (removing whitespace),\n       * or input masking.\n       */\n      nativeInput.value = value;\n    }\n  }\n  /**\n   * dir is a globally enumerated attribute.\n   * As a result, creating these as properties\n   * can have unintended side effects. Instead, we\n   * listen for attribute changes and inherit them\n   * to the inner `<input>` element.\n   */\n  onDirChanged(newValue) {\n    this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), {\n      dir: newValue\n    });\n    forceUpdate(this);\n  }\n  /**\n   * This prevents the native input from emitting the click event.\n   * Instead, the click event from the ion-input is emitted.\n   */\n  onClickCapture(ev) {\n    const nativeInput = this.nativeInput;\n    if (nativeInput && ev.target === nativeInput) {\n      ev.stopPropagation();\n      this.el.click();\n    }\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign(Object.assign({}, inheritAriaAttributes(this.el)), inheritAttributes(this.el, ['tabindex', 'title', 'data-form-type', 'dir']));\n  }\n  connectedCallback() {\n    const {\n      el\n    } = this;\n    this.slotMutationController = createSlotMutationController(el, ['label', 'start', 'end'], () => forceUpdate(this));\n    this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n    this.debounceChanged();\n    {\n      document.dispatchEvent(new CustomEvent('ionInputDidLoad', {\n        detail: this.el\n      }));\n    }\n  }\n  componentDidLoad() {\n    this.originalIonInput = this.ionInput;\n    /**\n     * Set the type on the password toggle in the event that this input's\n     * type was set async and does not match the default type for the password toggle.\n     * This can happen when the type is bound using a JS framework binding syntax\n     * such as [type] in Angular.\n     */\n    this.onTypeChange();\n    this.debounceChanged();\n  }\n  componentDidRender() {\n    var _a;\n    (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n  }\n  disconnectedCallback() {\n    {\n      document.dispatchEvent(new CustomEvent('ionInputDidUnload', {\n        detail: this.el\n      }));\n    }\n    if (this.slotMutationController) {\n      this.slotMutationController.destroy();\n      this.slotMutationController = undefined;\n    }\n    if (this.notchController) {\n      this.notchController.destroy();\n      this.notchController = undefined;\n    }\n  }\n  /**\n   * Sets focus on the native `input` in `ion-input`. Use this method instead of the global\n   * `input.focus()`.\n   *\n   * Developers who wish to focus an input when a page enters\n   * should call `setFocus()` in the `ionViewDidEnter()` lifecycle method.\n   *\n   * Developers who wish to focus an input when an overlay is presented\n   * should call `setFocus` after `didPresent` has resolved.\n   *\n   * See [managing focus](/docs/developing/managing-focus) for more information.\n   */\n  async setFocus() {\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  }\n  /**\n   * Returns the native `<input>` element used under the hood.\n   */\n  async getInputElement() {\n    /**\n     * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n     * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n     */\n    if (!this.nativeInput) {\n      await new Promise(resolve => componentOnReady(this.el, resolve));\n    }\n    return Promise.resolve(this.nativeInput);\n  }\n  /**\n   * Emits an `ionChange` event.\n   *\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitValueChange(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    // Emitting a value change should update the internal state for tracking the focused value\n    this.focusedValue = newValue;\n    this.ionChange.emit({\n      value: newValue,\n      event\n    });\n  }\n  /**\n   * Emits an `ionInput` event.\n   */\n  emitInputChange(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    this.ionInput.emit({\n      value: newValue,\n      event\n    });\n  }\n  shouldClearOnEdit() {\n    const {\n      type,\n      clearOnEdit\n    } = this;\n    return clearOnEdit === undefined ? type === 'password' : clearOnEdit;\n  }\n  getValue() {\n    return typeof this.value === 'number' ? this.value.toString() : (this.value || '').toString();\n  }\n  checkClearOnEdit(ev) {\n    if (!this.shouldClearOnEdit()) {\n      return;\n    }\n    /**\n     * The following keys do not modify the\n     * contents of the input. As a result, pressing\n     * them should not edit the input.\n     *\n     * We can't check to see if the value of the input\n     * was changed because we call checkClearOnEdit\n     * in a keydown listener, and the key has not yet\n     * been added to the input.\n     */\n    const IGNORED_KEYS = ['Enter', 'Tab', 'Shift', 'Meta', 'Alt', 'Control'];\n    const pressedIgnoredKey = IGNORED_KEYS.includes(ev.key);\n    /**\n     * Clear the input if the control has not been previously cleared during focus.\n     * Do not clear if the user hitting enter to submit a form.\n     */\n    if (!this.didInputClearOnEdit && this.hasValue() && !pressedIgnoredKey) {\n      this.value = '';\n      this.emitInputChange(ev);\n    }\n    /**\n     * Pressing an IGNORED_KEYS first and\n     * then an allowed key will cause the input to not\n     * be cleared.\n     */\n    if (!pressedIgnoredKey) {\n      this.didInputClearOnEdit = true;\n    }\n  }\n  hasValue() {\n    return this.getValue().length > 0;\n  }\n  /**\n   * Renders the helper text or error text values\n   */\n  renderHintText() {\n    const {\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    return [h(\"div\", {\n      id: helperTextId,\n      class: \"helper-text\"\n    }, helperText), h(\"div\", {\n      id: errorTextId,\n      class: \"error-text\"\n    }, errorText)];\n  }\n  getHintTextID() {\n    const {\n      el,\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n      return errorTextId;\n    }\n    if (helperText) {\n      return helperTextId;\n    }\n    return undefined;\n  }\n  renderCounter() {\n    const {\n      counter,\n      maxlength,\n      counterFormatter,\n      value\n    } = this;\n    if (counter !== true || maxlength === undefined) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"counter\"\n    }, getCounterText(value, maxlength, counterFormatter));\n  }\n  /**\n   * Responsible for rendering helper text,\n   * error text, and counter. This element should only\n   * be rendered if hint text is set or counter is enabled.\n   */\n  renderBottomContent() {\n    const {\n      counter,\n      helperText,\n      errorText,\n      maxlength\n    } = this;\n    /**\n     * undefined and empty string values should\n     * be treated as not having helper/error text.\n     */\n    const hasHintText = !!helperText || !!errorText;\n    const hasCounter = counter === true && maxlength !== undefined;\n    if (!hasHintText && !hasCounter) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"input-bottom\"\n    }, this.renderHintText(), this.renderCounter());\n  }\n  renderLabel() {\n    const {\n      label\n    } = this;\n    return h(\"div\", {\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !this.hasLabel\n      }\n    }, label === undefined ? h(\"slot\", {\n      name: \"label\"\n    }) : h(\"div\", {\n      class: \"label-text\"\n    }, label));\n  }\n  /**\n   * Gets any content passed into the `label` slot,\n   * not the <slot> definition.\n   */\n  get labelSlot() {\n    return this.el.querySelector('[slot=\"label\"]');\n  }\n  /**\n   * Returns `true` if label content is provided\n   * either by a prop or a content. If you want\n   * to get the plaintext value of the label use\n   * the `labelText` getter instead.\n   */\n  get hasLabel() {\n    return this.label !== undefined || this.labelSlot !== null;\n  }\n  /**\n   * Renders the border container\n   * when fill=\"outline\".\n   */\n  renderLabelContainer() {\n    const mode = getIonMode(this);\n    const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n    if (hasOutlineFill) {\n      /**\n       * The outline fill has a special outline\n       * that appears around the input and the label.\n       * Certain stacked and floating label placements cause the\n       * label to translate up and create a \"cut out\"\n       * inside of that border by using the notch-spacer element.\n       */\n      return [h(\"div\", {\n        class: \"input-outline-container\"\n      }, h(\"div\", {\n        class: \"input-outline-start\"\n      }), h(\"div\", {\n        class: {\n          'input-outline-notch': true,\n          'input-outline-notch-hidden': !this.hasLabel\n        }\n      }, h(\"div\", {\n        class: \"notch-spacer\",\n        \"aria-hidden\": \"true\",\n        ref: el => this.notchSpacerEl = el\n      }, this.label)), h(\"div\", {\n        class: \"input-outline-end\"\n      })), this.renderLabel()];\n    }\n    /**\n     * If not using the outline style,\n     * we can render just the label.\n     */\n    return this.renderLabel();\n  }\n  render() {\n    const {\n      disabled,\n      fill,\n      readonly,\n      shape,\n      inputId,\n      labelPlacement,\n      el,\n      hasFocus,\n      clearInputIcon\n    } = this;\n    const mode = getIonMode(this);\n    const value = this.getValue();\n    const inItem = hostContext('ion-item', this.el);\n    const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n    const defaultClearIcon = mode === 'ios' ? closeCircle : closeSharp;\n    const clearIconData = clearInputIcon !== null && clearInputIcon !== void 0 ? clearInputIcon : defaultClearIcon;\n    const hasValue = this.hasValue();\n    const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n    /**\n     * If the label is stacked, it should always sit above the input.\n     * For floating labels, the label should move above the input if\n     * the input has a value, is focused, or has anything in either\n     * the start or end slot.\n     *\n     * If there is content in the start slot, the label would overlap\n     * it if not forced to float. This is also applied to the end slot\n     * because with the default or solid fills, the input is not\n     * vertically centered in the container, but the label is. This\n     * causes the slots and label to appear vertically offset from each\n     * other when the label isn't floating above the input. This doesn't\n     * apply to the outline fill, but this was not accounted for to keep\n     * things consistent.\n     *\n     * TODO(FW-5592): Remove hasStartEndSlots condition\n     */\n    const labelShouldFloat = labelPlacement === 'stacked' || labelPlacement === 'floating' && (hasValue || hasFocus || hasStartEndSlots);\n    return h(Host, {\n      key: '41b2526627e7d2773a80f011b123284203a71ca0',\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'has-value': hasValue,\n        'has-focus': hasFocus,\n        'label-floating': labelShouldFloat,\n        [`input-fill-${fill}`]: fill !== undefined,\n        [`input-shape-${shape}`]: shape !== undefined,\n        [`input-label-placement-${labelPlacement}`]: true,\n        'in-item': inItem,\n        'in-item-color': hostContext('ion-item.ion-color', this.el),\n        'input-disabled': disabled\n      })\n    }, h(\"label\", {\n      key: '9ab078363e32528102b441ad1791d83f86fdcbdc',\n      class: \"input-wrapper\",\n      htmlFor: inputId,\n      onClick: this.onLabelClick\n    }, this.renderLabelContainer(), h(\"div\", {\n      key: 'e34b594980ec62e4c618e827fadf7669a39ad0d8',\n      class: \"native-wrapper\",\n      onClick: this.onLabelClick\n    }, h(\"slot\", {\n      key: '12dc04ead5502e9e5736240e918bf9331bf7b5d9',\n      name: \"start\"\n    }), h(\"input\", Object.assign({\n      key: 'df356eb4ced23109b2c0242f36dc043aba8782d6',\n      class: \"native-input\",\n      ref: input => this.nativeInput = input,\n      id: inputId,\n      disabled: disabled,\n      autoCapitalize: this.autocapitalize,\n      autoComplete: this.autocomplete,\n      autoCorrect: this.autocorrect,\n      autoFocus: this.autofocus,\n      enterKeyHint: this.enterkeyhint,\n      inputMode: this.inputmode,\n      min: this.min,\n      max: this.max,\n      minLength: this.minlength,\n      maxLength: this.maxlength,\n      multiple: this.multiple,\n      name: this.name,\n      pattern: this.pattern,\n      placeholder: this.placeholder || '',\n      readOnly: readonly,\n      required: this.required,\n      spellcheck: this.spellcheck,\n      step: this.step,\n      type: this.type,\n      value: value,\n      onInput: this.onInput,\n      onChange: this.onChange,\n      onBlur: this.onBlur,\n      onFocus: this.onFocus,\n      onKeyDown: this.onKeydown,\n      onCompositionstart: this.onCompositionStart,\n      onCompositionend: this.onCompositionEnd,\n      \"aria-describedby\": this.getHintTextID(),\n      \"aria-invalid\": this.getHintTextID() === this.errorTextId\n    }, this.inheritedAttributes)), this.clearInput && !readonly && !disabled && h(\"button\", {\n      key: 'f79f68cabcd4ea99419331174a377827db0c0741',\n      \"aria-label\": \"reset\",\n      type: \"button\",\n      class: \"input-clear-icon\",\n      onPointerDown: ev => {\n        /**\n         * This prevents mobile browsers from\n         * blurring the input when the clear\n         * button is activated.\n         */\n        ev.preventDefault();\n      },\n      onClick: this.clearTextInput\n    }, h(\"ion-icon\", {\n      key: '237ec07ec2e10f08818a332bb596578c2c49f770',\n      \"aria-hidden\": \"true\",\n      icon: clearIconData\n    })), h(\"slot\", {\n      key: '1f0a3624aa3e8dc3c307a6762230ab698768a5e5',\n      name: \"end\"\n    })), shouldRenderHighlight && h(\"div\", {\n      key: '8a8cbb82695a722a0010b53dd0b1f1f97534a20b',\n      class: \"input-highlight\"\n    })), this.renderBottomContent());\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"debounce\": [\"debounceChanged\"],\n      \"type\": [\"onTypeChange\"],\n      \"value\": [\"valueChanged\"],\n      \"dir\": [\"onDirChanged\"]\n    };\n  }\n};\nlet inputIds = 0;\nInput.style = {\n  ios: inputIosCss,\n  md: inputMdCss\n};\nexport { Input as ion_input };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,QAAQ,MAAM;AAAA,EAClB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,UAAU,aAAa,UAAU;AACtC,SAAK,eAAe,GAAG,KAAK,OAAO;AACnC,SAAK,cAAc,GAAG,KAAK,OAAO;AAClC,SAAK,sBAAsB,CAAC;AAC5B,SAAK,cAAc;AAOnB,SAAK,sBAAsB;AAS3B,SAAK,WAAW;AAKhB,SAAK,iBAAiB;AAItB,SAAK,eAAe;AAIpB,SAAK,cAAc;AAMnB,SAAK,YAAY;AAIjB,SAAK,aAAa;AAIlB,SAAK,UAAU;AAIf,SAAK,WAAW;AAShB,SAAK,iBAAiB;AAItB,SAAK,OAAO,KAAK;AAIjB,SAAK,WAAW;AAIhB,SAAK,WAAW;AAIhB,SAAK,aAAa;AAIlB,SAAK,OAAO;AAIZ,SAAK,QAAQ;AACb,SAAK,UAAU,QAAM;AACnB,YAAM,QAAQ,GAAG;AACjB,UAAI,OAAO;AACT,aAAK,QAAQ,MAAM,SAAS;AAAA,MAC9B;AACA,WAAK,gBAAgB,EAAE;AAAA,IACzB;AACA,SAAK,WAAW,QAAM;AACpB,WAAK,gBAAgB,EAAE;AAAA,IACzB;AACA,SAAK,SAAS,QAAM;AAClB,WAAK,WAAW;AAChB,UAAI,KAAK,iBAAiB,KAAK,OAAO;AAKpC,aAAK,gBAAgB,EAAE;AAAA,MACzB;AACA,WAAK,sBAAsB;AAC3B,WAAK,QAAQ,KAAK,EAAE;AAAA,IACtB;AACA,SAAK,UAAU,QAAM;AACnB,WAAK,WAAW;AAChB,WAAK,eAAe,KAAK;AACzB,WAAK,SAAS,KAAK,EAAE;AAAA,IACvB;AACA,SAAK,YAAY,QAAM;AACrB,WAAK,iBAAiB,EAAE;AAAA,IAC1B;AACA,SAAK,qBAAqB,MAAM;AAC9B,WAAK,cAAc;AAAA,IACrB;AACA,SAAK,mBAAmB,MAAM;AAC5B,WAAK,cAAc;AAAA,IACrB;AACA,SAAK,iBAAiB,QAAM;AAC1B,UAAI,KAAK,cAAc,CAAC,KAAK,YAAY,CAAC,KAAK,YAAY,IAAI;AAC7D,WAAG,eAAe;AAClB,WAAG,gBAAgB;AAEnB,aAAK,SAAS;AAAA,MAChB;AACA,WAAK,QAAQ;AACb,WAAK,gBAAgB,EAAE;AAAA,IACzB;AAKA,SAAK,eAAe,QAAM;AAGxB,UAAI,GAAG,WAAW,GAAG,eAAe;AAClC,WAAG,gBAAgB;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAKJ,SAAK,WAAW,aAAa,SAAY,qBAAqB,QAAQ,qBAAqB,SAAS,mBAAmB,WAAW,cAAc,UAAU,QAAQ;AAAA,EACpK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AACb,UAAM,iBAAiB,KAAK,GAAG,cAAc,2BAA2B;AACxE,QAAI,gBAAgB;AAClB,qBAAe,OAAO,KAAK;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,UAAM,cAAc,KAAK;AACzB,UAAM,QAAQ,KAAK,SAAS;AAC5B,QAAI,eAAe,YAAY,UAAU,SAAS,CAAC,KAAK,aAAa;AASnE,kBAAY,QAAQ;AAAA,IACtB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,UAAU;AACrB,SAAK,sBAAsB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,mBAAmB,GAAG;AAAA,MACpF,KAAK;AAAA,IACP,CAAC;AACD,gBAAY,IAAI;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,IAAI;AACjB,UAAM,cAAc,KAAK;AACzB,QAAI,eAAe,GAAG,WAAW,aAAa;AAC5C,SAAG,gBAAgB;AACnB,WAAK,GAAG,MAAM;AAAA,IAChB;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,sBAAsB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,sBAAsB,KAAK,EAAE,CAAC,GAAG,kBAAkB,KAAK,IAAI,CAAC,YAAY,SAAS,kBAAkB,KAAK,CAAC,CAAC;AAAA,EACxK;AAAA,EACA,oBAAoB;AAClB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,SAAK,yBAAyB,6BAA6B,IAAI,CAAC,SAAS,SAAS,KAAK,GAAG,MAAM,YAAY,IAAI,CAAC;AACjH,SAAK,kBAAkB,sBAAsB,IAAI,MAAM,KAAK,eAAe,MAAM,KAAK,SAAS;AAC/F,SAAK,gBAAgB;AACrB;AACE,eAAS,cAAc,IAAI,YAAY,mBAAmB;AAAA,QACxD,QAAQ,KAAK;AAAA,MACf,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,SAAK,mBAAmB,KAAK;AAO7B,SAAK,aAAa;AAClB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,qBAAqB;AACnB,QAAI;AACJ,KAAC,KAAK,KAAK,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,oBAAoB;AAAA,EAC1F;AAAA,EACA,uBAAuB;AACrB;AACE,eAAS,cAAc,IAAI,YAAY,qBAAqB;AAAA,QAC1D,QAAQ,KAAK;AAAA,MACf,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,KAAK,wBAAwB;AAC/B,WAAK,uBAAuB,QAAQ;AACpC,WAAK,yBAAyB;AAAA,IAChC;AACA,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,QAAQ;AAC7B,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaM,WAAW;AAAA;AACf,UAAI,KAAK,aAAa;AACpB,aAAK,YAAY,MAAM;AAAA,MACzB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,kBAAkB;AAAA;AAKtB,UAAI,CAAC,KAAK,aAAa;AACrB,cAAM,IAAI,QAAQ,aAAW,iBAAiB,KAAK,IAAI,OAAO,CAAC;AAAA,MACjE;AACA,aAAO,QAAQ,QAAQ,KAAK,WAAW;AAAA,IACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,OAAO;AACrB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AAEJ,UAAM,WAAW,SAAS,OAAO,QAAQ,MAAM,SAAS;AAExD,SAAK,eAAe;AACpB,SAAK,UAAU,KAAK;AAAA,MAClB,OAAO;AAAA,MACP;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,OAAO;AACrB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AAEJ,UAAM,WAAW,SAAS,OAAO,QAAQ,MAAM,SAAS;AACxD,SAAK,SAAS,KAAK;AAAA,MACjB,OAAO;AAAA,MACP;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,gBAAgB,SAAY,SAAS,aAAa;AAAA,EAC3D;AAAA,EACA,WAAW;AACT,WAAO,OAAO,KAAK,UAAU,WAAW,KAAK,MAAM,SAAS,KAAK,KAAK,SAAS,IAAI,SAAS;AAAA,EAC9F;AAAA,EACA,iBAAiB,IAAI;AACnB,QAAI,CAAC,KAAK,kBAAkB,GAAG;AAC7B;AAAA,IACF;AAWA,UAAM,eAAe,CAAC,SAAS,OAAO,SAAS,QAAQ,OAAO,SAAS;AACvE,UAAM,oBAAoB,aAAa,SAAS,GAAG,GAAG;AAKtD,QAAI,CAAC,KAAK,uBAAuB,KAAK,SAAS,KAAK,CAAC,mBAAmB;AACtE,WAAK,QAAQ;AACb,WAAK,gBAAgB,EAAE;AAAA,IACzB;AAMA,QAAI,CAAC,mBAAmB;AACtB,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,WAAW;AACT,WAAO,KAAK,SAAS,EAAE,SAAS;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,EAAE,OAAO;AAAA,MACf,IAAI;AAAA,MACJ,OAAO;AAAA,IACT,GAAG,UAAU,GAAG,EAAE,OAAO;AAAA,MACvB,IAAI;AAAA,MACJ,OAAO;AAAA,IACT,GAAG,SAAS,CAAC;AAAA,EACf;AAAA,EACA,gBAAgB;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,GAAG,UAAU,SAAS,aAAa,KAAK,GAAG,UAAU,SAAS,aAAa,KAAK,WAAW;AAC7F,aAAO;AAAA,IACT;AACA,QAAI,YAAY;AACd,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,YAAY,QAAQ,cAAc,QAAW;AAC/C;AAAA,IACF;AACA,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,IACT,GAAG,eAAe,OAAO,WAAW,gBAAgB,CAAC;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB;AACpB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAKJ,UAAM,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,UAAM,aAAa,YAAY,QAAQ,cAAc;AACrD,QAAI,CAAC,eAAe,CAAC,YAAY;AAC/B;AAAA,IACF;AACA,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,IACT,GAAG,KAAK,eAAe,GAAG,KAAK,cAAc,CAAC;AAAA,EAChD;AAAA,EACA,cAAc;AACZ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,QACL,sBAAsB;AAAA,QACtB,6BAA6B,CAAC,KAAK;AAAA,MACrC;AAAA,IACF,GAAG,UAAU,SAAY,EAAE,QAAQ;AAAA,MACjC,MAAM;AAAA,IACR,CAAC,IAAI,EAAE,OAAO;AAAA,MACZ,OAAO;AAAA,IACT,GAAG,KAAK,CAAC;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACd,WAAO,KAAK,GAAG,cAAc,gBAAgB;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,UAAa,KAAK,cAAc;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,iBAAiB,SAAS,QAAQ,KAAK,SAAS;AACtD,QAAI,gBAAgB;AAQlB,aAAO,CAAC,EAAE,OAAO;AAAA,QACf,OAAO;AAAA,MACT,GAAG,EAAE,OAAO;AAAA,QACV,OAAO;AAAA,MACT,CAAC,GAAG,EAAE,OAAO;AAAA,QACX,OAAO;AAAA,UACL,uBAAuB;AAAA,UACvB,8BAA8B,CAAC,KAAK;AAAA,QACtC;AAAA,MACF,GAAG,EAAE,OAAO;AAAA,QACV,OAAO;AAAA,QACP,eAAe;AAAA,QACf,KAAK,QAAM,KAAK,gBAAgB;AAAA,MAClC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE,OAAO;AAAA,QACxB,OAAO;AAAA,MACT,CAAC,CAAC,GAAG,KAAK,YAAY,CAAC;AAAA,IACzB;AAKA,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,SAAS,YAAY,YAAY,KAAK,EAAE;AAC9C,UAAM,wBAAwB,SAAS,QAAQ,SAAS,aAAa,CAAC;AACtE,UAAM,mBAAmB,SAAS,QAAQ,cAAc;AACxD,UAAM,gBAAgB,mBAAmB,QAAQ,mBAAmB,SAAS,iBAAiB;AAC9F,UAAM,WAAW,KAAK,SAAS;AAC/B,UAAM,mBAAmB,GAAG,cAAc,8BAA8B,MAAM;AAkB9E,UAAM,mBAAmB,mBAAmB,aAAa,mBAAmB,eAAe,YAAY,YAAY;AACnH,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,mBAAmB,KAAK,OAAO;AAAA,QACpC,CAAC,IAAI,GAAG;AAAA,QACR,aAAa;AAAA,QACb,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,CAAC,cAAc,IAAI,EAAE,GAAG,SAAS;AAAA,QACjC,CAAC,eAAe,KAAK,EAAE,GAAG,UAAU;AAAA,QACpC,CAAC,yBAAyB,cAAc,EAAE,GAAG;AAAA,QAC7C,WAAW;AAAA,QACX,iBAAiB,YAAY,sBAAsB,KAAK,EAAE;AAAA,QAC1D,kBAAkB;AAAA,MACpB,CAAC;AAAA,IACH,GAAG,EAAE,SAAS;AAAA,MACZ,KAAK;AAAA,MACL,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS,KAAK;AAAA,IAChB,GAAG,KAAK,qBAAqB,GAAG,EAAE,OAAO;AAAA,MACvC,KAAK;AAAA,MACL,OAAO;AAAA,MACP,SAAS,KAAK;AAAA,IAChB,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,MACL,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,SAAS,OAAO,OAAO;AAAA,MAC3B,KAAK;AAAA,MACL,OAAO;AAAA,MACP,KAAK,WAAS,KAAK,cAAc;AAAA,MACjC,IAAI;AAAA,MACJ;AAAA,MACA,gBAAgB,KAAK;AAAA,MACrB,cAAc,KAAK;AAAA,MACnB,aAAa,KAAK;AAAA,MAClB,WAAW,KAAK;AAAA,MAChB,cAAc,KAAK;AAAA,MACnB,WAAW,KAAK;AAAA,MAChB,KAAK,KAAK;AAAA,MACV,KAAK,KAAK;AAAA,MACV,WAAW,KAAK;AAAA,MAChB,WAAW,KAAK;AAAA,MAChB,UAAU,KAAK;AAAA,MACf,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,aAAa,KAAK,eAAe;AAAA,MACjC,UAAU;AAAA,MACV,UAAU,KAAK;AAAA,MACf,YAAY,KAAK;AAAA,MACjB,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX;AAAA,MACA,SAAS,KAAK;AAAA,MACd,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,MACd,WAAW,KAAK;AAAA,MAChB,oBAAoB,KAAK;AAAA,MACzB,kBAAkB,KAAK;AAAA,MACvB,oBAAoB,KAAK,cAAc;AAAA,MACvC,gBAAgB,KAAK,cAAc,MAAM,KAAK;AAAA,IAChD,GAAG,KAAK,mBAAmB,CAAC,GAAG,KAAK,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,UAAU;AAAA,MACtF,KAAK;AAAA,MACL,cAAc;AAAA,MACd,MAAM;AAAA,MACN,OAAO;AAAA,MACP,eAAe,QAAM;AAMnB,WAAG,eAAe;AAAA,MACpB;AAAA,MACA,SAAS,KAAK;AAAA,IAChB,GAAG,EAAE,YAAY;AAAA,MACf,KAAK;AAAA,MACL,eAAe;AAAA,MACf,MAAM;AAAA,IACR,CAAC,CAAC,GAAG,EAAE,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,MAAM;AAAA,IACR,CAAC,CAAC,GAAG,yBAAyB,EAAE,OAAO;AAAA,MACrC,KAAK;AAAA,MACL,OAAO;AAAA,IACT,CAAC,CAAC,GAAG,KAAK,oBAAoB,CAAC;AAAA,EACjC;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,YAAY,CAAC,iBAAiB;AAAA,MAC9B,QAAQ,CAAC,cAAc;AAAA,MACvB,SAAS,CAAC,cAAc;AAAA,MACxB,OAAO,CAAC,cAAc;AAAA,IACxB;AAAA,EACF;AACF;AACA,IAAI,WAAW;AACf,MAAM,QAAQ;AAAA,EACZ,KAAK;AAAA,EACL,IAAI;AACN;", "names": []}