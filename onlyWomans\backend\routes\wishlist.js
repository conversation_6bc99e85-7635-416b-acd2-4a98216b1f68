const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');

// Sample wishlist data
let wishlists = [
  {
    userId: 'user123',
    items: [
      {
        productId: 2,
        name: 'Silk Saree with Blouse',
        price: 4999,
        originalPrice: 6999,
        image: '/assets/products/saree1.jpg',
        brand: 'EthnicElegance',
        addedAt: new Date('2024-06-20')
      }
    ]
  }
];

// @route   GET /api/wishlist
// @desc    Get user wishlist
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    let userWishlist = wishlists.find(wishlist => wishlist.userId === req.user.id);
    
    if (!userWishlist) {
      userWishlist = { userId: req.user.id, items: [] };
      wishlists.push(userWishlist);
    }
    
    res.json({
      success: true,
      count: userWishlist.items.length,
      data: userWishlist.items
    });
  } catch (error) {
    console.error('Error fetching wishlist:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   POST /api/wishlist
// @desc    Add item to wishlist
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    const { productId, name, price, originalPrice, image, brand } = req.body;
    
    let userWishlist = wishlists.find(wishlist => wishlist.userId === req.user.id);
    
    if (!userWishlist) {
      userWishlist = { userId: req.user.id, items: [] };
      wishlists.push(userWishlist);
    }
    
    // Check if item already exists
    const existingItem = userWishlist.items.find(item => item.productId === productId);
    
    if (existingItem) {
      return res.status(400).json({
        success: false,
        message: 'Item already in wishlist'
      });
    }
    
    const newItem = {
      productId,
      name,
      price,
      originalPrice,
      image,
      brand,
      addedAt: new Date()
    };
    
    userWishlist.items.push(newItem);
    
    res.status(201).json({
      success: true,
      data: newItem,
      message: 'Item added to wishlist'
    });
  } catch (error) {
    console.error('Error adding to wishlist:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   DELETE /api/wishlist/:productId
// @desc    Remove item from wishlist
// @access  Private
router.delete('/:productId', auth, async (req, res) => {
  try {
    const productId = parseInt(req.params.productId);
    const userWishlist = wishlists.find(wishlist => wishlist.userId === req.user.id);
    
    if (!userWishlist) {
      return res.status(404).json({
        success: false,
        message: 'Wishlist not found'
      });
    }
    
    const itemIndex = userWishlist.items.findIndex(item => item.productId === productId);
    
    if (itemIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Item not found in wishlist'
      });
    }
    
    userWishlist.items.splice(itemIndex, 1);
    
    res.json({
      success: true,
      message: 'Item removed from wishlist'
    });
  } catch (error) {
    console.error('Error removing from wishlist:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   POST /api/wishlist/:productId/move-to-cart
// @desc    Move item from wishlist to cart
// @access  Private
router.post('/:productId/move-to-cart', auth, async (req, res) => {
  try {
    const productId = parseInt(req.params.productId);
    const userWishlist = wishlists.find(wishlist => wishlist.userId === req.user.id);
    
    if (!userWishlist) {
      return res.status(404).json({
        success: false,
        message: 'Wishlist not found'
      });
    }
    
    const itemIndex = userWishlist.items.findIndex(item => item.productId === productId);
    
    if (itemIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Item not found in wishlist'
      });
    }
    
    const item = userWishlist.items[itemIndex];
    
    // Here you would typically add to cart
    // For now, just remove from wishlist
    userWishlist.items.splice(itemIndex, 1);
    
    res.json({
      success: true,
      message: 'Item moved to cart',
      data: item
    });
  } catch (error) {
    console.error('Error moving to cart:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   DELETE /api/wishlist
// @desc    Clear entire wishlist
// @access  Private
router.delete('/', auth, async (req, res) => {
  try {
    const userWishlist = wishlists.find(wishlist => wishlist.userId === req.user.id);
    
    if (!userWishlist) {
      return res.status(404).json({
        success: false,
        message: 'Wishlist not found'
      });
    }
    
    userWishlist.items = [];
    
    res.json({
      success: true,
      message: 'Wishlist cleared'
    });
  } catch (error) {
    console.error('Error clearing wishlist:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

module.exports = router;
