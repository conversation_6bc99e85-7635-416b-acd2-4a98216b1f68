const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');

// @route   GET /api/social/feed
// @desc    Get social media feed
// @access  Public
router.get('/feed', async (req, res) => {
  try {
    // Mock social feed
    const feed = [
      {
        id: 1,
        type: 'outfit',
        user: 'Priya S.',
        image: '/assets/social/outfit1.jpg',
        caption: 'Love this ethnic look! 💕',
        likes: 45,
        comments: 12
      }
    ];
    
    res.json({
      success: true,
      data: feed
    });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Server Error' });
  }
});

module.exports = router;
