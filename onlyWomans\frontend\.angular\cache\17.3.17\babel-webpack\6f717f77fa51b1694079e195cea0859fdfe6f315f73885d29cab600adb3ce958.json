{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nconst _c0 = a0 => [\"/categories\", a0];\nfunction HomeComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54);\n    i0.ɵɵelement(2, \"img\", 55);\n    i0.ɵɵelementStart(3, \"div\", 56)(4, \"span\", 57);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 58)(7, \"h3\", 59);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 60);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 61);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(7, _c0, category_r1.slug));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", category_r1.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r1.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r1.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r1.productCount, \"+ items\");\n  }\n}\nfunction HomeComponent_div_51_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 79);\n    i0.ɵɵtext(1, \"New\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_51_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", product_r2.discount, \"% Off\");\n  }\n}\nfunction HomeComponent_div_51_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r2.originalPrice, \"\");\n  }\n}\nfunction HomeComponent_div_51_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const star_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(star_r3);\n  }\n}\nfunction HomeComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63);\n    i0.ɵɵelement(2, \"img\", 55);\n    i0.ɵɵelementStart(3, \"div\", 64);\n    i0.ɵɵtemplate(4, HomeComponent_div_51_span_4_Template, 2, 0, \"span\", 65)(5, HomeComponent_div_51_span_5_Template, 2, 1, \"span\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 67)(7, \"i\", 68);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 69)(10, \"span\", 70);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"h3\", 71);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 72)(15, \"span\", 73);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, HomeComponent_div_51_span_17_Template, 2, 1, \"span\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 75)(19, \"div\", 76);\n    i0.ɵɵtemplate(20, HomeComponent_div_51_span_20_Template, 2, 1, \"span\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 78);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r2 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r2.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r2.isNew);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r2.discount);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", product_r2.isWishlisted);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r2.isWishlisted ? \"\\u2764\\uFE0F\" : \"\\uD83E\\uDD0D\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r2.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r2.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r2.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r2.originalPrice);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getStars(product_r2.rating));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r2.reviewCount, \")\");\n  }\n}\nexport let HomeComponent = /*#__PURE__*/(() => {\n  class HomeComponent {\n    constructor() {\n      this.featuredCategories = [{\n        name: 'Ethnic Wear',\n        slug: 'ethnic-wear',\n        description: 'Traditional elegance for every occasion',\n        image: 'assets/images/categories/ethnic.jpg',\n        icon: '🥻',\n        productCount: 1250\n      }, {\n        name: 'Western Wear',\n        slug: 'western-wear',\n        description: 'Contemporary styles for modern women',\n        image: 'assets/images/categories/western.jpg',\n        icon: '👗',\n        productCount: 980\n      }, {\n        name: 'Beauty & Makeup',\n        slug: 'beauty-makeup',\n        description: 'Enhance your natural beauty',\n        image: 'assets/images/categories/beauty.jpg',\n        icon: '💄',\n        productCount: 750\n      }, {\n        name: 'Accessories',\n        slug: 'accessories',\n        description: 'Complete your look with style',\n        image: 'assets/images/categories/accessories.jpg',\n        icon: '👜',\n        productCount: 650\n      }, {\n        name: 'Footwear',\n        slug: 'footwear',\n        description: 'Step out in confidence',\n        image: 'assets/images/categories/footwear.jpg',\n        icon: '👠',\n        productCount: 420\n      }, {\n        name: 'Jewelry',\n        slug: 'jewelry',\n        description: 'Sparkle with every piece',\n        image: 'assets/images/categories/jewelry.jpg',\n        icon: '💎',\n        productCount: 380\n      }];\n      this.featuredProducts = [{\n        id: 1,\n        name: 'Floral Maxi Dress',\n        brand: 'StyleVogue',\n        price: 2499,\n        originalPrice: 3499,\n        discount: 29,\n        image: 'assets/images/products/dress1.jpg',\n        rating: 4.5,\n        reviewCount: 128,\n        isNew: true,\n        isWishlisted: false\n      }, {\n        id: 2,\n        name: 'Silk Saree with Blouse',\n        brand: 'EthnicElegance',\n        price: 4999,\n        originalPrice: 6999,\n        discount: 29,\n        image: 'assets/images/products/saree1.jpg',\n        rating: 4.8,\n        reviewCount: 95,\n        isNew: false,\n        isWishlisted: true\n      }, {\n        id: 3,\n        name: 'Designer Handbag',\n        brand: 'LuxeCollection',\n        price: 1899,\n        originalPrice: null,\n        discount: null,\n        image: 'assets/images/products/bag1.jpg',\n        rating: 4.3,\n        reviewCount: 67,\n        isNew: true,\n        isWishlisted: false\n      }, {\n        id: 4,\n        name: 'Makeup Palette Set',\n        brand: 'GlamBeauty',\n        price: 1299,\n        originalPrice: 1799,\n        discount: 28,\n        image: 'assets/images/products/makeup1.jpg',\n        rating: 4.6,\n        reviewCount: 203,\n        isNew: false,\n        isWishlisted: false\n      }];\n    }\n    ngOnInit() {\n      // Initialize component\n    }\n    getStars(rating) {\n      const stars = [];\n      const fullStars = Math.floor(rating);\n      const hasHalfStar = rating % 1 !== 0;\n      for (let i = 0; i < fullStars; i++) {\n        stars.push('⭐');\n      }\n      if (hasHalfStar) {\n        stars.push('⭐');\n      }\n      while (stars.length < 5) {\n        stars.push('☆');\n      }\n      return stars;\n    }\n    static {\n      this.ɵfac = function HomeComponent_Factory(t) {\n        return new (t || HomeComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HomeComponent,\n        selectors: [[\"ow-home\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 103,\n        vars: 2,\n        consts: [[1, \"home-container\"], [1, \"hero-section\"], [1, \"hero-content\"], [1, \"hero-text\"], [1, \"hero-title\"], [1, \"title-line\"], [1, \"title-line\", \"highlight\"], [1, \"hero-subtitle\"], [1, \"hero-actions\"], [\"routerLink\", \"/shop\", 1, \"btn\", \"btn-primary\", \"btn-lg\"], [1, \"icon\"], [\"routerLink\", \"/categories\", 1, \"btn\", \"btn-outline\", \"btn-lg\"], [1, \"hero-image\"], [1, \"image-container\"], [\"src\", \"assets/images/hero-woman.jpg\", \"alt\", \"Elegant Woman in Fashion\", 1, \"main-image\"], [1, \"floating-elements\"], [1, \"floating-item\", \"item-1\"], [1, \"floating-item\", \"item-2\"], [1, \"floating-item\", \"item-3\"], [1, \"floating-item\", \"item-4\"], [1, \"categories-section\"], [1, \"container\"], [1, \"section-header\"], [1, \"section-title\"], [1, \"section-subtitle\"], [1, \"categories-grid\"], [\"class\", \"category-card\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"featured-section\"], [1, \"products-grid\"], [\"class\", \"product-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"section-footer\"], [\"routerLink\", \"/shop\", 1, \"btn\", \"btn-outline\", \"btn-lg\"], [1, \"offers-section\"], [1, \"offers-grid\"], [1, \"offer-card\", \"offer-primary\"], [1, \"offer-content\"], [1, \"offer-title\"], [1, \"offer-description\"], [1, \"btn\", \"btn-primary\"], [1, \"offer-icon\"], [1, \"offer-card\", \"offer-secondary\"], [1, \"btn\", \"btn-secondary\"], [1, \"offer-card\", \"offer-accent\"], [1, \"btn\", \"btn-outline\"], [1, \"newsletter-section\"], [1, \"newsletter-content\"], [1, \"newsletter-text\"], [1, \"newsletter-title\"], [1, \"newsletter-subtitle\"], [1, \"newsletter-form\"], [1, \"form-group\"], [\"type\", \"email\", \"placeholder\", \"Enter your email address\", 1, \"email-input\"], [1, \"newsletter-note\"], [1, \"category-card\", 3, \"routerLink\"], [1, \"category-image\"], [3, \"src\", \"alt\"], [1, \"category-overlay\"], [1, \"category-icon\"], [1, \"category-info\"], [1, \"category-name\"], [1, \"category-description\"], [1, \"category-count\"], [1, \"product-card\"], [1, \"product-image\"], [1, \"product-badges\"], [\"class\", \"badge badge-new\", 4, \"ngIf\"], [\"class\", \"badge badge-sale\", 4, \"ngIf\"], [1, \"wishlist-btn\"], [1, \"heart-icon\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"product-rating\"], [1, \"stars\"], [4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"badge\", \"badge-new\"], [1, \"badge\", \"badge-sale\"], [1, \"original-price\"]],\n        template: function HomeComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"section\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4)(5, \"span\", 5);\n            i0.ɵɵtext(6, \"Fashion That\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"span\", 6);\n            i0.ɵɵtext(8, \"Celebrates You\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"p\", 7);\n            i0.ɵɵtext(10, \" Discover curated collections designed exclusively for women. From ethnic elegance to western chic, find your perfect style. \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"div\", 8)(12, \"button\", 9)(13, \"span\");\n            i0.ɵɵtext(14, \"Shop Now\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"i\", 10);\n            i0.ɵɵtext(16, \"\\uD83D\\uDECD\\uFE0F\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"button\", 11)(18, \"span\");\n            i0.ɵɵtext(19, \"Explore Categories\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"i\", 10);\n            i0.ɵɵtext(21, \"\\u2728\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(22, \"div\", 12)(23, \"div\", 13);\n            i0.ɵɵelement(24, \"img\", 14);\n            i0.ɵɵelementStart(25, \"div\", 15)(26, \"div\", 16);\n            i0.ɵɵtext(27, \"\\uD83D\\uDC57\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"div\", 17);\n            i0.ɵɵtext(29, \"\\uD83D\\uDC84\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"div\", 18);\n            i0.ɵɵtext(31, \"\\uD83D\\uDC60\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"div\", 19);\n            i0.ɵɵtext(33, \"\\uD83D\\uDC8E\");\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(34, \"section\", 20)(35, \"div\", 21)(36, \"div\", 22)(37, \"h2\", 23);\n            i0.ɵɵtext(38, \"Shop by Category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"p\", 24);\n            i0.ɵɵtext(40, \"Explore our curated collections for every occasion\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(41, \"div\", 25);\n            i0.ɵɵtemplate(42, HomeComponent_div_42_Template, 13, 9, \"div\", 26);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(43, \"section\", 27)(44, \"div\", 21)(45, \"div\", 22)(46, \"h2\", 23);\n            i0.ɵɵtext(47, \"Trending Now\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"p\", 24);\n            i0.ɵɵtext(49, \"Handpicked favorites that are flying off the shelves\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(50, \"div\", 28);\n            i0.ɵɵtemplate(51, HomeComponent_div_51_Template, 23, 13, \"div\", 29);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"div\", 30)(53, \"button\", 31);\n            i0.ɵɵtext(54, \" View All Products \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(55, \"section\", 32)(56, \"div\", 21)(57, \"div\", 33)(58, \"div\", 34)(59, \"div\", 35)(60, \"h3\", 36);\n            i0.ɵɵtext(61, \"New User Special\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(62, \"p\", 37);\n            i0.ɵɵtext(63, \"Get 30% off on your first order\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"button\", 38);\n            i0.ɵɵtext(65, \"Claim Now\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(66, \"div\", 39);\n            i0.ɵɵtext(67, \"\\uD83C\\uDF81\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(68, \"div\", 40)(69, \"div\", 35)(70, \"h3\", 36);\n            i0.ɵɵtext(71, \"Free Shipping\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(72, \"p\", 37);\n            i0.ɵɵtext(73, \"On orders above \\u20B9999\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"button\", 41);\n            i0.ɵɵtext(75, \"Shop Now\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(76, \"div\", 39);\n            i0.ɵɵtext(77, \"\\uD83D\\uDE9A\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(78, \"div\", 42)(79, \"div\", 35)(80, \"h3\", 36);\n            i0.ɵɵtext(81, \"Beauty Box\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(82, \"p\", 37);\n            i0.ɵɵtext(83, \"Curated makeup essentials\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(84, \"button\", 43);\n            i0.ɵɵtext(85, \"Explore\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(86, \"div\", 39);\n            i0.ɵɵtext(87, \"\\uD83D\\uDC84\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(88, \"section\", 44)(89, \"div\", 21)(90, \"div\", 45)(91, \"div\", 46)(92, \"h2\", 47);\n            i0.ɵɵtext(93, \"Stay in Style\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(94, \"p\", 48);\n            i0.ɵɵtext(95, \" Get the latest fashion trends, beauty tips, and exclusive offers delivered to your inbox. \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(96, \"div\", 49)(97, \"div\", 50);\n            i0.ɵɵelement(98, \"input\", 51);\n            i0.ɵɵelementStart(99, \"button\", 38);\n            i0.ɵɵtext(100, \"Subscribe\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(101, \"p\", 52);\n            i0.ɵɵtext(102, \" Join 50,000+ women who trust us for their fashion needs. \");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(42);\n            i0.ɵɵproperty(\"ngForOf\", ctx.featuredCategories);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngForOf\", ctx.featuredProducts);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterModule, i2.RouterLink],\n        styles: [\".home-container[_ngcontent-%COMP%]{min-height:100vh}.container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:0 var(--space-lg)}.hero-section[_ngcontent-%COMP%]{padding:var(--space-3xl) 0;background:linear-gradient(135deg,var(--primary-50) 0%,var(--secondary-50) 100%);position:relative;overflow:hidden}.hero-content[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:0 var(--space-lg);display:grid;grid-template-columns:1fr 1fr;gap:var(--space-3xl);align-items:center}.hero-text[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%]{font-size:var(--font-size-5xl);font-weight:var(--font-weight-bold);line-height:var(--line-height-tight);margin-bottom:var(--space-lg)}.hero-text[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%]   .title-line[_ngcontent-%COMP%]{display:block;color:var(--text-primary)}.hero-text[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%]   .highlight[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--primary-600),var(--secondary-600));-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.hero-text[_ngcontent-%COMP%]   .hero-subtitle[_ngcontent-%COMP%]{font-size:var(--font-size-lg);color:var(--text-secondary);line-height:var(--line-height-relaxed);margin-bottom:var(--space-2xl)}.hero-text[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]{display:flex;gap:var(--space-lg);flex-wrap:wrap}.hero-image[_ngcontent-%COMP%]{position:relative}.hero-image[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{position:relative;border-radius:var(--radius-2xl);overflow:hidden;box-shadow:var(--shadow-xl)}.hero-image[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]{width:100%;height:500px;object-fit:cover}.hero-image[_ngcontent-%COMP%]   .floating-elements[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none}.hero-image[_ngcontent-%COMP%]   .floating-item[_ngcontent-%COMP%]{position:absolute;font-size:var(--font-size-2xl);animation:float 3s ease-in-out infinite}.hero-image[_ngcontent-%COMP%]   .floating-item.item-1[_ngcontent-%COMP%]{top:20%;right:10%;animation-delay:0s}.hero-image[_ngcontent-%COMP%]   .floating-item.item-2[_ngcontent-%COMP%]{top:60%;right:20%;animation-delay:1s}.hero-image[_ngcontent-%COMP%]   .floating-item.item-3[_ngcontent-%COMP%]{bottom:30%;left:10%;animation-delay:2s}.hero-image[_ngcontent-%COMP%]   .floating-item.item-4[_ngcontent-%COMP%]{top:40%;left:5%;animation-delay:1.5s}.section-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:var(--space-3xl)}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:var(--font-size-3xl);font-weight:var(--font-weight-bold);color:var(--text-primary);margin-bottom:var(--space-md)}.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:var(--font-size-lg);color:var(--text-secondary);max-width:600px;margin:0 auto}.categories-section[_ngcontent-%COMP%]{padding:var(--space-3xl) 0;background:var(--bg-primary)}.categories-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:var(--space-xl)}.category-card[_ngcontent-%COMP%]{background:var(--bg-primary);border-radius:var(--radius-xl);overflow:hidden;box-shadow:var(--shadow-md);transition:all var(--transition-normal);cursor:pointer;text-decoration:none;color:inherit}.category-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:var(--shadow-xl)}.category-card[_ngcontent-%COMP%]:hover   .category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transform:scale(1.1)}.category-card[_ngcontent-%COMP%]:hover   .category-overlay[_ngcontent-%COMP%]{opacity:1}.category-card[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%]{position:relative;height:200px;overflow:hidden}.category-card[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform var(--transition-slow)}.category-card[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%]   .category-overlay[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,#ec4899cc,#a855f7cc);display:flex;align-items:center;justify-content:center;opacity:0;transition:opacity var(--transition-normal)}.category-card[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%]   .category-overlay[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]{font-size:var(--font-size-4xl)}.category-card[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]{padding:var(--space-lg)}.category-card[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%]{font-size:var(--font-size-xl);font-weight:var(--font-weight-semibold);color:var(--text-primary);margin-bottom:var(--space-sm)}.category-card[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-description[_ngcontent-%COMP%]{color:var(--text-secondary);margin-bottom:var(--space-md);line-height:var(--line-height-relaxed)}.category-card[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-count[_ngcontent-%COMP%]{color:var(--primary-600);font-weight:var(--font-weight-medium);font-size:var(--font-size-sm)}.featured-section[_ngcontent-%COMP%]{padding:var(--space-3xl) 0;background:var(--bg-secondary)}.products-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:var(--space-xl);margin-bottom:var(--space-2xl)}.section-footer[_ngcontent-%COMP%]{text-align:center}.offers-section[_ngcontent-%COMP%]{padding:var(--space-3xl) 0;background:var(--bg-primary)}.offers-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:var(--space-xl)}.offer-card[_ngcontent-%COMP%]{padding:var(--space-2xl);border-radius:var(--radius-xl);display:flex;align-items:center;justify-content:space-between;transition:transform var(--transition-normal)}.offer-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px)}.offer-card.offer-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--primary-500),var(--primary-600));color:var(--text-inverse)}.offer-card.offer-secondary[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--secondary-500),var(--secondary-600));color:var(--text-inverse)}.offer-card.offer-accent[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--accent-400),var(--accent-500));color:var(--text-inverse)}.offer-card[_ngcontent-%COMP%]   .offer-content[_ngcontent-%COMP%]{flex:1}.offer-card[_ngcontent-%COMP%]   .offer-content[_ngcontent-%COMP%]   .offer-title[_ngcontent-%COMP%]{font-size:var(--font-size-xl);font-weight:var(--font-weight-bold);margin-bottom:var(--space-sm)}.offer-card[_ngcontent-%COMP%]   .offer-content[_ngcontent-%COMP%]   .offer-description[_ngcontent-%COMP%]{margin-bottom:var(--space-lg);opacity:.9}.offer-card[_ngcontent-%COMP%]   .offer-icon[_ngcontent-%COMP%]{font-size:var(--font-size-4xl);margin-left:var(--space-lg)}.newsletter-section[_ngcontent-%COMP%]{padding:var(--space-3xl) 0;background:linear-gradient(135deg,var(--primary-600),var(--secondary-600));color:var(--text-inverse)}.newsletter-content[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:var(--space-3xl);align-items:center}.newsletter-text[_ngcontent-%COMP%]   .newsletter-title[_ngcontent-%COMP%]{font-size:var(--font-size-3xl);font-weight:var(--font-weight-bold);margin-bottom:var(--space-md)}.newsletter-text[_ngcontent-%COMP%]   .newsletter-subtitle[_ngcontent-%COMP%]{font-size:var(--font-size-lg);opacity:.9;line-height:var(--line-height-relaxed)}.newsletter-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{display:flex;gap:var(--space-md);margin-bottom:var(--space-md)}.newsletter-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .email-input[_ngcontent-%COMP%]{flex:1;padding:var(--space-md);border:none;border-radius:var(--radius-lg);font-size:var(--font-size-base)}.newsletter-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .email-input[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #ffffff4d}.newsletter-form[_ngcontent-%COMP%]   .newsletter-note[_ngcontent-%COMP%]{font-size:var(--font-size-sm);opacity:.8;margin:0}@media (max-width: 768px){.hero-content[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:var(--space-2xl);text-align:center}.hero-text[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%]{font-size:var(--font-size-3xl)}.hero-actions[_ngcontent-%COMP%]{justify-content:center}.categories-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.products-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(250px,1fr))}.offers-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.offer-card[_ngcontent-%COMP%]{flex-direction:column;text-align:center}.offer-card[_ngcontent-%COMP%]   .offer-icon[_ngcontent-%COMP%]{margin:var(--space-lg) 0 0 0}.newsletter-content[_ngcontent-%COMP%]{grid-template-columns:1fr;text-align:center}.newsletter-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{flex-direction:column}}\"]\n      });\n    }\n  }\n  return HomeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}