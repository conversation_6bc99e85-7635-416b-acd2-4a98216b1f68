{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-checkbox.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { i as inheritAriaAttributes, a as renderHiddenInput } from './helpers-1O4D2b7y.js';\nimport { c as createColorClasses, h as hostContext } from './theme-DiVJyqlX.js';\nconst checkboxIosCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #0054e9);--border-color-checked:var(--ion-color-primary, #0054e9);--checkmark-color:var(--ion-color-primary-contrast, #fff);--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper,:host(.in-item:not(.checkbox-label-placement-stacked):not([slot])) .native-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;width:var(--size);height:var(--size);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}.checkbox-bottom{padding-top:4px;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;font-size:0.75rem;white-space:normal}:host(.checkbox-label-placement-stacked) .checkbox-bottom{font-size:1rem}.checkbox-bottom .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.checkbox-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .checkbox-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .checkbox-bottom .helper-text{display:none}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse;-ms-flex-pack:start;justify-content:start}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column;text-align:center}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-justify-space-between),:host(.checkbox-justify-start),:host(.checkbox-justify-end),:host(.checkbox-alignment-start),:host(.checkbox-alignment-center){display:block}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:50%;--border-width:0.125rem;--border-style:solid;--border-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.23);--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--size:min(1.375rem, 55.836px);--checkmark-width:1.5px}:host(.checkbox-disabled){opacity:0.3}\";\nconst checkboxMdCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #0054e9);--border-color-checked:var(--ion-color-primary, #0054e9);--checkmark-color:var(--ion-color-primary-contrast, #fff);--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper,:host(.in-item:not(.checkbox-label-placement-stacked):not([slot])) .native-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;width:var(--size);height:var(--size);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}.checkbox-bottom{padding-top:4px;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;font-size:0.75rem;white-space:normal}:host(.checkbox-label-placement-stacked) .checkbox-bottom{font-size:1rem}.checkbox-bottom .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.checkbox-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .checkbox-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .checkbox-bottom .helper-text{display:none}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse;-ms-flex-pack:start;justify-content:start}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column;text-align:center}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-justify-space-between),:host(.checkbox-justify-start),:host(.checkbox-justify-end),:host(.checkbox-alignment-start),:host(.checkbox-alignment-center){display:block}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:calc(var(--size) * .125);--border-width:2px;--border-style:solid;--border-color:rgb(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--checkmark-width:3;--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--transition:background 180ms cubic-bezier(0.4, 0, 0.2, 1);--size:18px}.checkbox-icon path{stroke-dasharray:30;stroke-dashoffset:30}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{stroke-dashoffset:0;-webkit-transition:stroke-dashoffset 90ms linear 90ms;transition:stroke-dashoffset 90ms linear 90ms}:host(.checkbox-disabled) .label-text-wrapper{opacity:0.38}:host(.checkbox-disabled) .native-wrapper{opacity:0.63}\";\nconst Checkbox = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.inputId = `ion-cb-${checkboxIds++}`;\n    this.inputLabelId = `${this.inputId}-lbl`;\n    this.helperTextId = `${this.inputId}-helper-text`;\n    this.errorTextId = `${this.inputId}-error-text`;\n    this.inheritedAttributes = {};\n    /**\n     * The name of the control, which is submitted with the form data.\n     */\n    this.name = this.inputId;\n    /**\n     * If `true`, the checkbox is selected.\n     */\n    this.checked = false;\n    /**\n     * If `true`, the checkbox will visually appear as indeterminate.\n     */\n    this.indeterminate = false;\n    /**\n     * If `true`, the user cannot interact with the checkbox.\n     */\n    this.disabled = false;\n    /**\n     * The value of the checkbox does not mean if it's checked or not, use the `checked`\n     * property for that.\n     *\n     * The value of a checkbox is analogous to the value of an `<input type=\"checkbox\">`,\n     * it's only used when the checkbox participates in a native `<form>`.\n     */\n    this.value = 'on';\n    /**\n     * Where to place the label relative to the checkbox.\n     * `\"start\"`: The label will appear to the left of the checkbox in LTR and to the right in RTL.\n     * `\"end\"`: The label will appear to the right of the checkbox in LTR and to the left in RTL.\n     * `\"fixed\"`: The label has the same behavior as `\"start\"` except it also has a fixed width. Long text will be truncated with ellipses (\"...\").\n     * `\"stacked\"`: The label will appear above the checkbox regardless of the direction. The alignment of the label can be controlled with the `alignment` property.\n     */\n    this.labelPlacement = 'start';\n    /**\n     * If true, screen readers will announce it as a required field. This property\n     * works only for accessibility purposes, it will not prevent the form from\n     * submitting if the value is invalid.\n     */\n    this.required = false;\n    /**\n     * Sets the checked property and emits\n     * the ionChange event. Use this to update the\n     * checked state in response to user-generated\n     * actions such as a click.\n     */\n    this.setChecked = state => {\n      const isChecked = this.checked = state;\n      this.ionChange.emit({\n        checked: isChecked,\n        value: this.value\n      });\n    };\n    this.toggleChecked = ev => {\n      ev.preventDefault();\n      this.setFocus();\n      this.setChecked(!this.checked);\n      this.indeterminate = false;\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.onKeyDown = ev => {\n      if (ev.key === ' ') {\n        ev.preventDefault();\n        if (!this.disabled) {\n          this.toggleChecked(ev);\n        }\n      }\n    };\n    this.onClick = ev => {\n      if (this.disabled) {\n        return;\n      }\n      this.toggleChecked(ev);\n    };\n    /**\n     * Stops propagation when the display label is clicked,\n     * otherwise, two clicks will be triggered.\n     */\n    this.onDivLabelClick = ev => {\n      ev.stopPropagation();\n    };\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign({}, inheritAriaAttributes(this.el));\n  }\n  /** @internal */\n  async setFocus() {\n    if (this.focusEl) {\n      this.focusEl.focus();\n    }\n  }\n  getHintTextID() {\n    const {\n      el,\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n      return errorTextId;\n    }\n    if (helperText) {\n      return helperTextId;\n    }\n    return undefined;\n  }\n  /**\n   * Responsible for rendering helper text and error text.\n   * This element should only be rendered if hint text is set.\n   */\n  renderHintText() {\n    const {\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    /**\n     * undefined and empty string values should\n     * be treated as not having helper/error text.\n     */\n    const hasHintText = !!helperText || !!errorText;\n    if (!hasHintText) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"checkbox-bottom\"\n    }, h(\"div\", {\n      id: helperTextId,\n      class: \"helper-text\",\n      part: \"supporting-text helper-text\"\n    }, helperText), h(\"div\", {\n      id: errorTextId,\n      class: \"error-text\",\n      part: \"supporting-text error-text\"\n    }, errorText));\n  }\n  render() {\n    const {\n      color,\n      checked,\n      disabled,\n      el,\n      getSVGPath,\n      indeterminate,\n      inheritedAttributes,\n      inputId,\n      justify,\n      labelPlacement,\n      name,\n      value,\n      alignment,\n      required\n    } = this;\n    const mode = getIonMode(this);\n    const path = getSVGPath(mode, indeterminate);\n    const hasLabelContent = el.textContent !== '';\n    renderHiddenInput(true, el, name, checked ? value : '', disabled);\n    // The host element must have a checkbox role to ensure proper VoiceOver\n    // support in Safari for accessibility.\n    return h(Host, {\n      key: '26cbe7220e555107200e9b5deeae754aa534a80b',\n      role: \"checkbox\",\n      \"aria-checked\": indeterminate ? 'mixed' : `${checked}`,\n      \"aria-describedby\": this.getHintTextID(),\n      \"aria-invalid\": this.getHintTextID() === this.errorTextId,\n      \"aria-labelledby\": hasLabelContent ? this.inputLabelId : null,\n      \"aria-label\": inheritedAttributes['aria-label'] || null,\n      \"aria-disabled\": disabled ? 'true' : null,\n      tabindex: disabled ? undefined : 0,\n      onKeyDown: this.onKeyDown,\n      class: createColorClasses(color, {\n        [mode]: true,\n        'in-item': hostContext('ion-item', el),\n        'checkbox-checked': checked,\n        'checkbox-disabled': disabled,\n        'checkbox-indeterminate': indeterminate,\n        interactive: true,\n        [`checkbox-justify-${justify}`]: justify !== undefined,\n        [`checkbox-alignment-${alignment}`]: alignment !== undefined,\n        [`checkbox-label-placement-${labelPlacement}`]: true\n      }),\n      onClick: this.onClick\n    }, h(\"label\", {\n      key: 'f025cec5ff08e8be4487b9cc0324616ca5dfae2a',\n      class: \"checkbox-wrapper\",\n      htmlFor: inputId\n    }, h(\"input\", Object.assign({\n      key: 'dc53f7e4e240dc2e18556e6350df2b5c3169f553',\n      type: \"checkbox\",\n      checked: checked ? true : undefined,\n      disabled: disabled,\n      id: inputId,\n      onChange: this.toggleChecked,\n      onFocus: () => this.onFocus(),\n      onBlur: () => this.onBlur(),\n      ref: focusEl => this.focusEl = focusEl,\n      required: required\n    }, inheritedAttributes)), h(\"div\", {\n      key: 'a625e9b50c3b617de8bbbfd624d772454fecaf2d',\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !hasLabelContent\n      },\n      part: \"label\",\n      id: this.inputLabelId,\n      onClick: this.onDivLabelClick\n    }, h(\"slot\", {\n      key: '87d1a90691327945f4343406706e4ab27f453844'\n    }), this.renderHintText()), h(\"div\", {\n      key: 'b57fed8cdecee4df1ef0d57f157267ee77fac653',\n      class: \"native-wrapper\"\n    }, h(\"svg\", {\n      key: 'd472a06ec6c8b74dfb651415d2236db8080f6805',\n      class: \"checkbox-icon\",\n      viewBox: \"0 0 24 24\",\n      part: \"container\"\n    }, path))));\n  }\n  getSVGPath(mode, indeterminate) {\n    let path = indeterminate ? h(\"path\", {\n      d: \"M6 12L18 12\",\n      part: \"mark\"\n    }) : h(\"path\", {\n      d: \"M5.9,12.5l3.8,3.8l8.8-8.8\",\n      part: \"mark\"\n    });\n    if (mode === 'md') {\n      path = indeterminate ? h(\"path\", {\n        d: \"M2 12H22\",\n        part: \"mark\"\n      }) : h(\"path\", {\n        d: \"M1.73,12.91 8.1,19.28 22.79,4.59\",\n        part: \"mark\"\n      });\n    }\n    return path;\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nlet checkboxIds = 0;\nCheckbox.style = {\n  ios: checkboxIosCss,\n  md: checkboxMdCss\n};\nexport { Checkbox as ion_checkbox };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,IAAM,WAAW,MAAM;AAAA,EACrB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,UAAU,UAAU,aAAa;AACtC,SAAK,eAAe,GAAG,KAAK,OAAO;AACnC,SAAK,eAAe,GAAG,KAAK,OAAO;AACnC,SAAK,cAAc,GAAG,KAAK,OAAO;AAClC,SAAK,sBAAsB,CAAC;AAI5B,SAAK,OAAO,KAAK;AAIjB,SAAK,UAAU;AAIf,SAAK,gBAAgB;AAIrB,SAAK,WAAW;AAQhB,SAAK,QAAQ;AAQb,SAAK,iBAAiB;AAMtB,SAAK,WAAW;AAOhB,SAAK,aAAa,WAAS;AACzB,YAAM,YAAY,KAAK,UAAU;AACjC,WAAK,UAAU,KAAK;AAAA,QAClB,SAAS;AAAA,QACT,OAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH;AACA,SAAK,gBAAgB,QAAM;AACzB,SAAG,eAAe;AAClB,WAAK,SAAS;AACd,WAAK,WAAW,CAAC,KAAK,OAAO;AAC7B,WAAK,gBAAgB;AAAA,IACvB;AACA,SAAK,UAAU,MAAM;AACnB,WAAK,SAAS,KAAK;AAAA,IACrB;AACA,SAAK,SAAS,MAAM;AAClB,WAAK,QAAQ,KAAK;AAAA,IACpB;AACA,SAAK,YAAY,QAAM;AACrB,UAAI,GAAG,QAAQ,KAAK;AAClB,WAAG,eAAe;AAClB,YAAI,CAAC,KAAK,UAAU;AAClB,eAAK,cAAc,EAAE;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AACA,SAAK,UAAU,QAAM;AACnB,UAAI,KAAK,UAAU;AACjB;AAAA,MACF;AACA,WAAK,cAAc,EAAE;AAAA,IACvB;AAKA,SAAK,kBAAkB,QAAM;AAC3B,SAAG,gBAAgB;AAAA,IACrB;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,sBAAsB,OAAO,OAAO,CAAC,GAAG,sBAAsB,KAAK,EAAE,CAAC;AAAA,EAC7E;AAAA;AAAA,EAEM,WAAW;AAAA;AACf,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ,MAAM;AAAA,MACrB;AAAA,IACF;AAAA;AAAA,EACA,gBAAgB;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,GAAG,UAAU,SAAS,aAAa,KAAK,GAAG,UAAU,SAAS,aAAa,KAAK,WAAW;AAC7F,aAAO;AAAA,IACT;AACA,QAAI,YAAY;AACd,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAKJ,UAAM,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AACA,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,MAAM;AAAA,IACR,GAAG,UAAU,GAAG,EAAE,OAAO;AAAA,MACvB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,MAAM;AAAA,IACR,GAAG,SAAS,CAAC;AAAA,EACf;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,OAAO,WAAW,MAAM,aAAa;AAC3C,UAAM,kBAAkB,GAAG,gBAAgB;AAC3C,sBAAkB,MAAM,IAAI,MAAM,UAAU,QAAQ,IAAI,QAAQ;AAGhE,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,MAAM;AAAA,MACN,gBAAgB,gBAAgB,UAAU,GAAG,OAAO;AAAA,MACpD,oBAAoB,KAAK,cAAc;AAAA,MACvC,gBAAgB,KAAK,cAAc,MAAM,KAAK;AAAA,MAC9C,mBAAmB,kBAAkB,KAAK,eAAe;AAAA,MACzD,cAAc,oBAAoB,YAAY,KAAK;AAAA,MACnD,iBAAiB,WAAW,SAAS;AAAA,MACrC,UAAU,WAAW,SAAY;AAAA,MACjC,WAAW,KAAK;AAAA,MAChB,OAAO,mBAAmB,OAAO;AAAA,QAC/B,CAAC,IAAI,GAAG;AAAA,QACR,WAAW,YAAY,YAAY,EAAE;AAAA,QACrC,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,QACrB,0BAA0B;AAAA,QAC1B,aAAa;AAAA,QACb,CAAC,oBAAoB,OAAO,EAAE,GAAG,YAAY;AAAA,QAC7C,CAAC,sBAAsB,SAAS,EAAE,GAAG,cAAc;AAAA,QACnD,CAAC,4BAA4B,cAAc,EAAE,GAAG;AAAA,MAClD,CAAC;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,GAAG,EAAE,SAAS;AAAA,MACZ,KAAK;AAAA,MACL,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG,EAAE,SAAS,OAAO,OAAO;AAAA,MAC1B,KAAK;AAAA,MACL,MAAM;AAAA,MACN,SAAS,UAAU,OAAO;AAAA,MAC1B;AAAA,MACA,IAAI;AAAA,MACJ,UAAU,KAAK;AAAA,MACf,SAAS,MAAM,KAAK,QAAQ;AAAA,MAC5B,QAAQ,MAAM,KAAK,OAAO;AAAA,MAC1B,KAAK,aAAW,KAAK,UAAU;AAAA,MAC/B;AAAA,IACF,GAAG,mBAAmB,CAAC,GAAG,EAAE,OAAO;AAAA,MACjC,KAAK;AAAA,MACL,OAAO;AAAA,QACL,sBAAsB;AAAA,QACtB,6BAA6B,CAAC;AAAA,MAChC;AAAA,MACA,MAAM;AAAA,MACN,IAAI,KAAK;AAAA,MACT,SAAS,KAAK;AAAA,IAChB,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,GAAG,KAAK,eAAe,CAAC,GAAG,EAAE,OAAO;AAAA,MACnC,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,SAAS;AAAA,MACT,MAAM;AAAA,IACR,GAAG,IAAI,CAAC,CAAC,CAAC;AAAA,EACZ;AAAA,EACA,WAAW,MAAM,eAAe;AAC9B,QAAI,OAAO,gBAAgB,EAAE,QAAQ;AAAA,MACnC,GAAG;AAAA,MACH,MAAM;AAAA,IACR,CAAC,IAAI,EAAE,QAAQ;AAAA,MACb,GAAG;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AACD,QAAI,SAAS,MAAM;AACjB,aAAO,gBAAgB,EAAE,QAAQ;AAAA,QAC/B,GAAG;AAAA,QACH,MAAM;AAAA,MACR,CAAC,IAAI,EAAE,QAAQ;AAAA,QACb,GAAG;AAAA,QACH,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;AACA,IAAI,cAAc;AAClB,SAAS,QAAQ;AAAA,EACf,KAAK;AAAA,EACL,IAAI;AACN;", "names": []}