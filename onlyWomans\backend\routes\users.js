const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');

// @route   GET /api/users/profile
// @desc    Get user profile
// @access  Private
router.get('/profile', auth, async (req, res) => {
  try {
    // Mock user data
    const user = {
      id: req.user.id,
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '+91 9876543210',
      avatar: '/assets/avatars/default.jpg',
      addresses: [
        {
          id: 1,
          type: 'home',
          name: '<PERSON><PERSON>',
          phone: '+91 9876543210',
          address: '123 MG Road',
          city: 'Mumbai',
          state: 'Maharashtra',
          pincode: '400001',
          isDefault: true
        }
      ],
      preferences: {
        size: 'M',
        favoriteCategories: ['ethnic-wear', 'western-wear'],
        newsletter: true,
        notifications: true
      },
      createdAt: new Date('2024-01-15')
    };
    
    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   PUT /api/users/profile
// @desc    Update user profile
// @access  Private
router.put('/profile', auth, async (req, res) => {
  try {
    const { name, phone, preferences } = req.body;
    
    // Mock update logic
    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: { name, phone, preferences }
    });
  } catch (error) {
    console.error('Error updating profile:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   POST /api/users/addresses
// @desc    Add new address
// @access  Private
router.post('/addresses', auth, async (req, res) => {
  try {
    const addressData = req.body;
    
    res.status(201).json({
      success: true,
      message: 'Address added successfully',
      data: { id: Date.now(), ...addressData }
    });
  } catch (error) {
    console.error('Error adding address:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

module.exports = router;
