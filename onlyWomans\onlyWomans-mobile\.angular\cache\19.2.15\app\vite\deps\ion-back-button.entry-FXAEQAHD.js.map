{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-back-button.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, l as config, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { i as inheritAriaAttributes } from './helpers-1O4D2b7y.js';\nimport { o as openURL, c as createColorClasses, h as hostContext } from './theme-DiVJyqlX.js';\nimport { c as chevronBack, a as arrowBackSharp } from './index-BLV6ykCk.js';\nconst backButtonIosCss = \":host{--background:transparent;--color-focused:currentColor;--color-hover:currentColor;--icon-margin-top:0;--icon-margin-bottom:0;--icon-padding-top:0;--icon-padding-end:0;--icon-padding-bottom:0;--icon-padding-start:0;--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--min-width:auto;--min-height:auto;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;--opacity:1;--ripple-color:currentColor;--transition:background-color, opacity 100ms linear;display:none;min-width:var(--min-width);min-height:var(--min-height);color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-font-kerning:none;font-kerning:none}ion-ripple-effect{color:var(--ripple-color)}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.show-back-button){display:block}:host(.back-button-disabled){cursor:default;opacity:0.5;pointer-events:none}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;opacity:var(--opacity);overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}ion-icon{-webkit-padding-start:var(--icon-padding-start);padding-inline-start:var(--icon-padding-start);-webkit-padding-end:var(--icon-padding-end);padding-inline-end:var(--icon-padding-end);padding-top:var(--icon-padding-top);padding-bottom:var(--icon-padding-bottom);-webkit-margin-start:var(--icon-margin-start);margin-inline-start:var(--icon-margin-start);-webkit-margin-end:var(--icon-margin-end);margin-inline-end:var(--icon-margin-end);margin-top:var(--icon-margin-top);margin-bottom:var(--icon-margin-bottom);display:inherit;font-size:var(--icon-font-size);font-weight:var(--icon-font-weight);pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-color.ion-focused) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-hover:transparent;--background-hover-opacity:1;--background-focused:currentColor;--background-focused-opacity:.1;--border-radius:4px;--color:var(--ion-color-primary, #0054e9);--icon-margin-end:1px;--icon-margin-start:-4px;--icon-font-size:1.6em;--min-height:32px;font-size:clamp(17px, 1.0625rem, 21.998px)}.button-native{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:visible;z-index:99}:host(.ion-activated) .button-native{opacity:0.4}@media (any-hover: hover){:host(:hover){opacity:0.6}}\";\nconst backButtonMdCss = \":host{--background:transparent;--color-focused:currentColor;--color-hover:currentColor;--icon-margin-top:0;--icon-margin-bottom:0;--icon-padding-top:0;--icon-padding-end:0;--icon-padding-bottom:0;--icon-padding-start:0;--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--min-width:auto;--min-height:auto;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;--opacity:1;--ripple-color:currentColor;--transition:background-color, opacity 100ms linear;display:none;min-width:var(--min-width);min-height:var(--min-height);color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-font-kerning:none;font-kerning:none}ion-ripple-effect{color:var(--ripple-color)}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.show-back-button){display:block}:host(.back-button-disabled){cursor:default;opacity:0.5;pointer-events:none}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;opacity:var(--opacity);overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}ion-icon{-webkit-padding-start:var(--icon-padding-start);padding-inline-start:var(--icon-padding-start);-webkit-padding-end:var(--icon-padding-end);padding-inline-end:var(--icon-padding-end);padding-top:var(--icon-padding-top);padding-bottom:var(--icon-padding-bottom);-webkit-margin-start:var(--icon-margin-start);margin-inline-start:var(--icon-margin-start);-webkit-margin-end:var(--icon-margin-end);margin-inline-end:var(--icon-margin-end);margin-top:var(--icon-margin-top);margin-bottom:var(--icon-margin-bottom);display:inherit;font-size:var(--icon-font-size);font-weight:var(--icon-font-weight);pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-color.ion-focused) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--border-radius:4px;--background-focused:currentColor;--background-focused-opacity:.12;--background-hover:currentColor;--background-hover-opacity:0.04;--color:currentColor;--icon-margin-end:0;--icon-margin-start:0;--icon-font-size:1.5rem;--icon-font-weight:normal;--min-height:32px;--min-width:44px;--padding-start:12px;--padding-end:12px;font-size:0.875rem;font-weight:500;text-transform:uppercase}:host(.back-button-has-icon-only){--border-radius:50%;min-width:48px;min-height:48px;aspect-ratio:1/1}.button-native{-webkit-box-shadow:none;box-shadow:none}.button-text{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:0;padding-bottom:0}ion-icon{line-height:0.67;text-align:start}@media (any-hover: hover){:host(.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}:host(.ion-color.ion-focused) .button-native::after{background:var(--ion-color-base)}\";\nconst BackButton = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.inheritedAttributes = {};\n    /**\n     * If `true`, the user cannot interact with the button.\n     */\n    this.disabled = false;\n    /**\n     * The type of the button.\n     */\n    this.type = 'button';\n    this.onClick = async ev => {\n      const nav = this.el.closest('ion-nav');\n      ev.preventDefault();\n      if (nav && (await nav.canGoBack())) {\n        return nav.pop({\n          animationBuilder: this.routerAnimation,\n          skipIfBusy: true\n        });\n      }\n      return openURL(this.defaultHref, ev, 'back', this.routerAnimation);\n    };\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n    if (this.defaultHref === undefined) {\n      this.defaultHref = config.get('backButtonDefaultHref');\n    }\n  }\n  get backButtonIcon() {\n    const icon = this.icon;\n    if (icon != null) {\n      // icon is set on the component or by the config\n      return icon;\n    }\n    if (getIonMode(this) === 'ios') {\n      // default ios back button icon\n      return config.get('backButtonIcon', chevronBack);\n    }\n    // default md back button icon\n    return config.get('backButtonIcon', arrowBackSharp);\n  }\n  get backButtonText() {\n    const defaultBackButtonText = getIonMode(this) === 'ios' ? 'Back' : null;\n    return this.text != null ? this.text : config.get('backButtonText', defaultBackButtonText);\n  }\n  get hasIconOnly() {\n    return this.backButtonIcon && !this.backButtonText;\n  }\n  get rippleType() {\n    // If the button only has an icon we use the unbounded\n    // \"circular\" ripple effect\n    if (this.hasIconOnly) {\n      return 'unbounded';\n    }\n    return 'bounded';\n  }\n  render() {\n    const {\n      color,\n      defaultHref,\n      disabled,\n      type,\n      hasIconOnly,\n      backButtonIcon,\n      backButtonText,\n      icon,\n      inheritedAttributes\n    } = this;\n    const showBackButton = defaultHref !== undefined;\n    const mode = getIonMode(this);\n    const ariaLabel = inheritedAttributes['aria-label'] || backButtonText || 'back';\n    return h(Host, {\n      key: '5466624a10f1ab56f5469e6dc07080303880f2fe',\n      onClick: this.onClick,\n      class: createColorClasses(color, {\n        [mode]: true,\n        button: true,\n        // ion-buttons target .button\n        'back-button-disabled': disabled,\n        'back-button-has-icon-only': hasIconOnly,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'ion-activatable': true,\n        'ion-focusable': true,\n        'show-back-button': showBackButton\n      })\n    }, h(\"button\", {\n      key: '63bc75ef0ad7cc9fb79e58217a3314b20acd73e3',\n      type: type,\n      disabled: disabled,\n      class: \"button-native\",\n      part: \"native\",\n      \"aria-label\": ariaLabel\n    }, h(\"span\", {\n      key: '5d3eacbd11af2245c6e1151cab446a0d96559ad8',\n      class: \"button-inner\"\n    }, backButtonIcon && h(\"ion-icon\", {\n      key: '6439af0ae463764174e7d3207f02267811df666d',\n      part: \"icon\",\n      icon: backButtonIcon,\n      \"aria-hidden\": \"true\",\n      lazy: false,\n      \"flip-rtl\": icon === undefined\n    }), backButtonText && h(\"span\", {\n      key: '8ee89fb18dfdb5b75948a8b197ff4cdbc008742f',\n      part: \"text\",\n      \"aria-hidden\": \"true\",\n      class: \"button-text\"\n    }, backButtonText)), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: '63803a884998bc73bea5afe0b2a0a14e3fa4d6bf',\n      type: this.rippleType\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nBackButton.style = {\n  ios: backButtonIosCss,\n  md: backButtonMdCss\n};\nexport { BackButton as ion_back_button };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,mBAAmB;AACzB,IAAM,kBAAkB;AACxB,IAAM,aAAa,MAAM;AAAA,EACvB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,sBAAsB,CAAC;AAI5B,SAAK,WAAW;AAIhB,SAAK,OAAO;AACZ,SAAK,UAAU,CAAM,OAAM;AACzB,YAAM,MAAM,KAAK,GAAG,QAAQ,SAAS;AACrC,SAAG,eAAe;AAClB,UAAI,QAAQ,MAAM,IAAI,UAAU,IAAI;AAClC,eAAO,IAAI,IAAI;AAAA,UACb,kBAAkB,KAAK;AAAA,UACvB,YAAY;AAAA,QACd,CAAC;AAAA,MACH;AACA,aAAO,QAAQ,KAAK,aAAa,IAAI,QAAQ,KAAK,eAAe;AAAA,IACnE;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,sBAAsB,sBAAsB,KAAK,EAAE;AACxD,QAAI,KAAK,gBAAgB,QAAW;AAClC,WAAK,cAAc,OAAO,IAAI,uBAAuB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,IAAI,iBAAiB;AACnB,UAAM,OAAO,KAAK;AAClB,QAAI,QAAQ,MAAM;AAEhB,aAAO;AAAA,IACT;AACA,QAAI,WAAW,IAAI,MAAM,OAAO;AAE9B,aAAO,OAAO,IAAI,kBAAkB,WAAW;AAAA,IACjD;AAEA,WAAO,OAAO,IAAI,kBAAkB,cAAc;AAAA,EACpD;AAAA,EACA,IAAI,iBAAiB;AACnB,UAAM,wBAAwB,WAAW,IAAI,MAAM,QAAQ,SAAS;AACpE,WAAO,KAAK,QAAQ,OAAO,KAAK,OAAO,OAAO,IAAI,kBAAkB,qBAAqB;AAAA,EAC3F;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,kBAAkB,CAAC,KAAK;AAAA,EACtC;AAAA,EACA,IAAI,aAAa;AAGf,QAAI,KAAK,aAAa;AACpB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,iBAAiB,gBAAgB;AACvC,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,YAAY,oBAAoB,YAAY,KAAK,kBAAkB;AACzE,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,SAAS,KAAK;AAAA,MACd,OAAO,mBAAmB,OAAO;AAAA,QAC/B,CAAC,IAAI,GAAG;AAAA,QACR,QAAQ;AAAA;AAAA,QAER,wBAAwB;AAAA,QACxB,6BAA6B;AAAA,QAC7B,cAAc,YAAY,eAAe,KAAK,EAAE;AAAA,QAChD,oBAAoB,YAAY,sBAAsB,KAAK,EAAE;AAAA,QAC7D,mBAAmB;AAAA,QACnB,iBAAiB;AAAA,QACjB,oBAAoB;AAAA,MACtB,CAAC;AAAA,IACH,GAAG,EAAE,UAAU;AAAA,MACb,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,MAAM;AAAA,MACN,cAAc;AAAA,IAChB,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,kBAAkB,EAAE,YAAY;AAAA,MACjC,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,eAAe;AAAA,MACf,MAAM;AAAA,MACN,YAAY,SAAS;AAAA,IACvB,CAAC,GAAG,kBAAkB,EAAE,QAAQ;AAAA,MAC9B,KAAK;AAAA,MACL,MAAM;AAAA,MACN,eAAe;AAAA,MACf,OAAO;AAAA,IACT,GAAG,cAAc,CAAC,GAAG,SAAS,QAAQ,EAAE,qBAAqB;AAAA,MAC3D,KAAK;AAAA,MACL,MAAM,KAAK;AAAA,IACb,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;AACA,WAAW,QAAQ;AAAA,EACjB,KAAK;AAAA,EACL,IAAI;AACN;", "names": []}