.ow-header {
  --background: linear-gradient(135deg, var(--ow-rose-500) 0%, var(--ow-purple-500) 100%);
  --color: white;
}

.ow-content {
  --background: linear-gradient(135deg, var(--ow-rose-50) 0%, var(--ow-purple-50) 100%);
}

/* User Section */
.user-section {
  padding: var(--ow-space-xl) var(--ow-space-md) var(--ow-space-lg);
  color: white;
  
  .user-info {
    display: flex;
    align-items: center;
    
    .user-avatar {
      width: 80px;
      height: 80px;
      border-radius: var(--ow-radius-full);
      overflow: hidden;
      margin-right: var(--ow-space-md);
      border: 3px solid rgba(255, 255, 255, 0.3);
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    
    .user-details {
      flex: 1;
      
      .user-name {
        margin: 0 0 var(--ow-space-xs) 0;
        font-family: var(--ow-font-family-display);
        font-size: 1.5rem;
        font-weight: 600;
      }
      
      .user-email {
        margin: 0 0 var(--ow-space-xs) 0;
        opacity: 0.9;
        font-size: 0.9rem;
      }
      
      .member-since {
        margin: 0;
        opacity: 0.8;
        font-size: 0.8rem;
      }
    }
  }
}

/* Stats Section */
.stats-section {
  padding: var(--ow-space-md);
  margin-top: -20px;
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--ow-space-md);
    
    .stat-item {
      background: white;
      border-radius: var(--ow-radius-lg);
      padding: var(--ow-space-lg);
      text-align: center;
      box-shadow: var(--ow-shadow-md);
      
      .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: var(--ow-space-xs);
      }
      
      .stat-label {
        color: var(--ion-color-medium);
        font-size: 0.8rem;
        margin: 0;
      }
    }
  }
}

/* Menu Section */
.menu-section {
  padding: var(--ow-space-md);
  
  .menu-items {
    .menu-item {
      display: flex;
      align-items: center;
      background: white;
      border-radius: var(--ow-radius-lg);
      padding: var(--ow-space-md);
      margin-bottom: var(--ow-space-md);
      box-shadow: var(--ow-shadow-sm);
      transition: all 0.3s ease;
      
      &:active {
        transform: scale(0.98);
        box-shadow: var(--ow-shadow-lg);
      }
      
      .menu-icon {
        width: 50px;
        height: 50px;
        border-radius: var(--ow-radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: var(--ow-space-md);
        
        .icon {
          font-size: 1.5rem;
          color: white;
        }
      }
      
      .menu-content {
        flex: 1;
        
        .menu-title {
          margin: 0 0 var(--ow-space-xs) 0;
          color: var(--ion-color-dark);
        }
        
        .menu-subtitle {
          margin: 0;
          color: var(--ion-color-medium);
          font-size: 0.85rem;
        }
      }
      
      .chevron-icon {
        font-size: 1.2rem;
      }
    }
  }
}

/* Logout Section */
.logout-section {
  padding: var(--ow-space-md);
  text-align: center;
  
  .logout-btn {
    --border-radius: var(--ow-radius-lg);
    width: 100%;
    font-weight: 500;
  }
}

.bottom-spacing {
  height: var(--ow-space-xl);
}
