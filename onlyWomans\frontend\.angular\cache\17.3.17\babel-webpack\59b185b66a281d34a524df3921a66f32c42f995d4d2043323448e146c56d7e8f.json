{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptors } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { importProvidersFrom } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n// Routes\nconst routes = [{\n  path: '',\n  loadComponent: () => import('./app/pages/home/<USER>').then(m => m.HomeComponent)\n}, {\n  path: 'shop',\n  loadComponent: () => import('./app/pages/shop/shop.component').then(m => m.ShopComponent)\n}, {\n  path: 'categories',\n  loadComponent: () => import('./app/pages/categories/categories.component').then(m => m.CategoriesComponent)\n}, {\n  path: 'categories/:category',\n  loadComponent: () => import('./app/pages/category/category.component').then(m => m.CategoryComponent)\n}, {\n  path: 'product/:id',\n  loadComponent: () => import('./app/pages/product/product.component').then(m => m.ProductComponent)\n}, {\n  path: 'cart',\n  loadComponent: () => import('./app/pages/cart/cart.component').then(m => m.CartComponent)\n}, {\n  path: 'wishlist',\n  loadComponent: () => import('./app/pages/wishlist/wishlist.component').then(m => m.WishlistComponent)\n}, {\n  path: 'profile',\n  loadComponent: () => import('./app/pages/profile/profile.component').then(m => m.ProfileComponent)\n}, {\n  path: 'auth/login',\n  loadComponent: () => import('./app/pages/auth/login/login.component').then(m => m.LoginComponent)\n}, {\n  path: 'auth/register',\n  loadComponent: () => import('./app/pages/auth/register/register.component').then(m => m.RegisterComponent)\n}, {\n  path: 'beauty',\n  loadComponent: () => import('./app/pages/beauty/beauty.component').then(m => m.BeautyComponent)\n}, {\n  path: 'ethnic',\n  loadComponent: () => import('./app/pages/ethnic/ethnic.component').then(m => m.EthnicComponent)\n}, {\n  path: 'western',\n  loadComponent: () => import('./app/pages/western/western.component').then(m => m.WesternComponent)\n}, {\n  path: 'accessories',\n  loadComponent: () => import('./app/pages/accessories/accessories.component').then(m => m.AccessoriesComponent)\n}, {\n  path: '**',\n  redirectTo: ''\n}];\n// HTTP Interceptors\nconst authInterceptor = (req, next) => {\n  const token = localStorage.getItem('onlywomans_token');\n  if (token) {\n    req = req.clone({\n      setHeaders: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  return next(req);\n};\nconst errorInterceptor = (req, next) => {\n  return next(req).pipe(\n    // Add error handling here\n  );\n};\nbootstrapApplication(AppComponent, {\n  providers: [provideRouter(routes), provideHttpClient(withInterceptors([authInterceptor, errorInterceptor])), provideAnimations(), importProvidersFrom(CommonModule)]\n}).catch(err => console.error(err));", "map": {"version": 3, "names": ["bootstrapApplication", "AppComponent", "provideRouter", "provideHttpClient", "withInterceptors", "provideAnimations", "importProvidersFrom", "CommonModule", "routes", "path", "loadComponent", "then", "m", "HomeComponent", "ShopComponent", "CategoriesComponent", "CategoryComponent", "ProductComponent", "CartComponent", "WishlistComponent", "ProfileComponent", "LoginComponent", "RegisterComponent", "BeautyComponent", "EthnicComponent", "WesternComponent", "AccessoriesComponent", "redirectTo", "authInterceptor", "req", "next", "token", "localStorage", "getItem", "clone", "setHeaders", "Authorization", "errorInterceptor", "pipe", "providers", "catch", "err", "console", "error"], "sources": ["E:\\Fahion\\DFashion\\onlyWomans\\frontend\\src\\main.ts"], "sourcesContent": ["import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptors } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { importProvidersFrom } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n// Routes\nconst routes = [\n  {\n    path: '',\n    loadComponent: () => import('./app/pages/home/<USER>').then(m => m.HomeComponent)\n  },\n  {\n    path: 'shop',\n    loadComponent: () => import('./app/pages/shop/shop.component').then(m => m.ShopComponent)\n  },\n  {\n    path: 'categories',\n    loadComponent: () => import('./app/pages/categories/categories.component').then(m => m.CategoriesComponent)\n  },\n  {\n    path: 'categories/:category',\n    loadComponent: () => import('./app/pages/category/category.component').then(m => m.CategoryComponent)\n  },\n  {\n    path: 'product/:id',\n    loadComponent: () => import('./app/pages/product/product.component').then(m => m.ProductComponent)\n  },\n  {\n    path: 'cart',\n    loadComponent: () => import('./app/pages/cart/cart.component').then(m => m.CartComponent)\n  },\n  {\n    path: 'wishlist',\n    loadComponent: () => import('./app/pages/wishlist/wishlist.component').then(m => m.WishlistComponent)\n  },\n  {\n    path: 'profile',\n    loadComponent: () => import('./app/pages/profile/profile.component').then(m => m.ProfileComponent)\n  },\n  {\n    path: 'auth/login',\n    loadComponent: () => import('./app/pages/auth/login/login.component').then(m => m.LoginComponent)\n  },\n  {\n    path: 'auth/register',\n    loadComponent: () => import('./app/pages/auth/register/register.component').then(m => m.RegisterComponent)\n  },\n  {\n    path: 'beauty',\n    loadComponent: () => import('./app/pages/beauty/beauty.component').then(m => m.BeautyComponent)\n  },\n  {\n    path: 'ethnic',\n    loadComponent: () => import('./app/pages/ethnic/ethnic.component').then(m => m.EthnicComponent)\n  },\n  {\n    path: 'western',\n    loadComponent: () => import('./app/pages/western/western.component').then(m => m.WesternComponent)\n  },\n  {\n    path: 'accessories',\n    loadComponent: () => import('./app/pages/accessories/accessories.component').then(m => m.AccessoriesComponent)\n  },\n  {\n    path: '**',\n    redirectTo: ''\n  }\n];\n\n// HTTP Interceptors\nconst authInterceptor = (req: any, next: any) => {\n  const token = localStorage.getItem('onlywomans_token');\n  if (token) {\n    req = req.clone({\n      setHeaders: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  return next(req);\n};\n\nconst errorInterceptor = (req: any, next: any) => {\n  return next(req).pipe(\n    // Add error handling here\n  );\n};\n\nbootstrapApplication(AppComponent, {\n  providers: [\n    provideRouter(routes),\n    provideHttpClient(withInterceptors([authInterceptor, errorInterceptor])),\n    provideAnimations(),\n    importProvidersFrom(CommonModule)\n  ]\n}).catch(err => console.error(err));\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC1E,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,mBAAmB,QAAQ,eAAe;AACnD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa;CACzF,EACD;EACEJ,IAAI,EAAE,MAAM;EACZC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,aAAa;CACzF,EACD;EACEL,IAAI,EAAE,YAAY;EAClBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,6CAA6C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,mBAAmB;CAC3G,EACD;EACEN,IAAI,EAAE,sBAAsB;EAC5BC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,yCAAyC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,iBAAiB;CACrG,EACD;EACEP,IAAI,EAAE,aAAa;EACnBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,gBAAgB;CAClG,EACD;EACER,IAAI,EAAE,MAAM;EACZC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,aAAa;CACzF,EACD;EACET,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,yCAAyC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,iBAAiB;CACrG,EACD;EACEV,IAAI,EAAE,SAAS;EACfC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,gBAAgB;CAClG,EACD;EACEX,IAAI,EAAE,YAAY;EAClBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACS,cAAc;CACjG,EACD;EACEZ,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACU,iBAAiB;CAC1G,EACD;EACEb,IAAI,EAAE,QAAQ;EACdC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACW,eAAe;CAC/F,EACD;EACEd,IAAI,EAAE,QAAQ;EACdC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACY,eAAe;CAC/F,EACD;EACEf,IAAI,EAAE,SAAS;EACfC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACa,gBAAgB;CAClG,EACD;EACEhB,IAAI,EAAE,aAAa;EACnBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,+CAA+C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACc,oBAAoB;CAC9G,EACD;EACEjB,IAAI,EAAE,IAAI;EACVkB,UAAU,EAAE;CACb,CACF;AAED;AACA,MAAMC,eAAe,GAAGA,CAACC,GAAQ,EAAEC,IAAS,KAAI;EAC9C,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;EACtD,IAAIF,KAAK,EAAE;IACTF,GAAG,GAAGA,GAAG,CAACK,KAAK,CAAC;MACdC,UAAU,EAAE;QACVC,aAAa,EAAE,UAAUL,KAAK;;KAEjC,CAAC;;EAEJ,OAAOD,IAAI,CAACD,GAAG,CAAC;AAClB,CAAC;AAED,MAAMQ,gBAAgB,GAAGA,CAACR,GAAQ,EAAEC,IAAS,KAAI;EAC/C,OAAOA,IAAI,CAACD,GAAG,CAAC,CAACS,IAAI;IACnB;EAAA,CACD;AACH,CAAC;AAEDtC,oBAAoB,CAACC,YAAY,EAAE;EACjCsC,SAAS,EAAE,CACTrC,aAAa,CAACM,MAAM,CAAC,EACrBL,iBAAiB,CAACC,gBAAgB,CAAC,CAACwB,eAAe,EAAES,gBAAgB,CAAC,CAAC,CAAC,EACxEhC,iBAAiB,EAAE,EACnBC,mBAAmB,CAACC,YAAY,CAAC;CAEpC,CAAC,CAACiC,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}