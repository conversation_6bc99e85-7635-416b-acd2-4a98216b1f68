{"ast": null, "code": "import { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nexport class Scheduler {\n  constructor(schedulerActionCtor, now = Scheduler.now) {\n    this.schedulerActionCtor = schedulerActionCtor;\n    this.now = now;\n  }\n  schedule(work, delay = 0, state) {\n    return new this.schedulerActionCtor(this, work).schedule(state, delay);\n  }\n}\nScheduler.now = dateTimestampProvider.now;\n//# sourceMappingURL=Scheduler.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}