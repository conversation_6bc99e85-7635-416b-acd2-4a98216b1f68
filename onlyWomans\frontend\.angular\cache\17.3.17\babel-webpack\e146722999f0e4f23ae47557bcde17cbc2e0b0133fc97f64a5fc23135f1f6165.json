{"ast": null, "code": "import { operate } from '../util/lift';\nimport { concatAll } from './concatAll';\nimport { popScheduler } from '../util/args';\nimport { from } from '../observable/from';\nexport function concat(...args) {\n  const scheduler = popScheduler(args);\n  return operate((source, subscriber) => {\n    concatAll()(from([source, ...args], scheduler)).subscribe(subscriber);\n  });\n}\n//# sourceMappingURL=concat.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}