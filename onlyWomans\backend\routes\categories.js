const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');

// Sample categories data for women's fashion
const categories = [
  {
    id: 1,
    name: 'Ethnic Wear',
    slug: 'ethnic-wear',
    description: 'Traditional Indian wear for women including sarees, lehengas, suits',
    image: '/assets/categories/ethnic.jpg',
    icon: '🥻',
    subcategories: [
      { id: 11, name: 'Sarees', slug: 'sarees' },
      { id: 12, name: 'Lehengas', slug: 'lehengas' },
      { id: 13, name: 'Salwar Suits', slug: 'salwar-suits' },
      { id: 14, name: '<PERSON><PERSON>', slug: 'kurtis' },
      { id: 15, name: 'Anarka<PERSON> Suits', slug: 'anarkali-suits' }
    ],
    productCount: 1250,
    featured: true
  },
  {
    id: 2,
    name: 'Western Wear',
    slug: 'western-wear',
    description: 'Contemporary western outfits for modern women',
    image: '/assets/categories/western.jpg',
    icon: '👗',
    subcategories: [
      { id: 21, name: 'Dress<PERSON>', slug: 'dresses' },
      { id: 22, name: '<PERSON><PERSON> & Blouses', slug: 'tops-blouses' },
      { id: 23, name: '<PERSON><PERSON> & Trousers', slug: 'jeans-trousers' },
      { id: 24, name: 'Skirts', slug: 'skirts' },
      { id: 25, name: 'Jumpsuits', slug: 'jumpsuits' }
    ],
    productCount: 980,
    featured: true
  },
  {
    id: 3,
    name: 'Beauty & Makeup',
    slug: 'beauty-makeup',
    description: 'Cosmetics, skincare, and beauty products',
    image: '/assets/categories/beauty.jpg',
    icon: '💄',
    subcategories: [
      { id: 31, name: 'Face Makeup', slug: 'face-makeup' },
      { id: 32, name: 'Eye Makeup', slug: 'eye-makeup' },
      { id: 33, name: 'Lip Products', slug: 'lip-products' },
      { id: 34, name: 'Skincare', slug: 'skincare' },
      { id: 35, name: 'Hair Care', slug: 'hair-care' }
    ],
    productCount: 750,
    featured: true
  },
  {
    id: 4,
    name: 'Accessories',
    slug: 'accessories',
    description: 'Bags, belts, scarves and fashion accessories',
    image: '/assets/categories/accessories.jpg',
    icon: '👜',
    subcategories: [
      { id: 41, name: 'Handbags', slug: 'handbags' },
      { id: 42, name: 'Wallets', slug: 'wallets' },
      { id: 43, name: 'Belts', slug: 'belts' },
      { id: 44, name: 'Scarves', slug: 'scarves' },
      { id: 45, name: 'Sunglasses', slug: 'sunglasses' }
    ],
    productCount: 650,
    featured: true
  },
  {
    id: 5,
    name: 'Footwear',
    slug: 'footwear',
    description: 'Shoes, sandals, heels for every occasion',
    image: '/assets/categories/footwear.jpg',
    icon: '👠',
    subcategories: [
      { id: 51, name: 'Heels', slug: 'heels' },
      { id: 52, name: 'Flats', slug: 'flats' },
      { id: 53, name: 'Sandals', slug: 'sandals' },
      { id: 54, name: 'Boots', slug: 'boots' },
      { id: 55, name: 'Sneakers', slug: 'sneakers' }
    ],
    productCount: 420,
    featured: false
  },
  {
    id: 6,
    name: 'Jewelry',
    slug: 'jewelry',
    description: 'Earrings, necklaces, rings and fashion jewelry',
    image: '/assets/categories/jewelry.jpg',
    icon: '💎',
    subcategories: [
      { id: 61, name: 'Earrings', slug: 'earrings' },
      { id: 62, name: 'Necklaces', slug: 'necklaces' },
      { id: 63, name: 'Rings', slug: 'rings' },
      { id: 64, name: 'Bracelets', slug: 'bracelets' },
      { id: 65, name: 'Anklets', slug: 'anklets' }
    ],
    productCount: 380,
    featured: false
  }
];

// @route   GET /api/categories
// @desc    Get all categories
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { featured } = req.query;
    
    let filteredCategories = categories;
    
    if (featured === 'true') {
      filteredCategories = categories.filter(cat => cat.featured);
    }
    
    res.json({
      success: true,
      count: filteredCategories.length,
      data: filteredCategories
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   GET /api/categories/:slug
// @desc    Get category by slug
// @access  Public
router.get('/:slug', async (req, res) => {
  try {
    const category = categories.find(cat => cat.slug === req.params.slug);
    
    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }
    
    res.json({
      success: true,
      data: category
    });
  } catch (error) {
    console.error('Error fetching category:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   GET /api/categories/:slug/subcategories
// @desc    Get subcategories for a category
// @access  Public
router.get('/:slug/subcategories', async (req, res) => {
  try {
    const category = categories.find(cat => cat.slug === req.params.slug);
    
    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }
    
    res.json({
      success: true,
      data: category.subcategories
    });
  } catch (error) {
    console.error('Error fetching subcategories:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   POST /api/categories
// @desc    Create new category (Admin only)
// @access  Private/Admin
router.post('/', auth, async (req, res) => {
  try {
    // This would typically save to database
    // For now, just return success message
    res.json({
      success: true,
      message: 'Category creation endpoint - implement database logic'
    });
  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   PUT /api/categories/:id
// @desc    Update category (Admin only)
// @access  Private/Admin
router.put('/:id', auth, async (req, res) => {
  try {
    // This would typically update in database
    // For now, just return success message
    res.json({
      success: true,
      message: 'Category update endpoint - implement database logic'
    });
  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   DELETE /api/categories/:id
// @desc    Delete category (Admin only)
// @access  Private/Admin
router.delete('/:id', auth, async (req, res) => {
  try {
    // This would typically delete from database
    // For now, just return success message
    res.json({
      success: true,
      message: 'Category deletion endpoint - implement database logic'
    });
  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

module.exports = router;
