{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/status-tap-7t9T91bG.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { f as readTask, w as writeTask } from './index-B_U9CtaY.js';\nimport { f as findClosestIonContent, s as scrollToTop } from './index-BlJTBdxG.js';\nimport { c as componentOnReady } from './helpers-1O4D2b7y.js';\nconst startStatusTap = () => {\n  const win = window;\n  win.addEventListener('statusTap', () => {\n    readTask(() => {\n      const width = win.innerWidth;\n      const height = win.innerHeight;\n      const el = document.elementFromPoint(width / 2, height / 2);\n      if (!el) {\n        return;\n      }\n      const contentEl = findClosestIonContent(el);\n      if (contentEl) {\n        new Promise(resolve => componentOnReady(contentEl, resolve)).then(() => {\n          writeTask(async () => {\n            /**\n             * If scrolling and user taps status bar,\n             * only calling scrollToTop is not enough\n             * as engines like WebKit will jump the\n             * scroll position back down and complete\n             * any in-progress momentum scrolling.\n             */\n            contentEl.style.setProperty('--overflow', 'hidden');\n            await scrollToTop(contentEl, 300);\n            contentEl.style.removeProperty('--overflow');\n          });\n        });\n      }\n    });\n  });\n};\nexport { startStatusTap };"], "mappings": ";;;;;;;;;;;;;;;;AAMA,IAAM,iBAAiB,MAAM;AAC3B,QAAM,MAAM;AACZ,MAAI,iBAAiB,aAAa,MAAM;AACtC,aAAS,MAAM;AACb,YAAM,QAAQ,IAAI;AAClB,YAAM,SAAS,IAAI;AACnB,YAAM,KAAK,SAAS,iBAAiB,QAAQ,GAAG,SAAS,CAAC;AAC1D,UAAI,CAAC,IAAI;AACP;AAAA,MACF;AACA,YAAM,YAAY,sBAAsB,EAAE;AAC1C,UAAI,WAAW;AACb,YAAI,QAAQ,aAAW,iBAAiB,WAAW,OAAO,CAAC,EAAE,KAAK,MAAM;AACtE,oBAAU,MAAY;AAQpB,sBAAU,MAAM,YAAY,cAAc,QAAQ;AAClD,kBAAM,YAAY,WAAW,GAAG;AAChC,sBAAU,MAAM,eAAe,YAAY;AAAA,UAC7C,EAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;", "names": []}