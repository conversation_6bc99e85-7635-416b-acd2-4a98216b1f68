{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ios.transition-BmXtMRXZ.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport { g as getIonPageElement } from './index-DfBA5ztX.js';\nimport './index-B_U9CtaY.js';\nimport './index-ZjP4CjeZ.js';\nimport './helpers-1O4D2b7y.js';\nconst DURATION = 540;\n// TODO(FW-2832): types\nconst getClonedElement = tagName => {\n  return document.querySelector(`${tagName}.ion-cloned-element`);\n};\nconst shadow = el => {\n  return el.shadowRoot || el;\n};\nconst getLargeTitle = refEl => {\n  const tabs = refEl.tagName === 'ION-TABS' ? refEl : refEl.querySelector('ion-tabs');\n  const query = 'ion-content ion-header:not(.header-collapse-condense-inactive) ion-title.title-large';\n  if (tabs != null) {\n    const activeTab = tabs.querySelector('ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)');\n    return activeTab != null ? activeTab.querySelector(query) : null;\n  }\n  return refEl.querySelector(query);\n};\nconst getBackButton = (refEl, backDirection) => {\n  const tabs = refEl.tagName === 'ION-TABS' ? refEl : refEl.querySelector('ion-tabs');\n  let buttonsList = [];\n  if (tabs != null) {\n    const activeTab = tabs.querySelector('ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)');\n    if (activeTab != null) {\n      buttonsList = activeTab.querySelectorAll('ion-buttons');\n    }\n  } else {\n    buttonsList = refEl.querySelectorAll('ion-buttons');\n  }\n  for (const buttons of buttonsList) {\n    const parentHeader = buttons.closest('ion-header');\n    const activeHeader = parentHeader && !parentHeader.classList.contains('header-collapse-condense-inactive');\n    const backButton = buttons.querySelector('ion-back-button');\n    const buttonsCollapse = buttons.classList.contains('buttons-collapse');\n    const startSlot = buttons.slot === 'start' || buttons.slot === '';\n    if (backButton !== null && startSlot && (buttonsCollapse && activeHeader && backDirection || !buttonsCollapse)) {\n      return backButton;\n    }\n  }\n  return null;\n};\nconst createLargeTitleTransition = (rootAnimation, rtl, backDirection, enteringEl, leavingEl) => {\n  const enteringBackButton = getBackButton(enteringEl, backDirection);\n  const leavingLargeTitle = getLargeTitle(leavingEl);\n  const enteringLargeTitle = getLargeTitle(enteringEl);\n  const leavingBackButton = getBackButton(leavingEl, backDirection);\n  const shouldAnimationForward = enteringBackButton !== null && leavingLargeTitle !== null && !backDirection;\n  const shouldAnimationBackward = enteringLargeTitle !== null && leavingBackButton !== null && backDirection;\n  if (shouldAnimationForward) {\n    const leavingLargeTitleBox = leavingLargeTitle.getBoundingClientRect();\n    const enteringBackButtonBox = enteringBackButton.getBoundingClientRect();\n    const enteringBackButtonTextEl = shadow(enteringBackButton).querySelector('.button-text');\n    // Text element not rendered if developers pass text=\"\" to the back button\n    const enteringBackButtonTextBox = enteringBackButtonTextEl === null || enteringBackButtonTextEl === void 0 ? void 0 : enteringBackButtonTextEl.getBoundingClientRect();\n    const leavingLargeTitleTextEl = shadow(leavingLargeTitle).querySelector('.toolbar-title');\n    const leavingLargeTitleTextBox = leavingLargeTitleTextEl.getBoundingClientRect();\n    animateLargeTitle(rootAnimation, rtl, backDirection, leavingLargeTitle, leavingLargeTitleBox, leavingLargeTitleTextBox, enteringBackButtonBox, enteringBackButtonTextEl, enteringBackButtonTextBox);\n    animateBackButton(rootAnimation, rtl, backDirection, enteringBackButton, enteringBackButtonBox, enteringBackButtonTextEl, enteringBackButtonTextBox, leavingLargeTitle, leavingLargeTitleTextBox);\n  } else if (shouldAnimationBackward) {\n    const enteringLargeTitleBox = enteringLargeTitle.getBoundingClientRect();\n    const leavingBackButtonBox = leavingBackButton.getBoundingClientRect();\n    const leavingBackButtonTextEl = shadow(leavingBackButton).querySelector('.button-text');\n    // Text element not rendered if developers pass text=\"\" to the back button\n    const leavingBackButtonTextBox = leavingBackButtonTextEl === null || leavingBackButtonTextEl === void 0 ? void 0 : leavingBackButtonTextEl.getBoundingClientRect();\n    const enteringLargeTitleTextEl = shadow(enteringLargeTitle).querySelector('.toolbar-title');\n    const enteringLargeTitleTextBox = enteringLargeTitleTextEl.getBoundingClientRect();\n    animateLargeTitle(rootAnimation, rtl, backDirection, enteringLargeTitle, enteringLargeTitleBox, enteringLargeTitleTextBox, leavingBackButtonBox, leavingBackButtonTextEl, leavingBackButtonTextBox);\n    animateBackButton(rootAnimation, rtl, backDirection, leavingBackButton, leavingBackButtonBox, leavingBackButtonTextEl, leavingBackButtonTextBox, enteringLargeTitle, enteringLargeTitleTextBox);\n  }\n  return {\n    forward: shouldAnimationForward,\n    backward: shouldAnimationBackward\n  };\n};\nconst animateBackButton = (rootAnimation, rtl, backDirection, backButtonEl, backButtonBox, backButtonTextEl, backButtonTextBox, largeTitleEl, largeTitleTextBox) => {\n  var _a, _b;\n  const BACK_BUTTON_START_OFFSET = rtl ? `calc(100% - ${backButtonBox.right + 4}px)` : `${backButtonBox.left - 4}px`;\n  const TEXT_ORIGIN_X = rtl ? 'right' : 'left';\n  const ICON_ORIGIN_X = rtl ? 'left' : 'right';\n  const CONTAINER_ORIGIN_X = rtl ? 'right' : 'left';\n  let WIDTH_SCALE = 1;\n  let HEIGHT_SCALE = 1;\n  let TEXT_START_SCALE = `scale(${HEIGHT_SCALE})`;\n  const TEXT_END_SCALE = 'scale(1)';\n  if (backButtonTextEl && backButtonTextBox) {\n    /**\n     * When the title and back button texts match then they should overlap during the\n     * page transition. If the texts do not match up then the back button text scale\n     * adjusts to not perfectly match the large title text otherwise the proportions\n     * will be incorrect. When the texts match we scale both the width and height to\n     * account for font weight differences between the title and back button.\n     */\n    const doTitleAndButtonTextsMatch = ((_a = backButtonTextEl.textContent) === null || _a === void 0 ? void 0 : _a.trim()) === ((_b = largeTitleEl.textContent) === null || _b === void 0 ? void 0 : _b.trim());\n    WIDTH_SCALE = largeTitleTextBox.width / backButtonTextBox.width;\n    /**\n     * Subtract an offset to account for slight sizing/padding differences between the\n     * title and the back button.\n     */\n    HEIGHT_SCALE = (largeTitleTextBox.height - LARGE_TITLE_SIZE_OFFSET) / backButtonTextBox.height;\n    /**\n     * Even though we set TEXT_START_SCALE to HEIGHT_SCALE above, we potentially need\n     * to re-compute this here since the HEIGHT_SCALE may have changed.\n     */\n    TEXT_START_SCALE = doTitleAndButtonTextsMatch ? `scale(${WIDTH_SCALE}, ${HEIGHT_SCALE})` : `scale(${HEIGHT_SCALE})`;\n  }\n  const backButtonIconEl = shadow(backButtonEl).querySelector('ion-icon');\n  const backButtonIconBox = backButtonIconEl.getBoundingClientRect();\n  /**\n   * We need to offset the container by the icon dimensions\n   * so that the back button text aligns with the large title\n   * text. Otherwise, the back button icon will align with the\n   * large title text but the back button text will not.\n   */\n  const CONTAINER_START_TRANSLATE_X = rtl ? `${backButtonIconBox.width / 2 - (backButtonIconBox.right - backButtonBox.right)}px` : `${backButtonBox.left - backButtonIconBox.width / 2}px`;\n  const CONTAINER_END_TRANSLATE_X = rtl ? `-${window.innerWidth - backButtonBox.right}px` : `${backButtonBox.left}px`;\n  /**\n   * Back button container should be\n   * aligned to the top of the title container\n   * so the texts overlap as the back button\n   * text begins to fade in.\n   */\n  const CONTAINER_START_TRANSLATE_Y = `${largeTitleTextBox.top}px`;\n  /**\n   * The cloned back button should align exactly with the\n   * real back button on the entering page otherwise there will\n   * be a layout shift.\n   */\n  const CONTAINER_END_TRANSLATE_Y = `${backButtonBox.top}px`;\n  /**\n   * In the forward direction, the cloned back button\n   * container should translate from over the large title\n   * to over the back button. In the backward direction,\n   * it should translate from over the back button to over\n   * the large title.\n   */\n  const FORWARD_CONTAINER_KEYFRAMES = [{\n    offset: 0,\n    transform: `translate3d(${CONTAINER_START_TRANSLATE_X}, ${CONTAINER_START_TRANSLATE_Y}, 0)`\n  }, {\n    offset: 1,\n    transform: `translate3d(${CONTAINER_END_TRANSLATE_X}, ${CONTAINER_END_TRANSLATE_Y}, 0)`\n  }];\n  const BACKWARD_CONTAINER_KEYFRAMES = [{\n    offset: 0,\n    transform: `translate3d(${CONTAINER_END_TRANSLATE_X}, ${CONTAINER_END_TRANSLATE_Y}, 0)`\n  }, {\n    offset: 1,\n    transform: `translate3d(${CONTAINER_START_TRANSLATE_X}, ${CONTAINER_START_TRANSLATE_Y}, 0)`\n  }];\n  const CONTAINER_KEYFRAMES = backDirection ? BACKWARD_CONTAINER_KEYFRAMES : FORWARD_CONTAINER_KEYFRAMES;\n  /**\n   * In the forward direction, the text in the cloned back button\n   * should start to be (roughly) the size of the large title\n   * and then scale down to be the size of the actual back button.\n   * The text should also translate, but that translate is handled\n   * by the container keyframes.\n   */\n  const FORWARD_TEXT_KEYFRAMES = [{\n    offset: 0,\n    opacity: 0,\n    transform: TEXT_START_SCALE\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: TEXT_END_SCALE\n  }];\n  const BACKWARD_TEXT_KEYFRAMES = [{\n    offset: 0,\n    opacity: 1,\n    transform: TEXT_END_SCALE\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: TEXT_START_SCALE\n  }];\n  const TEXT_KEYFRAMES = backDirection ? BACKWARD_TEXT_KEYFRAMES : FORWARD_TEXT_KEYFRAMES;\n  /**\n   * The icon should scale in/out in the second\n   * half of the animation. The icon should also\n   * translate, but that translate is handled by the\n   * container keyframes.\n   */\n  const FORWARD_ICON_KEYFRAMES = [{\n    offset: 0,\n    opacity: 0,\n    transform: 'scale(0.6)'\n  }, {\n    offset: 0.6,\n    opacity: 0,\n    transform: 'scale(0.6)'\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: 'scale(1)'\n  }];\n  const BACKWARD_ICON_KEYFRAMES = [{\n    offset: 0,\n    opacity: 1,\n    transform: 'scale(1)'\n  }, {\n    offset: 0.2,\n    opacity: 0,\n    transform: 'scale(0.6)'\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: 'scale(0.6)'\n  }];\n  const ICON_KEYFRAMES = backDirection ? BACKWARD_ICON_KEYFRAMES : FORWARD_ICON_KEYFRAMES;\n  const enteringBackButtonTextAnimation = createAnimation();\n  const enteringBackButtonIconAnimation = createAnimation();\n  const enteringBackButtonAnimation = createAnimation();\n  const clonedBackButtonEl = getClonedElement('ion-back-button');\n  const clonedBackButtonTextEl = shadow(clonedBackButtonEl).querySelector('.button-text');\n  const clonedBackButtonIconEl = shadow(clonedBackButtonEl).querySelector('ion-icon');\n  clonedBackButtonEl.text = backButtonEl.text;\n  clonedBackButtonEl.mode = backButtonEl.mode;\n  clonedBackButtonEl.icon = backButtonEl.icon;\n  clonedBackButtonEl.color = backButtonEl.color;\n  clonedBackButtonEl.disabled = backButtonEl.disabled;\n  clonedBackButtonEl.style.setProperty('display', 'block');\n  clonedBackButtonEl.style.setProperty('position', 'fixed');\n  enteringBackButtonIconAnimation.addElement(clonedBackButtonIconEl);\n  enteringBackButtonTextAnimation.addElement(clonedBackButtonTextEl);\n  enteringBackButtonAnimation.addElement(clonedBackButtonEl);\n  enteringBackButtonAnimation.beforeStyles({\n    position: 'absolute',\n    top: '0px',\n    [CONTAINER_ORIGIN_X]: '0px'\n  })\n  /**\n   * The write hooks must be set on this animation as it is guaranteed to run. Other\n   * animations such as the back button text animation will not run if the back button\n   * has no visible text.\n   */.beforeAddWrite(() => {\n    backButtonEl.style.setProperty('display', 'none');\n    clonedBackButtonEl.style.setProperty(TEXT_ORIGIN_X, BACK_BUTTON_START_OFFSET);\n  }).afterAddWrite(() => {\n    backButtonEl.style.setProperty('display', '');\n    clonedBackButtonEl.style.setProperty('display', 'none');\n    clonedBackButtonEl.style.removeProperty(TEXT_ORIGIN_X);\n  }).keyframes(CONTAINER_KEYFRAMES);\n  enteringBackButtonTextAnimation.beforeStyles({\n    'transform-origin': `${TEXT_ORIGIN_X} top`\n  }).keyframes(TEXT_KEYFRAMES);\n  enteringBackButtonIconAnimation.beforeStyles({\n    'transform-origin': `${ICON_ORIGIN_X} center`\n  }).keyframes(ICON_KEYFRAMES);\n  rootAnimation.addAnimation([enteringBackButtonTextAnimation, enteringBackButtonIconAnimation, enteringBackButtonAnimation]);\n};\nconst animateLargeTitle = (rootAnimation, rtl, backDirection, largeTitleEl, largeTitleBox, largeTitleTextBox, backButtonBox, backButtonTextEl, backButtonTextBox) => {\n  var _a, _b;\n  /**\n   * The horizontal transform origin for the large title\n   */\n  const ORIGIN_X = rtl ? 'right' : 'left';\n  const TITLE_START_OFFSET = rtl ? `calc(100% - ${largeTitleBox.right}px)` : `${largeTitleBox.left}px`;\n  /**\n   * The cloned large should align exactly with the\n   * real large title on the leaving page otherwise there will\n   * be a layout shift.\n   */\n  const START_TRANSLATE_X = '0px';\n  const START_TRANSLATE_Y = `${largeTitleBox.top}px`;\n  /**\n   * How much to offset the large title translation by.\n   * This accounts for differences in sizing between the large\n   * title and the back button due to padding and font weight.\n   */\n  const LARGE_TITLE_TRANSLATION_OFFSET = 8;\n  let END_TRANSLATE_X = rtl ? `-${window.innerWidth - backButtonBox.right - LARGE_TITLE_TRANSLATION_OFFSET}px` : `${backButtonBox.x + LARGE_TITLE_TRANSLATION_OFFSET}px`;\n  /**\n   * How much to scale the large title up/down by.\n   */\n  let HEIGHT_SCALE = 0.5;\n  /**\n   * The large title always starts full size.\n   */\n  const START_SCALE = 'scale(1)';\n  /**\n   * By default, we don't worry about having the large title scaled to perfectly\n   * match the back button because we don't know if the back button's text matches\n   * the large title's text.\n   */\n  let END_SCALE = `scale(${HEIGHT_SCALE})`;\n  // Text element not rendered if developers pass text=\"\" to the back button\n  if (backButtonTextEl && backButtonTextBox) {\n    /**\n     * The scaled title should (roughly) overlap the back button. This ensures that\n     * the back button and title overlap during the animation. Note that since both\n     * elements either fade in or fade out over the course of the animation, neither\n     * element will be fully visible on top of the other. As a result, the overlap\n     * does not need to be perfect, so approximate values are acceptable here.\n     */\n    END_TRANSLATE_X = rtl ? `-${window.innerWidth - backButtonTextBox.right - LARGE_TITLE_TRANSLATION_OFFSET}px` : `${backButtonTextBox.x - LARGE_TITLE_TRANSLATION_OFFSET}px`;\n    /**\n     * In the forward direction, the large title should start at its normal size and\n     * then scale down to be (roughly) the size of the back button on the other view.\n     * In the backward direction, the large title should start at (roughly) the size\n     * of the back button and then scale up to its original size.\n     * Note that since both elements either fade in or fade out over the course of the\n     * animation, neither element will be fully visible on top of the other. As a result,\n     * the overlap  does not need to be perfect, so approximate values are acceptable here.\n     */\n    /**\n     * When the title and back button texts match then they should overlap during the\n     * page transition. If the texts do not match up then the large title text scale\n     * adjusts to not perfectly match the back button text otherwise the proportions\n     * will be incorrect. When the texts match we scale both the width and height to\n     * account for font weight differences between the title and back button.\n     */\n    const doTitleAndButtonTextsMatch = ((_a = backButtonTextEl.textContent) === null || _a === void 0 ? void 0 : _a.trim()) === ((_b = largeTitleEl.textContent) === null || _b === void 0 ? void 0 : _b.trim());\n    const WIDTH_SCALE = backButtonTextBox.width / largeTitleTextBox.width;\n    HEIGHT_SCALE = backButtonTextBox.height / (largeTitleTextBox.height - LARGE_TITLE_SIZE_OFFSET);\n    /**\n     * Even though we set TEXT_START_SCALE to HEIGHT_SCALE above, we potentially need\n     * to re-compute this here since the HEIGHT_SCALE may have changed.\n     */\n    END_SCALE = doTitleAndButtonTextsMatch ? `scale(${WIDTH_SCALE}, ${HEIGHT_SCALE})` : `scale(${HEIGHT_SCALE})`;\n  }\n  /**\n   * The midpoints of the back button and the title should align such that the back\n   * button and title appear to be centered with each other.\n   */\n  const backButtonMidPoint = backButtonBox.top + backButtonBox.height / 2;\n  const titleMidPoint = largeTitleBox.height * HEIGHT_SCALE / 2;\n  const END_TRANSLATE_Y = `${backButtonMidPoint - titleMidPoint}px`;\n  const BACKWARDS_KEYFRAMES = [{\n    offset: 0,\n    opacity: 0,\n    transform: `translate3d(${END_TRANSLATE_X}, ${END_TRANSLATE_Y}, 0) ${END_SCALE}`\n  }, {\n    offset: 0.1,\n    opacity: 0\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: `translate3d(${START_TRANSLATE_X}, ${START_TRANSLATE_Y}, 0) ${START_SCALE}`\n  }];\n  const FORWARDS_KEYFRAMES = [{\n    offset: 0,\n    opacity: 0.99,\n    transform: `translate3d(${START_TRANSLATE_X}, ${START_TRANSLATE_Y}, 0) ${START_SCALE}`\n  }, {\n    offset: 0.6,\n    opacity: 0\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: `translate3d(${END_TRANSLATE_X}, ${END_TRANSLATE_Y}, 0) ${END_SCALE}`\n  }];\n  const KEYFRAMES = backDirection ? BACKWARDS_KEYFRAMES : FORWARDS_KEYFRAMES;\n  const clonedTitleEl = getClonedElement('ion-title');\n  const clonedLargeTitleAnimation = createAnimation();\n  clonedTitleEl.innerText = largeTitleEl.innerText;\n  clonedTitleEl.size = largeTitleEl.size;\n  clonedTitleEl.color = largeTitleEl.color;\n  clonedLargeTitleAnimation.addElement(clonedTitleEl);\n  clonedLargeTitleAnimation.beforeStyles({\n    'transform-origin': `${ORIGIN_X} top`,\n    /**\n     * Since font size changes will cause\n     * the dimension of the large title to change\n     * we need to set the cloned title height\n     * equal to that of the original large title height.\n     */\n    height: `${largeTitleBox.height}px`,\n    display: '',\n    position: 'relative',\n    [ORIGIN_X]: TITLE_START_OFFSET\n  }).beforeAddWrite(() => {\n    largeTitleEl.style.setProperty('opacity', '0');\n  }).afterAddWrite(() => {\n    largeTitleEl.style.setProperty('opacity', '');\n    clonedTitleEl.style.setProperty('display', 'none');\n  }).keyframes(KEYFRAMES);\n  rootAnimation.addAnimation(clonedLargeTitleAnimation);\n};\nconst iosTransitionAnimation = (navEl, opts) => {\n  var _a;\n  try {\n    const EASING = 'cubic-bezier(0.32,0.72,0,1)';\n    const OPACITY = 'opacity';\n    const TRANSFORM = 'transform';\n    const CENTER = '0%';\n    const OFF_OPACITY = 0.8;\n    const isRTL = navEl.ownerDocument.dir === 'rtl';\n    const OFF_RIGHT = isRTL ? '-99.5%' : '99.5%';\n    const OFF_LEFT = isRTL ? '33%' : '-33%';\n    const enteringEl = opts.enteringEl;\n    const leavingEl = opts.leavingEl;\n    const backDirection = opts.direction === 'back';\n    const contentEl = enteringEl.querySelector(':scope > ion-content');\n    const headerEls = enteringEl.querySelectorAll(':scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *');\n    const enteringToolBarEls = enteringEl.querySelectorAll(':scope > ion-header > ion-toolbar');\n    const rootAnimation = createAnimation();\n    const enteringContentAnimation = createAnimation();\n    rootAnimation.addElement(enteringEl).duration(((_a = opts.duration) !== null && _a !== void 0 ? _a : 0) || DURATION).easing(opts.easing || EASING).fill('both').beforeRemoveClass('ion-page-invisible');\n    // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n    if (leavingEl && navEl !== null && navEl !== undefined) {\n      const navDecorAnimation = createAnimation();\n      navDecorAnimation.addElement(navEl);\n      rootAnimation.addAnimation(navDecorAnimation);\n    }\n    if (!contentEl && enteringToolBarEls.length === 0 && headerEls.length === 0) {\n      enteringContentAnimation.addElement(enteringEl.querySelector(':scope > .ion-page, :scope > ion-nav, :scope > ion-tabs')); // REVIEW\n    } else {\n      enteringContentAnimation.addElement(contentEl); // REVIEW\n      enteringContentAnimation.addElement(headerEls);\n    }\n    rootAnimation.addAnimation(enteringContentAnimation);\n    if (backDirection) {\n      enteringContentAnimation.beforeClearStyles([OPACITY]).fromTo('transform', `translateX(${OFF_LEFT})`, `translateX(${CENTER})`).fromTo(OPACITY, OFF_OPACITY, 1);\n    } else {\n      // entering content, forward direction\n      enteringContentAnimation.beforeClearStyles([OPACITY]).fromTo('transform', `translateX(${OFF_RIGHT})`, `translateX(${CENTER})`);\n    }\n    if (contentEl) {\n      const enteringTransitionEffectEl = shadow(contentEl).querySelector('.transition-effect');\n      if (enteringTransitionEffectEl) {\n        const enteringTransitionCoverEl = enteringTransitionEffectEl.querySelector('.transition-cover');\n        const enteringTransitionShadowEl = enteringTransitionEffectEl.querySelector('.transition-shadow');\n        const enteringTransitionEffect = createAnimation();\n        const enteringTransitionCover = createAnimation();\n        const enteringTransitionShadow = createAnimation();\n        enteringTransitionEffect.addElement(enteringTransitionEffectEl).beforeStyles({\n          opacity: '1',\n          display: 'block'\n        }).afterStyles({\n          opacity: '',\n          display: ''\n        });\n        enteringTransitionCover.addElement(enteringTransitionCoverEl) // REVIEW\n        .beforeClearStyles([OPACITY]).fromTo(OPACITY, 0, 0.1);\n        enteringTransitionShadow.addElement(enteringTransitionShadowEl) // REVIEW\n        .beforeClearStyles([OPACITY]).fromTo(OPACITY, 0.03, 0.7);\n        enteringTransitionEffect.addAnimation([enteringTransitionCover, enteringTransitionShadow]);\n        enteringContentAnimation.addAnimation([enteringTransitionEffect]);\n      }\n    }\n    const enteringContentHasLargeTitle = enteringEl.querySelector('ion-header.header-collapse-condense');\n    const {\n      forward,\n      backward\n    } = createLargeTitleTransition(rootAnimation, isRTL, backDirection, enteringEl, leavingEl);\n    enteringToolBarEls.forEach(enteringToolBarEl => {\n      const enteringToolBar = createAnimation();\n      enteringToolBar.addElement(enteringToolBarEl);\n      rootAnimation.addAnimation(enteringToolBar);\n      const enteringTitle = createAnimation();\n      enteringTitle.addElement(enteringToolBarEl.querySelector('ion-title')); // REVIEW\n      const enteringToolBarButtons = createAnimation();\n      const buttons = Array.from(enteringToolBarEl.querySelectorAll('ion-buttons,[menuToggle]'));\n      const parentHeader = enteringToolBarEl.closest('ion-header');\n      const inactiveHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.classList.contains('header-collapse-condense-inactive');\n      let buttonsToAnimate;\n      if (backDirection) {\n        buttonsToAnimate = buttons.filter(button => {\n          const isCollapseButton = button.classList.contains('buttons-collapse');\n          return isCollapseButton && !inactiveHeader || !isCollapseButton;\n        });\n      } else {\n        buttonsToAnimate = buttons.filter(button => !button.classList.contains('buttons-collapse'));\n      }\n      enteringToolBarButtons.addElement(buttonsToAnimate);\n      const enteringToolBarItems = createAnimation();\n      enteringToolBarItems.addElement(enteringToolBarEl.querySelectorAll(':scope > *:not(ion-title):not(ion-buttons):not([menuToggle])'));\n      const enteringToolBarBg = createAnimation();\n      enteringToolBarBg.addElement(shadow(enteringToolBarEl).querySelector('.toolbar-background')); // REVIEW\n      const enteringBackButton = createAnimation();\n      const backButtonEl = enteringToolBarEl.querySelector('ion-back-button');\n      if (backButtonEl) {\n        enteringBackButton.addElement(backButtonEl);\n      }\n      enteringToolBar.addAnimation([enteringTitle, enteringToolBarButtons, enteringToolBarItems, enteringToolBarBg, enteringBackButton]);\n      enteringToolBarButtons.fromTo(OPACITY, 0.01, 1);\n      enteringToolBarItems.fromTo(OPACITY, 0.01, 1);\n      if (backDirection) {\n        if (!inactiveHeader) {\n          enteringTitle.fromTo('transform', `translateX(${OFF_LEFT})`, `translateX(${CENTER})`).fromTo(OPACITY, 0.01, 1);\n        }\n        enteringToolBarItems.fromTo('transform', `translateX(${OFF_LEFT})`, `translateX(${CENTER})`);\n        // back direction, entering page has a back button\n        enteringBackButton.fromTo(OPACITY, 0.01, 1);\n      } else {\n        // entering toolbar, forward direction\n        if (!enteringContentHasLargeTitle) {\n          enteringTitle.fromTo('transform', `translateX(${OFF_RIGHT})`, `translateX(${CENTER})`).fromTo(OPACITY, 0.01, 1);\n        }\n        enteringToolBarItems.fromTo('transform', `translateX(${OFF_RIGHT})`, `translateX(${CENTER})`);\n        enteringToolBarBg.beforeClearStyles([OPACITY, 'transform']);\n        const translucentHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.translucent;\n        if (!translucentHeader) {\n          enteringToolBarBg.fromTo(OPACITY, 0.01, 'var(--opacity)');\n        } else {\n          enteringToolBarBg.fromTo('transform', isRTL ? 'translateX(-100%)' : 'translateX(100%)', 'translateX(0px)');\n        }\n        // forward direction, entering page has a back button\n        if (!forward) {\n          enteringBackButton.fromTo(OPACITY, 0.01, 1);\n        }\n        if (backButtonEl && !forward) {\n          const enteringBackBtnText = createAnimation();\n          enteringBackBtnText.addElement(shadow(backButtonEl).querySelector('.button-text')) // REVIEW\n          .fromTo(`transform`, isRTL ? 'translateX(-100px)' : 'translateX(100px)', 'translateX(0px)');\n          enteringToolBar.addAnimation(enteringBackBtnText);\n        }\n      }\n    });\n    // setup leaving view\n    if (leavingEl) {\n      const leavingContent = createAnimation();\n      const leavingContentEl = leavingEl.querySelector(':scope > ion-content');\n      const leavingToolBarEls = leavingEl.querySelectorAll(':scope > ion-header > ion-toolbar');\n      const leavingHeaderEls = leavingEl.querySelectorAll(':scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *');\n      if (!leavingContentEl && leavingToolBarEls.length === 0 && leavingHeaderEls.length === 0) {\n        leavingContent.addElement(leavingEl.querySelector(':scope > .ion-page, :scope > ion-nav, :scope > ion-tabs')); // REVIEW\n      } else {\n        leavingContent.addElement(leavingContentEl); // REVIEW\n        leavingContent.addElement(leavingHeaderEls);\n      }\n      rootAnimation.addAnimation(leavingContent);\n      if (backDirection) {\n        // leaving content, back direction\n        leavingContent.beforeClearStyles([OPACITY]).fromTo('transform', `translateX(${CENTER})`, isRTL ? 'translateX(-100%)' : 'translateX(100%)');\n        const leavingPage = getIonPageElement(leavingEl);\n        rootAnimation.afterAddWrite(() => {\n          if (rootAnimation.getDirection() === 'normal') {\n            leavingPage.style.setProperty('display', 'none');\n          }\n        });\n      } else {\n        // leaving content, forward direction\n        leavingContent.fromTo('transform', `translateX(${CENTER})`, `translateX(${OFF_LEFT})`).fromTo(OPACITY, 1, OFF_OPACITY);\n      }\n      if (leavingContentEl) {\n        const leavingTransitionEffectEl = shadow(leavingContentEl).querySelector('.transition-effect');\n        if (leavingTransitionEffectEl) {\n          const leavingTransitionCoverEl = leavingTransitionEffectEl.querySelector('.transition-cover');\n          const leavingTransitionShadowEl = leavingTransitionEffectEl.querySelector('.transition-shadow');\n          const leavingTransitionEffect = createAnimation();\n          const leavingTransitionCover = createAnimation();\n          const leavingTransitionShadow = createAnimation();\n          leavingTransitionEffect.addElement(leavingTransitionEffectEl).beforeStyles({\n            opacity: '1',\n            display: 'block'\n          }).afterStyles({\n            opacity: '',\n            display: ''\n          });\n          leavingTransitionCover.addElement(leavingTransitionCoverEl) // REVIEW\n          .beforeClearStyles([OPACITY]).fromTo(OPACITY, 0.1, 0);\n          leavingTransitionShadow.addElement(leavingTransitionShadowEl) // REVIEW\n          .beforeClearStyles([OPACITY]).fromTo(OPACITY, 0.7, 0.03);\n          leavingTransitionEffect.addAnimation([leavingTransitionCover, leavingTransitionShadow]);\n          leavingContent.addAnimation([leavingTransitionEffect]);\n        }\n      }\n      leavingToolBarEls.forEach(leavingToolBarEl => {\n        const leavingToolBar = createAnimation();\n        leavingToolBar.addElement(leavingToolBarEl);\n        const leavingTitle = createAnimation();\n        leavingTitle.addElement(leavingToolBarEl.querySelector('ion-title')); // REVIEW\n        const leavingToolBarButtons = createAnimation();\n        const buttons = leavingToolBarEl.querySelectorAll('ion-buttons,[menuToggle]');\n        const parentHeader = leavingToolBarEl.closest('ion-header');\n        const inactiveHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.classList.contains('header-collapse-condense-inactive');\n        const buttonsToAnimate = Array.from(buttons).filter(button => {\n          const isCollapseButton = button.classList.contains('buttons-collapse');\n          return isCollapseButton && !inactiveHeader || !isCollapseButton;\n        });\n        leavingToolBarButtons.addElement(buttonsToAnimate);\n        const leavingToolBarItems = createAnimation();\n        const leavingToolBarItemEls = leavingToolBarEl.querySelectorAll(':scope > *:not(ion-title):not(ion-buttons):not([menuToggle])');\n        if (leavingToolBarItemEls.length > 0) {\n          leavingToolBarItems.addElement(leavingToolBarItemEls);\n        }\n        const leavingToolBarBg = createAnimation();\n        leavingToolBarBg.addElement(shadow(leavingToolBarEl).querySelector('.toolbar-background')); // REVIEW\n        const leavingBackButton = createAnimation();\n        const backButtonEl = leavingToolBarEl.querySelector('ion-back-button');\n        if (backButtonEl) {\n          leavingBackButton.addElement(backButtonEl);\n        }\n        leavingToolBar.addAnimation([leavingTitle, leavingToolBarButtons, leavingToolBarItems, leavingBackButton, leavingToolBarBg]);\n        rootAnimation.addAnimation(leavingToolBar);\n        // fade out leaving toolbar items\n        leavingBackButton.fromTo(OPACITY, 0.99, 0);\n        leavingToolBarButtons.fromTo(OPACITY, 0.99, 0);\n        leavingToolBarItems.fromTo(OPACITY, 0.99, 0);\n        if (backDirection) {\n          if (!inactiveHeader) {\n            // leaving toolbar, back direction\n            leavingTitle.fromTo('transform', `translateX(${CENTER})`, isRTL ? 'translateX(-100%)' : 'translateX(100%)').fromTo(OPACITY, 0.99, 0);\n          }\n          leavingToolBarItems.fromTo('transform', `translateX(${CENTER})`, isRTL ? 'translateX(-100%)' : 'translateX(100%)');\n          leavingToolBarBg.beforeClearStyles([OPACITY, 'transform']);\n          // leaving toolbar, back direction, and there's no entering toolbar\n          // should just slide out, no fading out\n          const translucentHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.translucent;\n          if (!translucentHeader) {\n            leavingToolBarBg.fromTo(OPACITY, 'var(--opacity)', 0);\n          } else {\n            leavingToolBarBg.fromTo('transform', 'translateX(0px)', isRTL ? 'translateX(-100%)' : 'translateX(100%)');\n          }\n          if (backButtonEl && !backward) {\n            const leavingBackBtnText = createAnimation();\n            leavingBackBtnText.addElement(shadow(backButtonEl).querySelector('.button-text')) // REVIEW\n            .fromTo('transform', `translateX(${CENTER})`, `translateX(${(isRTL ? -124 : 124) + 'px'})`);\n            leavingToolBar.addAnimation(leavingBackBtnText);\n          }\n        } else {\n          // leaving toolbar, forward direction\n          if (!inactiveHeader) {\n            leavingTitle.fromTo('transform', `translateX(${CENTER})`, `translateX(${OFF_LEFT})`).fromTo(OPACITY, 0.99, 0).afterClearStyles([TRANSFORM, OPACITY]);\n          }\n          leavingToolBarItems.fromTo('transform', `translateX(${CENTER})`, `translateX(${OFF_LEFT})`).afterClearStyles([TRANSFORM, OPACITY]);\n          leavingBackButton.afterClearStyles([OPACITY]);\n          leavingTitle.afterClearStyles([OPACITY]);\n          leavingToolBarButtons.afterClearStyles([OPACITY]);\n        }\n      });\n    }\n    return rootAnimation;\n  } catch (err) {\n    throw err;\n  }\n};\n/**\n * The scale of the back button during the animation\n * is computed based on the scale of the large title\n * and vice versa. However, we need to account for slight\n * variations in the size of the large title due to\n * padding and font weight. This value should be used to subtract\n * a small amount from the large title height when computing scales\n * to get more accurate scale results.\n */\nconst LARGE_TITLE_SIZE_OFFSET = 10;\nexport { iosTransitionAnimation, shadow };"], "mappings": ";;;;;;;;AAQA,IAAM,WAAW;AAEjB,IAAM,mBAAmB,aAAW;AAClC,SAAO,SAAS,cAAc,GAAG,OAAO,qBAAqB;AAC/D;AACA,IAAM,SAAS,QAAM;AACnB,SAAO,GAAG,cAAc;AAC1B;AACA,IAAM,gBAAgB,WAAS;AAC7B,QAAM,OAAO,MAAM,YAAY,aAAa,QAAQ,MAAM,cAAc,UAAU;AAClF,QAAM,QAAQ;AACd,MAAI,QAAQ,MAAM;AAChB,UAAM,YAAY,KAAK,cAAc,2DAA2D;AAChG,WAAO,aAAa,OAAO,UAAU,cAAc,KAAK,IAAI;AAAA,EAC9D;AACA,SAAO,MAAM,cAAc,KAAK;AAClC;AACA,IAAM,gBAAgB,CAAC,OAAO,kBAAkB;AAC9C,QAAM,OAAO,MAAM,YAAY,aAAa,QAAQ,MAAM,cAAc,UAAU;AAClF,MAAI,cAAc,CAAC;AACnB,MAAI,QAAQ,MAAM;AAChB,UAAM,YAAY,KAAK,cAAc,2DAA2D;AAChG,QAAI,aAAa,MAAM;AACrB,oBAAc,UAAU,iBAAiB,aAAa;AAAA,IACxD;AAAA,EACF,OAAO;AACL,kBAAc,MAAM,iBAAiB,aAAa;AAAA,EACpD;AACA,aAAW,WAAW,aAAa;AACjC,UAAM,eAAe,QAAQ,QAAQ,YAAY;AACjD,UAAM,eAAe,gBAAgB,CAAC,aAAa,UAAU,SAAS,mCAAmC;AACzG,UAAM,aAAa,QAAQ,cAAc,iBAAiB;AAC1D,UAAM,kBAAkB,QAAQ,UAAU,SAAS,kBAAkB;AACrE,UAAM,YAAY,QAAQ,SAAS,WAAW,QAAQ,SAAS;AAC/D,QAAI,eAAe,QAAQ,cAAc,mBAAmB,gBAAgB,iBAAiB,CAAC,kBAAkB;AAC9G,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,6BAA6B,CAAC,eAAe,KAAK,eAAe,YAAY,cAAc;AAC/F,QAAM,qBAAqB,cAAc,YAAY,aAAa;AAClE,QAAM,oBAAoB,cAAc,SAAS;AACjD,QAAM,qBAAqB,cAAc,UAAU;AACnD,QAAM,oBAAoB,cAAc,WAAW,aAAa;AAChE,QAAM,yBAAyB,uBAAuB,QAAQ,sBAAsB,QAAQ,CAAC;AAC7F,QAAM,0BAA0B,uBAAuB,QAAQ,sBAAsB,QAAQ;AAC7F,MAAI,wBAAwB;AAC1B,UAAM,uBAAuB,kBAAkB,sBAAsB;AACrE,UAAM,wBAAwB,mBAAmB,sBAAsB;AACvE,UAAM,2BAA2B,OAAO,kBAAkB,EAAE,cAAc,cAAc;AAExF,UAAM,4BAA4B,6BAA6B,QAAQ,6BAA6B,SAAS,SAAS,yBAAyB,sBAAsB;AACrK,UAAM,0BAA0B,OAAO,iBAAiB,EAAE,cAAc,gBAAgB;AACxF,UAAM,2BAA2B,wBAAwB,sBAAsB;AAC/E,sBAAkB,eAAe,KAAK,eAAe,mBAAmB,sBAAsB,0BAA0B,uBAAuB,0BAA0B,yBAAyB;AAClM,sBAAkB,eAAe,KAAK,eAAe,oBAAoB,uBAAuB,0BAA0B,2BAA2B,mBAAmB,wBAAwB;AAAA,EAClM,WAAW,yBAAyB;AAClC,UAAM,wBAAwB,mBAAmB,sBAAsB;AACvE,UAAM,uBAAuB,kBAAkB,sBAAsB;AACrE,UAAM,0BAA0B,OAAO,iBAAiB,EAAE,cAAc,cAAc;AAEtF,UAAM,2BAA2B,4BAA4B,QAAQ,4BAA4B,SAAS,SAAS,wBAAwB,sBAAsB;AACjK,UAAM,2BAA2B,OAAO,kBAAkB,EAAE,cAAc,gBAAgB;AAC1F,UAAM,4BAA4B,yBAAyB,sBAAsB;AACjF,sBAAkB,eAAe,KAAK,eAAe,oBAAoB,uBAAuB,2BAA2B,sBAAsB,yBAAyB,wBAAwB;AAClM,sBAAkB,eAAe,KAAK,eAAe,mBAAmB,sBAAsB,yBAAyB,0BAA0B,oBAAoB,yBAAyB;AAAA,EAChM;AACA,SAAO;AAAA,IACL,SAAS;AAAA,IACT,UAAU;AAAA,EACZ;AACF;AACA,IAAM,oBAAoB,CAAC,eAAe,KAAK,eAAe,cAAc,eAAe,kBAAkB,mBAAmB,cAAc,sBAAsB;AAClK,MAAI,IAAI;AACR,QAAM,2BAA2B,MAAM,eAAe,cAAc,QAAQ,CAAC,QAAQ,GAAG,cAAc,OAAO,CAAC;AAC9G,QAAM,gBAAgB,MAAM,UAAU;AACtC,QAAM,gBAAgB,MAAM,SAAS;AACrC,QAAM,qBAAqB,MAAM,UAAU;AAC3C,MAAI,cAAc;AAClB,MAAI,eAAe;AACnB,MAAI,mBAAmB,SAAS,YAAY;AAC5C,QAAM,iBAAiB;AACvB,MAAI,oBAAoB,mBAAmB;AAQzC,UAAM,+BAA+B,KAAK,iBAAiB,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,SAAS,KAAK,aAAa,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAC1M,kBAAc,kBAAkB,QAAQ,kBAAkB;AAK1D,oBAAgB,kBAAkB,SAAS,2BAA2B,kBAAkB;AAKxF,uBAAmB,6BAA6B,SAAS,WAAW,KAAK,YAAY,MAAM,SAAS,YAAY;AAAA,EAClH;AACA,QAAM,mBAAmB,OAAO,YAAY,EAAE,cAAc,UAAU;AACtE,QAAM,oBAAoB,iBAAiB,sBAAsB;AAOjE,QAAM,8BAA8B,MAAM,GAAG,kBAAkB,QAAQ,KAAK,kBAAkB,QAAQ,cAAc,MAAM,OAAO,GAAG,cAAc,OAAO,kBAAkB,QAAQ,CAAC;AACpL,QAAM,4BAA4B,MAAM,IAAI,OAAO,aAAa,cAAc,KAAK,OAAO,GAAG,cAAc,IAAI;AAO/G,QAAM,8BAA8B,GAAG,kBAAkB,GAAG;AAM5D,QAAM,4BAA4B,GAAG,cAAc,GAAG;AAQtD,QAAM,8BAA8B,CAAC;AAAA,IACnC,QAAQ;AAAA,IACR,WAAW,eAAe,2BAA2B,KAAK,2BAA2B;AAAA,EACvF,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,WAAW,eAAe,yBAAyB,KAAK,yBAAyB;AAAA,EACnF,CAAC;AACD,QAAM,+BAA+B,CAAC;AAAA,IACpC,QAAQ;AAAA,IACR,WAAW,eAAe,yBAAyB,KAAK,yBAAyB;AAAA,EACnF,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,WAAW,eAAe,2BAA2B,KAAK,2BAA2B;AAAA,EACvF,CAAC;AACD,QAAM,sBAAsB,gBAAgB,+BAA+B;AAQ3E,QAAM,yBAAyB,CAAC;AAAA,IAC9B,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,CAAC;AACD,QAAM,0BAA0B,CAAC;AAAA,IAC/B,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,CAAC;AACD,QAAM,iBAAiB,gBAAgB,0BAA0B;AAOjE,QAAM,yBAAyB,CAAC;AAAA,IAC9B,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,CAAC;AACD,QAAM,0BAA0B,CAAC;AAAA,IAC/B,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,CAAC;AACD,QAAM,iBAAiB,gBAAgB,0BAA0B;AACjE,QAAM,kCAAkC,gBAAgB;AACxD,QAAM,kCAAkC,gBAAgB;AACxD,QAAM,8BAA8B,gBAAgB;AACpD,QAAM,qBAAqB,iBAAiB,iBAAiB;AAC7D,QAAM,yBAAyB,OAAO,kBAAkB,EAAE,cAAc,cAAc;AACtF,QAAM,yBAAyB,OAAO,kBAAkB,EAAE,cAAc,UAAU;AAClF,qBAAmB,OAAO,aAAa;AACvC,qBAAmB,OAAO,aAAa;AACvC,qBAAmB,OAAO,aAAa;AACvC,qBAAmB,QAAQ,aAAa;AACxC,qBAAmB,WAAW,aAAa;AAC3C,qBAAmB,MAAM,YAAY,WAAW,OAAO;AACvD,qBAAmB,MAAM,YAAY,YAAY,OAAO;AACxD,kCAAgC,WAAW,sBAAsB;AACjE,kCAAgC,WAAW,sBAAsB;AACjE,8BAA4B,WAAW,kBAAkB;AACzD,8BAA4B,aAAa;AAAA,IACvC,UAAU;AAAA,IACV,KAAK;AAAA,IACL,CAAC,kBAAkB,GAAG;AAAA,EACxB,CAAC,EAKG,eAAe,MAAM;AACvB,iBAAa,MAAM,YAAY,WAAW,MAAM;AAChD,uBAAmB,MAAM,YAAY,eAAe,wBAAwB;AAAA,EAC9E,CAAC,EAAE,cAAc,MAAM;AACrB,iBAAa,MAAM,YAAY,WAAW,EAAE;AAC5C,uBAAmB,MAAM,YAAY,WAAW,MAAM;AACtD,uBAAmB,MAAM,eAAe,aAAa;AAAA,EACvD,CAAC,EAAE,UAAU,mBAAmB;AAChC,kCAAgC,aAAa;AAAA,IAC3C,oBAAoB,GAAG,aAAa;AAAA,EACtC,CAAC,EAAE,UAAU,cAAc;AAC3B,kCAAgC,aAAa;AAAA,IAC3C,oBAAoB,GAAG,aAAa;AAAA,EACtC,CAAC,EAAE,UAAU,cAAc;AAC3B,gBAAc,aAAa,CAAC,iCAAiC,iCAAiC,2BAA2B,CAAC;AAC5H;AACA,IAAM,oBAAoB,CAAC,eAAe,KAAK,eAAe,cAAc,eAAe,mBAAmB,eAAe,kBAAkB,sBAAsB;AACnK,MAAI,IAAI;AAIR,QAAM,WAAW,MAAM,UAAU;AACjC,QAAM,qBAAqB,MAAM,eAAe,cAAc,KAAK,QAAQ,GAAG,cAAc,IAAI;AAMhG,QAAM,oBAAoB;AAC1B,QAAM,oBAAoB,GAAG,cAAc,GAAG;AAM9C,QAAM,iCAAiC;AACvC,MAAI,kBAAkB,MAAM,IAAI,OAAO,aAAa,cAAc,QAAQ,8BAA8B,OAAO,GAAG,cAAc,IAAI,8BAA8B;AAIlK,MAAI,eAAe;AAInB,QAAM,cAAc;AAMpB,MAAI,YAAY,SAAS,YAAY;AAErC,MAAI,oBAAoB,mBAAmB;AAQzC,sBAAkB,MAAM,IAAI,OAAO,aAAa,kBAAkB,QAAQ,8BAA8B,OAAO,GAAG,kBAAkB,IAAI,8BAA8B;AAiBtK,UAAM,+BAA+B,KAAK,iBAAiB,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,SAAS,KAAK,aAAa,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAC1M,UAAM,cAAc,kBAAkB,QAAQ,kBAAkB;AAChE,mBAAe,kBAAkB,UAAU,kBAAkB,SAAS;AAKtE,gBAAY,6BAA6B,SAAS,WAAW,KAAK,YAAY,MAAM,SAAS,YAAY;AAAA,EAC3G;AAKA,QAAM,qBAAqB,cAAc,MAAM,cAAc,SAAS;AACtE,QAAM,gBAAgB,cAAc,SAAS,eAAe;AAC5D,QAAM,kBAAkB,GAAG,qBAAqB,aAAa;AAC7D,QAAM,sBAAsB,CAAC;AAAA,IAC3B,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW,eAAe,eAAe,KAAK,eAAe,QAAQ,SAAS;AAAA,EAChF,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW,eAAe,iBAAiB,KAAK,iBAAiB,QAAQ,WAAW;AAAA,EACtF,CAAC;AACD,QAAM,qBAAqB,CAAC;AAAA,IAC1B,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW,eAAe,iBAAiB,KAAK,iBAAiB,QAAQ,WAAW;AAAA,EACtF,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW,eAAe,eAAe,KAAK,eAAe,QAAQ,SAAS;AAAA,EAChF,CAAC;AACD,QAAM,YAAY,gBAAgB,sBAAsB;AACxD,QAAM,gBAAgB,iBAAiB,WAAW;AAClD,QAAM,4BAA4B,gBAAgB;AAClD,gBAAc,YAAY,aAAa;AACvC,gBAAc,OAAO,aAAa;AAClC,gBAAc,QAAQ,aAAa;AACnC,4BAA0B,WAAW,aAAa;AAClD,4BAA0B,aAAa;AAAA,IACrC,oBAAoB,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAO/B,QAAQ,GAAG,cAAc,MAAM;AAAA,IAC/B,SAAS;AAAA,IACT,UAAU;AAAA,IACV,CAAC,QAAQ,GAAG;AAAA,EACd,CAAC,EAAE,eAAe,MAAM;AACtB,iBAAa,MAAM,YAAY,WAAW,GAAG;AAAA,EAC/C,CAAC,EAAE,cAAc,MAAM;AACrB,iBAAa,MAAM,YAAY,WAAW,EAAE;AAC5C,kBAAc,MAAM,YAAY,WAAW,MAAM;AAAA,EACnD,CAAC,EAAE,UAAU,SAAS;AACtB,gBAAc,aAAa,yBAAyB;AACtD;AACA,IAAM,yBAAyB,CAAC,OAAO,SAAS;AAC9C,MAAI;AACJ,MAAI;AACF,UAAM,SAAS;AACf,UAAM,UAAU;AAChB,UAAM,YAAY;AAClB,UAAM,SAAS;AACf,UAAM,cAAc;AACpB,UAAM,QAAQ,MAAM,cAAc,QAAQ;AAC1C,UAAM,YAAY,QAAQ,WAAW;AACrC,UAAM,WAAW,QAAQ,QAAQ;AACjC,UAAM,aAAa,KAAK;AACxB,UAAM,YAAY,KAAK;AACvB,UAAM,gBAAgB,KAAK,cAAc;AACzC,UAAM,YAAY,WAAW,cAAc,sBAAsB;AACjE,UAAM,YAAY,WAAW,iBAAiB,mEAAmE;AACjH,UAAM,qBAAqB,WAAW,iBAAiB,mCAAmC;AAC1F,UAAM,gBAAgB,gBAAgB;AACtC,UAAM,2BAA2B,gBAAgB;AACjD,kBAAc,WAAW,UAAU,EAAE,WAAW,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,KAAK,MAAM,QAAQ,EAAE,OAAO,KAAK,UAAU,MAAM,EAAE,KAAK,MAAM,EAAE,kBAAkB,oBAAoB;AAEtM,QAAI,aAAa,UAAU,QAAQ,UAAU,QAAW;AACtD,YAAM,oBAAoB,gBAAgB;AAC1C,wBAAkB,WAAW,KAAK;AAClC,oBAAc,aAAa,iBAAiB;AAAA,IAC9C;AACA,QAAI,CAAC,aAAa,mBAAmB,WAAW,KAAK,UAAU,WAAW,GAAG;AAC3E,+BAAyB,WAAW,WAAW,cAAc,yDAAyD,CAAC;AAAA,IACzH,OAAO;AACL,+BAAyB,WAAW,SAAS;AAC7C,+BAAyB,WAAW,SAAS;AAAA,IAC/C;AACA,kBAAc,aAAa,wBAAwB;AACnD,QAAI,eAAe;AACjB,+BAAyB,kBAAkB,CAAC,OAAO,CAAC,EAAE,OAAO,aAAa,cAAc,QAAQ,KAAK,cAAc,MAAM,GAAG,EAAE,OAAO,SAAS,aAAa,CAAC;AAAA,IAC9J,OAAO;AAEL,+BAAyB,kBAAkB,CAAC,OAAO,CAAC,EAAE,OAAO,aAAa,cAAc,SAAS,KAAK,cAAc,MAAM,GAAG;AAAA,IAC/H;AACA,QAAI,WAAW;AACb,YAAM,6BAA6B,OAAO,SAAS,EAAE,cAAc,oBAAoB;AACvF,UAAI,4BAA4B;AAC9B,cAAM,4BAA4B,2BAA2B,cAAc,mBAAmB;AAC9F,cAAM,6BAA6B,2BAA2B,cAAc,oBAAoB;AAChG,cAAM,2BAA2B,gBAAgB;AACjD,cAAM,0BAA0B,gBAAgB;AAChD,cAAM,2BAA2B,gBAAgB;AACjD,iCAAyB,WAAW,0BAA0B,EAAE,aAAa;AAAA,UAC3E,SAAS;AAAA,UACT,SAAS;AAAA,QACX,CAAC,EAAE,YAAY;AAAA,UACb,SAAS;AAAA,UACT,SAAS;AAAA,QACX,CAAC;AACD,gCAAwB,WAAW,yBAAyB,EAC3D,kBAAkB,CAAC,OAAO,CAAC,EAAE,OAAO,SAAS,GAAG,GAAG;AACpD,iCAAyB,WAAW,0BAA0B,EAC7D,kBAAkB,CAAC,OAAO,CAAC,EAAE,OAAO,SAAS,MAAM,GAAG;AACvD,iCAAyB,aAAa,CAAC,yBAAyB,wBAAwB,CAAC;AACzF,iCAAyB,aAAa,CAAC,wBAAwB,CAAC;AAAA,MAClE;AAAA,IACF;AACA,UAAM,+BAA+B,WAAW,cAAc,qCAAqC;AACnG,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,2BAA2B,eAAe,OAAO,eAAe,YAAY,SAAS;AACzF,uBAAmB,QAAQ,uBAAqB;AAC9C,YAAM,kBAAkB,gBAAgB;AACxC,sBAAgB,WAAW,iBAAiB;AAC5C,oBAAc,aAAa,eAAe;AAC1C,YAAM,gBAAgB,gBAAgB;AACtC,oBAAc,WAAW,kBAAkB,cAAc,WAAW,CAAC;AACrE,YAAM,yBAAyB,gBAAgB;AAC/C,YAAM,UAAU,MAAM,KAAK,kBAAkB,iBAAiB,0BAA0B,CAAC;AACzF,YAAM,eAAe,kBAAkB,QAAQ,YAAY;AAC3D,YAAM,iBAAiB,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,UAAU,SAAS,mCAAmC;AACtJ,UAAI;AACJ,UAAI,eAAe;AACjB,2BAAmB,QAAQ,OAAO,YAAU;AAC1C,gBAAM,mBAAmB,OAAO,UAAU,SAAS,kBAAkB;AACrE,iBAAO,oBAAoB,CAAC,kBAAkB,CAAC;AAAA,QACjD,CAAC;AAAA,MACH,OAAO;AACL,2BAAmB,QAAQ,OAAO,YAAU,CAAC,OAAO,UAAU,SAAS,kBAAkB,CAAC;AAAA,MAC5F;AACA,6BAAuB,WAAW,gBAAgB;AAClD,YAAM,uBAAuB,gBAAgB;AAC7C,2BAAqB,WAAW,kBAAkB,iBAAiB,8DAA8D,CAAC;AAClI,YAAM,oBAAoB,gBAAgB;AAC1C,wBAAkB,WAAW,OAAO,iBAAiB,EAAE,cAAc,qBAAqB,CAAC;AAC3F,YAAM,qBAAqB,gBAAgB;AAC3C,YAAM,eAAe,kBAAkB,cAAc,iBAAiB;AACtE,UAAI,cAAc;AAChB,2BAAmB,WAAW,YAAY;AAAA,MAC5C;AACA,sBAAgB,aAAa,CAAC,eAAe,wBAAwB,sBAAsB,mBAAmB,kBAAkB,CAAC;AACjI,6BAAuB,OAAO,SAAS,MAAM,CAAC;AAC9C,2BAAqB,OAAO,SAAS,MAAM,CAAC;AAC5C,UAAI,eAAe;AACjB,YAAI,CAAC,gBAAgB;AACnB,wBAAc,OAAO,aAAa,cAAc,QAAQ,KAAK,cAAc,MAAM,GAAG,EAAE,OAAO,SAAS,MAAM,CAAC;AAAA,QAC/G;AACA,6BAAqB,OAAO,aAAa,cAAc,QAAQ,KAAK,cAAc,MAAM,GAAG;AAE3F,2BAAmB,OAAO,SAAS,MAAM,CAAC;AAAA,MAC5C,OAAO;AAEL,YAAI,CAAC,8BAA8B;AACjC,wBAAc,OAAO,aAAa,cAAc,SAAS,KAAK,cAAc,MAAM,GAAG,EAAE,OAAO,SAAS,MAAM,CAAC;AAAA,QAChH;AACA,6BAAqB,OAAO,aAAa,cAAc,SAAS,KAAK,cAAc,MAAM,GAAG;AAC5F,0BAAkB,kBAAkB,CAAC,SAAS,WAAW,CAAC;AAC1D,cAAM,oBAAoB,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa;AACnG,YAAI,CAAC,mBAAmB;AACtB,4BAAkB,OAAO,SAAS,MAAM,gBAAgB;AAAA,QAC1D,OAAO;AACL,4BAAkB,OAAO,aAAa,QAAQ,sBAAsB,oBAAoB,iBAAiB;AAAA,QAC3G;AAEA,YAAI,CAAC,SAAS;AACZ,6BAAmB,OAAO,SAAS,MAAM,CAAC;AAAA,QAC5C;AACA,YAAI,gBAAgB,CAAC,SAAS;AAC5B,gBAAM,sBAAsB,gBAAgB;AAC5C,8BAAoB,WAAW,OAAO,YAAY,EAAE,cAAc,cAAc,CAAC,EAChF,OAAO,aAAa,QAAQ,uBAAuB,qBAAqB,iBAAiB;AAC1F,0BAAgB,aAAa,mBAAmB;AAAA,QAClD;AAAA,MACF;AAAA,IACF,CAAC;AAED,QAAI,WAAW;AACb,YAAM,iBAAiB,gBAAgB;AACvC,YAAM,mBAAmB,UAAU,cAAc,sBAAsB;AACvE,YAAM,oBAAoB,UAAU,iBAAiB,mCAAmC;AACxF,YAAM,mBAAmB,UAAU,iBAAiB,mEAAmE;AACvH,UAAI,CAAC,oBAAoB,kBAAkB,WAAW,KAAK,iBAAiB,WAAW,GAAG;AACxF,uBAAe,WAAW,UAAU,cAAc,yDAAyD,CAAC;AAAA,MAC9G,OAAO;AACL,uBAAe,WAAW,gBAAgB;AAC1C,uBAAe,WAAW,gBAAgB;AAAA,MAC5C;AACA,oBAAc,aAAa,cAAc;AACzC,UAAI,eAAe;AAEjB,uBAAe,kBAAkB,CAAC,OAAO,CAAC,EAAE,OAAO,aAAa,cAAc,MAAM,KAAK,QAAQ,sBAAsB,kBAAkB;AACzI,cAAM,cAAc,kBAAkB,SAAS;AAC/C,sBAAc,cAAc,MAAM;AAChC,cAAI,cAAc,aAAa,MAAM,UAAU;AAC7C,wBAAY,MAAM,YAAY,WAAW,MAAM;AAAA,UACjD;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AAEL,uBAAe,OAAO,aAAa,cAAc,MAAM,KAAK,cAAc,QAAQ,GAAG,EAAE,OAAO,SAAS,GAAG,WAAW;AAAA,MACvH;AACA,UAAI,kBAAkB;AACpB,cAAM,4BAA4B,OAAO,gBAAgB,EAAE,cAAc,oBAAoB;AAC7F,YAAI,2BAA2B;AAC7B,gBAAM,2BAA2B,0BAA0B,cAAc,mBAAmB;AAC5F,gBAAM,4BAA4B,0BAA0B,cAAc,oBAAoB;AAC9F,gBAAM,0BAA0B,gBAAgB;AAChD,gBAAM,yBAAyB,gBAAgB;AAC/C,gBAAM,0BAA0B,gBAAgB;AAChD,kCAAwB,WAAW,yBAAyB,EAAE,aAAa;AAAA,YACzE,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC,EAAE,YAAY;AAAA,YACb,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AACD,iCAAuB,WAAW,wBAAwB,EACzD,kBAAkB,CAAC,OAAO,CAAC,EAAE,OAAO,SAAS,KAAK,CAAC;AACpD,kCAAwB,WAAW,yBAAyB,EAC3D,kBAAkB,CAAC,OAAO,CAAC,EAAE,OAAO,SAAS,KAAK,IAAI;AACvD,kCAAwB,aAAa,CAAC,wBAAwB,uBAAuB,CAAC;AACtF,yBAAe,aAAa,CAAC,uBAAuB,CAAC;AAAA,QACvD;AAAA,MACF;AACA,wBAAkB,QAAQ,sBAAoB;AAC5C,cAAM,iBAAiB,gBAAgB;AACvC,uBAAe,WAAW,gBAAgB;AAC1C,cAAM,eAAe,gBAAgB;AACrC,qBAAa,WAAW,iBAAiB,cAAc,WAAW,CAAC;AACnE,cAAM,wBAAwB,gBAAgB;AAC9C,cAAM,UAAU,iBAAiB,iBAAiB,0BAA0B;AAC5E,cAAM,eAAe,iBAAiB,QAAQ,YAAY;AAC1D,cAAM,iBAAiB,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,UAAU,SAAS,mCAAmC;AACtJ,cAAM,mBAAmB,MAAM,KAAK,OAAO,EAAE,OAAO,YAAU;AAC5D,gBAAM,mBAAmB,OAAO,UAAU,SAAS,kBAAkB;AACrE,iBAAO,oBAAoB,CAAC,kBAAkB,CAAC;AAAA,QACjD,CAAC;AACD,8BAAsB,WAAW,gBAAgB;AACjD,cAAM,sBAAsB,gBAAgB;AAC5C,cAAM,wBAAwB,iBAAiB,iBAAiB,8DAA8D;AAC9H,YAAI,sBAAsB,SAAS,GAAG;AACpC,8BAAoB,WAAW,qBAAqB;AAAA,QACtD;AACA,cAAM,mBAAmB,gBAAgB;AACzC,yBAAiB,WAAW,OAAO,gBAAgB,EAAE,cAAc,qBAAqB,CAAC;AACzF,cAAM,oBAAoB,gBAAgB;AAC1C,cAAM,eAAe,iBAAiB,cAAc,iBAAiB;AACrE,YAAI,cAAc;AAChB,4BAAkB,WAAW,YAAY;AAAA,QAC3C;AACA,uBAAe,aAAa,CAAC,cAAc,uBAAuB,qBAAqB,mBAAmB,gBAAgB,CAAC;AAC3H,sBAAc,aAAa,cAAc;AAEzC,0BAAkB,OAAO,SAAS,MAAM,CAAC;AACzC,8BAAsB,OAAO,SAAS,MAAM,CAAC;AAC7C,4BAAoB,OAAO,SAAS,MAAM,CAAC;AAC3C,YAAI,eAAe;AACjB,cAAI,CAAC,gBAAgB;AAEnB,yBAAa,OAAO,aAAa,cAAc,MAAM,KAAK,QAAQ,sBAAsB,kBAAkB,EAAE,OAAO,SAAS,MAAM,CAAC;AAAA,UACrI;AACA,8BAAoB,OAAO,aAAa,cAAc,MAAM,KAAK,QAAQ,sBAAsB,kBAAkB;AACjH,2BAAiB,kBAAkB,CAAC,SAAS,WAAW,CAAC;AAGzD,gBAAM,oBAAoB,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa;AACnG,cAAI,CAAC,mBAAmB;AACtB,6BAAiB,OAAO,SAAS,kBAAkB,CAAC;AAAA,UACtD,OAAO;AACL,6BAAiB,OAAO,aAAa,mBAAmB,QAAQ,sBAAsB,kBAAkB;AAAA,UAC1G;AACA,cAAI,gBAAgB,CAAC,UAAU;AAC7B,kBAAM,qBAAqB,gBAAgB;AAC3C,+BAAmB,WAAW,OAAO,YAAY,EAAE,cAAc,cAAc,CAAC,EAC/E,OAAO,aAAa,cAAc,MAAM,KAAK,eAAe,QAAQ,OAAO,OAAO,IAAI,GAAG;AAC1F,2BAAe,aAAa,kBAAkB;AAAA,UAChD;AAAA,QACF,OAAO;AAEL,cAAI,CAAC,gBAAgB;AACnB,yBAAa,OAAO,aAAa,cAAc,MAAM,KAAK,cAAc,QAAQ,GAAG,EAAE,OAAO,SAAS,MAAM,CAAC,EAAE,iBAAiB,CAAC,WAAW,OAAO,CAAC;AAAA,UACrJ;AACA,8BAAoB,OAAO,aAAa,cAAc,MAAM,KAAK,cAAc,QAAQ,GAAG,EAAE,iBAAiB,CAAC,WAAW,OAAO,CAAC;AACjI,4BAAkB,iBAAiB,CAAC,OAAO,CAAC;AAC5C,uBAAa,iBAAiB,CAAC,OAAO,CAAC;AACvC,gCAAsB,iBAAiB,CAAC,OAAO,CAAC;AAAA,QAClD;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,SAAS,KAAK;AACZ,UAAM;AAAA,EACR;AACF;AAUA,IAAM,0BAA0B;", "names": []}