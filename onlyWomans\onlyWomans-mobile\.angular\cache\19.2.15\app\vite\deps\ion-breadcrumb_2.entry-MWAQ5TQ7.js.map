{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-breadcrumb_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { i as inheritAriaAttributes } from './helpers-1O4D2b7y.js';\nimport { c as createColorClasses, o as openURL, h as hostContext } from './theme-DiVJyqlX.js';\nimport { m as chevronForwardOutline, n as ellipsisHorizontal } from './index-BLV6ykCk.js';\nconst breadcrumbIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex:0 0 auto;flex:0 0 auto;-ms-flex-align:center;align-items:center;color:var(--color);font-size:1rem;font-weight:400;line-height:1.5}.breadcrumb-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:100%;outline:none;background:inherit}:host(.breadcrumb-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.breadcrumb-active){color:var(--color-active)}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .breadcrumb-native{background:var(--background-focused)}@media (any-hover: hover){:host(.ion-activatable:hover){color:var(--color-hover)}:host(.ion-activatable.in-breadcrumbs-color:hover),:host(.ion-activatable.ion-color:hover){color:var(--ion-color-shade)}}.breadcrumb-separator{display:-ms-inline-flexbox;display:inline-flex}:host(.breadcrumb-collapsed) .breadcrumb-native{display:none}:host(.in-breadcrumbs-color),:host(.in-breadcrumbs-color.breadcrumb-active){color:var(--ion-color-base)}:host(.in-breadcrumbs-color) .breadcrumb-separator{color:var(--ion-color-base)}:host(.ion-color){color:var(--ion-color-base)}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumb-separator{color:rgba(var(--ion-color-contrast-rgb), 0.8)}:host(.in-toolbar-color.breadcrumb-active){color:var(--ion-color-contrast)}.breadcrumbs-collapsed-indicator{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex:1 1 100%;flex:1 1 100%;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:32px;height:18px;border:0;outline:none;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.breadcrumbs-collapsed-indicator ion-icon{margin-top:1px;font-size:1.375rem}:host{--color:var(--ion-color-step-850, var(--ion-text-color-step-150, #2d4665));--color-active:var(--ion-text-color, #03060b);--color-hover:var(--ion-text-color, #03060b);--color-focused:var(--color-active);--background-focused:var(--ion-color-step-50, var(--ion-background-color-step-50, rgba(233, 237, 243, 0.7)));font-size:clamp(16px, 1rem, 22px)}:host(.breadcrumb-active){font-weight:600}.breadcrumb-native{border-radius:4px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:5px;padding-bottom:5px;border:1px solid transparent}:host(.ion-focused) .breadcrumb-native{border-radius:8px}:host(.in-breadcrumbs-color.ion-focused) .breadcrumb-native,:host(.ion-color.ion-focused) .breadcrumb-native{background:rgba(var(--ion-color-base-rgb), 0.1);color:var(--ion-color-base)}:host(.ion-focused) ::slotted(ion-icon),:host(.in-breadcrumbs-color.ion-focused) ::slotted(ion-icon),:host(.ion-color.ion-focused) ::slotted(ion-icon){color:var(--ion-color-step-750, var(--ion-text-color-step-250, #445b78))}.breadcrumb-separator{color:var(--ion-color-step-550, var(--ion-text-color-step-450, #73849a))}::slotted(ion-icon){color:var(--ion-color-step-400, var(--ion-text-color-step-600, #92a0b3));font-size:min(1.125rem, 21.6px)}::slotted(ion-icon[slot=start]){-webkit-margin-end:8px;margin-inline-end:8px}::slotted(ion-icon[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px}:host(.breadcrumb-active) ::slotted(ion-icon){color:var(--ion-color-step-850, var(--ion-text-color-step-150, #242d39))}.breadcrumbs-collapsed-indicator{border-radius:4px;background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e9edf3));color:var(--ion-color-step-550, var(--ion-text-color-step-450, #73849a))}.breadcrumbs-collapsed-indicator:hover{opacity:0.45}.breadcrumbs-collapsed-indicator:focus{background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9e0ea))}.breadcrumbs-collapsed-indicator ion-icon{font-size:min(1.375rem, 22px)}\";\nconst breadcrumbMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex:0 0 auto;flex:0 0 auto;-ms-flex-align:center;align-items:center;color:var(--color);font-size:1rem;font-weight:400;line-height:1.5}.breadcrumb-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:100%;outline:none;background:inherit}:host(.breadcrumb-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.breadcrumb-active){color:var(--color-active)}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .breadcrumb-native{background:var(--background-focused)}@media (any-hover: hover){:host(.ion-activatable:hover){color:var(--color-hover)}:host(.ion-activatable.in-breadcrumbs-color:hover),:host(.ion-activatable.ion-color:hover){color:var(--ion-color-shade)}}.breadcrumb-separator{display:-ms-inline-flexbox;display:inline-flex}:host(.breadcrumb-collapsed) .breadcrumb-native{display:none}:host(.in-breadcrumbs-color),:host(.in-breadcrumbs-color.breadcrumb-active){color:var(--ion-color-base)}:host(.in-breadcrumbs-color) .breadcrumb-separator{color:var(--ion-color-base)}:host(.ion-color){color:var(--ion-color-base)}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumb-separator{color:rgba(var(--ion-color-contrast-rgb), 0.8)}:host(.in-toolbar-color.breadcrumb-active){color:var(--ion-color-contrast)}.breadcrumbs-collapsed-indicator{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex:1 1 100%;flex:1 1 100%;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:32px;height:18px;border:0;outline:none;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.breadcrumbs-collapsed-indicator ion-icon{margin-top:1px;font-size:1.375rem}:host{--color:var(--ion-color-step-600, var(--ion-text-color-step-400, #677483));--color-active:var(--ion-text-color, #03060b);--color-hover:var(--ion-text-color, #03060b);--color-focused:var(--ion-color-step-800, var(--ion-text-color-step-200, #35404e));--background-focused:var(--ion-color-step-50, var(--ion-background-color-step-50, #fff))}:host(.breadcrumb-active){font-weight:500}.breadcrumb-native{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px}.breadcrumb-separator{-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:-1px}:host(.ion-focused) .breadcrumb-native{border-radius:4px;-webkit-box-shadow:0px 1px 2px rgba(0, 0, 0, 0.2), 0px 2px 8px rgba(0, 0, 0, 0.12);box-shadow:0px 1px 2px rgba(0, 0, 0, 0.2), 0px 2px 8px rgba(0, 0, 0, 0.12)}.breadcrumb-separator{color:var(--ion-color-step-550, var(--ion-text-color-step-450, #73849a))}::slotted(ion-icon){color:var(--ion-color-step-550, var(--ion-text-color-step-450, #7d8894));font-size:1.125rem}::slotted(ion-icon[slot=start]){-webkit-margin-end:8px;margin-inline-end:8px}::slotted(ion-icon[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px}:host(.breadcrumb-active) ::slotted(ion-icon){color:var(--ion-color-step-850, var(--ion-text-color-step-150, #222d3a))}.breadcrumbs-collapsed-indicator{border-radius:2px;background:var(--ion-color-step-100, var(--ion-background-color-step-100, #eef1f3));color:var(--ion-color-step-550, var(--ion-text-color-step-450, #73849a))}.breadcrumbs-collapsed-indicator:hover{opacity:0.7}.breadcrumbs-collapsed-indicator:focus{background:var(--ion-color-step-150, var(--ion-background-color-step-150, #dfe5e8))}\";\nconst Breadcrumb = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.collapsedClick = createEvent(this, \"collapsedClick\", 7);\n    this.inheritedAttributes = {};\n    /** @internal */\n    this.collapsed = false;\n    /**\n     * If `true`, the breadcrumb will take on a different look to show that\n     * it is the currently active breadcrumb. Defaults to `true` for the\n     * last breadcrumb if it is not set on any.\n     */\n    this.active = false;\n    /**\n     * If `true`, the user cannot interact with the breadcrumb.\n     */\n    this.disabled = false;\n    /**\n     * When using a router, it specifies the transition direction when navigating to\n     * another page using `href`.\n     */\n    this.routerDirection = 'forward';\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.collapsedIndicatorClick = () => {\n      this.collapsedClick.emit({\n        ionShadowTarget: this.collapsedRef\n      });\n    };\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  isClickable() {\n    return this.href !== undefined;\n  }\n  render() {\n    const {\n      color,\n      active,\n      collapsed,\n      disabled,\n      download,\n      el,\n      inheritedAttributes,\n      last,\n      routerAnimation,\n      routerDirection,\n      separator,\n      showCollapsedIndicator,\n      target\n    } = this;\n    const clickable = this.isClickable();\n    const TagType = this.href === undefined ? 'span' : 'a';\n    // Links can still be tabbed to when set to disabled if they have an href\n    // in order to truly disable them we can keep it as an anchor but remove the href\n    const href = disabled ? undefined : this.href;\n    const mode = getIonMode(this);\n    const attrs = TagType === 'span' ? {} : {\n      download,\n      href,\n      target\n    };\n    // If the breadcrumb is collapsed, check if it contains the collapsed indicator\n    // to show the separator as long as it isn't also the last breadcrumb\n    // otherwise if not collapsed use the value in separator\n    const showSeparator = last ? false : collapsed ? showCollapsedIndicator && !last ? true : false : separator;\n    return h(Host, {\n      key: '32ca61c83721dff52b5e97171ed449dce3584a55',\n      onClick: ev => openURL(href, ev, routerDirection, routerAnimation),\n      \"aria-disabled\": disabled ? 'true' : null,\n      class: createColorClasses(color, {\n        [mode]: true,\n        'breadcrumb-active': active,\n        'breadcrumb-collapsed': collapsed,\n        'breadcrumb-disabled': disabled,\n        'in-breadcrumbs-color': hostContext('ion-breadcrumbs[color]', el),\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'ion-activatable': clickable,\n        'ion-focusable': clickable\n      })\n    }, h(TagType, Object.assign({\n      key: '479feb845f4a6d8009d5422b33eb423730b9722b'\n    }, attrs, {\n      class: \"breadcrumb-native\",\n      part: \"native\",\n      disabled: disabled,\n      onFocus: this.onFocus,\n      onBlur: this.onBlur\n    }, inheritedAttributes), h(\"slot\", {\n      key: '3c5dcaeb0d258235d1b7707868026ff1d1404099',\n      name: \"start\"\n    }), h(\"slot\", {\n      key: 'f1cfb934443cd97dc220882c5e3596ea879d66cf'\n    }), h(\"slot\", {\n      key: '539710121b5b1f3ee8d4c24a9651b67c2ae08add',\n      name: \"end\"\n    })), showCollapsedIndicator && h(\"button\", {\n      key: 'ed53a95ccd89022c8b7bee0658a221ec62a5c73b',\n      part: \"collapsed-indicator\",\n      \"aria-label\": \"Show more breadcrumbs\",\n      onClick: () => this.collapsedIndicatorClick(),\n      ref: collapsedEl => this.collapsedRef = collapsedEl,\n      class: {\n        'breadcrumbs-collapsed-indicator': true\n      }\n    }, h(\"ion-icon\", {\n      key: 'a849e1142a86f06f207cf11662fa2a560ab7fc6a',\n      \"aria-hidden\": \"true\",\n      icon: ellipsisHorizontal,\n      lazy: false\n    })), showSeparator && (\n    /**\n     * Separators should not be announced by narrators.\n     * We add aria-hidden on the span so that this applies\n     * to any custom separators too.\n     */\n    h(\"span\", {\n      key: 'fc3c741cb01fafef8b26046c7ee5b190efc69a7c',\n      class: \"breadcrumb-separator\",\n      part: \"separator\",\n      \"aria-hidden\": \"true\"\n    }, h(\"slot\", {\n      key: '4871932ae1dae520767e0713e7cee2d11b0bba6d',\n      name: \"separator\"\n    }, mode === 'ios' ? h(\"ion-icon\", {\n      icon: chevronForwardOutline,\n      lazy: false,\n      \"flip-rtl\": true\n    }) : h(\"span\", null, \"/\")))));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nBreadcrumb.style = {\n  ios: breadcrumbIosCss,\n  md: breadcrumbMdCss\n};\nconst breadcrumbsIosCss = \":host{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator ion-icon{color:var(--ion-color-contrast)}:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator{background:rgba(var(--ion-color-contrast-rgb), 0.11)}:host(.in-toolbar){-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;-ms-flex-pack:center;justify-content:center}\";\nconst breadcrumbsMdCss = \":host{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator ion-icon{color:var(--ion-color-contrast)}:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator{background:rgba(var(--ion-color-contrast-rgb), 0.11)}:host(.in-toolbar){-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0}\";\nconst Breadcrumbs = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionCollapsedClick = createEvent(this, \"ionCollapsedClick\", 7);\n    /**\n     * The number of breadcrumbs to show before the collapsed indicator.\n     * If `itemsBeforeCollapse` + `itemsAfterCollapse` is greater than `maxItems`,\n     * the breadcrumbs will not be collapsed.\n     */\n    this.itemsBeforeCollapse = 1;\n    /**\n     * The number of breadcrumbs to show after the collapsed indicator.\n     * If `itemsBeforeCollapse` + `itemsAfterCollapse` is greater than `maxItems`,\n     * the breadcrumbs will not be collapsed.\n     */\n    this.itemsAfterCollapse = 1;\n    this.breadcrumbsInit = () => {\n      this.setBreadcrumbSeparator();\n      this.setMaxItems();\n    };\n    this.resetActiveBreadcrumb = () => {\n      const breadcrumbs = this.getBreadcrumbs();\n      // Only reset the active breadcrumb if we were the ones to change it\n      // otherwise use the one set on the component\n      const activeBreadcrumb = breadcrumbs.find(breadcrumb => breadcrumb.active);\n      if (activeBreadcrumb && this.activeChanged) {\n        activeBreadcrumb.active = false;\n      }\n    };\n    this.setMaxItems = () => {\n      const {\n        itemsAfterCollapse,\n        itemsBeforeCollapse,\n        maxItems\n      } = this;\n      const breadcrumbs = this.getBreadcrumbs();\n      for (const breadcrumb of breadcrumbs) {\n        breadcrumb.showCollapsedIndicator = false;\n        breadcrumb.collapsed = false;\n      }\n      // If the number of breadcrumbs exceeds the maximum number of items\n      // that should show and the items before / after collapse do not\n      // exceed the maximum items then we need to collapse the breadcrumbs\n      const shouldCollapse = maxItems !== undefined && breadcrumbs.length > maxItems && itemsBeforeCollapse + itemsAfterCollapse <= maxItems;\n      if (shouldCollapse) {\n        // Show the collapsed indicator in the first breadcrumb that collapses\n        breadcrumbs.forEach((breadcrumb, index) => {\n          if (index === itemsBeforeCollapse) {\n            breadcrumb.showCollapsedIndicator = true;\n          }\n          // Collapse all breadcrumbs that have an index greater than or equal to\n          // the number before collapse and an index less than the total number\n          // of breadcrumbs minus the items that should show after the collapse\n          if (index >= itemsBeforeCollapse && index < breadcrumbs.length - itemsAfterCollapse) {\n            breadcrumb.collapsed = true;\n          }\n        });\n      }\n    };\n    this.setBreadcrumbSeparator = () => {\n      const {\n        itemsAfterCollapse,\n        itemsBeforeCollapse,\n        maxItems\n      } = this;\n      const breadcrumbs = this.getBreadcrumbs();\n      // Check if an active breadcrumb exists already\n      const active = breadcrumbs.find(breadcrumb => breadcrumb.active);\n      // Set the separator on all but the last breadcrumb\n      for (const breadcrumb of breadcrumbs) {\n        // The only time the last breadcrumb changes is when\n        // itemsAfterCollapse is set to 0, in this case the\n        // last breadcrumb will be the collapsed indicator\n        const last = maxItems !== undefined && itemsAfterCollapse === 0 ? breadcrumb === breadcrumbs[itemsBeforeCollapse] : breadcrumb === breadcrumbs[breadcrumbs.length - 1];\n        breadcrumb.last = last;\n        // If the breadcrumb has defined whether or not to show the\n        // separator then use that value, otherwise check if it's the\n        // last breadcrumb\n        const separator = breadcrumb.separator !== undefined ? breadcrumb.separator : last ? undefined : true;\n        breadcrumb.separator = separator;\n        // If there is not an active breadcrumb already\n        // set the last one to active\n        if (!active && last) {\n          breadcrumb.active = true;\n          this.activeChanged = true;\n        }\n      }\n    };\n    this.getBreadcrumbs = () => {\n      return Array.from(this.el.querySelectorAll('ion-breadcrumb'));\n    };\n    this.slotChanged = () => {\n      this.resetActiveBreadcrumb();\n      this.breadcrumbsInit();\n    };\n  }\n  onCollapsedClick(ev) {\n    const breadcrumbs = this.getBreadcrumbs();\n    const collapsedBreadcrumbs = breadcrumbs.filter(breadcrumb => breadcrumb.collapsed);\n    this.ionCollapsedClick.emit(Object.assign(Object.assign({}, ev.detail), {\n      collapsedBreadcrumbs\n    }));\n  }\n  maxItemsChanged() {\n    this.resetActiveBreadcrumb();\n    this.breadcrumbsInit();\n  }\n  componentWillLoad() {\n    this.breadcrumbsInit();\n  }\n  render() {\n    const {\n      color,\n      collapsed\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'fe64e9cdf597ede2db140bf5fa05a0359d82db57',\n      class: createColorClasses(color, {\n        [mode]: true,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'breadcrumbs-collapsed': collapsed\n      })\n    }, h(\"slot\", {\n      key: 'a2c99b579e339055c50a613d5c6b61032f5ddffe',\n      onSlotchange: this.slotChanged\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"maxItems\": [\"maxItemsChanged\"],\n      \"itemsBeforeCollapse\": [\"maxItemsChanged\"],\n      \"itemsAfterCollapse\": [\"maxItemsChanged\"]\n    };\n  }\n};\nBreadcrumbs.style = {\n  ios: breadcrumbsIosCss,\n  md: breadcrumbsMdCss\n};\nexport { Breadcrumb as ion_breadcrumb, Breadcrumbs as ion_breadcrumbs };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,mBAAmB;AACzB,IAAM,kBAAkB;AACxB,IAAM,aAAa,MAAM;AAAA,EACvB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,SAAK,sBAAsB,CAAC;AAE5B,SAAK,YAAY;AAMjB,SAAK,SAAS;AAId,SAAK,WAAW;AAKhB,SAAK,kBAAkB;AACvB,SAAK,UAAU,MAAM;AACnB,WAAK,SAAS,KAAK;AAAA,IACrB;AACA,SAAK,SAAS,MAAM;AAClB,WAAK,QAAQ,KAAK;AAAA,IACpB;AACA,SAAK,0BAA0B,MAAM;AACnC,WAAK,eAAe,KAAK;AAAA,QACvB,iBAAiB,KAAK;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,sBAAsB,sBAAsB,KAAK,EAAE;AAAA,EAC1D;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,YAAY,KAAK,YAAY;AACnC,UAAM,UAAU,KAAK,SAAS,SAAY,SAAS;AAGnD,UAAM,OAAO,WAAW,SAAY,KAAK;AACzC,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,QAAQ,YAAY,SAAS,CAAC,IAAI;AAAA,MACtC;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAIA,UAAM,gBAAgB,OAAO,QAAQ,YAAY,0BAA0B,CAAC,OAAO,OAAO,QAAQ;AAClG,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,SAAS,QAAM,QAAQ,MAAM,IAAI,iBAAiB,eAAe;AAAA,MACjE,iBAAiB,WAAW,SAAS;AAAA,MACrC,OAAO,mBAAmB,OAAO;AAAA,QAC/B,CAAC,IAAI,GAAG;AAAA,QACR,qBAAqB;AAAA,QACrB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,wBAAwB,YAAY,0BAA0B,EAAE;AAAA,QAChE,cAAc,YAAY,eAAe,KAAK,EAAE;AAAA,QAChD,oBAAoB,YAAY,sBAAsB,KAAK,EAAE;AAAA,QAC7D,mBAAmB;AAAA,QACnB,iBAAiB;AAAA,MACnB,CAAC;AAAA,IACH,GAAG,EAAE,SAAS,OAAO,OAAO;AAAA,MAC1B,KAAK;AAAA,IACP,GAAG,OAAO;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN;AAAA,MACA,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,IACf,GAAG,mBAAmB,GAAG,EAAE,QAAQ;AAAA,MACjC,KAAK;AAAA,MACL,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,QAAQ;AAAA,MACZ,KAAK;AAAA,IACP,CAAC,GAAG,EAAE,QAAQ;AAAA,MACZ,KAAK;AAAA,MACL,MAAM;AAAA,IACR,CAAC,CAAC,GAAG,0BAA0B,EAAE,UAAU;AAAA,MACzC,KAAK;AAAA,MACL,MAAM;AAAA,MACN,cAAc;AAAA,MACd,SAAS,MAAM,KAAK,wBAAwB;AAAA,MAC5C,KAAK,iBAAe,KAAK,eAAe;AAAA,MACxC,OAAO;AAAA,QACL,mCAAmC;AAAA,MACrC;AAAA,IACF,GAAG,EAAE,YAAY;AAAA,MACf,KAAK;AAAA,MACL,eAAe;AAAA,MACf,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,IAML,EAAE,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,IACjB,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,MACL,MAAM;AAAA,IACR,GAAG,SAAS,QAAQ,EAAE,YAAY;AAAA,MAChC,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC,IAAI,EAAE,QAAQ,MAAM,GAAG,CAAC,CAAC,CAAE;AAAA,EAC9B;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;AACA,WAAW,QAAQ;AAAA,EACjB,KAAK;AAAA,EACL,IAAI;AACN;AACA,IAAM,oBAAoB;AAC1B,IAAM,mBAAmB;AACzB,IAAM,cAAc,MAAM;AAAA,EACxB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,oBAAoB,YAAY,MAAM,qBAAqB,CAAC;AAMjE,SAAK,sBAAsB;AAM3B,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB,MAAM;AAC3B,WAAK,uBAAuB;AAC5B,WAAK,YAAY;AAAA,IACnB;AACA,SAAK,wBAAwB,MAAM;AACjC,YAAM,cAAc,KAAK,eAAe;AAGxC,YAAM,mBAAmB,YAAY,KAAK,gBAAc,WAAW,MAAM;AACzE,UAAI,oBAAoB,KAAK,eAAe;AAC1C,yBAAiB,SAAS;AAAA,MAC5B;AAAA,IACF;AACA,SAAK,cAAc,MAAM;AACvB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,cAAc,KAAK,eAAe;AACxC,iBAAW,cAAc,aAAa;AACpC,mBAAW,yBAAyB;AACpC,mBAAW,YAAY;AAAA,MACzB;AAIA,YAAM,iBAAiB,aAAa,UAAa,YAAY,SAAS,YAAY,sBAAsB,sBAAsB;AAC9H,UAAI,gBAAgB;AAElB,oBAAY,QAAQ,CAAC,YAAY,UAAU;AACzC,cAAI,UAAU,qBAAqB;AACjC,uBAAW,yBAAyB;AAAA,UACtC;AAIA,cAAI,SAAS,uBAAuB,QAAQ,YAAY,SAAS,oBAAoB;AACnF,uBAAW,YAAY;AAAA,UACzB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,SAAK,yBAAyB,MAAM;AAClC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,cAAc,KAAK,eAAe;AAExC,YAAM,SAAS,YAAY,KAAK,gBAAc,WAAW,MAAM;AAE/D,iBAAW,cAAc,aAAa;AAIpC,cAAM,OAAO,aAAa,UAAa,uBAAuB,IAAI,eAAe,YAAY,mBAAmB,IAAI,eAAe,YAAY,YAAY,SAAS,CAAC;AACrK,mBAAW,OAAO;AAIlB,cAAM,YAAY,WAAW,cAAc,SAAY,WAAW,YAAY,OAAO,SAAY;AACjG,mBAAW,YAAY;AAGvB,YAAI,CAAC,UAAU,MAAM;AACnB,qBAAW,SAAS;AACpB,eAAK,gBAAgB;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AACA,SAAK,iBAAiB,MAAM;AAC1B,aAAO,MAAM,KAAK,KAAK,GAAG,iBAAiB,gBAAgB,CAAC;AAAA,IAC9D;AACA,SAAK,cAAc,MAAM;AACvB,WAAK,sBAAsB;AAC3B,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,iBAAiB,IAAI;AACnB,UAAM,cAAc,KAAK,eAAe;AACxC,UAAM,uBAAuB,YAAY,OAAO,gBAAc,WAAW,SAAS;AAClF,SAAK,kBAAkB,KAAK,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,GAAG,MAAM,GAAG;AAAA,MACtE;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,kBAAkB;AAChB,SAAK,sBAAsB;AAC3B,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,oBAAoB;AAClB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,mBAAmB,OAAO;AAAA,QAC/B,CAAC,IAAI,GAAG;AAAA,QACR,cAAc,YAAY,eAAe,KAAK,EAAE;AAAA,QAChD,oBAAoB,YAAY,sBAAsB,KAAK,EAAE;AAAA,QAC7D,yBAAyB;AAAA,MAC3B,CAAC;AAAA,IACH,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,MACL,cAAc,KAAK;AAAA,IACrB,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,YAAY,CAAC,iBAAiB;AAAA,MAC9B,uBAAuB,CAAC,iBAAiB;AAAA,MACzC,sBAAsB,CAAC,iBAAiB;AAAA,IAC1C;AAAA,EACF;AACF;AACA,YAAY,QAAQ;AAAA,EAClB,KAAK;AAAA,EACL,IAAI;AACN;", "names": []}