<ion-header [translucent]="true" class="ow-header">
  <ion-toolbar class="ow-toolbar">
    <ion-title class="ow-heading">Beauty & Makeup ✨</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ow-content">
  <!-- Beauty Categories -->
  <div class="categories-section">
    <h3 class="section-title ow-heading-md">Shop by Category</h3>
    <div class="categories-grid">
      <div 
        *ngFor="let category of beautyCategories" 
        class="category-card ow-card"
        (click)="onCategoryClick(category)">
        <div class="category-icon" [style.background]="category.color">
          <span class="icon-emoji">{{ category.icon }}</span>
        </div>
        <h4 class="category-name ow-heading-sm">{{ category.name }}</h4>
      </div>
    </div>
  </div>

  <!-- Beauty Tutorials -->
  <div class="tutorials-section">
    <h3 class="section-title ow-heading-md">Beauty Tutorials 📹</h3>
    <div class="tutorials-grid">
      <div 
        *ngFor="let tutorial of tutorials" 
        class="tutorial-card ow-card"
        (click)="onTutorialClick(tutorial)">
        <div class="tutorial-thumbnail">
          <img [src]="tutorial.thumbnail" [alt]="tutorial.title">
          <div class="play-button">
            <ion-icon name="play" class="play-icon"></ion-icon>
          </div>
          <div class="duration-badge">{{ tutorial.duration }}</div>
        </div>
        <div class="tutorial-info">
          <h4 class="tutorial-title ow-heading-sm">{{ tutorial.title }}</h4>
          <p class="tutorial-views">{{ tutorial.views }} views</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Featured Products -->
  <div class="products-section">
    <h3 class="section-title ow-heading-md">Featured Products</h3>
    <div class="products-grid">
      <div 
        *ngFor="let product of beautyProducts" 
        class="product-card ow-product-card"
        (click)="onProductClick(product)">
        
        <div class="product-image-container">
          <img [src]="product.image" [alt]="product.name" class="product-image">
        </div>
        
        <div class="product-info">
          <h4 class="product-name ow-heading-sm">{{ product.name }}</h4>
          <div class="product-rating">
            <ion-icon name="star" class="star-icon"></ion-icon>
            <span class="rating-value">{{ product.rating }}</span>
            <span class="rating-count">({{ product.reviews }})</span>
          </div>
          <div class="product-price">
            <span class="current-price ow-text-primary">₹{{ product.price }}</span>
            <span class="original-price">₹{{ product.originalPrice }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bottom Spacing -->
  <div class="bottom-spacing"></div>
</ion-content>
