const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');

// Sample orders data
let orders = [
  {
    id: 1,
    userId: 'user123',
    orderNumber: '*********',
    status: 'delivered',
    items: [
      {
        productId: 1,
        name: 'Floral Maxi Dress',
        price: 2499,
        quantity: 1,
        image: '/assets/products/dress1.jpg'
      }
    ],
    totalAmount: 2499,
    shippingAddress: {
      name: '<PERSON><PERSON>',
      phone: '+91 9876543210',
      address: '123 MG Road',
      city: 'Mumbai',
      state: 'Maharashtra',
      pincode: '400001'
    },
    paymentMethod: 'card',
    paymentStatus: 'paid',
    createdAt: new Date('2024-06-20'),
    deliveredAt: new Date('2024-06-23')
  }
];

// @route   GET /api/orders
// @desc    Get user orders
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const userOrders = orders.filter(order => order.userId === req.user.id);
    
    res.json({
      success: true,
      count: userOrders.length,
      data: userOrders
    });
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   GET /api/orders/:id
// @desc    Get order by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const order = orders.find(order => 
      order.id === parseInt(req.params.id) && order.userId === req.user.id
    );
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }
    
    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    console.error('Error fetching order:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   POST /api/orders
// @desc    Create new order
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    const { items, shippingAddress, paymentMethod } = req.body;
    
    // Calculate total amount
    const totalAmount = items.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
    
    const newOrder = {
      id: orders.length + 1,
      userId: req.user.id,
      orderNumber: `OW2024${String(orders.length + 1).padStart(3, '0')}`,
      status: 'pending',
      items,
      totalAmount,
      shippingAddress,
      paymentMethod,
      paymentStatus: 'pending',
      createdAt: new Date()
    };
    
    orders.push(newOrder);
    
    res.status(201).json({
      success: true,
      data: newOrder,
      message: 'Order created successfully'
    });
  } catch (error) {
    console.error('Error creating order:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   PUT /api/orders/:id/status
// @desc    Update order status
// @access  Private
router.put('/:id/status', auth, async (req, res) => {
  try {
    const { status } = req.body;
    const orderIndex = orders.findIndex(order => 
      order.id === parseInt(req.params.id) && order.userId === req.user.id
    );
    
    if (orderIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }
    
    orders[orderIndex].status = status;
    
    if (status === 'delivered') {
      orders[orderIndex].deliveredAt = new Date();
    }
    
    res.json({
      success: true,
      data: orders[orderIndex],
      message: 'Order status updated successfully'
    });
  } catch (error) {
    console.error('Error updating order status:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   POST /api/orders/:id/cancel
// @desc    Cancel order
// @access  Private
router.post('/:id/cancel', auth, async (req, res) => {
  try {
    const orderIndex = orders.findIndex(order => 
      order.id === parseInt(req.params.id) && order.userId === req.user.id
    );
    
    if (orderIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }
    
    if (orders[orderIndex].status === 'delivered') {
      return res.status(400).json({
        success: false,
        message: 'Cannot cancel delivered order'
      });
    }
    
    orders[orderIndex].status = 'cancelled';
    orders[orderIndex].cancelledAt = new Date();
    
    res.json({
      success: true,
      data: orders[orderIndex],
      message: 'Order cancelled successfully'
    });
  } catch (error) {
    console.error('Error cancelling order:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

module.exports = router;
