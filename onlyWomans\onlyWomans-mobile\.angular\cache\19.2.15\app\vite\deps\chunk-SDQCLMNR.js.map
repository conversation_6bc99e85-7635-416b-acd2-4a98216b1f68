{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/index-BlJTBdxG.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as componentOnReady } from './helpers-1O4D2b7y.js';\nimport { t as printRequiredElementError } from './index-B_U9CtaY.js';\nconst ION_CONTENT_TAG_NAME = 'ION-CONTENT';\nconst ION_CONTENT_ELEMENT_SELECTOR = 'ion-content';\nconst ION_CONTENT_CLASS_SELECTOR = '.ion-content-scroll-host';\n/**\n * Selector used for implementations reliant on `<ion-content>` for scroll event changes.\n *\n * Developers should use the `.ion-content-scroll-host` selector to target the element emitting\n * scroll events. With virtual scroll implementations this will be the host element for\n * the scroll viewport.\n */\nconst ION_CONTENT_SELECTOR = `${ION_CONTENT_ELEMENT_SELECTOR}, ${ION_CONTENT_CLASS_SELECTOR}`;\nconst isIonContent = el => el.tagName === ION_CONTENT_TAG_NAME;\n/**\n * Waits for the element host fully initialize before\n * returning the inner scroll element.\n *\n * For `ion-content` the scroll target will be the result\n * of the `getScrollElement` function.\n *\n * For custom implementations it will be the element host\n * or a selector within the host, if supplied through `scrollTarget`.\n */\nconst getScrollElement = async el => {\n  if (isIonContent(el)) {\n    await new Promise(resolve => componentOnReady(el, resolve));\n    return el.getScrollElement();\n  }\n  return el;\n};\n/**\n * Queries the element matching the selector for IonContent.\n * See ION_CONTENT_SELECTOR for the selector used.\n */\nconst findIonContent = el => {\n  /**\n   * First we try to query the custom scroll host selector in cases where\n   * the implementation is using an outer `ion-content` with an inner custom\n   * scroll container.\n   */\n  const customContentHost = el.querySelector(ION_CONTENT_CLASS_SELECTOR);\n  if (customContentHost) {\n    return customContentHost;\n  }\n  return el.querySelector(ION_CONTENT_SELECTOR);\n};\n/**\n * Queries the closest element matching the selector for IonContent.\n */\nconst findClosestIonContent = el => {\n  return el.closest(ION_CONTENT_SELECTOR);\n};\n/**\n * Scrolls to the top of the element. If an `ion-content` is found, it will scroll\n * using the public API `scrollToTop` with a duration.\n */\nconst scrollToTop = (el, durationMs) => {\n  if (isIonContent(el)) {\n    const content = el;\n    return content.scrollToTop(durationMs);\n  }\n  return Promise.resolve(el.scrollTo({\n    top: 0,\n    left: 0,\n    behavior: 'smooth'\n  }));\n};\n/**\n * Scrolls by a specified X/Y distance in the component. If an `ion-content` is found, it will scroll\n * using the public API `scrollByPoint` with a duration.\n */\nconst scrollByPoint = (el, x, y, durationMs) => {\n  if (isIonContent(el)) {\n    const content = el;\n    return content.scrollByPoint(x, y, durationMs);\n  }\n  return Promise.resolve(el.scrollBy({\n    top: y,\n    left: x,\n    behavior: durationMs > 0 ? 'smooth' : 'auto'\n  }));\n};\n/**\n * Prints an error informing developers that an implementation requires an element to be used\n * within either the `ion-content` selector or the `.ion-content-scroll-host` class.\n */\nconst printIonContentErrorMsg = el => {\n  return printRequiredElementError(el, ION_CONTENT_ELEMENT_SELECTOR);\n};\n/**\n * Several components in Ionic need to prevent scrolling\n * during a gesture (card modal, range, item sliding, etc).\n * Use this utility to account for ion-content and custom content hosts.\n */\nconst disableContentScrollY = contentEl => {\n  if (isIonContent(contentEl)) {\n    const ionContent = contentEl;\n    const initialScrollY = ionContent.scrollY;\n    ionContent.scrollY = false;\n    /**\n     * This should be passed into resetContentScrollY\n     * so that we can revert ion-content's scrollY to the\n     * correct state. For example, if scrollY = false\n     * initially, we do not want to enable scrolling\n     * when we call resetContentScrollY.\n     */\n    return initialScrollY;\n  } else {\n    contentEl.style.setProperty('overflow', 'hidden');\n    return true;\n  }\n};\nconst resetContentScrollY = (contentEl, initialScrollY) => {\n  if (isIonContent(contentEl)) {\n    contentEl.scrollY = initialScrollY;\n  } else {\n    contentEl.style.removeProperty('overflow');\n  }\n};\nexport { ION_CONTENT_CLASS_SELECTOR as I, findIonContent as a, ION_CONTENT_ELEMENT_SELECTOR as b, scrollByPoint as c, disableContentScrollY as d, findClosestIonContent as f, getScrollElement as g, isIonContent as i, printIonContentErrorMsg as p, resetContentScrollY as r, scrollToTop as s };"], "mappings": ";;;;;;;;;;;AAKA,IAAM,uBAAuB;AAC7B,IAAM,+BAA+B;AACrC,IAAM,6BAA6B;AAQnC,IAAM,uBAAuB,GAAG,4BAA4B,KAAK,0BAA0B;AAC3F,IAAM,eAAe,QAAM,GAAG,YAAY;AAW1C,IAAM,mBAAmB,CAAM,OAAM;AACnC,MAAI,aAAa,EAAE,GAAG;AACpB,UAAM,IAAI,QAAQ,aAAW,iBAAiB,IAAI,OAAO,CAAC;AAC1D,WAAO,GAAG,iBAAiB;AAAA,EAC7B;AACA,SAAO;AACT;AAKA,IAAM,iBAAiB,QAAM;AAM3B,QAAM,oBAAoB,GAAG,cAAc,0BAA0B;AACrE,MAAI,mBAAmB;AACrB,WAAO;AAAA,EACT;AACA,SAAO,GAAG,cAAc,oBAAoB;AAC9C;AAIA,IAAM,wBAAwB,QAAM;AAClC,SAAO,GAAG,QAAQ,oBAAoB;AACxC;AAKA,IAAM,cAAc,CAAC,IAAI,eAAe;AACtC,MAAI,aAAa,EAAE,GAAG;AACpB,UAAM,UAAU;AAChB,WAAO,QAAQ,YAAY,UAAU;AAAA,EACvC;AACA,SAAO,QAAQ,QAAQ,GAAG,SAAS;AAAA,IACjC,KAAK;AAAA,IACL,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC,CAAC;AACJ;AAKA,IAAM,gBAAgB,CAAC,IAAI,GAAG,GAAG,eAAe;AAC9C,MAAI,aAAa,EAAE,GAAG;AACpB,UAAM,UAAU;AAChB,WAAO,QAAQ,cAAc,GAAG,GAAG,UAAU;AAAA,EAC/C;AACA,SAAO,QAAQ,QAAQ,GAAG,SAAS;AAAA,IACjC,KAAK;AAAA,IACL,MAAM;AAAA,IACN,UAAU,aAAa,IAAI,WAAW;AAAA,EACxC,CAAC,CAAC;AACJ;AAKA,IAAM,0BAA0B,QAAM;AACpC,SAAO,0BAA0B,IAAI,4BAA4B;AACnE;AAMA,IAAM,wBAAwB,eAAa;AACzC,MAAI,aAAa,SAAS,GAAG;AAC3B,UAAM,aAAa;AACnB,UAAM,iBAAiB,WAAW;AAClC,eAAW,UAAU;AAQrB,WAAO;AAAA,EACT,OAAO;AACL,cAAU,MAAM,YAAY,YAAY,QAAQ;AAChD,WAAO;AAAA,EACT;AACF;AACA,IAAM,sBAAsB,CAAC,WAAW,mBAAmB;AACzD,MAAI,aAAa,SAAS,GAAG;AAC3B,cAAU,UAAU;AAAA,EACtB,OAAO;AACL,cAAU,MAAM,eAAe,UAAU;AAAA,EAC3C;AACF;", "names": []}