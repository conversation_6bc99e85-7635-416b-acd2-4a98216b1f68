{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/input.utils-zWijNCrx.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-ZjP4CjeZ.js';\nimport { r as raf } from './helpers-1O4D2b7y.js';\nimport { o as printIonError } from './index-B_U9CtaY.js';\n\n/**\n * Used to update a scoped component that uses emulated slots. This fires when\n * content is passed into the slot or when the content inside of a slot changes.\n * This is not needed for components using native slots in the Shadow DOM.\n * @internal\n * @param el The host element to observe\n * @param slotName mutationCallback will fire when nodes on these slot(s) change\n * @param mutationCallback The callback to fire whenever the slotted content changes\n */\nconst createSlotMutationController = (el, slotName, mutationCallback) => {\n  let hostMutationObserver;\n  let slottedContentMutationObserver;\n  if (win !== undefined && 'MutationObserver' in win) {\n    const slots = Array.isArray(slotName) ? slotName : [slotName];\n    hostMutationObserver = new MutationObserver(entries => {\n      for (const entry of entries) {\n        for (const node of entry.addedNodes) {\n          /**\n           * Check to see if the added node\n           *  is our slotted content.\n           */\n          if (node.nodeType === Node.ELEMENT_NODE && slots.includes(node.slot)) {\n            /**\n             * If so, we want to watch the slotted\n             * content itself for changes. This lets us\n             * detect when content inside of the slot changes.\n             */\n            mutationCallback();\n            /**\n             * Adding the listener in an raf\n             * waits until Stencil moves the slotted element\n             * into the correct place in the event that\n             * slotted content is being added.\n             */\n            raf(() => watchForSlotChange(node));\n            return;\n          }\n        }\n      }\n    });\n    hostMutationObserver.observe(el, {\n      childList: true,\n      /**\n       * This fixes an issue with the `ion-input` and\n       * `ion-textarea` not re-rendering in some cases\n       * when using the label slot functionality.\n       *\n       * HTML element patches in Stencil that are enabled\n       * by the `experimentalSlotFixes` flag in Stencil v4\n       * result in DOM manipulations that won't trigger\n       * the current mutation observer configuration and\n       * callback.\n       */\n      subtree: true\n    });\n  }\n  /**\n   * Listen for changes inside of the slotted content.\n   * We can listen for subtree changes here to be\n   * informed of text within the slotted content\n   * changing. Doing this on the host is possible\n   * but it is much more expensive to do because\n   * it also listens for changes to the internals\n   * of the component.\n   */\n  const watchForSlotChange = slottedEl => {\n    var _a;\n    if (slottedContentMutationObserver) {\n      slottedContentMutationObserver.disconnect();\n      slottedContentMutationObserver = undefined;\n    }\n    slottedContentMutationObserver = new MutationObserver(entries => {\n      mutationCallback();\n      for (const entry of entries) {\n        for (const node of entry.removedNodes) {\n          /**\n           * If the element was removed then we\n           * need to destroy the MutationObserver\n           * so the element can be garbage collected.\n           */\n          if (node.nodeType === Node.ELEMENT_NODE && node.slot === slotName) {\n            destroySlottedContentObserver();\n          }\n        }\n      }\n    });\n    /**\n     * Listen for changes inside of the element\n     * as well as anything deep in the tree.\n     * We listen on the parentElement so that we can\n     * detect when slotted element itself is removed.\n     */\n    slottedContentMutationObserver.observe((_a = slottedEl.parentElement) !== null && _a !== void 0 ? _a : slottedEl, {\n      subtree: true,\n      childList: true\n    });\n  };\n  const destroy = () => {\n    if (hostMutationObserver) {\n      hostMutationObserver.disconnect();\n      hostMutationObserver = undefined;\n    }\n    destroySlottedContentObserver();\n  };\n  const destroySlottedContentObserver = () => {\n    if (slottedContentMutationObserver) {\n      slottedContentMutationObserver.disconnect();\n      slottedContentMutationObserver = undefined;\n    }\n  };\n  return {\n    destroy\n  };\n};\nconst getCounterText = (value, maxLength, counterFormatter) => {\n  const valueLength = value == null ? 0 : value.toString().length;\n  const defaultCounterText = defaultCounterFormatter(valueLength, maxLength);\n  /**\n   * If developers did not pass a custom formatter,\n   * use the default one.\n   */\n  if (counterFormatter === undefined) {\n    return defaultCounterText;\n  }\n  /**\n   * Otherwise, try to use the custom formatter\n   * and fallback to the default formatter if\n   * there was an error.\n   */\n  try {\n    return counterFormatter(valueLength, maxLength);\n  } catch (e) {\n    printIonError('[ion-input] - Exception in provided `counterFormatter`:', e);\n    return defaultCounterText;\n  }\n};\nconst defaultCounterFormatter = (length, maxlength) => {\n  return `${length} / ${maxlength}`;\n};\nexport { createSlotMutationController as c, getCounterText as g };"], "mappings": ";;;;;;;;;;;AAgBA,IAAM,+BAA+B,CAAC,IAAI,UAAU,qBAAqB;AACvE,MAAI;AACJ,MAAI;AACJ,MAAI,QAAQ,UAAa,sBAAsB,KAAK;AAClD,UAAM,QAAQ,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AAC5D,2BAAuB,IAAI,iBAAiB,aAAW;AACrD,iBAAW,SAAS,SAAS;AAC3B,mBAAW,QAAQ,MAAM,YAAY;AAKnC,cAAI,KAAK,aAAa,KAAK,gBAAgB,MAAM,SAAS,KAAK,IAAI,GAAG;AAMpE,6BAAiB;AAOjB,gBAAI,MAAM,mBAAmB,IAAI,CAAC;AAClC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,yBAAqB,QAAQ,IAAI;AAAA,MAC/B,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAYX,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAUA,QAAM,qBAAqB,eAAa;AACtC,QAAI;AACJ,QAAI,gCAAgC;AAClC,qCAA+B,WAAW;AAC1C,uCAAiC;AAAA,IACnC;AACA,qCAAiC,IAAI,iBAAiB,aAAW;AAC/D,uBAAiB;AACjB,iBAAW,SAAS,SAAS;AAC3B,mBAAW,QAAQ,MAAM,cAAc;AAMrC,cAAI,KAAK,aAAa,KAAK,gBAAgB,KAAK,SAAS,UAAU;AACjE,0CAA8B;AAAA,UAChC;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAOD,mCAA+B,SAAS,KAAK,UAAU,mBAAmB,QAAQ,OAAO,SAAS,KAAK,WAAW;AAAA,MAChH,SAAS;AAAA,MACT,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AACA,QAAM,UAAU,MAAM;AACpB,QAAI,sBAAsB;AACxB,2BAAqB,WAAW;AAChC,6BAAuB;AAAA,IACzB;AACA,kCAA8B;AAAA,EAChC;AACA,QAAM,gCAAgC,MAAM;AAC1C,QAAI,gCAAgC;AAClC,qCAA+B,WAAW;AAC1C,uCAAiC;AAAA,IACnC;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,EACF;AACF;AACA,IAAM,iBAAiB,CAAC,OAAO,WAAW,qBAAqB;AAC7D,QAAM,cAAc,SAAS,OAAO,IAAI,MAAM,SAAS,EAAE;AACzD,QAAM,qBAAqB,wBAAwB,aAAa,SAAS;AAKzE,MAAI,qBAAqB,QAAW;AAClC,WAAO;AAAA,EACT;AAMA,MAAI;AACF,WAAO,iBAAiB,aAAa,SAAS;AAAA,EAChD,SAAS,GAAG;AACV,kBAAc,2DAA2D,CAAC;AAC1E,WAAO;AAAA,EACT;AACF;AACA,IAAM,0BAA0B,CAAC,QAAQ,cAAc;AACrD,SAAO,GAAG,MAAM,MAAM,SAAS;AACjC;", "names": []}