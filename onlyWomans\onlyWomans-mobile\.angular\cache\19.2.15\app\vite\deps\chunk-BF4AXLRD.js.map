{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/components/gesture-controller.js", "../../../../../../node_modules/@ionic/core/components/index3.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nclass GestureController {\n  constructor() {\n    this.gestureId = 0;\n    this.requestedStart = new Map();\n    this.disabledGestures = new Map();\n    this.disabledScroll = new Set();\n  }\n  /**\n   * Creates a gesture delegate based on the GestureConfig passed\n   */\n  createGesture(config) {\n    var _a;\n    return new GestureDelegate(this, this.newID(), config.name, (_a = config.priority) !== null && _a !== void 0 ? _a : 0, !!config.disableScroll);\n  }\n  /**\n   * Creates a blocker that will block any other gesture events from firing. Set in the ion-gesture component.\n   */\n  createBlocker(opts = {}) {\n    return new BlockerDelegate(this, this.newID(), opts.disable, !!opts.disableScroll);\n  }\n  start(gestureName, id, priority) {\n    if (!this.canStart(gestureName)) {\n      this.requestedStart.delete(id);\n      return false;\n    }\n    this.requestedStart.set(id, priority);\n    return true;\n  }\n  capture(gestureName, id, priority) {\n    if (!this.start(gestureName, id, priority)) {\n      return false;\n    }\n    const requestedStart = this.requestedStart;\n    let maxPriority = -1e4;\n    requestedStart.forEach(value => {\n      maxPriority = Math.max(maxPriority, value);\n    });\n    if (maxPriority === priority) {\n      this.capturedId = id;\n      requestedStart.clear();\n      const event = new CustomEvent('ionGestureCaptured', {\n        detail: {\n          gestureName\n        }\n      });\n      document.dispatchEvent(event);\n      return true;\n    }\n    requestedStart.delete(id);\n    return false;\n  }\n  release(id) {\n    this.requestedStart.delete(id);\n    if (this.capturedId === id) {\n      this.capturedId = undefined;\n    }\n  }\n  disableGesture(gestureName, id) {\n    let set = this.disabledGestures.get(gestureName);\n    if (set === undefined) {\n      set = new Set();\n      this.disabledGestures.set(gestureName, set);\n    }\n    set.add(id);\n  }\n  enableGesture(gestureName, id) {\n    const set = this.disabledGestures.get(gestureName);\n    if (set !== undefined) {\n      set.delete(id);\n    }\n  }\n  disableScroll(id) {\n    this.disabledScroll.add(id);\n    if (this.disabledScroll.size === 1) {\n      document.body.classList.add(BACKDROP_NO_SCROLL);\n    }\n  }\n  enableScroll(id) {\n    this.disabledScroll.delete(id);\n    if (this.disabledScroll.size === 0) {\n      document.body.classList.remove(BACKDROP_NO_SCROLL);\n    }\n  }\n  canStart(gestureName) {\n    if (this.capturedId !== undefined) {\n      // a gesture already captured\n      return false;\n    }\n    if (this.isDisabled(gestureName)) {\n      return false;\n    }\n    return true;\n  }\n  isCaptured() {\n    return this.capturedId !== undefined;\n  }\n  isScrollDisabled() {\n    return this.disabledScroll.size > 0;\n  }\n  isDisabled(gestureName) {\n    const disabled = this.disabledGestures.get(gestureName);\n    if (disabled && disabled.size > 0) {\n      return true;\n    }\n    return false;\n  }\n  newID() {\n    this.gestureId++;\n    return this.gestureId;\n  }\n}\nclass GestureDelegate {\n  constructor(ctrl, id, name, priority, disableScroll) {\n    this.id = id;\n    this.name = name;\n    this.disableScroll = disableScroll;\n    this.priority = priority * 1000000 + id;\n    this.ctrl = ctrl;\n  }\n  canStart() {\n    if (!this.ctrl) {\n      return false;\n    }\n    return this.ctrl.canStart(this.name);\n  }\n  start() {\n    if (!this.ctrl) {\n      return false;\n    }\n    return this.ctrl.start(this.name, this.id, this.priority);\n  }\n  capture() {\n    if (!this.ctrl) {\n      return false;\n    }\n    const captured = this.ctrl.capture(this.name, this.id, this.priority);\n    if (captured && this.disableScroll) {\n      this.ctrl.disableScroll(this.id);\n    }\n    return captured;\n  }\n  release() {\n    if (this.ctrl) {\n      this.ctrl.release(this.id);\n      if (this.disableScroll) {\n        this.ctrl.enableScroll(this.id);\n      }\n    }\n  }\n  destroy() {\n    this.release();\n    this.ctrl = undefined;\n  }\n}\nclass BlockerDelegate {\n  constructor(ctrl, id, disable, disableScroll) {\n    this.id = id;\n    this.disable = disable;\n    this.disableScroll = disableScroll;\n    this.ctrl = ctrl;\n  }\n  block() {\n    if (!this.ctrl) {\n      return;\n    }\n    if (this.disable) {\n      for (const gesture of this.disable) {\n        this.ctrl.disableGesture(gesture, this.id);\n      }\n    }\n    if (this.disableScroll) {\n      this.ctrl.disableScroll(this.id);\n    }\n  }\n  unblock() {\n    if (!this.ctrl) {\n      return;\n    }\n    if (this.disable) {\n      for (const gesture of this.disable) {\n        this.ctrl.enableGesture(gesture, this.id);\n      }\n    }\n    if (this.disableScroll) {\n      this.ctrl.enableScroll(this.id);\n    }\n  }\n  destroy() {\n    this.unblock();\n    this.ctrl = undefined;\n  }\n}\nconst BACKDROP_NO_SCROLL = 'backdrop-no-scroll';\nconst GESTURE_CONTROLLER = new GestureController();\nexport { BACKDROP_NO_SCROLL as B, GESTURE_CONTROLLER as G };", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { G as GESTURE_CONTROLLER } from './gesture-controller.js';\nconst addEventListener = (el,\n// TODO(FW-2832): type\neventName, callback, opts) => {\n  // use event listener options when supported\n  // otherwise it's just a boolean for the \"capture\" arg\n  const listenerOpts = supportsPassive(el) ? {\n    capture: false,\n    passive: !!opts.passive\n  } : false;\n  let add;\n  let remove;\n  if (el['__zone_symbol__addEventListener']) {\n    add = '__zone_symbol__addEventListener';\n    remove = '__zone_symbol__removeEventListener';\n  } else {\n    add = 'addEventListener';\n    remove = 'removeEventListener';\n  }\n  el[add](eventName, callback, listenerOpts);\n  return () => {\n    el[remove](eventName, callback, listenerOpts);\n  };\n};\nconst supportsPassive = node => {\n  if (_sPassive === undefined) {\n    try {\n      const opts = Object.defineProperty({}, 'passive', {\n        get: () => {\n          _sPassive = true;\n        }\n      });\n      node.addEventListener('optsTest', () => {\n        return;\n      }, opts);\n    } catch (e) {\n      _sPassive = false;\n    }\n  }\n  return !!_sPassive;\n};\nlet _sPassive;\nconst MOUSE_WAIT = 2000;\n// TODO(FW-2832): types\nconst createPointerEvents = (el, pointerDown, pointerMove, pointerUp, options) => {\n  let rmTouchStart;\n  let rmTouchMove;\n  let rmTouchEnd;\n  let rmTouchCancel;\n  let rmMouseStart;\n  let rmMouseMove;\n  let rmMouseUp;\n  let lastTouchEvent = 0;\n  const handleTouchStart = ev => {\n    lastTouchEvent = Date.now() + MOUSE_WAIT;\n    if (!pointerDown(ev)) {\n      return;\n    }\n    if (!rmTouchMove && pointerMove) {\n      rmTouchMove = addEventListener(el, 'touchmove', pointerMove, options);\n    }\n    /**\n     * Events are dispatched on the element that is tapped and bubble up to\n     * the reference element in the gesture. In the event that the element this\n     * event was first dispatched on is removed from the DOM, the event will no\n     * longer bubble up to our reference element. This leaves the gesture in an\n     * unusable state. To account for this, the touchend and touchcancel listeners\n     * should be added to the event target so that they still fire even if the target\n     * is removed from the DOM.\n     */\n    if (!rmTouchEnd) {\n      rmTouchEnd = addEventListener(ev.target, 'touchend', handleTouchEnd, options);\n    }\n    if (!rmTouchCancel) {\n      rmTouchCancel = addEventListener(ev.target, 'touchcancel', handleTouchEnd, options);\n    }\n  };\n  const handleMouseDown = ev => {\n    if (lastTouchEvent > Date.now()) {\n      return;\n    }\n    if (!pointerDown(ev)) {\n      return;\n    }\n    if (!rmMouseMove && pointerMove) {\n      rmMouseMove = addEventListener(getDocument(el), 'mousemove', pointerMove, options);\n    }\n    if (!rmMouseUp) {\n      rmMouseUp = addEventListener(getDocument(el), 'mouseup', handleMouseUp, options);\n    }\n  };\n  const handleTouchEnd = ev => {\n    stopTouch();\n    if (pointerUp) {\n      pointerUp(ev);\n    }\n  };\n  const handleMouseUp = ev => {\n    stopMouse();\n    if (pointerUp) {\n      pointerUp(ev);\n    }\n  };\n  const stopTouch = () => {\n    if (rmTouchMove) {\n      rmTouchMove();\n    }\n    if (rmTouchEnd) {\n      rmTouchEnd();\n    }\n    if (rmTouchCancel) {\n      rmTouchCancel();\n    }\n    rmTouchMove = rmTouchEnd = rmTouchCancel = undefined;\n  };\n  const stopMouse = () => {\n    if (rmMouseMove) {\n      rmMouseMove();\n    }\n    if (rmMouseUp) {\n      rmMouseUp();\n    }\n    rmMouseMove = rmMouseUp = undefined;\n  };\n  const stop = () => {\n    stopTouch();\n    stopMouse();\n  };\n  const enable = (isEnabled = true) => {\n    if (!isEnabled) {\n      if (rmTouchStart) {\n        rmTouchStart();\n      }\n      if (rmMouseStart) {\n        rmMouseStart();\n      }\n      rmTouchStart = rmMouseStart = undefined;\n      stop();\n    } else {\n      if (!rmTouchStart) {\n        rmTouchStart = addEventListener(el, 'touchstart', handleTouchStart, options);\n      }\n      if (!rmMouseStart) {\n        rmMouseStart = addEventListener(el, 'mousedown', handleMouseDown, options);\n      }\n    }\n  };\n  const destroy = () => {\n    enable(false);\n    pointerUp = pointerMove = pointerDown = undefined;\n  };\n  return {\n    enable,\n    stop,\n    destroy\n  };\n};\nconst getDocument = node => {\n  return node instanceof Document ? node : node.ownerDocument;\n};\nconst createPanRecognizer = (direction, thresh, maxAngle) => {\n  const radians = maxAngle * (Math.PI / 180);\n  const isDirX = direction === 'x';\n  const maxCosine = Math.cos(radians);\n  const threshold = thresh * thresh;\n  let startX = 0;\n  let startY = 0;\n  let dirty = false;\n  let isPan = 0;\n  return {\n    start(x, y) {\n      startX = x;\n      startY = y;\n      isPan = 0;\n      dirty = true;\n    },\n    detect(x, y) {\n      if (!dirty) {\n        return false;\n      }\n      const deltaX = x - startX;\n      const deltaY = y - startY;\n      const distance = deltaX * deltaX + deltaY * deltaY;\n      if (distance < threshold) {\n        return false;\n      }\n      const hypotenuse = Math.sqrt(distance);\n      const cosine = (isDirX ? deltaX : deltaY) / hypotenuse;\n      if (cosine > maxCosine) {\n        isPan = 1;\n      } else if (cosine < -maxCosine) {\n        isPan = -1;\n      } else {\n        isPan = 0;\n      }\n      dirty = false;\n      return true;\n    },\n    isGesture() {\n      return isPan !== 0;\n    },\n    getDirection() {\n      return isPan;\n    }\n  };\n};\n\n// TODO(FW-2832): types\nconst createGesture = config => {\n  let hasCapturedPan = false;\n  let hasStartedPan = false;\n  let hasFiredStart = true;\n  let isMoveQueued = false;\n  const finalConfig = Object.assign({\n    disableScroll: false,\n    direction: 'x',\n    gesturePriority: 0,\n    passive: true,\n    maxAngle: 40,\n    threshold: 10\n  }, config);\n  const canStart = finalConfig.canStart;\n  const onWillStart = finalConfig.onWillStart;\n  const onStart = finalConfig.onStart;\n  const onEnd = finalConfig.onEnd;\n  const notCaptured = finalConfig.notCaptured;\n  const onMove = finalConfig.onMove;\n  const threshold = finalConfig.threshold;\n  const passive = finalConfig.passive;\n  const blurOnStart = finalConfig.blurOnStart;\n  const detail = {\n    type: 'pan',\n    startX: 0,\n    startY: 0,\n    startTime: 0,\n    currentX: 0,\n    currentY: 0,\n    velocityX: 0,\n    velocityY: 0,\n    deltaX: 0,\n    deltaY: 0,\n    currentTime: 0,\n    event: undefined,\n    data: undefined\n  };\n  const pan = createPanRecognizer(finalConfig.direction, finalConfig.threshold, finalConfig.maxAngle);\n  const gesture = GESTURE_CONTROLLER.createGesture({\n    name: config.gestureName,\n    priority: config.gesturePriority,\n    disableScroll: config.disableScroll\n  });\n  const pointerDown = ev => {\n    const timeStamp = now(ev);\n    if (hasStartedPan || !hasFiredStart) {\n      return false;\n    }\n    updateDetail(ev, detail);\n    detail.startX = detail.currentX;\n    detail.startY = detail.currentY;\n    detail.startTime = detail.currentTime = timeStamp;\n    detail.velocityX = detail.velocityY = detail.deltaX = detail.deltaY = 0;\n    detail.event = ev;\n    // Check if gesture can start\n    if (canStart && canStart(detail) === false) {\n      return false;\n    }\n    // Release fallback\n    gesture.release();\n    // Start gesture\n    if (!gesture.start()) {\n      return false;\n    }\n    hasStartedPan = true;\n    if (threshold === 0) {\n      return tryToCapturePan();\n    }\n    pan.start(detail.startX, detail.startY);\n    return true;\n  };\n  const pointerMove = ev => {\n    // fast path, if gesture is currently captured\n    // do minimum job to get user-land even dispatched\n    if (hasCapturedPan) {\n      if (!isMoveQueued && hasFiredStart) {\n        isMoveQueued = true;\n        calcGestureData(detail, ev);\n        requestAnimationFrame(fireOnMove);\n      }\n      return;\n    }\n    // gesture is currently being detected\n    calcGestureData(detail, ev);\n    if (pan.detect(detail.currentX, detail.currentY)) {\n      if (!pan.isGesture() || !tryToCapturePan()) {\n        abortGesture();\n      }\n    }\n  };\n  const fireOnMove = () => {\n    // Since fireOnMove is called inside a RAF, onEnd() might be called,\n    // we must double check hasCapturedPan\n    if (!hasCapturedPan) {\n      return;\n    }\n    isMoveQueued = false;\n    if (onMove) {\n      onMove(detail);\n    }\n  };\n  const tryToCapturePan = () => {\n    if (!gesture.capture()) {\n      return false;\n    }\n    hasCapturedPan = true;\n    hasFiredStart = false;\n    // reset start position since the real user-land event starts here\n    // If the pan detector threshold is big, not resetting the start position\n    // will cause a jump in the animation equal to the detector threshold.\n    // the array of positions used to calculate the gesture velocity does not\n    // need to be cleaned, more points in the positions array always results in a\n    // more accurate value of the velocity.\n    detail.startX = detail.currentX;\n    detail.startY = detail.currentY;\n    detail.startTime = detail.currentTime;\n    if (onWillStart) {\n      onWillStart(detail).then(fireOnStart);\n    } else {\n      fireOnStart();\n    }\n    return true;\n  };\n  const blurActiveElement = () => {\n    if (typeof document !== 'undefined') {\n      const activeElement = document.activeElement;\n      if (activeElement === null || activeElement === void 0 ? void 0 : activeElement.blur) {\n        activeElement.blur();\n      }\n    }\n  };\n  const fireOnStart = () => {\n    if (blurOnStart) {\n      blurActiveElement();\n    }\n    if (onStart) {\n      onStart(detail);\n    }\n    hasFiredStart = true;\n  };\n  const reset = () => {\n    hasCapturedPan = false;\n    hasStartedPan = false;\n    isMoveQueued = false;\n    hasFiredStart = true;\n    gesture.release();\n  };\n  // END *************************\n  const pointerUp = ev => {\n    const tmpHasCaptured = hasCapturedPan;\n    const tmpHasFiredStart = hasFiredStart;\n    reset();\n    if (!tmpHasFiredStart) {\n      return;\n    }\n    calcGestureData(detail, ev);\n    // Try to capture press\n    if (tmpHasCaptured) {\n      if (onEnd) {\n        onEnd(detail);\n      }\n      return;\n    }\n    // Not captured any event\n    if (notCaptured) {\n      notCaptured(detail);\n    }\n  };\n  const pointerEvents = createPointerEvents(finalConfig.el, pointerDown, pointerMove, pointerUp, {\n    passive\n  });\n  const abortGesture = () => {\n    reset();\n    pointerEvents.stop();\n    if (notCaptured) {\n      notCaptured(detail);\n    }\n  };\n  return {\n    enable(enable = true) {\n      if (!enable) {\n        if (hasCapturedPan) {\n          pointerUp(undefined);\n        }\n        reset();\n      }\n      pointerEvents.enable(enable);\n    },\n    destroy() {\n      gesture.destroy();\n      pointerEvents.destroy();\n    }\n  };\n};\nconst calcGestureData = (detail, ev) => {\n  if (!ev) {\n    return;\n  }\n  const prevX = detail.currentX;\n  const prevY = detail.currentY;\n  const prevT = detail.currentTime;\n  updateDetail(ev, detail);\n  const currentX = detail.currentX;\n  const currentY = detail.currentY;\n  const timestamp = detail.currentTime = now(ev);\n  const timeDelta = timestamp - prevT;\n  if (timeDelta > 0 && timeDelta < 100) {\n    const velocityX = (currentX - prevX) / timeDelta;\n    const velocityY = (currentY - prevY) / timeDelta;\n    detail.velocityX = velocityX * 0.7 + detail.velocityX * 0.3;\n    detail.velocityY = velocityY * 0.7 + detail.velocityY * 0.3;\n  }\n  detail.deltaX = currentX - detail.startX;\n  detail.deltaY = currentY - detail.startY;\n  detail.event = ev;\n};\nconst updateDetail = (ev, detail) => {\n  // get X coordinates for either a mouse click\n  // or a touch depending on the given event\n  let x = 0;\n  let y = 0;\n  if (ev) {\n    const changedTouches = ev.changedTouches;\n    if (changedTouches && changedTouches.length > 0) {\n      const touch = changedTouches[0];\n      x = touch.clientX;\n      y = touch.clientY;\n    } else if (ev.pageX !== undefined) {\n      x = ev.pageX;\n      y = ev.pageY;\n    }\n  }\n  detail.currentX = x;\n  detail.currentY = y;\n};\nconst now = ev => {\n  return ev.timeStamp || Date.now();\n};\nexport { GESTURE_CONTROLLER, createGesture };"], "mappings": ";AAGA,IAAM,oBAAN,MAAwB;AAAA,EACtB,cAAc;AACZ,SAAK,YAAY;AACjB,SAAK,iBAAiB,oBAAI,IAAI;AAC9B,SAAK,mBAAmB,oBAAI,IAAI;AAChC,SAAK,iBAAiB,oBAAI,IAAI;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,QAAQ;AACpB,QAAI;AACJ,WAAO,IAAI,gBAAgB,MAAM,KAAK,MAAM,GAAG,OAAO,OAAO,KAAK,OAAO,cAAc,QAAQ,OAAO,SAAS,KAAK,GAAG,CAAC,CAAC,OAAO,aAAa;AAAA,EAC/I;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,OAAO,CAAC,GAAG;AACvB,WAAO,IAAI,gBAAgB,MAAM,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,KAAK,aAAa;AAAA,EACnF;AAAA,EACA,MAAM,aAAa,IAAI,UAAU;AAC/B,QAAI,CAAC,KAAK,SAAS,WAAW,GAAG;AAC/B,WAAK,eAAe,OAAO,EAAE;AAC7B,aAAO;AAAA,IACT;AACA,SAAK,eAAe,IAAI,IAAI,QAAQ;AACpC,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,aAAa,IAAI,UAAU;AACjC,QAAI,CAAC,KAAK,MAAM,aAAa,IAAI,QAAQ,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,KAAK;AAC5B,QAAI,cAAc;AAClB,mBAAe,QAAQ,WAAS;AAC9B,oBAAc,KAAK,IAAI,aAAa,KAAK;AAAA,IAC3C,CAAC;AACD,QAAI,gBAAgB,UAAU;AAC5B,WAAK,aAAa;AAClB,qBAAe,MAAM;AACrB,YAAM,QAAQ,IAAI,YAAY,sBAAsB;AAAA,QAClD,QAAQ;AAAA,UACN;AAAA,QACF;AAAA,MACF,CAAC;AACD,eAAS,cAAc,KAAK;AAC5B,aAAO;AAAA,IACT;AACA,mBAAe,OAAO,EAAE;AACxB,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,IAAI;AACV,SAAK,eAAe,OAAO,EAAE;AAC7B,QAAI,KAAK,eAAe,IAAI;AAC1B,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,eAAe,aAAa,IAAI;AAC9B,QAAI,MAAM,KAAK,iBAAiB,IAAI,WAAW;AAC/C,QAAI,QAAQ,QAAW;AACrB,YAAM,oBAAI,IAAI;AACd,WAAK,iBAAiB,IAAI,aAAa,GAAG;AAAA,IAC5C;AACA,QAAI,IAAI,EAAE;AAAA,EACZ;AAAA,EACA,cAAc,aAAa,IAAI;AAC7B,UAAM,MAAM,KAAK,iBAAiB,IAAI,WAAW;AACjD,QAAI,QAAQ,QAAW;AACrB,UAAI,OAAO,EAAE;AAAA,IACf;AAAA,EACF;AAAA,EACA,cAAc,IAAI;AAChB,SAAK,eAAe,IAAI,EAAE;AAC1B,QAAI,KAAK,eAAe,SAAS,GAAG;AAClC,eAAS,KAAK,UAAU,IAAI,kBAAkB;AAAA,IAChD;AAAA,EACF;AAAA,EACA,aAAa,IAAI;AACf,SAAK,eAAe,OAAO,EAAE;AAC7B,QAAI,KAAK,eAAe,SAAS,GAAG;AAClC,eAAS,KAAK,UAAU,OAAO,kBAAkB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,SAAS,aAAa;AACpB,QAAI,KAAK,eAAe,QAAW;AAEjC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,WAAW,WAAW,GAAG;AAChC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AACX,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,eAAe,OAAO;AAAA,EACpC;AAAA,EACA,WAAW,aAAa;AACtB,UAAM,WAAW,KAAK,iBAAiB,IAAI,WAAW;AACtD,QAAI,YAAY,SAAS,OAAO,GAAG;AACjC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,SAAK;AACL,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,MAAM,IAAI,MAAM,UAAU,eAAe;AACnD,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,gBAAgB;AACrB,SAAK,WAAW,WAAW,MAAU;AACrC,SAAK,OAAO;AAAA,EACd;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,MAAM;AACd,aAAO;AAAA,IACT;AACA,WAAO,KAAK,KAAK,SAAS,KAAK,IAAI;AAAA,EACrC;AAAA,EACA,QAAQ;AACN,QAAI,CAAC,KAAK,MAAM;AACd,aAAO;AAAA,IACT;AACA,WAAO,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,QAAQ;AAAA,EAC1D;AAAA,EACA,UAAU;AACR,QAAI,CAAC,KAAK,MAAM;AACd,aAAO;AAAA,IACT;AACA,UAAM,WAAW,KAAK,KAAK,QAAQ,KAAK,MAAM,KAAK,IAAI,KAAK,QAAQ;AACpE,QAAI,YAAY,KAAK,eAAe;AAClC,WAAK,KAAK,cAAc,KAAK,EAAE;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,QAAQ,KAAK,EAAE;AACzB,UAAI,KAAK,eAAe;AACtB,aAAK,KAAK,aAAa,KAAK,EAAE;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,MAAM,IAAI,SAAS,eAAe;AAC5C,SAAK,KAAK;AACV,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,QAAQ;AACN,QAAI,CAAC,KAAK,MAAM;AACd;AAAA,IACF;AACA,QAAI,KAAK,SAAS;AAChB,iBAAW,WAAW,KAAK,SAAS;AAClC,aAAK,KAAK,eAAe,SAAS,KAAK,EAAE;AAAA,MAC3C;AAAA,IACF;AACA,QAAI,KAAK,eAAe;AACtB,WAAK,KAAK,cAAc,KAAK,EAAE;AAAA,IACjC;AAAA,EACF;AAAA,EACA,UAAU;AACR,QAAI,CAAC,KAAK,MAAM;AACd;AAAA,IACF;AACA,QAAI,KAAK,SAAS;AAChB,iBAAW,WAAW,KAAK,SAAS;AAClC,aAAK,KAAK,cAAc,SAAS,KAAK,EAAE;AAAA,MAC1C;AAAA,IACF;AACA,QAAI,KAAK,eAAe;AACtB,WAAK,KAAK,aAAa,KAAK,EAAE;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,qBAAqB;AAC3B,IAAM,qBAAqB,IAAI,kBAAkB;;;AChMjD,IAAM,mBAAmB,CAAC,IAE1B,WAAW,UAAU,SAAS;AAG5B,QAAM,eAAe,gBAAgB,EAAE,IAAI;AAAA,IACzC,SAAS;AAAA,IACT,SAAS,CAAC,CAAC,KAAK;AAAA,EAClB,IAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,GAAG,iCAAiC,GAAG;AACzC,UAAM;AACN,aAAS;AAAA,EACX,OAAO;AACL,UAAM;AACN,aAAS;AAAA,EACX;AACA,KAAG,GAAG,EAAE,WAAW,UAAU,YAAY;AACzC,SAAO,MAAM;AACX,OAAG,MAAM,EAAE,WAAW,UAAU,YAAY;AAAA,EAC9C;AACF;AACA,IAAM,kBAAkB,UAAQ;AAC9B,MAAI,cAAc,QAAW;AAC3B,QAAI;AACF,YAAM,OAAO,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,QAChD,KAAK,MAAM;AACT,sBAAY;AAAA,QACd;AAAA,MACF,CAAC;AACD,WAAK,iBAAiB,YAAY,MAAM;AACtC;AAAA,MACF,GAAG,IAAI;AAAA,IACT,SAAS,GAAG;AACV,kBAAY;AAAA,IACd;AAAA,EACF;AACA,SAAO,CAAC,CAAC;AACX;AACA,IAAI;AACJ,IAAM,aAAa;AAEnB,IAAM,sBAAsB,CAAC,IAAI,aAAa,aAAa,WAAW,YAAY;AAChF,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,iBAAiB;AACrB,QAAM,mBAAmB,QAAM;AAC7B,qBAAiB,KAAK,IAAI,IAAI;AAC9B,QAAI,CAAC,YAAY,EAAE,GAAG;AACpB;AAAA,IACF;AACA,QAAI,CAAC,eAAe,aAAa;AAC/B,oBAAc,iBAAiB,IAAI,aAAa,aAAa,OAAO;AAAA,IACtE;AAUA,QAAI,CAAC,YAAY;AACf,mBAAa,iBAAiB,GAAG,QAAQ,YAAY,gBAAgB,OAAO;AAAA,IAC9E;AACA,QAAI,CAAC,eAAe;AAClB,sBAAgB,iBAAiB,GAAG,QAAQ,eAAe,gBAAgB,OAAO;AAAA,IACpF;AAAA,EACF;AACA,QAAM,kBAAkB,QAAM;AAC5B,QAAI,iBAAiB,KAAK,IAAI,GAAG;AAC/B;AAAA,IACF;AACA,QAAI,CAAC,YAAY,EAAE,GAAG;AACpB;AAAA,IACF;AACA,QAAI,CAAC,eAAe,aAAa;AAC/B,oBAAc,iBAAiB,YAAY,EAAE,GAAG,aAAa,aAAa,OAAO;AAAA,IACnF;AACA,QAAI,CAAC,WAAW;AACd,kBAAY,iBAAiB,YAAY,EAAE,GAAG,WAAW,eAAe,OAAO;AAAA,IACjF;AAAA,EACF;AACA,QAAM,iBAAiB,QAAM;AAC3B,cAAU;AACV,QAAI,WAAW;AACb,gBAAU,EAAE;AAAA,IACd;AAAA,EACF;AACA,QAAM,gBAAgB,QAAM;AAC1B,cAAU;AACV,QAAI,WAAW;AACb,gBAAU,EAAE;AAAA,IACd;AAAA,EACF;AACA,QAAM,YAAY,MAAM;AACtB,QAAI,aAAa;AACf,kBAAY;AAAA,IACd;AACA,QAAI,YAAY;AACd,iBAAW;AAAA,IACb;AACA,QAAI,eAAe;AACjB,oBAAc;AAAA,IAChB;AACA,kBAAc,aAAa,gBAAgB;AAAA,EAC7C;AACA,QAAM,YAAY,MAAM;AACtB,QAAI,aAAa;AACf,kBAAY;AAAA,IACd;AACA,QAAI,WAAW;AACb,gBAAU;AAAA,IACZ;AACA,kBAAc,YAAY;AAAA,EAC5B;AACA,QAAM,OAAO,MAAM;AACjB,cAAU;AACV,cAAU;AAAA,EACZ;AACA,QAAM,SAAS,CAAC,YAAY,SAAS;AACnC,QAAI,CAAC,WAAW;AACd,UAAI,cAAc;AAChB,qBAAa;AAAA,MACf;AACA,UAAI,cAAc;AAChB,qBAAa;AAAA,MACf;AACA,qBAAe,eAAe;AAC9B,WAAK;AAAA,IACP,OAAO;AACL,UAAI,CAAC,cAAc;AACjB,uBAAe,iBAAiB,IAAI,cAAc,kBAAkB,OAAO;AAAA,MAC7E;AACA,UAAI,CAAC,cAAc;AACjB,uBAAe,iBAAiB,IAAI,aAAa,iBAAiB,OAAO;AAAA,MAC3E;AAAA,IACF;AAAA,EACF;AACA,QAAM,UAAU,MAAM;AACpB,WAAO,KAAK;AACZ,gBAAY,cAAc,cAAc;AAAA,EAC1C;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,cAAc,UAAQ;AAC1B,SAAO,gBAAgB,WAAW,OAAO,KAAK;AAChD;AACA,IAAM,sBAAsB,CAAC,WAAW,QAAQ,aAAa;AAC3D,QAAM,UAAU,YAAY,KAAK,KAAK;AACtC,QAAM,SAAS,cAAc;AAC7B,QAAM,YAAY,KAAK,IAAI,OAAO;AAClC,QAAM,YAAY,SAAS;AAC3B,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,SAAO;AAAA,IACL,MAAM,GAAG,GAAG;AACV,eAAS;AACT,eAAS;AACT,cAAQ;AACR,cAAQ;AAAA,IACV;AAAA,IACA,OAAO,GAAG,GAAG;AACX,UAAI,CAAC,OAAO;AACV,eAAO;AAAA,MACT;AACA,YAAM,SAAS,IAAI;AACnB,YAAM,SAAS,IAAI;AACnB,YAAM,WAAW,SAAS,SAAS,SAAS;AAC5C,UAAI,WAAW,WAAW;AACxB,eAAO;AAAA,MACT;AACA,YAAM,aAAa,KAAK,KAAK,QAAQ;AACrC,YAAM,UAAU,SAAS,SAAS,UAAU;AAC5C,UAAI,SAAS,WAAW;AACtB,gBAAQ;AAAA,MACV,WAAW,SAAS,CAAC,WAAW;AAC9B,gBAAQ;AAAA,MACV,OAAO;AACL,gBAAQ;AAAA,MACV;AACA,cAAQ;AACR,aAAO;AAAA,IACT;AAAA,IACA,YAAY;AACV,aAAO,UAAU;AAAA,IACnB;AAAA,IACA,eAAe;AACb,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAGA,IAAM,gBAAgB,YAAU;AAC9B,MAAI,iBAAiB;AACrB,MAAI,gBAAgB;AACpB,MAAI,gBAAgB;AACpB,MAAI,eAAe;AACnB,QAAM,cAAc,OAAO,OAAO;AAAA,IAChC,eAAe;AAAA,IACf,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW;AAAA,EACb,GAAG,MAAM;AACT,QAAM,WAAW,YAAY;AAC7B,QAAM,cAAc,YAAY;AAChC,QAAM,UAAU,YAAY;AAC5B,QAAM,QAAQ,YAAY;AAC1B,QAAM,cAAc,YAAY;AAChC,QAAM,SAAS,YAAY;AAC3B,QAAM,YAAY,YAAY;AAC9B,QAAM,UAAU,YAAY;AAC5B,QAAM,cAAc,YAAY;AAChC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AACA,QAAM,MAAM,oBAAoB,YAAY,WAAW,YAAY,WAAW,YAAY,QAAQ;AAClG,QAAM,UAAU,mBAAmB,cAAc;AAAA,IAC/C,MAAM,OAAO;AAAA,IACb,UAAU,OAAO;AAAA,IACjB,eAAe,OAAO;AAAA,EACxB,CAAC;AACD,QAAM,cAAc,QAAM;AACxB,UAAM,YAAY,IAAI,EAAE;AACxB,QAAI,iBAAiB,CAAC,eAAe;AACnC,aAAO;AAAA,IACT;AACA,iBAAa,IAAI,MAAM;AACvB,WAAO,SAAS,OAAO;AACvB,WAAO,SAAS,OAAO;AACvB,WAAO,YAAY,OAAO,cAAc;AACxC,WAAO,YAAY,OAAO,YAAY,OAAO,SAAS,OAAO,SAAS;AACtE,WAAO,QAAQ;AAEf,QAAI,YAAY,SAAS,MAAM,MAAM,OAAO;AAC1C,aAAO;AAAA,IACT;AAEA,YAAQ,QAAQ;AAEhB,QAAI,CAAC,QAAQ,MAAM,GAAG;AACpB,aAAO;AAAA,IACT;AACA,oBAAgB;AAChB,QAAI,cAAc,GAAG;AACnB,aAAO,gBAAgB;AAAA,IACzB;AACA,QAAI,MAAM,OAAO,QAAQ,OAAO,MAAM;AACtC,WAAO;AAAA,EACT;AACA,QAAM,cAAc,QAAM;AAGxB,QAAI,gBAAgB;AAClB,UAAI,CAAC,gBAAgB,eAAe;AAClC,uBAAe;AACf,wBAAgB,QAAQ,EAAE;AAC1B,8BAAsB,UAAU;AAAA,MAClC;AACA;AAAA,IACF;AAEA,oBAAgB,QAAQ,EAAE;AAC1B,QAAI,IAAI,OAAO,OAAO,UAAU,OAAO,QAAQ,GAAG;AAChD,UAAI,CAAC,IAAI,UAAU,KAAK,CAAC,gBAAgB,GAAG;AAC1C,qBAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,MAAM;AAGvB,QAAI,CAAC,gBAAgB;AACnB;AAAA,IACF;AACA,mBAAe;AACf,QAAI,QAAQ;AACV,aAAO,MAAM;AAAA,IACf;AAAA,EACF;AACA,QAAM,kBAAkB,MAAM;AAC5B,QAAI,CAAC,QAAQ,QAAQ,GAAG;AACtB,aAAO;AAAA,IACT;AACA,qBAAiB;AACjB,oBAAgB;AAOhB,WAAO,SAAS,OAAO;AACvB,WAAO,SAAS,OAAO;AACvB,WAAO,YAAY,OAAO;AAC1B,QAAI,aAAa;AACf,kBAAY,MAAM,EAAE,KAAK,WAAW;AAAA,IACtC,OAAO;AACL,kBAAY;AAAA,IACd;AACA,WAAO;AAAA,EACT;AACA,QAAM,oBAAoB,MAAM;AAC9B,QAAI,OAAO,aAAa,aAAa;AACnC,YAAM,gBAAgB,SAAS;AAC/B,UAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,MAAM;AACpF,sBAAc,KAAK;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,QAAM,cAAc,MAAM;AACxB,QAAI,aAAa;AACf,wBAAkB;AAAA,IACpB;AACA,QAAI,SAAS;AACX,cAAQ,MAAM;AAAA,IAChB;AACA,oBAAgB;AAAA,EAClB;AACA,QAAM,QAAQ,MAAM;AAClB,qBAAiB;AACjB,oBAAgB;AAChB,mBAAe;AACf,oBAAgB;AAChB,YAAQ,QAAQ;AAAA,EAClB;AAEA,QAAM,YAAY,QAAM;AACtB,UAAM,iBAAiB;AACvB,UAAM,mBAAmB;AACzB,UAAM;AACN,QAAI,CAAC,kBAAkB;AACrB;AAAA,IACF;AACA,oBAAgB,QAAQ,EAAE;AAE1B,QAAI,gBAAgB;AAClB,UAAI,OAAO;AACT,cAAM,MAAM;AAAA,MACd;AACA;AAAA,IACF;AAEA,QAAI,aAAa;AACf,kBAAY,MAAM;AAAA,IACpB;AAAA,EACF;AACA,QAAM,gBAAgB,oBAAoB,YAAY,IAAI,aAAa,aAAa,WAAW;AAAA,IAC7F;AAAA,EACF,CAAC;AACD,QAAM,eAAe,MAAM;AACzB,UAAM;AACN,kBAAc,KAAK;AACnB,QAAI,aAAa;AACf,kBAAY,MAAM;AAAA,IACpB;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO,SAAS,MAAM;AACpB,UAAI,CAAC,QAAQ;AACX,YAAI,gBAAgB;AAClB,oBAAU,MAAS;AAAA,QACrB;AACA,cAAM;AAAA,MACR;AACA,oBAAc,OAAO,MAAM;AAAA,IAC7B;AAAA,IACA,UAAU;AACR,cAAQ,QAAQ;AAChB,oBAAc,QAAQ;AAAA,IACxB;AAAA,EACF;AACF;AACA,IAAM,kBAAkB,CAAC,QAAQ,OAAO;AACtC,MAAI,CAAC,IAAI;AACP;AAAA,EACF;AACA,QAAM,QAAQ,OAAO;AACrB,QAAM,QAAQ,OAAO;AACrB,QAAM,QAAQ,OAAO;AACrB,eAAa,IAAI,MAAM;AACvB,QAAM,WAAW,OAAO;AACxB,QAAM,WAAW,OAAO;AACxB,QAAM,YAAY,OAAO,cAAc,IAAI,EAAE;AAC7C,QAAM,YAAY,YAAY;AAC9B,MAAI,YAAY,KAAK,YAAY,KAAK;AACpC,UAAM,aAAa,WAAW,SAAS;AACvC,UAAM,aAAa,WAAW,SAAS;AACvC,WAAO,YAAY,YAAY,MAAM,OAAO,YAAY;AACxD,WAAO,YAAY,YAAY,MAAM,OAAO,YAAY;AAAA,EAC1D;AACA,SAAO,SAAS,WAAW,OAAO;AAClC,SAAO,SAAS,WAAW,OAAO;AAClC,SAAO,QAAQ;AACjB;AACA,IAAM,eAAe,CAAC,IAAI,WAAW;AAGnC,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACN,UAAM,iBAAiB,GAAG;AAC1B,QAAI,kBAAkB,eAAe,SAAS,GAAG;AAC/C,YAAM,QAAQ,eAAe,CAAC;AAC9B,UAAI,MAAM;AACV,UAAI,MAAM;AAAA,IACZ,WAAW,GAAG,UAAU,QAAW;AACjC,UAAI,GAAG;AACP,UAAI,GAAG;AAAA,IACT;AAAA,EACF;AACA,SAAO,WAAW;AAClB,SAAO,WAAW;AACpB;AACA,IAAM,MAAM,QAAM;AAChB,SAAO,GAAG,aAAa,KAAK,IAAI;AAClC;", "names": []}