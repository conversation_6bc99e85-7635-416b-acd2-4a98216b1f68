import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-feed',
  templateUrl: './feed.page.html',
  styleUrls: ['./feed.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class FeedPage implements OnInit {

  stories = [
    {
      id: 1,
      username: 'onlywomans_official',
      avatar: 'assets/stories/official.jpg',
      hasStory: true,
      isViewed: false
    },
    {
      id: 2,
      username: 'priya_fashion',
      avatar: 'assets/stories/priya.jpg',
      hasStory: true,
      isViewed: false
    },
    {
      id: 3,
      username: 'beauty_guru_sara',
      avatar: 'assets/stories/sara.jpg',
      hasStory: true,
      isViewed: true
    },
    {
      id: 4,
      username: 'ethnic_elegance',
      avatar: 'assets/stories/ethnic.jpg',
      hasStory: true,
      isViewed: false
    }
  ];

  posts = [
    {
      id: 1,
      user: {
        username: 'fashionista_riya',
        avatar: 'assets/users/riya.jpg',
        isVerified: true
      },
      images: ['assets/posts/post1_1.jpg', 'assets/posts/post1_2.jpg'],
      caption: 'Loving this new ethnic collection! 😍 Perfect for the festive season ✨ #EthnicWear #OnlyWomans #FashionInspo',
      likes: 1247,
      comments: 89,
      timeAgo: '2h',
      isLiked: false,
      isSaved: false,
      location: 'Mumbai, India'
    },
    {
      id: 2,
      user: {
        username: 'beauty_by_meera',
        avatar: 'assets/users/meera.jpg',
        isVerified: false
      },
      images: ['assets/posts/post2_1.jpg'],
      caption: 'New makeup tutorial is live! 💄 Swipe for the final look. Products used are all from @onlywomans_beauty 💕',
      likes: 892,
      comments: 156,
      timeAgo: '4h',
      isLiked: true,
      isSaved: true,
      location: 'Delhi, India'
    },
    {
      id: 3,
      user: {
        username: 'style_with_sneha',
        avatar: 'assets/users/sneha.jpg',
        isVerified: true
      },
      images: ['assets/posts/post3_1.jpg', 'assets/posts/post3_2.jpg', 'assets/posts/post3_3.jpg'],
      caption: 'Office to party transition looks! Which one is your favorite? 1, 2, or 3? 🤔 #OfficeWear #PartyLook #Styling',
      likes: 2156,
      comments: 234,
      timeAgo: '6h',
      isLiked: false,
      isSaved: false,
      location: 'Bangalore, India'
    }
  ];

  constructor() { }

  ngOnInit() {
  }

  onStoryClick(story: any) {
    console.log('Story clicked:', story);
  }

  onLikePost(post: any) {
    post.isLiked = !post.isLiked;
    post.likes += post.isLiked ? 1 : -1;
  }

  onSavePost(post: any) {
    post.isSaved = !post.isSaved;
  }

  onCommentClick(post: any) {
    console.log('Comment on post:', post);
  }

  onShareClick(post: any) {
    console.log('Share post:', post);
  }

  onUserClick(user: any) {
    console.log('User clicked:', user);
  }

  onLocationClick(location: string) {
    console.log('Location clicked:', location);
  }

  onImageDoubleClick(post: any) {
    if (!post.isLiked) {
      this.onLikePost(post);
    }
  }
}
