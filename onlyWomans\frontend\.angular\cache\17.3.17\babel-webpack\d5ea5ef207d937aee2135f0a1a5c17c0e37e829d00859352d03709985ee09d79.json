{"ast": null, "code": "import { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nimport { filter } from './filter';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { take } from './take';\nexport function elementAt(index, defaultValue) {\n  if (index < 0) {\n    throw new ArgumentOutOfRangeError();\n  }\n  const hasDefaultValue = arguments.length >= 2;\n  return source => source.pipe(filter((v, i) => i === index), take(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(() => new ArgumentOutOfRangeError()));\n}", "map": {"version": 3, "names": ["ArgumentOutOfRangeError", "filter", "throwIfEmpty", "defaultIfEmpty", "take", "elementAt", "index", "defaultValue", "hasDefaultValue", "arguments", "length", "source", "pipe", "v", "i"], "sources": ["E:/Fahion/DFashion/onlyWomans/frontend/node_modules/rxjs/dist/esm/internal/operators/elementAt.js"], "sourcesContent": ["import { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nimport { filter } from './filter';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { take } from './take';\nexport function elementAt(index, defaultValue) {\n    if (index < 0) {\n        throw new ArgumentOutOfRangeError();\n    }\n    const hasDefaultValue = arguments.length >= 2;\n    return (source) => source.pipe(filter((v, i) => i === index), take(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(() => new ArgumentOutOfRangeError()));\n}\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,iCAAiC;AACzE,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,IAAI,QAAQ,QAAQ;AAC7B,OAAO,SAASC,SAASA,CAACC,KAAK,EAAEC,YAAY,EAAE;EAC3C,IAAID,KAAK,GAAG,CAAC,EAAE;IACX,MAAM,IAAIN,uBAAuB,CAAC,CAAC;EACvC;EACA,MAAMQ,eAAe,GAAGC,SAAS,CAACC,MAAM,IAAI,CAAC;EAC7C,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAACX,MAAM,CAAC,CAACY,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKR,KAAK,CAAC,EAAEF,IAAI,CAAC,CAAC,CAAC,EAAEI,eAAe,GAAGL,cAAc,CAACI,YAAY,CAAC,GAAGL,YAAY,CAAC,MAAM,IAAIF,uBAAuB,CAAC,CAAC,CAAC,CAAC;AAC9K", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}