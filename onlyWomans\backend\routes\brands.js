const express = require('express');
const router = express.Router();

// Sample brands data
const brands = [
  { id: 1, name: 'StyleVogue', logo: '/assets/brands/stylevogue.jpg', featured: true },
  { id: 2, name: 'EthnicElegance', logo: '/assets/brands/ethnic.jpg', featured: true },
  { id: 3, name: 'LuxeCollection', logo: '/assets/brands/luxe.jpg', featured: false },
  { id: 4, name: 'GlamBeauty', logo: '/assets/brands/glam.jpg', featured: true }
];

// @route   GET /api/brands
// @desc    Get all brands
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { featured } = req.query;
    let filteredBrands = brands;
    
    if (featured === 'true') {
      filteredBrands = brands.filter(brand => brand.featured);
    }
    
    res.json({
      success: true,
      count: filteredBrands.length,
      data: filteredBrands
    });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Server Error' });
  }
});

module.exports = router;
