const express = require('express');
const router = express.Router();

// @route   GET /api/search
// @desc    Search products
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { q, category, minPrice, maxPrice, brand, sort } = req.query;
    
    // Mock search results
    const results = [
      {
        id: 1,
        name: 'Floral Maxi Dress',
        price: 2499,
        image: '/assets/products/dress1.jpg',
        brand: 'StyleVogue',
        category: 'western-wear'
      }
    ];
    
    res.json({
      success: true,
      query: q,
      count: results.length,
      data: results
    });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Server Error' });
  }
});

module.exports = router;
