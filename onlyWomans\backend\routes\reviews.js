const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');

// Sample reviews data
let reviews = [
  {
    id: 1,
    productId: 1,
    userId: 'user123',
    userName: 'Priya S.',
    rating: 5,
    title: 'Beautiful dress!',
    comment: 'Absolutely love this dress. The fabric is soft and the fit is perfect. Highly recommended!',
    images: ['/assets/reviews/review1.jpg'],
    verified: true,
    helpful: 12,
    createdAt: new Date('2024-06-15'),
    updatedAt: new Date('2024-06-15')
  },
  {
    id: 2,
    productId: 1,
    userId: 'user456',
    userName: '<PERSON>',
    rating: 4,
    title: 'Good quality',
    comment: 'Nice dress, good quality fabric. Only issue is it runs a bit small.',
    images: [],
    verified: true,
    helpful: 8,
    createdAt: new Date('2024-06-18'),
    updatedAt: new Date('2024-06-18')
  }
];

// @route   GET /api/reviews/product/:productId
// @desc    Get reviews for a product
// @access  Public
router.get('/product/:productId', async (req, res) => {
  try {
    const productId = parseInt(req.params.productId);
    const { page = 1, limit = 10, sort = 'newest' } = req.query;
    
    let productReviews = reviews.filter(review => review.productId === productId);
    
    // Sort reviews
    switch (sort) {
      case 'oldest':
        productReviews.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
        break;
      case 'highest':
        productReviews.sort((a, b) => b.rating - a.rating);
        break;
      case 'lowest':
        productReviews.sort((a, b) => a.rating - b.rating);
        break;
      case 'helpful':
        productReviews.sort((a, b) => b.helpful - a.helpful);
        break;
      default: // newest
        productReviews.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    }
    
    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const paginatedReviews = productReviews.slice(startIndex, endIndex);
    
    // Calculate rating summary
    const totalReviews = productReviews.length;
    const averageRating = totalReviews > 0 
      ? productReviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews 
      : 0;
    
    const ratingDistribution = {
      5: productReviews.filter(r => r.rating === 5).length,
      4: productReviews.filter(r => r.rating === 4).length,
      3: productReviews.filter(r => r.rating === 3).length,
      2: productReviews.filter(r => r.rating === 2).length,
      1: productReviews.filter(r => r.rating === 1).length
    };
    
    res.json({
      success: true,
      data: {
        reviews: paginatedReviews,
        summary: {
          totalReviews,
          averageRating: Math.round(averageRating * 10) / 10,
          ratingDistribution
        },
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalReviews / limit),
          hasNext: endIndex < totalReviews,
          hasPrev: startIndex > 0
        }
      }
    });
  } catch (error) {
    console.error('Error fetching reviews:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   POST /api/reviews
// @desc    Create a review
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    const { productId, rating, title, comment, images = [] } = req.body;
    
    // Check if user already reviewed this product
    const existingReview = reviews.find(
      review => review.productId === productId && review.userId === req.user.id
    );
    
    if (existingReview) {
      return res.status(400).json({
        success: false,
        message: 'You have already reviewed this product'
      });
    }
    
    const newReview = {
      id: reviews.length + 1,
      productId: parseInt(productId),
      userId: req.user.id,
      userName: req.user.name || 'Anonymous',
      rating: parseInt(rating),
      title,
      comment,
      images,
      verified: true, // Assume verified for now
      helpful: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    reviews.push(newReview);
    
    res.status(201).json({
      success: true,
      data: newReview,
      message: 'Review created successfully'
    });
  } catch (error) {
    console.error('Error creating review:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   PUT /api/reviews/:id
// @desc    Update a review
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const reviewId = parseInt(req.params.id);
    const { rating, title, comment, images } = req.body;
    
    const reviewIndex = reviews.findIndex(
      review => review.id === reviewId && review.userId === req.user.id
    );
    
    if (reviewIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }
    
    // Update review
    if (rating) reviews[reviewIndex].rating = parseInt(rating);
    if (title) reviews[reviewIndex].title = title;
    if (comment) reviews[reviewIndex].comment = comment;
    if (images) reviews[reviewIndex].images = images;
    reviews[reviewIndex].updatedAt = new Date();
    
    res.json({
      success: true,
      data: reviews[reviewIndex],
      message: 'Review updated successfully'
    });
  } catch (error) {
    console.error('Error updating review:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   DELETE /api/reviews/:id
// @desc    Delete a review
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const reviewId = parseInt(req.params.id);
    
    const reviewIndex = reviews.findIndex(
      review => review.id === reviewId && review.userId === req.user.id
    );
    
    if (reviewIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }
    
    reviews.splice(reviewIndex, 1);
    
    res.json({
      success: true,
      message: 'Review deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting review:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// @route   POST /api/reviews/:id/helpful
// @desc    Mark review as helpful
// @access  Private
router.post('/:id/helpful', auth, async (req, res) => {
  try {
    const reviewId = parseInt(req.params.id);
    
    const reviewIndex = reviews.findIndex(review => review.id === reviewId);
    
    if (reviewIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }
    
    reviews[reviewIndex].helpful += 1;
    
    res.json({
      success: true,
      data: reviews[reviewIndex],
      message: 'Review marked as helpful'
    });
  } catch (error) {
    console.error('Error marking review as helpful:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

module.exports = router;
