const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');

// @route   GET /api/analytics/dashboard
// @desc    Get analytics dashboard data
// @access  Private/Admin
router.get('/dashboard', auth, async (req, res) => {
  try {
    // Mock analytics data
    const analytics = {
      totalUsers: 1250,
      totalOrders: 890,
      totalRevenue: 125000,
      topCategories: ['ethnic-wear', 'western-wear', 'beauty'],
      recentActivity: []
    };
    
    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Server Error' });
  }
});

module.exports = router;
