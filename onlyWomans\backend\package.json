{"name": "onlywomans-backend", "version": "1.0.0", "description": "Backend API for OnlyWomans - Women's Fashion E-commerce Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node scripts/seedData.js"}, "keywords": ["women", "fashion", "ecommerce", "api", "nodejs", "mongodb"], "author": "OnlyWomans Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^1.40.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "mongoose": "^7.5.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "razorpay": "^2.9.2", "socket.io": "^4.7.2", "stripe": "^13.5.0", "uuid": "^9.0.0"}, "devDependencies": {"jest": "^29.6.4", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}