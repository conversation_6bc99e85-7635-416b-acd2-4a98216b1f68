{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/focus-visible-BmVRXR1y.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst ION_FOCUSED = 'ion-focused';\nconst ION_FOCUSABLE = 'ion-focusable';\nconst FOCUS_KEYS = ['Tab', 'ArrowDown', 'Space', 'Escape', ' ', 'Shift', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'Home', 'End'];\nconst startFocusVisible = rootEl => {\n  let currentFocus = [];\n  let keyboardMode = true;\n  const ref = rootEl ? rootEl.shadowRoot : document;\n  const root = rootEl ? rootEl : document.body;\n  const setFocus = elements => {\n    currentFocus.forEach(el => el.classList.remove(ION_FOCUSED));\n    elements.forEach(el => el.classList.add(ION_FOCUSED));\n    currentFocus = elements;\n  };\n  const pointerDown = () => {\n    keyboardMode = false;\n    setFocus([]);\n  };\n  const onKeydown = ev => {\n    keyboardMode = FOCUS_KEYS.includes(ev.key);\n    if (!keyboardMode) {\n      setFocus([]);\n    }\n  };\n  const onFocusin = ev => {\n    if (keyboardMode && ev.composedPath !== undefined) {\n      const toFocus = ev.composedPath().filter(el => {\n        // TODO(FW-2832): type\n        if (el.classList) {\n          return el.classList.contains(ION_FOCUSABLE);\n        }\n        return false;\n      });\n      setFocus(toFocus);\n    }\n  };\n  const onFocusout = () => {\n    if (ref.activeElement === root) {\n      setFocus([]);\n    }\n  };\n  ref.addEventListener('keydown', onKeydown);\n  ref.addEventListener('focusin', onFocusin);\n  ref.addEventListener('focusout', onFocusout);\n  ref.addEventListener('touchstart', pointerDown, {\n    passive: true\n  });\n  ref.addEventListener('mousedown', pointerDown);\n  const destroy = () => {\n    ref.removeEventListener('keydown', onKeydown);\n    ref.removeEventListener('focusin', onFocusin);\n    ref.removeEventListener('focusout', onFocusout);\n    ref.removeEventListener('touchstart', pointerDown);\n    ref.removeEventListener('mousedown', pointerDown);\n  };\n  return {\n    destroy,\n    setFocus\n  };\n};\nexport { startFocusVisible };"], "mappings": ";AAGA,IAAM,cAAc;AACpB,IAAM,gBAAgB;AACtB,IAAM,aAAa,CAAC,OAAO,aAAa,SAAS,UAAU,KAAK,SAAS,SAAS,aAAa,cAAc,WAAW,QAAQ,KAAK;AACrI,IAAM,oBAAoB,YAAU;AAClC,MAAI,eAAe,CAAC;AACpB,MAAI,eAAe;AACnB,QAAM,MAAM,SAAS,OAAO,aAAa;AACzC,QAAM,OAAO,SAAS,SAAS,SAAS;AACxC,QAAM,WAAW,cAAY;AAC3B,iBAAa,QAAQ,QAAM,GAAG,UAAU,OAAO,WAAW,CAAC;AAC3D,aAAS,QAAQ,QAAM,GAAG,UAAU,IAAI,WAAW,CAAC;AACpD,mBAAe;AAAA,EACjB;AACA,QAAM,cAAc,MAAM;AACxB,mBAAe;AACf,aAAS,CAAC,CAAC;AAAA,EACb;AACA,QAAM,YAAY,QAAM;AACtB,mBAAe,WAAW,SAAS,GAAG,GAAG;AACzC,QAAI,CAAC,cAAc;AACjB,eAAS,CAAC,CAAC;AAAA,IACb;AAAA,EACF;AACA,QAAM,YAAY,QAAM;AACtB,QAAI,gBAAgB,GAAG,iBAAiB,QAAW;AACjD,YAAM,UAAU,GAAG,aAAa,EAAE,OAAO,QAAM;AAE7C,YAAI,GAAG,WAAW;AAChB,iBAAO,GAAG,UAAU,SAAS,aAAa;AAAA,QAC5C;AACA,eAAO;AAAA,MACT,CAAC;AACD,eAAS,OAAO;AAAA,IAClB;AAAA,EACF;AACA,QAAM,aAAa,MAAM;AACvB,QAAI,IAAI,kBAAkB,MAAM;AAC9B,eAAS,CAAC,CAAC;AAAA,IACb;AAAA,EACF;AACA,MAAI,iBAAiB,WAAW,SAAS;AACzC,MAAI,iBAAiB,WAAW,SAAS;AACzC,MAAI,iBAAiB,YAAY,UAAU;AAC3C,MAAI,iBAAiB,cAAc,aAAa;AAAA,IAC9C,SAAS;AAAA,EACX,CAAC;AACD,MAAI,iBAAiB,aAAa,WAAW;AAC7C,QAAM,UAAU,MAAM;AACpB,QAAI,oBAAoB,WAAW,SAAS;AAC5C,QAAI,oBAAoB,WAAW,SAAS;AAC5C,QAAI,oBAAoB,YAAY,UAAU;AAC9C,QAAI,oBAAoB,cAAc,WAAW;AACjD,QAAI,oBAAoB,aAAa,WAAW;AAAA,EAClD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;", "names": []}