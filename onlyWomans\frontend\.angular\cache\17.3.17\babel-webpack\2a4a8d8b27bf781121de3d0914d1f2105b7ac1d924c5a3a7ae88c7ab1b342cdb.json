{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nconst _c0 = a0 => [\"/categories\", a0];\nfunction HomeComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54);\n    i0.ɵɵelement(2, \"img\", 55);\n    i0.ɵɵelementStart(3, \"div\", 56)(4, \"span\", 57);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 58)(7, \"h3\", 59);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 60);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 61);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(7, _c0, category_r1.slug));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", category_r1.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r1.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r1.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r1.productCount, \"+ items\");\n  }\n}\nfunction HomeComponent_div_51_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 79);\n    i0.ɵɵtext(1, \"New\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_51_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", product_r2.discount, \"% Off\");\n  }\n}\nfunction HomeComponent_div_51_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r2.originalPrice, \"\");\n  }\n}\nfunction HomeComponent_div_51_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const star_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(star_r3);\n  }\n}\nfunction HomeComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63);\n    i0.ɵɵelement(2, \"img\", 55);\n    i0.ɵɵelementStart(3, \"div\", 64);\n    i0.ɵɵtemplate(4, HomeComponent_div_51_span_4_Template, 2, 0, \"span\", 65)(5, HomeComponent_div_51_span_5_Template, 2, 1, \"span\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 67)(7, \"i\", 68);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 69)(10, \"span\", 70);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"h3\", 71);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 72)(15, \"span\", 73);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, HomeComponent_div_51_span_17_Template, 2, 1, \"span\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 75)(19, \"div\", 76);\n    i0.ɵɵtemplate(20, HomeComponent_div_51_span_20_Template, 2, 1, \"span\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 78);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r2 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r2.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r2.isNew);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r2.discount);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", product_r2.isWishlisted);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r2.isWishlisted ? \"\\u2764\\uFE0F\" : \"\\uD83E\\uDD0D\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r2.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r2.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r2.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r2.originalPrice);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getStars(product_r2.rating));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r2.reviewCount, \")\");\n  }\n}\nexport class HomeComponent {\n  constructor() {\n    this.featuredCategories = [{\n      name: 'Ethnic Wear',\n      slug: 'ethnic-wear',\n      description: 'Traditional elegance for every occasion',\n      image: 'assets/images/categories/ethnic.jpg',\n      icon: '🥻',\n      productCount: 1250\n    }, {\n      name: 'Western Wear',\n      slug: 'western-wear',\n      description: 'Contemporary styles for modern women',\n      image: 'assets/images/categories/western.jpg',\n      icon: '👗',\n      productCount: 980\n    }, {\n      name: 'Beauty & Makeup',\n      slug: 'beauty-makeup',\n      description: 'Enhance your natural beauty',\n      image: 'assets/images/categories/beauty.jpg',\n      icon: '💄',\n      productCount: 750\n    }, {\n      name: 'Accessories',\n      slug: 'accessories',\n      description: 'Complete your look with style',\n      image: 'assets/images/categories/accessories.jpg',\n      icon: '👜',\n      productCount: 650\n    }, {\n      name: 'Footwear',\n      slug: 'footwear',\n      description: 'Step out in confidence',\n      image: 'assets/images/categories/footwear.jpg',\n      icon: '👠',\n      productCount: 420\n    }, {\n      name: 'Jewelry',\n      slug: 'jewelry',\n      description: 'Sparkle with every piece',\n      image: 'assets/images/categories/jewelry.jpg',\n      icon: '💎',\n      productCount: 380\n    }];\n    this.featuredProducts = [{\n      id: 1,\n      name: 'Floral Maxi Dress',\n      brand: 'StyleVogue',\n      price: 2499,\n      originalPrice: 3499,\n      discount: 29,\n      image: 'assets/images/products/dress1.jpg',\n      rating: 4.5,\n      reviewCount: 128,\n      isNew: true,\n      isWishlisted: false\n    }, {\n      id: 2,\n      name: 'Silk Saree with Blouse',\n      brand: 'EthnicElegance',\n      price: 4999,\n      originalPrice: 6999,\n      discount: 29,\n      image: 'assets/images/products/saree1.jpg',\n      rating: 4.8,\n      reviewCount: 95,\n      isNew: false,\n      isWishlisted: true\n    }, {\n      id: 3,\n      name: 'Designer Handbag',\n      brand: 'LuxeCollection',\n      price: 1899,\n      originalPrice: null,\n      discount: null,\n      image: 'assets/images/products/bag1.jpg',\n      rating: 4.3,\n      reviewCount: 67,\n      isNew: true,\n      isWishlisted: false\n    }, {\n      id: 4,\n      name: 'Makeup Palette Set',\n      brand: 'GlamBeauty',\n      price: 1299,\n      originalPrice: 1799,\n      discount: 28,\n      image: 'assets/images/products/makeup1.jpg',\n      rating: 4.6,\n      reviewCount: 203,\n      isNew: false,\n      isWishlisted: false\n    }];\n  }\n  ngOnInit() {\n    // Initialize component\n  }\n  getStars(rating) {\n    const stars = [];\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 !== 0;\n    for (let i = 0; i < fullStars; i++) {\n      stars.push('⭐');\n    }\n    if (hasHalfStar) {\n      stars.push('⭐');\n    }\n    while (stars.length < 5) {\n      stars.push('☆');\n    }\n    return stars;\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"ow-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 103,\n      vars: 2,\n      consts: [[1, \"home-container\"], [1, \"hero-section\"], [1, \"hero-content\"], [1, \"hero-text\"], [1, \"hero-title\"], [1, \"title-line\"], [1, \"title-line\", \"highlight\"], [1, \"hero-subtitle\"], [1, \"hero-actions\"], [\"routerLink\", \"/shop\", 1, \"btn\", \"btn-primary\", \"btn-lg\"], [1, \"icon\"], [\"routerLink\", \"/categories\", 1, \"btn\", \"btn-outline\", \"btn-lg\"], [1, \"hero-image\"], [1, \"image-container\"], [\"src\", \"assets/images/hero-woman.jpg\", \"alt\", \"Elegant Woman in Fashion\", 1, \"main-image\"], [1, \"floating-elements\"], [1, \"floating-item\", \"item-1\"], [1, \"floating-item\", \"item-2\"], [1, \"floating-item\", \"item-3\"], [1, \"floating-item\", \"item-4\"], [1, \"categories-section\"], [1, \"container\"], [1, \"section-header\"], [1, \"section-title\"], [1, \"section-subtitle\"], [1, \"categories-grid\"], [\"class\", \"category-card\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"featured-section\"], [1, \"products-grid\"], [\"class\", \"product-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"section-footer\"], [\"routerLink\", \"/shop\", 1, \"btn\", \"btn-outline\", \"btn-lg\"], [1, \"offers-section\"], [1, \"offers-grid\"], [1, \"offer-card\", \"offer-primary\"], [1, \"offer-content\"], [1, \"offer-title\"], [1, \"offer-description\"], [1, \"btn\", \"btn-primary\"], [1, \"offer-icon\"], [1, \"offer-card\", \"offer-secondary\"], [1, \"btn\", \"btn-secondary\"], [1, \"offer-card\", \"offer-accent\"], [1, \"btn\", \"btn-outline\"], [1, \"newsletter-section\"], [1, \"newsletter-content\"], [1, \"newsletter-text\"], [1, \"newsletter-title\"], [1, \"newsletter-subtitle\"], [1, \"newsletter-form\"], [1, \"form-group\"], [\"type\", \"email\", \"placeholder\", \"Enter your email address\", 1, \"email-input\"], [1, \"newsletter-note\"], [1, \"category-card\", 3, \"routerLink\"], [1, \"category-image\"], [3, \"src\", \"alt\"], [1, \"category-overlay\"], [1, \"category-icon\"], [1, \"category-info\"], [1, \"category-name\"], [1, \"category-description\"], [1, \"category-count\"], [1, \"product-card\"], [1, \"product-image\"], [1, \"product-badges\"], [\"class\", \"badge badge-new\", 4, \"ngIf\"], [\"class\", \"badge badge-sale\", 4, \"ngIf\"], [1, \"wishlist-btn\"], [1, \"heart-icon\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"product-rating\"], [1, \"stars\"], [4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"badge\", \"badge-new\"], [1, \"badge\", \"badge-sale\"], [1, \"original-price\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"section\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4)(5, \"span\", 5);\n          i0.ɵɵtext(6, \"Fashion That\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 6);\n          i0.ɵɵtext(8, \"Celebrates You\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"p\", 7);\n          i0.ɵɵtext(10, \" Discover curated collections designed exclusively for women. From ethnic elegance to western chic, find your perfect style. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"button\", 9)(13, \"span\");\n          i0.ɵɵtext(14, \"Shop Now\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"i\", 10);\n          i0.ɵɵtext(16, \"\\uD83D\\uDECD\\uFE0F\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"button\", 11)(18, \"span\");\n          i0.ɵɵtext(19, \"Explore Categories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"i\", 10);\n          i0.ɵɵtext(21, \"\\u2728\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"div\", 12)(23, \"div\", 13);\n          i0.ɵɵelement(24, \"img\", 14);\n          i0.ɵɵelementStart(25, \"div\", 15)(26, \"div\", 16);\n          i0.ɵɵtext(27, \"\\uD83D\\uDC57\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 17);\n          i0.ɵɵtext(29, \"\\uD83D\\uDC84\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 18);\n          i0.ɵɵtext(31, \"\\uD83D\\uDC60\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 19);\n          i0.ɵɵtext(33, \"\\uD83D\\uDC8E\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(34, \"section\", 20)(35, \"div\", 21)(36, \"div\", 22)(37, \"h2\", 23);\n          i0.ɵɵtext(38, \"Shop by Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"p\", 24);\n          i0.ɵɵtext(40, \"Explore our curated collections for every occasion\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 25);\n          i0.ɵɵtemplate(42, HomeComponent_div_42_Template, 13, 9, \"div\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(43, \"section\", 27)(44, \"div\", 21)(45, \"div\", 22)(46, \"h2\", 23);\n          i0.ɵɵtext(47, \"Trending Now\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"p\", 24);\n          i0.ɵɵtext(49, \"Handpicked favorites that are flying off the shelves\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 28);\n          i0.ɵɵtemplate(51, HomeComponent_div_51_Template, 23, 13, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"div\", 30)(53, \"button\", 31);\n          i0.ɵɵtext(54, \" View All Products \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(55, \"section\", 32)(56, \"div\", 21)(57, \"div\", 33)(58, \"div\", 34)(59, \"div\", 35)(60, \"h3\", 36);\n          i0.ɵɵtext(61, \"New User Special\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"p\", 37);\n          i0.ɵɵtext(63, \"Get 30% off on your first order\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"button\", 38);\n          i0.ɵɵtext(65, \"Claim Now\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"div\", 39);\n          i0.ɵɵtext(67, \"\\uD83C\\uDF81\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"div\", 40)(69, \"div\", 35)(70, \"h3\", 36);\n          i0.ɵɵtext(71, \"Free Shipping\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"p\", 37);\n          i0.ɵɵtext(73, \"On orders above \\u20B9999\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"button\", 41);\n          i0.ɵɵtext(75, \"Shop Now\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"div\", 39);\n          i0.ɵɵtext(77, \"\\uD83D\\uDE9A\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(78, \"div\", 42)(79, \"div\", 35)(80, \"h3\", 36);\n          i0.ɵɵtext(81, \"Beauty Box\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"p\", 37);\n          i0.ɵɵtext(83, \"Curated makeup essentials\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"button\", 43);\n          i0.ɵɵtext(85, \"Explore\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(86, \"div\", 39);\n          i0.ɵɵtext(87, \"\\uD83D\\uDC84\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(88, \"section\", 44)(89, \"div\", 21)(90, \"div\", 45)(91, \"div\", 46)(92, \"h2\", 47);\n          i0.ɵɵtext(93, \"Stay in Style\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"p\", 48);\n          i0.ɵɵtext(95, \" Get the latest fashion trends, beauty tips, and exclusive offers delivered to your inbox. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(96, \"div\", 49)(97, \"div\", 50);\n          i0.ɵɵelement(98, \"input\", 51);\n          i0.ɵɵelementStart(99, \"button\", 38);\n          i0.ɵɵtext(100, \"Subscribe\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(101, \"p\", 52);\n          i0.ɵɵtext(102, \" Join 50,000+ women who trust us for their fashion needs. \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(42);\n          i0.ɵɵproperty(\"ngForOf\", ctx.featuredCategories);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.featuredProducts);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterModule, i2.RouterLink],\n      styles: [\".home-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 var(--space-lg);\\n}\\n\\n.hero-section[_ngcontent-%COMP%] {\\n  padding: var(--space-3xl) 0;\\n  background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.hero-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 var(--space-lg);\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: var(--space-3xl);\\n  align-items: center;\\n}\\n\\n.hero-text[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-5xl);\\n  font-weight: var(--font-weight-bold);\\n  line-height: var(--line-height-tight);\\n  margin-bottom: var(--space-lg);\\n}\\n.hero-text[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%]   .title-line[_ngcontent-%COMP%] {\\n  display: block;\\n  color: var(--text-primary);\\n}\\n.hero-text[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%]   .highlight[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.hero-text[_ngcontent-%COMP%]   .hero-subtitle[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-lg);\\n  color: var(--text-secondary);\\n  line-height: var(--line-height-relaxed);\\n  margin-bottom: var(--space-2xl);\\n}\\n.hero-text[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--space-lg);\\n  flex-wrap: wrap;\\n}\\n\\n.hero-image[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.hero-image[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  border-radius: var(--radius-2xl);\\n  overflow: hidden;\\n  box-shadow: var(--shadow-xl);\\n}\\n.hero-image[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 500px;\\n  object-fit: cover;\\n}\\n.hero-image[_ngcontent-%COMP%]   .floating-elements[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n}\\n.hero-image[_ngcontent-%COMP%]   .floating-item[_ngcontent-%COMP%] {\\n  position: absolute;\\n  font-size: var(--font-size-2xl);\\n  animation: float 3s ease-in-out infinite;\\n}\\n.hero-image[_ngcontent-%COMP%]   .floating-item.item-1[_ngcontent-%COMP%] {\\n  top: 20%;\\n  right: 10%;\\n  animation-delay: 0s;\\n}\\n.hero-image[_ngcontent-%COMP%]   .floating-item.item-2[_ngcontent-%COMP%] {\\n  top: 60%;\\n  right: 20%;\\n  animation-delay: 1s;\\n}\\n.hero-image[_ngcontent-%COMP%]   .floating-item.item-3[_ngcontent-%COMP%] {\\n  bottom: 30%;\\n  left: 10%;\\n  animation-delay: 2s;\\n}\\n.hero-image[_ngcontent-%COMP%]   .floating-item.item-4[_ngcontent-%COMP%] {\\n  top: 40%;\\n  left: 5%;\\n  animation-delay: 1.5s;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: var(--space-3xl);\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-3xl);\\n  font-weight: var(--font-weight-bold);\\n  color: var(--text-primary);\\n  margin-bottom: var(--space-md);\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-lg);\\n  color: var(--text-secondary);\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n\\n.categories-section[_ngcontent-%COMP%] {\\n  padding: var(--space-3xl) 0;\\n  background: var(--bg-primary);\\n}\\n\\n.categories-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: var(--space-xl);\\n}\\n\\n.category-card[_ngcontent-%COMP%] {\\n  background: var(--bg-primary);\\n  border-radius: var(--radius-xl);\\n  overflow: hidden;\\n  box-shadow: var(--shadow-md);\\n  transition: all var(--transition-normal);\\n  cursor: pointer;\\n  text-decoration: none;\\n  color: inherit;\\n}\\n.category-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: var(--shadow-xl);\\n}\\n.category-card[_ngcontent-%COMP%]:hover   .category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.category-card[_ngcontent-%COMP%]:hover   .category-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.category-card[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n.category-card[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform var(--transition-slow);\\n}\\n.category-card[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%]   .category-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(236, 72, 153, 0.8), rgba(168, 85, 247, 0.8));\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 0;\\n  transition: opacity var(--transition-normal);\\n}\\n.category-card[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%]   .category-overlay[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-4xl);\\n}\\n.category-card[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%] {\\n  padding: var(--space-lg);\\n}\\n.category-card[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-xl);\\n  font-weight: var(--font-weight-semibold);\\n  color: var(--text-primary);\\n  margin-bottom: var(--space-sm);\\n}\\n.category-card[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-description[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n  margin-bottom: var(--space-md);\\n  line-height: var(--line-height-relaxed);\\n}\\n.category-card[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-count[_ngcontent-%COMP%] {\\n  color: var(--primary-600);\\n  font-weight: var(--font-weight-medium);\\n  font-size: var(--font-size-sm);\\n}\\n\\n.featured-section[_ngcontent-%COMP%] {\\n  padding: var(--space-3xl) 0;\\n  background: var(--bg-secondary);\\n}\\n\\n.products-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: var(--space-xl);\\n  margin-bottom: var(--space-2xl);\\n}\\n\\n.section-footer[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.offers-section[_ngcontent-%COMP%] {\\n  padding: var(--space-3xl) 0;\\n  background: var(--bg-primary);\\n}\\n\\n.offers-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: var(--space-xl);\\n}\\n\\n.offer-card[_ngcontent-%COMP%] {\\n  padding: var(--space-2xl);\\n  border-radius: var(--radius-xl);\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  transition: transform var(--transition-normal);\\n}\\n.offer-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n}\\n.offer-card.offer-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));\\n  color: var(--text-inverse);\\n}\\n.offer-card.offer-secondary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));\\n  color: var(--text-inverse);\\n}\\n.offer-card.offer-accent[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--accent-400), var(--accent-500));\\n  color: var(--text-inverse);\\n}\\n.offer-card[_ngcontent-%COMP%]   .offer-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.offer-card[_ngcontent-%COMP%]   .offer-content[_ngcontent-%COMP%]   .offer-title[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-xl);\\n  font-weight: var(--font-weight-bold);\\n  margin-bottom: var(--space-sm);\\n}\\n.offer-card[_ngcontent-%COMP%]   .offer-content[_ngcontent-%COMP%]   .offer-description[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-lg);\\n  opacity: 0.9;\\n}\\n.offer-card[_ngcontent-%COMP%]   .offer-icon[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-4xl);\\n  margin-left: var(--space-lg);\\n}\\n\\n.newsletter-section[_ngcontent-%COMP%] {\\n  padding: var(--space-3xl) 0;\\n  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));\\n  color: var(--text-inverse);\\n}\\n\\n.newsletter-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: var(--space-3xl);\\n  align-items: center;\\n}\\n\\n.newsletter-text[_ngcontent-%COMP%]   .newsletter-title[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-3xl);\\n  font-weight: var(--font-weight-bold);\\n  margin-bottom: var(--space-md);\\n}\\n.newsletter-text[_ngcontent-%COMP%]   .newsletter-subtitle[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-lg);\\n  opacity: 0.9;\\n  line-height: var(--line-height-relaxed);\\n}\\n\\n.newsletter-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--space-md);\\n  margin-bottom: var(--space-md);\\n}\\n.newsletter-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .email-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: var(--space-md);\\n  border: none;\\n  border-radius: var(--radius-lg);\\n  font-size: var(--font-size-base);\\n}\\n.newsletter-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .email-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);\\n}\\n.newsletter-form[_ngcontent-%COMP%]   .newsletter-note[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-sm);\\n  opacity: 0.8;\\n  margin: 0;\\n}\\n\\n@media (max-width: 768px) {\\n  .hero-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: var(--space-2xl);\\n    text-align: center;\\n  }\\n  .hero-text[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%] {\\n    font-size: var(--font-size-3xl);\\n  }\\n  .hero-actions[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .categories-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .products-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  }\\n  .offers-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .offer-card[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .offer-card[_ngcontent-%COMP%]   .offer-icon[_ngcontent-%COMP%] {\\n    margin: var(--space-lg) 0 0 0;\\n  }\\n  .newsletter-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    text-align: center;\\n  }\\n  .newsletter-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "category_r1", "slug", "ɵɵadvance", "image", "ɵɵsanitizeUrl", "name", "ɵɵtextInterpolate", "icon", "description", "ɵɵtextInterpolate1", "productCount", "product_r2", "discount", "originalPrice", "star_r3", "ɵɵtemplate", "HomeComponent_div_51_span_4_Template", "HomeComponent_div_51_span_5_Template", "HomeComponent_div_51_span_17_Template", "HomeComponent_div_51_span_20_Template", "isNew", "ɵɵclassProp", "isWishlisted", "brand", "price", "ctx_r3", "getStars", "rating", "reviewCount", "HomeComponent", "constructor", "featuredCategories", "featuredProducts", "id", "ngOnInit", "stars", "fullStars", "Math", "floor", "hasHalfStar", "i", "push", "length", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "HomeComponent_div_42_Template", "HomeComponent_div_51_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "RouterLink", "styles"], "sources": ["E:\\Fahion\\DFashion\\onlyWomans\\frontend\\src\\app\\pages\\home\\home.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n@Component({\n  selector: 'ow-home',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  template: `\n    <div class=\"home-container\">\n      <!-- Hero Section -->\n      <section class=\"hero-section\">\n        <div class=\"hero-content\">\n          <div class=\"hero-text\">\n            <h1 class=\"hero-title\">\n              <span class=\"title-line\">Fashion That</span>\n              <span class=\"title-line highlight\">Celebrates You</span>\n            </h1>\n            <p class=\"hero-subtitle\">\n              Discover curated collections designed exclusively for women. \n              From ethnic elegance to western chic, find your perfect style.\n            </p>\n            <div class=\"hero-actions\">\n              <button class=\"btn btn-primary btn-lg\" routerLink=\"/shop\">\n                <span>Shop Now</span>\n                <i class=\"icon\">🛍️</i>\n              </button>\n              <button class=\"btn btn-outline btn-lg\" routerLink=\"/categories\">\n                <span>Explore Categories</span>\n                <i class=\"icon\">✨</i>\n              </button>\n            </div>\n          </div>\n          <div class=\"hero-image\">\n            <div class=\"image-container\">\n              <img src=\"assets/images/hero-woman.jpg\" alt=\"Elegant Woman in Fashion\" class=\"main-image\">\n              <div class=\"floating-elements\">\n                <div class=\"floating-item item-1\">👗</div>\n                <div class=\"floating-item item-2\">💄</div>\n                <div class=\"floating-item item-3\">👠</div>\n                <div class=\"floating-item item-4\">💎</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Categories Section -->\n      <section class=\"categories-section\">\n        <div class=\"container\">\n          <div class=\"section-header\">\n            <h2 class=\"section-title\">Shop by Category</h2>\n            <p class=\"section-subtitle\">Explore our curated collections for every occasion</p>\n          </div>\n          \n          <div class=\"categories-grid\">\n            <div *ngFor=\"let category of featuredCategories\" \n                 class=\"category-card\"\n                 [routerLink]=\"['/categories', category.slug]\">\n              <div class=\"category-image\">\n                <img [src]=\"category.image\" [alt]=\"category.name\">\n                <div class=\"category-overlay\">\n                  <span class=\"category-icon\">{{ category.icon }}</span>\n                </div>\n              </div>\n              <div class=\"category-info\">\n                <h3 class=\"category-name\">{{ category.name }}</h3>\n                <p class=\"category-description\">{{ category.description }}</p>\n                <span class=\"category-count\">{{ category.productCount }}+ items</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Featured Products Section -->\n      <section class=\"featured-section\">\n        <div class=\"container\">\n          <div class=\"section-header\">\n            <h2 class=\"section-title\">Trending Now</h2>\n            <p class=\"section-subtitle\">Handpicked favorites that are flying off the shelves</p>\n          </div>\n          \n          <div class=\"products-grid\">\n            <div *ngFor=\"let product of featuredProducts\" class=\"product-card\">\n              <div class=\"product-image\">\n                <img [src]=\"product.image\" [alt]=\"product.name\">\n                <div class=\"product-badges\">\n                  <span *ngIf=\"product.isNew\" class=\"badge badge-new\">New</span>\n                  <span *ngIf=\"product.discount\" class=\"badge badge-sale\">{{ product.discount }}% Off</span>\n                </div>\n                <button class=\"wishlist-btn\" [class.active]=\"product.isWishlisted\">\n                  <i class=\"heart-icon\">{{ product.isWishlisted ? '❤️' : '🤍' }}</i>\n                </button>\n              </div>\n              <div class=\"product-info\">\n                <span class=\"product-brand\">{{ product.brand }}</span>\n                <h3 class=\"product-name\">{{ product.name }}</h3>\n                <div class=\"product-price\">\n                  <span class=\"current-price\">₹{{ product.price }}</span>\n                  <span *ngIf=\"product.originalPrice\" class=\"original-price\">₹{{ product.originalPrice }}</span>\n                </div>\n                <div class=\"product-rating\">\n                  <div class=\"stars\">\n                    <span *ngFor=\"let star of getStars(product.rating)\">{{ star }}</span>\n                  </div>\n                  <span class=\"rating-text\">({{ product.reviewCount }})</span>\n                </div>\n              </div>\n            </div>\n          </div>\n          \n          <div class=\"section-footer\">\n            <button class=\"btn btn-outline btn-lg\" routerLink=\"/shop\">\n              View All Products\n            </button>\n          </div>\n        </div>\n      </section>\n\n      <!-- Special Offers Section -->\n      <section class=\"offers-section\">\n        <div class=\"container\">\n          <div class=\"offers-grid\">\n            <div class=\"offer-card offer-primary\">\n              <div class=\"offer-content\">\n                <h3 class=\"offer-title\">New User Special</h3>\n                <p class=\"offer-description\">Get 30% off on your first order</p>\n                <button class=\"btn btn-primary\">Claim Now</button>\n              </div>\n              <div class=\"offer-icon\">🎁</div>\n            </div>\n            \n            <div class=\"offer-card offer-secondary\">\n              <div class=\"offer-content\">\n                <h3 class=\"offer-title\">Free Shipping</h3>\n                <p class=\"offer-description\">On orders above ₹999</p>\n                <button class=\"btn btn-secondary\">Shop Now</button>\n              </div>\n              <div class=\"offer-icon\">🚚</div>\n            </div>\n            \n            <div class=\"offer-card offer-accent\">\n              <div class=\"offer-content\">\n                <h3 class=\"offer-title\">Beauty Box</h3>\n                <p class=\"offer-description\">Curated makeup essentials</p>\n                <button class=\"btn btn-outline\">Explore</button>\n              </div>\n              <div class=\"offer-icon\">💄</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Newsletter Section -->\n      <section class=\"newsletter-section\">\n        <div class=\"container\">\n          <div class=\"newsletter-content\">\n            <div class=\"newsletter-text\">\n              <h2 class=\"newsletter-title\">Stay in Style</h2>\n              <p class=\"newsletter-subtitle\">\n                Get the latest fashion trends, beauty tips, and exclusive offers delivered to your inbox.\n              </p>\n            </div>\n            <div class=\"newsletter-form\">\n              <div class=\"form-group\">\n                <input type=\"email\" placeholder=\"Enter your email address\" class=\"email-input\">\n                <button class=\"btn btn-primary\">Subscribe</button>\n              </div>\n              <p class=\"newsletter-note\">\n                Join 50,000+ women who trust us for their fashion needs.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  `,\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit {\n  \n  featuredCategories = [\n    {\n      name: 'Ethnic Wear',\n      slug: 'ethnic-wear',\n      description: 'Traditional elegance for every occasion',\n      image: 'assets/images/categories/ethnic.jpg',\n      icon: '🥻',\n      productCount: 1250\n    },\n    {\n      name: 'Western Wear',\n      slug: 'western-wear',\n      description: 'Contemporary styles for modern women',\n      image: 'assets/images/categories/western.jpg',\n      icon: '👗',\n      productCount: 980\n    },\n    {\n      name: 'Beauty & Makeup',\n      slug: 'beauty-makeup',\n      description: 'Enhance your natural beauty',\n      image: 'assets/images/categories/beauty.jpg',\n      icon: '💄',\n      productCount: 750\n    },\n    {\n      name: 'Accessories',\n      slug: 'accessories',\n      description: 'Complete your look with style',\n      image: 'assets/images/categories/accessories.jpg',\n      icon: '👜',\n      productCount: 650\n    },\n    {\n      name: 'Footwear',\n      slug: 'footwear',\n      description: 'Step out in confidence',\n      image: 'assets/images/categories/footwear.jpg',\n      icon: '👠',\n      productCount: 420\n    },\n    {\n      name: 'Jewelry',\n      slug: 'jewelry',\n      description: 'Sparkle with every piece',\n      image: 'assets/images/categories/jewelry.jpg',\n      icon: '💎',\n      productCount: 380\n    }\n  ];\n\n  featuredProducts = [\n    {\n      id: 1,\n      name: 'Floral Maxi Dress',\n      brand: 'StyleVogue',\n      price: 2499,\n      originalPrice: 3499,\n      discount: 29,\n      image: 'assets/images/products/dress1.jpg',\n      rating: 4.5,\n      reviewCount: 128,\n      isNew: true,\n      isWishlisted: false\n    },\n    {\n      id: 2,\n      name: 'Silk Saree with Blouse',\n      brand: 'EthnicElegance',\n      price: 4999,\n      originalPrice: 6999,\n      discount: 29,\n      image: 'assets/images/products/saree1.jpg',\n      rating: 4.8,\n      reviewCount: 95,\n      isNew: false,\n      isWishlisted: true\n    },\n    {\n      id: 3,\n      name: 'Designer Handbag',\n      brand: 'LuxeCollection',\n      price: 1899,\n      originalPrice: null,\n      discount: null,\n      image: 'assets/images/products/bag1.jpg',\n      rating: 4.3,\n      reviewCount: 67,\n      isNew: true,\n      isWishlisted: false\n    },\n    {\n      id: 4,\n      name: 'Makeup Palette Set',\n      brand: 'GlamBeauty',\n      price: 1299,\n      originalPrice: 1799,\n      discount: 28,\n      image: 'assets/images/products/makeup1.jpg',\n      rating: 4.6,\n      reviewCount: 203,\n      isNew: false,\n      isWishlisted: false\n    }\n  ];\n\n  constructor() { }\n\n  ngOnInit(): void {\n    // Initialize component\n  }\n\n  getStars(rating: number): string[] {\n    const stars = [];\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 !== 0;\n    \n    for (let i = 0; i < fullStars; i++) {\n      stars.push('⭐');\n    }\n    \n    if (hasHalfStar) {\n      stars.push('⭐');\n    }\n    \n    while (stars.length < 5) {\n      stars.push('☆');\n    }\n    \n    return stars;\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;;;;;;IAyDhCC,EAHF,CAAAC,cAAA,cAEmD,cACrB;IAC1BD,EAAA,CAAAE,SAAA,cAAkD;IAEhDF,EADF,CAAAC,cAAA,cAA8B,eACA;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAEnDH,EAFmD,CAAAI,YAAA,EAAO,EAClD,EACF;IAEJJ,EADF,CAAAC,cAAA,cAA2B,aACC;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClDJ,EAAA,CAAAC,cAAA,YAAgC;IAAAD,EAAA,CAAAG,MAAA,IAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC9DJ,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAG,MAAA,IAAkC;IAEnEH,EAFmE,CAAAI,YAAA,EAAO,EAClE,EACF;;;;IAZDJ,EAAA,CAAAK,UAAA,eAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAC,IAAA,EAA6C;IAEzCT,EAAA,CAAAU,SAAA,GAAsB;IAACV,EAAvB,CAAAK,UAAA,QAAAG,WAAA,CAAAG,KAAA,EAAAX,EAAA,CAAAY,aAAA,CAAsB,QAAAJ,WAAA,CAAAK,IAAA,CAAsB;IAEnBb,EAAA,CAAAU,SAAA,GAAmB;IAAnBV,EAAA,CAAAc,iBAAA,CAAAN,WAAA,CAAAO,IAAA,CAAmB;IAIvBf,EAAA,CAAAU,SAAA,GAAmB;IAAnBV,EAAA,CAAAc,iBAAA,CAAAN,WAAA,CAAAK,IAAA,CAAmB;IACbb,EAAA,CAAAU,SAAA,GAA0B;IAA1BV,EAAA,CAAAc,iBAAA,CAAAN,WAAA,CAAAQ,WAAA,CAA0B;IAC7BhB,EAAA,CAAAU,SAAA,GAAkC;IAAlCV,EAAA,CAAAiB,kBAAA,KAAAT,WAAA,CAAAU,YAAA,YAAkC;;;;;IAoB7DlB,EAAA,CAAAC,cAAA,eAAoD;IAAAD,EAAA,CAAAG,MAAA,UAAG;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAC9DJ,EAAA,CAAAC,cAAA,eAAwD;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAlCJ,EAAA,CAAAU,SAAA,EAA2B;IAA3BV,EAAA,CAAAiB,kBAAA,KAAAE,UAAA,CAAAC,QAAA,UAA2B;;;;;IAWnFpB,EAAA,CAAAC,cAAA,eAA2D;IAAAD,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAnCJ,EAAA,CAAAU,SAAA,EAA4B;IAA5BV,EAAA,CAAAiB,kBAAA,WAAAE,UAAA,CAAAE,aAAA,KAA4B;;;;;IAIrFrB,EAAA,CAAAC,cAAA,WAAoD;IAAAD,EAAA,CAAAG,MAAA,GAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAjBJ,EAAA,CAAAU,SAAA,EAAU;IAAVV,EAAA,CAAAc,iBAAA,CAAAQ,OAAA,CAAU;;;;;IAnBpEtB,EADF,CAAAC,cAAA,cAAmE,cACtC;IACzBD,EAAA,CAAAE,SAAA,cAAgD;IAChDF,EAAA,CAAAC,cAAA,cAA4B;IAE1BD,EADA,CAAAuB,UAAA,IAAAC,oCAAA,mBAAoD,IAAAC,oCAAA,mBACI;IAC1DzB,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,iBAAmE,YAC3C;IAAAD,EAAA,CAAAG,MAAA,GAAwC;IAElEH,EAFkE,CAAAI,YAAA,EAAI,EAC3D,EACL;IAEJJ,EADF,CAAAC,cAAA,cAA0B,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACtDJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAE9CJ,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAG,MAAA,IAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAuB,UAAA,KAAAG,qCAAA,mBAA2D;IAC7D1B,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,eAA4B,eACP;IACjBD,EAAA,CAAAuB,UAAA,KAAAI,qCAAA,mBAAoD;IACtD3B,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,IAA2B;IAG3DH,EAH2D,CAAAI,YAAA,EAAO,EACxD,EACF,EACF;;;;;IAvBGJ,EAAA,CAAAU,SAAA,GAAqB;IAACV,EAAtB,CAAAK,UAAA,QAAAc,UAAA,CAAAR,KAAA,EAAAX,EAAA,CAAAY,aAAA,CAAqB,QAAAO,UAAA,CAAAN,IAAA,CAAqB;IAEtCb,EAAA,CAAAU,SAAA,GAAmB;IAAnBV,EAAA,CAAAK,UAAA,SAAAc,UAAA,CAAAS,KAAA,CAAmB;IACnB5B,EAAA,CAAAU,SAAA,EAAsB;IAAtBV,EAAA,CAAAK,UAAA,SAAAc,UAAA,CAAAC,QAAA,CAAsB;IAEFpB,EAAA,CAAAU,SAAA,EAAqC;IAArCV,EAAA,CAAA6B,WAAA,WAAAV,UAAA,CAAAW,YAAA,CAAqC;IAC1C9B,EAAA,CAAAU,SAAA,GAAwC;IAAxCV,EAAA,CAAAc,iBAAA,CAAAK,UAAA,CAAAW,YAAA,mCAAwC;IAIpC9B,EAAA,CAAAU,SAAA,GAAmB;IAAnBV,EAAA,CAAAc,iBAAA,CAAAK,UAAA,CAAAY,KAAA,CAAmB;IACtB/B,EAAA,CAAAU,SAAA,GAAkB;IAAlBV,EAAA,CAAAc,iBAAA,CAAAK,UAAA,CAAAN,IAAA,CAAkB;IAEbb,EAAA,CAAAU,SAAA,GAAoB;IAApBV,EAAA,CAAAiB,kBAAA,WAAAE,UAAA,CAAAa,KAAA,KAAoB;IACzChC,EAAA,CAAAU,SAAA,EAA2B;IAA3BV,EAAA,CAAAK,UAAA,SAAAc,UAAA,CAAAE,aAAA,CAA2B;IAITrB,EAAA,CAAAU,SAAA,GAA2B;IAA3BV,EAAA,CAAAK,UAAA,YAAA4B,MAAA,CAAAC,QAAA,CAAAf,UAAA,CAAAgB,MAAA,EAA2B;IAE1BnC,EAAA,CAAAU,SAAA,GAA2B;IAA3BV,EAAA,CAAAiB,kBAAA,MAAAE,UAAA,CAAAiB,WAAA,MAA2B;;;AA0EvE,OAAM,MAAOC,aAAa;EA4GxBC,YAAA;IA1GA,KAAAC,kBAAkB,GAAG,CACnB;MACE1B,IAAI,EAAE,aAAa;MACnBJ,IAAI,EAAE,aAAa;MACnBO,WAAW,EAAE,yCAAyC;MACtDL,KAAK,EAAE,qCAAqC;MAC5CI,IAAI,EAAE,IAAI;MACVG,YAAY,EAAE;KACf,EACD;MACEL,IAAI,EAAE,cAAc;MACpBJ,IAAI,EAAE,cAAc;MACpBO,WAAW,EAAE,sCAAsC;MACnDL,KAAK,EAAE,sCAAsC;MAC7CI,IAAI,EAAE,IAAI;MACVG,YAAY,EAAE;KACf,EACD;MACEL,IAAI,EAAE,iBAAiB;MACvBJ,IAAI,EAAE,eAAe;MACrBO,WAAW,EAAE,6BAA6B;MAC1CL,KAAK,EAAE,qCAAqC;MAC5CI,IAAI,EAAE,IAAI;MACVG,YAAY,EAAE;KACf,EACD;MACEL,IAAI,EAAE,aAAa;MACnBJ,IAAI,EAAE,aAAa;MACnBO,WAAW,EAAE,+BAA+B;MAC5CL,KAAK,EAAE,0CAA0C;MACjDI,IAAI,EAAE,IAAI;MACVG,YAAY,EAAE;KACf,EACD;MACEL,IAAI,EAAE,UAAU;MAChBJ,IAAI,EAAE,UAAU;MAChBO,WAAW,EAAE,wBAAwB;MACrCL,KAAK,EAAE,uCAAuC;MAC9CI,IAAI,EAAE,IAAI;MACVG,YAAY,EAAE;KACf,EACD;MACEL,IAAI,EAAE,SAAS;MACfJ,IAAI,EAAE,SAAS;MACfO,WAAW,EAAE,0BAA0B;MACvCL,KAAK,EAAE,sCAAsC;MAC7CI,IAAI,EAAE,IAAI;MACVG,YAAY,EAAE;KACf,CACF;IAED,KAAAsB,gBAAgB,GAAG,CACjB;MACEC,EAAE,EAAE,CAAC;MACL5B,IAAI,EAAE,mBAAmB;MACzBkB,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,IAAI;MACXX,aAAa,EAAE,IAAI;MACnBD,QAAQ,EAAE,EAAE;MACZT,KAAK,EAAE,mCAAmC;MAC1CwB,MAAM,EAAE,GAAG;MACXC,WAAW,EAAE,GAAG;MAChBR,KAAK,EAAE,IAAI;MACXE,YAAY,EAAE;KACf,EACD;MACEW,EAAE,EAAE,CAAC;MACL5B,IAAI,EAAE,wBAAwB;MAC9BkB,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE,IAAI;MACXX,aAAa,EAAE,IAAI;MACnBD,QAAQ,EAAE,EAAE;MACZT,KAAK,EAAE,mCAAmC;MAC1CwB,MAAM,EAAE,GAAG;MACXC,WAAW,EAAE,EAAE;MACfR,KAAK,EAAE,KAAK;MACZE,YAAY,EAAE;KACf,EACD;MACEW,EAAE,EAAE,CAAC;MACL5B,IAAI,EAAE,kBAAkB;MACxBkB,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE,IAAI;MACXX,aAAa,EAAE,IAAI;MACnBD,QAAQ,EAAE,IAAI;MACdT,KAAK,EAAE,iCAAiC;MACxCwB,MAAM,EAAE,GAAG;MACXC,WAAW,EAAE,EAAE;MACfR,KAAK,EAAE,IAAI;MACXE,YAAY,EAAE;KACf,EACD;MACEW,EAAE,EAAE,CAAC;MACL5B,IAAI,EAAE,oBAAoB;MAC1BkB,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,IAAI;MACXX,aAAa,EAAE,IAAI;MACnBD,QAAQ,EAAE,EAAE;MACZT,KAAK,EAAE,oCAAoC;MAC3CwB,MAAM,EAAE,GAAG;MACXC,WAAW,EAAE,GAAG;MAChBR,KAAK,EAAE,KAAK;MACZE,YAAY,EAAE;KACf,CACF;EAEe;EAEhBY,QAAQA,CAAA;IACN;EAAA;EAGFR,QAAQA,CAACC,MAAc;IACrB,MAAMQ,KAAK,GAAG,EAAE;IAChB,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACX,MAAM,CAAC;IACpC,MAAMY,WAAW,GAAGZ,MAAM,GAAG,CAAC,KAAK,CAAC;IAEpC,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,EAAEI,CAAC,EAAE,EAAE;MAClCL,KAAK,CAACM,IAAI,CAAC,GAAG,CAAC;;IAGjB,IAAIF,WAAW,EAAE;MACfJ,KAAK,CAACM,IAAI,CAAC,GAAG,CAAC;;IAGjB,OAAON,KAAK,CAACO,MAAM,GAAG,CAAC,EAAE;MACvBP,KAAK,CAACM,IAAI,CAAC,GAAG,CAAC;;IAGjB,OAAON,KAAK;EACd;;;uBApIWN,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAc,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAArD,EAAA,CAAAsD,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArKZ5D,EANV,CAAAC,cAAA,aAA4B,iBAEI,aACF,aACD,YACE,cACI;UAAAD,EAAA,CAAAG,MAAA,mBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAC5CJ,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAG,MAAA,qBAAc;UACnDH,EADmD,CAAAI,YAAA,EAAO,EACrD;UACLJ,EAAA,CAAAC,cAAA,WAAyB;UACvBD,EAAA,CAAAG,MAAA,qIAEF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAGAJ,EAFJ,CAAAC,cAAA,cAA0B,iBACkC,YAClD;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAO;UACrBJ,EAAA,CAAAC,cAAA,aAAgB;UAAAD,EAAA,CAAAG,MAAA,0BAAG;UACrBH,EADqB,CAAAI,YAAA,EAAI,EAChB;UAEPJ,EADF,CAAAC,cAAA,kBAAgE,YACxD;UAAAD,EAAA,CAAAG,MAAA,0BAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAC/BJ,EAAA,CAAAC,cAAA,aAAgB;UAAAD,EAAA,CAAAG,MAAA,cAAC;UAGvBH,EAHuB,CAAAI,YAAA,EAAI,EACd,EACL,EACF;UAEJJ,EADF,CAAAC,cAAA,eAAwB,eACO;UAC3BD,EAAA,CAAAE,SAAA,eAA0F;UAExFF,EADF,CAAAC,cAAA,eAA+B,eACK;UAAAD,EAAA,CAAAG,MAAA,oBAAE;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAC1CJ,EAAA,CAAAC,cAAA,eAAkC;UAAAD,EAAA,CAAAG,MAAA,oBAAE;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAC1CJ,EAAA,CAAAC,cAAA,eAAkC;UAAAD,EAAA,CAAAG,MAAA,oBAAE;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAC1CJ,EAAA,CAAAC,cAAA,eAAkC;UAAAD,EAAA,CAAAG,MAAA,oBAAE;UAK9CH,EAL8C,CAAAI,YAAA,EAAM,EACtC,EACF,EACF,EACF,EACE;UAMJJ,EAHN,CAAAC,cAAA,mBAAoC,eACX,eACO,cACA;UAAAD,EAAA,CAAAG,MAAA,wBAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC/CJ,EAAA,CAAAC,cAAA,aAA4B;UAAAD,EAAA,CAAAG,MAAA,0DAAkD;UAChFH,EADgF,CAAAI,YAAA,EAAI,EAC9E;UAENJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAuB,UAAA,KAAAuC,6BAAA,mBAEmD;UAezD9D,EAFI,CAAAI,YAAA,EAAM,EACF,EACE;UAMJJ,EAHN,CAAAC,cAAA,mBAAkC,eACT,eACO,cACA;UAAAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC3CJ,EAAA,CAAAC,cAAA,aAA4B;UAAAD,EAAA,CAAAG,MAAA,4DAAoD;UAClFH,EADkF,CAAAI,YAAA,EAAI,EAChF;UAENJ,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAuB,UAAA,KAAAwC,6BAAA,oBAAmE;UA0BrE/D,EAAA,CAAAI,YAAA,EAAM;UAGJJ,EADF,CAAAC,cAAA,eAA4B,kBACgC;UACxDD,EAAA,CAAAG,MAAA,2BACF;UAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACE;UAQAJ,EALV,CAAAC,cAAA,mBAAgC,eACP,eACI,eACe,eACT,cACD;UAAAD,EAAA,CAAAG,MAAA,wBAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC7CJ,EAAA,CAAAC,cAAA,aAA6B;UAAAD,EAAA,CAAAG,MAAA,uCAA+B;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAChEJ,EAAA,CAAAC,cAAA,kBAAgC;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAC3CH,EAD2C,CAAAI,YAAA,EAAS,EAC9C;UACNJ,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAG,MAAA,oBAAE;UAC5BH,EAD4B,CAAAI,YAAA,EAAM,EAC5B;UAIFJ,EAFJ,CAAAC,cAAA,eAAwC,eACX,cACD;UAAAD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC1CJ,EAAA,CAAAC,cAAA,aAA6B;UAAAD,EAAA,CAAAG,MAAA,iCAAoB;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACrDJ,EAAA,CAAAC,cAAA,kBAAkC;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAC5CH,EAD4C,CAAAI,YAAA,EAAS,EAC/C;UACNJ,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAG,MAAA,oBAAE;UAC5BH,EAD4B,CAAAI,YAAA,EAAM,EAC5B;UAIFJ,EAFJ,CAAAC,cAAA,eAAqC,eACR,cACD;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvCJ,EAAA,CAAAC,cAAA,aAA6B;UAAAD,EAAA,CAAAG,MAAA,iCAAyB;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAC1DJ,EAAA,CAAAC,cAAA,kBAAgC;UAAAD,EAAA,CAAAG,MAAA,eAAO;UACzCH,EADyC,CAAAI,YAAA,EAAS,EAC5C;UACNJ,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAG,MAAA,oBAAE;UAIlCH,EAJkC,CAAAI,YAAA,EAAM,EAC5B,EACF,EACF,EACE;UAOFJ,EAJR,CAAAC,cAAA,mBAAoC,eACX,eACW,eACD,cACE;UAAAD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC/CJ,EAAA,CAAAC,cAAA,aAA+B;UAC7BD,EAAA,CAAAG,MAAA,mGACF;UACFH,EADE,CAAAI,YAAA,EAAI,EACA;UAEJJ,EADF,CAAAC,cAAA,eAA6B,eACH;UACtBD,EAAA,CAAAE,SAAA,iBAA+E;UAC/EF,EAAA,CAAAC,cAAA,kBAAgC;UAAAD,EAAA,CAAAG,MAAA,kBAAS;UAC3CH,EAD2C,CAAAI,YAAA,EAAS,EAC9C;UACNJ,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAG,MAAA,mEACF;UAKVH,EALU,CAAAI,YAAA,EAAI,EACA,EACF,EACF,EACE,EACN;;;UAxH4BJ,EAAA,CAAAU,SAAA,IAAqB;UAArBV,EAAA,CAAAK,UAAA,YAAAwD,GAAA,CAAAtB,kBAAA,CAAqB;UA4BtBvC,EAAA,CAAAU,SAAA,GAAmB;UAAnBV,EAAA,CAAAK,UAAA,YAAAwD,GAAA,CAAArB,gBAAA,CAAmB;;;qBA7E5C1C,YAAY,EAAAkE,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEnE,YAAY,EAAAoE,EAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}