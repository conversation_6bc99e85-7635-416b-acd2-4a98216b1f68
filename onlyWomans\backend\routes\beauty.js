const express = require('express');
const router = express.Router();

// Sample beauty products
const beautyProducts = [
  {
    id: 1,
    name: 'Matte Li<PERSON>tick Set',
    brand: 'GlamBeauty',
    price: 899,
    category: 'lip-products',
    image: '/assets/beauty/lipstick1.jpg',
    shades: ['Red', 'Pink', 'Nude', 'Berry'],
    rating: 4.5
  },
  {
    id: 2,
    name: 'Eyeshadow Palette',
    brand: 'ColorPop',
    price: 1299,
    category: 'eye-makeup',
    image: '/assets/beauty/eyeshadow1.jpg',
    shades: ['Neutral', 'Smokey', 'Colorful'],
    rating: 4.7
  }
];

// @route   GET /api/beauty/products
// @desc    Get beauty products
// @access  Public
router.get('/products', async (req, res) => {
  try {
    const { category, brand } = req.query;
    let filteredProducts = beautyProducts;
    
    if (category) {
      filteredProducts = filteredProducts.filter(p => p.category === category);
    }
    
    if (brand) {
      filteredProducts = filteredProducts.filter(p => p.brand === brand);
    }
    
    res.json({
      success: true,
      count: filteredProducts.length,
      data: filteredProducts
    });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Server Error' });
  }
});

// @route   GET /api/beauty/tutorials
// @desc    Get beauty tutorials
// @access  Public
router.get('/tutorials', async (req, res) => {
  try {
    const tutorials = [
      {
        id: 1,
        title: 'Perfect Winged Eyeliner',
        duration: '5 min',
        difficulty: 'Beginner',
        thumbnail: '/assets/tutorials/eyeliner.jpg',
        videoUrl: '/assets/videos/eyeliner.mp4'
      }
    ];
    
    res.json({
      success: true,
      data: tutorials
    });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Server Error' });
  }
});

module.exports = router;
