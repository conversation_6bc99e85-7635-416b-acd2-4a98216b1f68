/* OnlyWomans Home Page Styles */

.ow-header {
  --background: linear-gradient(135deg, var(--ow-rose-500) 0%, var(--ow-purple-500) 100%);
  --color: white;
  box-shadow: var(--ow-shadow-lg);
}

.ow-toolbar {
  --background: transparent;
  --color: white;
  
  .header-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    
    .brand-name {
      font-family: var(--ow-font-family-display);
      font-size: 1.5rem;
      font-weight: 700;
      color: white;
    }
    
    .brand-tagline {
      font-size: 0.75rem;
      opacity: 0.9;
      margin-top: -2px;
    }
  }
  
  ion-button {
    --color: white;
    position: relative;
    
    .notification-badge,
    .cart-badge {
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 0.6rem;
      min-width: 16px;
      height: 16px;
    }
  }
}

.ow-content {
  --background: linear-gradient(135deg, var(--ow-rose-50) 0%, var(--ow-purple-50) 100%);
}

/* Welcome Section */
.welcome-section {
  padding: var(--ow-space-lg) var(--ow-space-md);
  margin-bottom: var(--ow-space-md);
  
  .welcome-content {
    text-align: center;
    
    h2 {
      margin-bottom: var(--ow-space-sm);
      color: var(--ion-color-dark);
    }
    
    .welcome-text {
      color: var(--ion-color-medium);
      font-size: 1rem;
      margin: 0;
    }
  }
}

/* Search Section */
.search-section {
  padding: 0 var(--ow-space-md) var(--ow-space-lg);
  
  .ow-search {
    --background: white;
    --border-radius: var(--ow-radius-lg);
    --box-shadow: var(--ow-shadow-md);
    --placeholder-color: var(--ion-color-medium);
    --icon-color: var(--ion-color-primary);
  }
}

/* Section Styles */
.section-title {
  margin: 0 0 var(--ow-space-md) 0;
  color: var(--ion-color-dark);
  padding: 0 var(--ow-space-md);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--ow-space-md);
  margin-bottom: var(--ow-space-md);
  
  .view-all-btn {
    --color: var(--ion-color-primary);
    font-size: 0.9rem;
    font-weight: 500;
  }
}

/* Offers Section */
.offers-section {
  margin-bottom: var(--ow-space-xl);

  .offers-carousel {
    display: flex;
    overflow-x: auto;
    gap: var(--ow-space-md);
    padding: 0 var(--ow-space-md);
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .offer-slide {
    flex: 0 0 280px;
    scroll-snap-align: start;
  }
  
  .offer-card {
    background: linear-gradient(135deg, var(--ow-rose-100) 0%, var(--ow-purple-100) 100%);
    border-radius: var(--ow-radius-lg);
    padding: var(--ow-space-lg);
    position: relative;
    overflow: hidden;
    min-height: 140px;
    
    .offer-content {
      position: relative;
      z-index: 2;
      
      .offer-title {
        margin: 0 0 var(--ow-space-xs) 0;
        color: var(--ion-color-dark);
      }
      
      .offer-subtitle {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--ion-color-primary);
        margin: 0 0 var(--ow-space-xs) 0;
      }
      
      .offer-description {
        color: var(--ion-color-medium);
        margin: 0 0 var(--ow-space-md) 0;
        font-size: 0.9rem;
      }
    }
    
    .offer-decoration {
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      
      .sparkle {
        position: absolute;
        font-size: 1.5rem;
        opacity: 0.3;
        animation: sparkle 2s infinite;
        
        &:nth-child(1) {
          top: 20%;
          right: 20%;
          animation-delay: 0s;
        }
        
        &:nth-child(2) {
          top: 60%;
          right: 10%;
          animation-delay: 0.7s;
        }
        
        &:nth-child(3) {
          top: 40%;
          right: 40%;
          animation-delay: 1.4s;
        }
      }
    }
  }
}

/* Categories Section */
.categories-section {
  margin-bottom: var(--ow-space-xl);
  
  .categories-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--ow-space-md);
    padding: 0 var(--ow-space-md);
    
    .category-card {
      background: white;
      border-radius: var(--ow-radius-lg);
      padding: var(--ow-space-lg);
      text-align: center;
      box-shadow: var(--ow-shadow-sm);
      transition: all 0.3s ease;
      
      &:active {
        transform: scale(0.98);
        box-shadow: var(--ow-shadow-lg);
      }
      
      .category-icon {
        width: 60px;
        height: 60px;
        border-radius: var(--ow-radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--ow-space-md);
        
        .icon-emoji {
          font-size: 2rem;
        }
      }
      
      .category-name {
        margin: 0 0 var(--ow-space-xs) 0;
        color: var(--ion-color-dark);
      }
      
      .category-description {
        color: var(--ion-color-medium);
        font-size: 0.85rem;
        margin: 0;
      }
    }
  }
}

/* Products Section */
.products-section {
  margin-bottom: var(--ow-space-xl);

  .products-carousel {
    display: flex;
    overflow-x: auto;
    gap: var(--ow-space-md);
    padding: 0 var(--ow-space-md);
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .product-slide {
    flex: 0 0 200px;
    scroll-snap-align: start;
  }
  
  .product-card {
    background: white;
    border-radius: var(--ow-radius-lg);
    overflow: hidden;
    box-shadow: var(--ow-shadow-sm);
    transition: all 0.3s ease;
    width: 200px;
    
    &:active {
      transform: translateY(-2px);
      box-shadow: var(--ow-shadow-lg);
    }
    
    .product-image-container {
      position: relative;
      height: 160px;
      overflow: hidden;
      
      .product-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .product-badge {
        position: absolute;
        top: var(--ow-space-sm);
        left: var(--ow-space-sm);
        background: var(--ion-color-primary);
        color: white;
        padding: 2px 8px;
        border-radius: var(--ow-radius-sm);
        font-size: 0.7rem;
        font-weight: 500;
      }
      
      .wishlist-btn {
        position: absolute;
        top: var(--ow-space-sm);
        right: var(--ow-space-sm);
        --background: rgba(255, 255, 255, 0.9);
        --color: var(--ion-color-primary);
        --border-radius: var(--ow-radius-full);
        width: 32px;
        height: 32px;
      }
    }
    
    .product-info {
      padding: var(--ow-space-md);
      
      .product-name {
        margin: 0 0 var(--ow-space-xs) 0;
        color: var(--ion-color-dark);
        font-size: 0.9rem;
        line-height: 1.3;
      }
      
      .product-rating {
        display: flex;
        align-items: center;
        margin-bottom: var(--ow-space-sm);
        
        .star-icon {
          color: var(--ow-gold-500);
          font-size: 0.8rem;
          margin-right: 2px;
        }
        
        .rating-value {
          font-size: 0.8rem;
          font-weight: 500;
          color: var(--ion-color-dark);
          margin-right: 4px;
        }
        
        .rating-count {
          font-size: 0.75rem;
          color: var(--ion-color-medium);
        }
      }
      
      .product-price {
        margin-bottom: var(--ow-space-md);
        
        .current-price {
          font-size: 1.1rem;
          font-weight: 600;
          margin-right: var(--ow-space-sm);
        }
        
        .original-price {
          font-size: 0.9rem;
          color: var(--ion-color-medium);
          text-decoration: line-through;
        }
      }
      
      .add-to-cart-btn {
        width: 100%;
        --border-radius: var(--ow-radius-md);
        font-size: 0.85rem;
      }
    }
  }
}

/* Inspiration Section */
.inspiration-section {
  margin-bottom: var(--ow-space-xl);
  
  .inspiration-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--ow-space-md);
    padding: 0 var(--ow-space-md);
    
    .inspiration-card {
      background: linear-gradient(135deg, white 0%, var(--ow-rose-50) 100%);
      border-radius: var(--ow-radius-lg);
      padding: var(--ow-space-lg);
      text-align: center;
      border: 1px solid var(--ow-rose-200);
      
      .inspiration-content {
        h4 {
          margin: 0 0 var(--ow-space-xs) 0;
          color: var(--ion-color-dark);
        }
        
        p {
          color: var(--ion-color-medium);
          font-size: 0.85rem;
          margin: 0 0 var(--ow-space-md) 0;
        }
      }
    }
  }
}

/* Bottom Spacing */
.bottom-spacing {
  height: var(--ow-space-xl);
}

/* Animations */
@keyframes sparkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}
