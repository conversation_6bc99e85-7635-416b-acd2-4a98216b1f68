<ion-header [translucent]="true" class="ow-header">
  <ion-toolbar class="ow-toolbar">
    <ion-title class="ow-heading">My Wishlist ❤️</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ow-content">
  <div *ngIf="wishlistItems.length > 0; else emptyWishlist" class="wishlist-content">
    <div class="wishlist-items">
      <div *ngFor="let item of wishlistItems" class="wishlist-item ow-card">
        <div class="item-image">
          <img [src]="item.image" [alt]="item.name">
        </div>
        <div class="item-details">
          <h4 class="item-name ow-heading-sm">{{ item.name }}</h4>
          <div class="item-rating">
            <ion-icon name="star" class="star-icon"></ion-icon>
            <span>{{ item.rating }} ({{ item.reviews }})</span>
          </div>
          <div class="item-price">
            <span class="current-price ow-text-primary">₹{{ item.price }}</span>
            <span class="original-price">₹{{ item.originalPrice }}</span>
          </div>
          <div class="item-actions">
            <ion-button fill="solid" class="ow-btn ow-btn-primary" (click)="addToCart(item)">
              Add to Cart
            </ion-button>
            <ion-button fill="clear" class="remove-btn" (click)="removeFromWishlist(item)">
              <ion-icon name="trash" class="ow-icon-muted"></ion-icon>
            </ion-button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-template #emptyWishlist>
    <div class="empty-wishlist">
      <ion-icon name="heart-outline" class="empty-icon"></ion-icon>
      <h3 class="ow-heading-md">Your wishlist is empty</h3>
      <p>Add items you love to see them here</p>
      <ion-button fill="solid" class="ow-btn ow-btn-primary">
        Start Shopping
      </ion-button>
    </div>
  </ng-template>
</ion-content>
