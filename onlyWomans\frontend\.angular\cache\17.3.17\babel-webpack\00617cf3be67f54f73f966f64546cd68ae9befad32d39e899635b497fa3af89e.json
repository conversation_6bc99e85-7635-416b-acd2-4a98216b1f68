{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptors } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { importProvidersFrom } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n// Routes\nconst routes = [{\n  path: '',\n  loadComponent: () => import('./app/pages/home/<USER>').then(m => m.HomeComponent)\n}, {\n  path: '**',\n  redirectTo: ''\n}];\n// HTTP Interceptors\nconst authInterceptor = (req, next) => {\n  const token = localStorage.getItem('onlywomans_token');\n  if (token) {\n    req = req.clone({\n      setHeaders: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  return next(req);\n};\nconst errorInterceptor = (req, next) => {\n  return next(req);\n};\nbootstrapApplication(AppComponent, {\n  providers: [provideRouter(routes), provideHttpClient(withInterceptors([authInterceptor, errorInterceptor])), provideAnimations(), importProvidersFrom(CommonModule)]\n}).catch(err => console.error(err));", "map": {"version": 3, "names": ["bootstrapApplication", "AppComponent", "provideRouter", "provideHttpClient", "withInterceptors", "provideAnimations", "importProvidersFrom", "CommonModule", "routes", "path", "loadComponent", "then", "m", "HomeComponent", "redirectTo", "authInterceptor", "req", "next", "token", "localStorage", "getItem", "clone", "setHeaders", "Authorization", "errorInterceptor", "providers", "catch", "err", "console", "error"], "sources": ["E:\\Fahion\\DFashion\\onlyWomans\\frontend\\src\\main.ts"], "sourcesContent": ["import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptors } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { importProvidersFrom } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n// Routes\nconst routes = [\n  {\n    path: '',\n    loadComponent: () => import('./app/pages/home/<USER>').then(m => m.HomeComponent)\n  },\n  {\n    path: '**',\n    redirectTo: ''\n  }\n];\n\n// HTTP Interceptors\nconst authInterceptor = (req: any, next: any) => {\n  const token = localStorage.getItem('onlywomans_token');\n  if (token) {\n    req = req.clone({\n      setHeaders: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  return next(req);\n};\n\nconst errorInterceptor = (req: any, next: any) => {\n  return next(req);\n};\n\nbootstrapApplication(AppComponent, {\n  providers: [\n    provideRouter(routes),\n    provideHttpClient(withInterceptors([authInterceptor, errorInterceptor])),\n    provideAnimations(),\n    importProvidersFrom(CommonModule)\n  ]\n}).catch(err => console.error(err));\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC1E,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,mBAAmB,QAAQ,eAAe;AACnD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa;CACzF,EACD;EACEJ,IAAI,EAAE,IAAI;EACVK,UAAU,EAAE;CACb,CACF;AAED;AACA,MAAMC,eAAe,GAAGA,CAACC,GAAQ,EAAEC,IAAS,KAAI;EAC9C,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;EACtD,IAAIF,KAAK,EAAE;IACTF,GAAG,GAAGA,GAAG,CAACK,KAAK,CAAC;MACdC,UAAU,EAAE;QACVC,aAAa,EAAE,UAAUL,KAAK;;KAEjC,CAAC;;EAEJ,OAAOD,IAAI,CAACD,GAAG,CAAC;AAClB,CAAC;AAED,MAAMQ,gBAAgB,GAAGA,CAACR,GAAQ,EAAEC,IAAS,KAAI;EAC/C,OAAOA,IAAI,CAACD,GAAG,CAAC;AAClB,CAAC;AAEDhB,oBAAoB,CAACC,YAAY,EAAE;EACjCwB,SAAS,EAAE,CACTvB,aAAa,CAACM,MAAM,CAAC,EACrBL,iBAAiB,CAACC,gBAAgB,CAAC,CAACW,eAAe,EAAES,gBAAgB,CAAC,CAAC,CAAC,EACxEnB,iBAAiB,EAAE,EACnBC,mBAAmB,CAACC,YAAY,CAAC;CAEpC,CAAC,CAACmB,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}