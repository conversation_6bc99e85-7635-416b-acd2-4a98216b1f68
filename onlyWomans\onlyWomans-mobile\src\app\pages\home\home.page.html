<ion-header [translucent]="true" class="ow-header">
  <ion-toolbar class="ow-toolbar">
    <ion-title class="ow-heading">
      <div class="header-content">
        <span class="brand-name">OnlyWomans</span>
        <span class="brand-tagline">✨ Fashion for Every Woman</span>
      </div>
    </ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="onSearchClick()" class="ow-btn">
        <ion-icon name="search" class="ow-icon"></ion-icon>
      </ion-button>
      <ion-button fill="clear" (click)="onNotificationClick()" class="ow-btn">
        <ion-icon name="notifications" class="ow-icon"></ion-icon>
        <ion-badge color="danger" class="notification-badge">3</ion-badge>
      </ion-button>
      <ion-button fill="clear" (click)="onCartClick()" class="ow-btn">
        <ion-icon name="bag" class="ow-icon"></ion-icon>
        <ion-badge color="primary" class="cart-badge">2</ion-badge>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ow-content">
  <!-- Welcome Section -->
  <div class="welcome-section ow-gradient-soft">
    <div class="welcome-content">
      <h2 class="ow-heading-lg">Good Morning, Priya! 🌸</h2>
      <p class="welcome-text">Discover the latest trends in fashion & beauty</p>
    </div>
  </div>

  <!-- Search Bar -->
  <div class="search-section">
    <ion-searchbar 
      placeholder="Search for dresses, sarees, makeup..." 
      class="ow-search"
      (ionInput)="onSearchClick()">
    </ion-searchbar>
  </div>

  <!-- Special Offers Carousel -->
  <div class="offers-section">
    <h3 class="section-title ow-heading-md">Special Offers 🎉</h3>
    <div class="offers-carousel">
      <div *ngFor="let offer of offers" class="offer-slide" (click)="onOfferClick(offer)">
        <div class="offer-card ow-card-elegant">
          <div class="offer-content">
            <h4 class="offer-title ow-heading-md">{{ offer.title }}</h4>
            <p class="offer-subtitle">{{ offer.subtitle }}</p>
            <p class="offer-description">{{ offer.description }}</p>
            <ion-button fill="solid" class="ow-btn ow-btn-primary">
              Shop Now
              <ion-icon name="arrow-forward" slot="end"></ion-icon>
            </ion-button>
          </div>
          <div class="offer-decoration">
            <div class="sparkle">✨</div>
            <div class="sparkle">💫</div>
            <div class="sparkle">⭐</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Categories Section -->
  <div class="categories-section">
    <div class="section-header">
      <h3 class="section-title ow-heading-md">Shop by Category</h3>
      <ion-button fill="clear" class="view-all-btn ow-text-primary">
        View All
        <ion-icon name="chevron-forward" slot="end"></ion-icon>
      </ion-button>
    </div>
    
    <div class="categories-grid">
      <div 
        *ngFor="let category of featuredCategories" 
        class="category-card ow-card"
        (click)="onCategoryClick(category)">
        <div class="category-icon" [style.background]="category.color">
          <span class="icon-emoji">{{ category.icon }}</span>
        </div>
        <h4 class="category-name ow-heading-sm">{{ category.name }}</h4>
        <p class="category-description">{{ category.description }}</p>
      </div>
    </div>
  </div>

  <!-- Featured Products -->
  <div class="products-section">
    <div class="section-header">
      <h3 class="section-title ow-heading-md">Trending Now 🔥</h3>
      <ion-button fill="clear" class="view-all-btn ow-text-primary">
        View All
        <ion-icon name="chevron-forward" slot="end"></ion-icon>
      </ion-button>
    </div>

    <div class="products-carousel">
      <div *ngFor="let product of featuredProducts" class="product-slide">
        <div class="product-card ow-product-card" (click)="onProductClick(product)">
          <div class="product-image-container">
            <img [src]="product.image" [alt]="product.name" class="product-image">
            <div class="product-badge ow-badge">{{ product.badge }}</div>
            <ion-button
              fill="clear"
              class="wishlist-btn"
              (click)="addToWishlist(product, $event)">
              <ion-icon name="heart-outline" class="ow-icon"></ion-icon>
            </ion-button>
          </div>

          <div class="product-info">
            <h4 class="product-name ow-heading-sm">{{ product.name }}</h4>
            <div class="product-rating">
              <ion-icon name="star" class="star-icon"></ion-icon>
              <span class="rating-value">{{ product.rating }}</span>
              <span class="rating-count">({{ product.reviews }})</span>
            </div>
            <div class="product-price">
              <span class="current-price ow-text-primary">₹{{ product.price }}</span>
              <span class="original-price">₹{{ product.originalPrice }}</span>
            </div>
            <ion-button
              fill="solid"
              size="small"
              class="ow-btn ow-btn-primary add-to-cart-btn"
              (click)="addToCart(product, $event)">
              Add to Cart
            </ion-button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Style Inspiration -->
  <div class="inspiration-section">
    <h3 class="section-title ow-heading-md">Style Inspiration 💫</h3>
    <div class="inspiration-grid">
      <div class="inspiration-card ow-card-elegant">
        <div class="inspiration-content">
          <h4 class="ow-heading-sm">Summer Vibes</h4>
          <p>Light fabrics & bright colors</p>
          <ion-button fill="outline" class="ow-btn ow-btn-outline">Explore</ion-button>
        </div>
      </div>
      <div class="inspiration-card ow-card-elegant">
        <div class="inspiration-content">
          <h4 class="ow-heading-sm">Office Chic</h4>
          <p>Professional & stylish</p>
          <ion-button fill="outline" class="ow-btn ow-btn-outline">Explore</ion-button>
        </div>
      </div>
    </div>
  </div>

  <!-- Bottom Spacing -->
  <div class="bottom-spacing"></div>
</ion-content>
