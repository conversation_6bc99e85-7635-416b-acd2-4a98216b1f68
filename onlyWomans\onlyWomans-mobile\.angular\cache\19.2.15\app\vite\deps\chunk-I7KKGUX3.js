import {
  win
} from "./chunk-AM533ZC5.js";

// node_modules/@ionic/core/dist/esm/capacitor-CFERIeaU.js
var getCapacitor = () => {
  if (win !== void 0) {
    return win.Capacitor;
  }
  return void 0;
};

export {
  getCapacitor
};
/*! Bundled license information:

@ionic/core/dist/esm/capacitor-CFERIeaU.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=chunk-I7KKGUX3.js.map
