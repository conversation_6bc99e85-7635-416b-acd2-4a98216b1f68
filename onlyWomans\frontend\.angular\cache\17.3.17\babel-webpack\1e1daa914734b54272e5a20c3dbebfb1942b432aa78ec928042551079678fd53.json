{"ast": null, "code": "import { from } from './from';\nexport function pairs(obj, scheduler) {\n  return from(Object.entries(obj), scheduler);\n}", "map": {"version": 3, "names": ["from", "pairs", "obj", "scheduler", "Object", "entries"], "sources": ["E:/Fahion/DFashion/onlyWomans/frontend/node_modules/rxjs/dist/esm/internal/observable/pairs.js"], "sourcesContent": ["import { from } from './from';\nexport function pairs(obj, scheduler) {\n    return from(Object.entries(obj), scheduler);\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,QAAQ;AAC7B,OAAO,SAASC,KAAKA,CAACC,GAAG,EAAEC,SAAS,EAAE;EAClC,OAAOH,IAAI,CAACI,MAAM,CAACC,OAAO,CAACH,GAAG,CAAC,EAAEC,SAAS,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}