{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nexport class QueueAction extends AsyncAction {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n  }\n  schedule(state, delay = 0) {\n    if (delay > 0) {\n      return super.schedule(state, delay);\n    }\n    this.delay = delay;\n    this.state = state;\n    this.scheduler.flush(this);\n    return this;\n  }\n  execute(state, delay) {\n    return delay > 0 || this.closed ? super.execute(state, delay) : this._execute(state, delay);\n  }\n  requestAsyncId(scheduler, id, delay = 0) {\n    if (delay != null && delay > 0 || delay == null && this.delay > 0) {\n      return super.requestAsyncId(scheduler, id, delay);\n    }\n    scheduler.flush(this);\n    return 0;\n  }\n}", "map": {"version": 3, "names": ["AsyncAction", "QueueAction", "constructor", "scheduler", "work", "schedule", "state", "delay", "flush", "execute", "closed", "_execute", "requestAsyncId", "id"], "sources": ["E:/Fahion/DFashion/onlyWomans/frontend/node_modules/rxjs/dist/esm/internal/scheduler/QueueAction.js"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nexport class QueueAction extends AsyncAction {\n    constructor(scheduler, work) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n    }\n    schedule(state, delay = 0) {\n        if (delay > 0) {\n            return super.schedule(state, delay);\n        }\n        this.delay = delay;\n        this.state = state;\n        this.scheduler.flush(this);\n        return this;\n    }\n    execute(state, delay) {\n        return delay > 0 || this.closed ? super.execute(state, delay) : this._execute(state, delay);\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        if ((delay != null && delay > 0) || (delay == null && this.delay > 0)) {\n            return super.requestAsyncId(scheduler, id, delay);\n        }\n        scheduler.flush(this);\n        return 0;\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,eAAe;AAC3C,OAAO,MAAMC,WAAW,SAASD,WAAW,CAAC;EACzCE,WAAWA,CAACC,SAAS,EAAEC,IAAI,EAAE;IACzB,KAAK,CAACD,SAAS,EAAEC,IAAI,CAAC;IACtB,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,IAAI,GAAGA,IAAI;EACpB;EACAC,QAAQA,CAACC,KAAK,EAAEC,KAAK,GAAG,CAAC,EAAE;IACvB,IAAIA,KAAK,GAAG,CAAC,EAAE;MACX,OAAO,KAAK,CAACF,QAAQ,CAACC,KAAK,EAAEC,KAAK,CAAC;IACvC;IACA,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACH,SAAS,CAACK,KAAK,CAAC,IAAI,CAAC;IAC1B,OAAO,IAAI;EACf;EACAC,OAAOA,CAACH,KAAK,EAAEC,KAAK,EAAE;IAClB,OAAOA,KAAK,GAAG,CAAC,IAAI,IAAI,CAACG,MAAM,GAAG,KAAK,CAACD,OAAO,CAACH,KAAK,EAAEC,KAAK,CAAC,GAAG,IAAI,CAACI,QAAQ,CAACL,KAAK,EAAEC,KAAK,CAAC;EAC/F;EACAK,cAAcA,CAACT,SAAS,EAAEU,EAAE,EAAEN,KAAK,GAAG,CAAC,EAAE;IACrC,IAAKA,KAAK,IAAI,IAAI,IAAIA,KAAK,GAAG,CAAC,IAAMA,KAAK,IAAI,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAAE,EAAE;MACnE,OAAO,KAAK,CAACK,cAAc,CAACT,SAAS,EAAEU,EAAE,EAAEN,KAAK,CAAC;IACrD;IACAJ,SAAS,CAACK,KAAK,CAAC,IAAI,CAAC;IACrB,OAAO,CAAC;EACZ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}