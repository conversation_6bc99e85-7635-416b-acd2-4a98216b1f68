import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-wishlist',
  templateUrl: './wishlist.page.html',
  styleUrls: ['./wishlist.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class WishlistPage implements OnInit {

  wishlistItems = [
    {
      id: 1,
      name: 'Floral Maxi Dress',
      price: 2499,
      originalPrice: 3499,
      image: 'assets/products/dress1.jpg',
      rating: 4.8,
      reviews: 124
    },
    {
      id: 2,
      name: 'Silk Saree Collection',
      price: 4999,
      originalPrice: 6999,
      image: 'assets/products/saree1.jpg',
      rating: 4.9,
      reviews: 89
    }
  ];

  constructor() { }

  ngOnInit() {
  }

  removeFromWishlist(item: any) {
    const index = this.wishlistItems.indexOf(item);
    if (index > -1) {
      this.wishlistItems.splice(index, 1);
    }
  }

  addToCart(item: any) {
    console.log('Added to cart:', item);
  }
}
