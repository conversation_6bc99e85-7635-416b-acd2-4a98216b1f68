<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>OnlyWomans - Fashion for Every Woman 🌸</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  
  <!-- SEO Meta Tags -->
  <meta name="description" content="Discover the latest in women's fashion, beauty, and accessories. Shop ethnic wear, western outfits, makeup, jewelry and more at OnlyWomans - your ultimate fashion destination.">
  <meta name="keywords" content="women fashion, ethnic wear, western wear, sarees, dresses, makeup, beauty, jewelry, accessories, online shopping, women clothing">
  <meta name="author" content="OnlyWomans Team">
  
  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="OnlyWomans - Fashion for Every Woman">
  <meta property="og:description" content="Discover the latest in women's fashion, beauty, and accessories. Shop ethnic wear, western outfits, makeup, jewelry and more.">
  <meta property="og:image" content="assets/images/og-image.jpg">
  <meta property="og:url" content="https://onlywomans.com">
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="OnlyWomans">
  
  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="OnlyWomans - Fashion for Every Woman">
  <meta name="twitter:description" content="Discover the latest in women's fashion, beauty, and accessories.">
  <meta name="twitter:image" content="assets/images/twitter-card.jpg">
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="assets/icons/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="assets/icons/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="assets/icons/favicon-16x16.png">
  
  <!-- PWA Manifest -->
  <link rel="manifest" href="manifest.json">
  <meta name="theme-color" content="#ec4899">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="OnlyWomans">
  
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- CSS Variables for Theme -->
  <style>
    :root {
      --primary-color: #ec4899;
      --secondary-color: #a855f7;
      --accent-color: #f59e0b;
      --background-color: #fdf2f8;
      --text-color: #1f2937;
      --border-color: #e5e7eb;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #fdf2f8 0%, #faf5ff 100%);
      color: var(--text-color);
      line-height: 1.6;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    /* Loading Screen Styles */
    .initial-loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #ec4899, #a855f7);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      color: white;
    }
    
    .loading-content {
      text-align: center;
      animation: fadeInUp 0.8s ease-out;
    }
    
    .loading-logo {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }
    
    .loading-text {
      font-size: 1.2rem;
      opacity: 0.9;
      margin-bottom: 2rem;
    }
    
    .loading-spinner {
      display: flex;
      justify-content: center;
      gap: 0.5rem;
    }
    
    .spinner-dot {
      width: 12px;
      height: 12px;
      background: white;
      border-radius: 50%;
      animation: bounce 1.4s ease-in-out infinite both;
    }
    
    .spinner-dot:nth-child(1) { animation-delay: -0.32s; }
    .spinner-dot:nth-child(2) { animation-delay: -0.16s; }
    
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    @keyframes bounce {
      0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
      }
      40% {
        transform: scale(1);
        opacity: 1;
      }
    }
    
    /* Hide loading screen when app loads */
    .app-loaded .initial-loading {
      display: none;
    }
  </style>
</head>
<body>
  <!-- Initial Loading Screen -->
  <div class="initial-loading" id="initialLoading">
    <div class="loading-content">
      <div class="loading-logo">
        <span>🌸</span>
        <span>OnlyWomans</span>
      </div>
      <div class="loading-text">Fashion for Every Woman</div>
      <div class="loading-spinner">
        <div class="spinner-dot"></div>
        <div class="spinner-dot"></div>
        <div class="spinner-dot"></div>
      </div>
    </div>
  </div>

  <!-- Angular App Root -->
  <ow-root></ow-root>
  
  <!-- Remove loading screen when Angular loads -->
  <script>
    // Hide loading screen when Angular app is ready
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(function() {
        document.body.classList.add('app-loaded');
      }, 2000);
    });
    
    // Service Worker Registration
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/ngsw-worker.js')
          .then(registration => {
            console.log('SW registered: ', registration);
          })
          .catch(registrationError => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  </script>
</body>
</html>
