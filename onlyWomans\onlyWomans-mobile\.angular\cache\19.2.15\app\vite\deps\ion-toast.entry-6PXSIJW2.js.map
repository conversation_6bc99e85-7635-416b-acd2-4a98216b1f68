{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-toast.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { m as printIonWarning, r as registerInstance, d as createEvent, l as config, e as getIonMode, o as printIonError, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-AaTyISnm.js';\nimport { g as getElementRoot, r as raf } from './helpers-1O4D2b7y.js';\nimport { c as createLockController } from './lock-controller-B-hirT0v.js';\nimport { O as OVERLAY_GESTURE_PRIORITY, d as createDelegateController, e as createTriggerController, i as isCancel, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod, s as safeCall, G as GESTURE } from './overlays-8Y2rA-ps.js';\nimport { c as createColorClasses, g as getClassMap } from './theme-DiVJyqlX.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport { w as win } from './index-ZjP4CjeZ.js';\nimport { createGesture } from './index-CfgBF1SE.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './framework-delegate-DxcnWic_.js';\nimport './gesture-controller-BTEOs1at.js';\n\n/**\n * Calculate the CSS top and bottom position of the toast, to be used\n * as starting points for the animation keyframes.\n *\n * The default animations for both MD and iOS\n * use translateY, which calculates from the\n * top edge of the screen. This behavior impacts\n * how we compute the offset when a toast has\n * position='bottom' since we need to calculate from\n * the bottom edge of the screen instead.\n *\n * @param position The value of the toast's position prop.\n * @param positionAnchor The element the toast should be anchored to,\n * if applicable.\n * @param mode The toast component's mode (md, ios, etc).\n * @param toast A reference to the toast element itself.\n */\nfunction getAnimationPosition(position, positionAnchor, mode, toast) {\n  /**\n   * Start with a predefined offset from the edge the toast will be\n   * positioned relative to, whether on the screen or anchor element.\n   */\n  let offset;\n  if (mode === 'md') {\n    offset = position === 'top' ? 8 : -8;\n  } else {\n    offset = position === 'top' ? 10 : -10;\n  }\n  /**\n   * If positionAnchor is defined, add in the distance from the target\n   * screen edge to the target anchor edge. For position=\"top\", the\n   * bottom anchor edge is targeted. For position=\"bottom\", the top\n   * anchor edge is targeted.\n   */\n  if (positionAnchor && win) {\n    warnIfAnchorIsHidden(positionAnchor, toast);\n    const box = positionAnchor.getBoundingClientRect();\n    if (position === 'top') {\n      offset += box.bottom;\n    } else if (position === 'bottom') {\n      /**\n       * Just box.top is the distance from the top edge of the screen\n       * to the top edge of the anchor. We want to calculate from the\n       * bottom edge of the screen instead.\n       */\n      offset -= win.innerHeight - box.top;\n    }\n    /**\n     * We don't include safe area here because that should already be\n     * accounted for when checking the position of the anchor.\n     */\n    return {\n      top: `${offset}px`,\n      bottom: `${offset}px`\n    };\n  } else {\n    return {\n      top: `calc(${offset}px + var(--ion-safe-area-top, 0px))`,\n      bottom: `calc(${offset}px - var(--ion-safe-area-bottom, 0px))`\n    };\n  }\n}\n/**\n * If the anchor element is hidden, getBoundingClientRect()\n * will return all 0s for it, which can cause unexpected\n * results in the position calculation when animating.\n */\nfunction warnIfAnchorIsHidden(positionAnchor, toast) {\n  if (positionAnchor.offsetParent === null) {\n    printIonWarning('[ion-toast] - The positionAnchor element for ion-toast was found in the DOM, but appears to be hidden. This may lead to unexpected positioning of the toast.', toast);\n  }\n}\n/**\n * Returns the top offset required to place\n * the toast in the middle of the screen.\n * Only needed when position=\"toast\".\n * @param toastHeight - The height of the ion-toast element\n * @param wrapperHeight - The height of the .toast-wrapper element\n * inside the toast's shadow root.\n */\nconst getOffsetForMiddlePosition = (toastHeight, wrapperHeight) => {\n  return Math.floor(toastHeight / 2 - wrapperHeight / 2);\n};\n\n/**\n * iOS Toast Enter Animation\n */\nconst iosEnterAnimation = (baseEl, opts) => {\n  const baseAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  const {\n    position,\n    top,\n    bottom\n  } = opts;\n  const root = getElementRoot(baseEl);\n  const wrapperEl = root.querySelector('.toast-wrapper');\n  wrapperAnimation.addElement(wrapperEl);\n  switch (position) {\n    case 'top':\n      wrapperAnimation.fromTo('transform', 'translateY(-100%)', `translateY(${top})`);\n      break;\n    case 'middle':\n      const topPosition = getOffsetForMiddlePosition(baseEl.clientHeight, wrapperEl.clientHeight);\n      wrapperEl.style.top = `${topPosition}px`;\n      wrapperAnimation.fromTo('opacity', 0.01, 1);\n      break;\n    default:\n      wrapperAnimation.fromTo('transform', 'translateY(100%)', `translateY(${bottom})`);\n      break;\n  }\n  return baseAnimation.easing('cubic-bezier(.155,1.105,.295,1.12)').duration(400).addAnimation(wrapperAnimation);\n};\n\n/**\n * iOS Toast Leave Animation\n */\nconst iosLeaveAnimation = (baseEl, opts) => {\n  const baseAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  const {\n    position,\n    top,\n    bottom\n  } = opts;\n  const root = getElementRoot(baseEl);\n  const wrapperEl = root.querySelector('.toast-wrapper');\n  wrapperAnimation.addElement(wrapperEl);\n  switch (position) {\n    case 'top':\n      wrapperAnimation.fromTo('transform', `translateY(${top})`, 'translateY(-100%)');\n      break;\n    case 'middle':\n      wrapperAnimation.fromTo('opacity', 0.99, 0);\n      break;\n    default:\n      wrapperAnimation.fromTo('transform', `translateY(${bottom})`, 'translateY(100%)');\n      break;\n  }\n  return baseAnimation.easing('cubic-bezier(.36,.66,.04,1)').duration(300).addAnimation(wrapperAnimation);\n};\n\n/**\n * MD Toast Enter Animation\n */\nconst mdEnterAnimation = (baseEl, opts) => {\n  const baseAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  const {\n    position,\n    top,\n    bottom\n  } = opts;\n  const root = getElementRoot(baseEl);\n  const wrapperEl = root.querySelector('.toast-wrapper');\n  wrapperAnimation.addElement(wrapperEl);\n  switch (position) {\n    case 'top':\n      wrapperEl.style.setProperty('transform', `translateY(${top})`);\n      wrapperAnimation.fromTo('opacity', 0.01, 1);\n      break;\n    case 'middle':\n      const topPosition = getOffsetForMiddlePosition(baseEl.clientHeight, wrapperEl.clientHeight);\n      wrapperEl.style.top = `${topPosition}px`;\n      wrapperAnimation.fromTo('opacity', 0.01, 1);\n      break;\n    default:\n      wrapperEl.style.setProperty('transform', `translateY(${bottom})`);\n      wrapperAnimation.fromTo('opacity', 0.01, 1);\n      break;\n  }\n  return baseAnimation.easing('cubic-bezier(.36,.66,.04,1)').duration(400).addAnimation(wrapperAnimation);\n};\n\n/**\n * md Toast Leave Animation\n */\nconst mdLeaveAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  const root = getElementRoot(baseEl);\n  const wrapperEl = root.querySelector('.toast-wrapper');\n  wrapperAnimation.addElement(wrapperEl).fromTo('opacity', 0.99, 0);\n  return baseAnimation.easing('cubic-bezier(.36,.66,.04,1)').duration(300).addAnimation(wrapperAnimation);\n};\n\n/**\n * Create a gesture that allows the Toast\n * to be swiped to dismiss.\n * @param el - The Toast element\n * @param toastPosition - The last computed position of the Toast. This is computed in the \"present\" method.\n * @param onDismiss - A callback to fire when the Toast was swiped to dismiss.\n */\nconst createSwipeToDismissGesture = (el, toastPosition, onDismiss) => {\n  /**\n   * Users should swipe on the visible toast wrapper\n   * rather than on ion-toast which covers the entire screen.\n   * When testing the class instance the inner wrapper will not\n   * be defined. As a result, we use a placeholder element in those environments.\n   */\n  const wrapperEl = getElementRoot(el).querySelector('.toast-wrapper');\n  const hostElHeight = el.clientHeight;\n  const wrapperElBox = wrapperEl.getBoundingClientRect();\n  /**\n   * The maximum amount that\n   * the toast can be swiped. This should\n   * account for the wrapper element's height\n   * too so the toast can be swiped offscreen\n   * completely.\n   */\n  let MAX_SWIPE_DISTANCE = 0;\n  /**\n   * The step value at which a toast\n   * is eligible for dismissing via gesture.\n   */\n  const DISMISS_THRESHOLD = 0.5;\n  /**\n   * The middle position Toast starts 50% of the way\n   * through the animation, so we need to use this\n   * as the starting point for our step values.\n   */\n  const STEP_OFFSET = el.position === 'middle' ? 0.5 : 0;\n  /**\n   * When the Toast is at the top users will be\n   * swiping up. As a result, the delta values will be\n   * negative numbers which will result in negative steps\n   * and thresholds. As a result, we need to make those numbers\n   * positive.\n   */\n  const INVERSION_FACTOR = el.position === 'top' ? -1 : 1;\n  /**\n   * The top offset that places the\n   * toast in the middle of the screen.\n   * Only needed when position=\"middle\".\n   */\n  const topPosition = getOffsetForMiddlePosition(hostElHeight, wrapperElBox.height);\n  const SWIPE_UP_DOWN_KEYFRAMES = [{\n    offset: 0,\n    transform: `translateY(-${topPosition + wrapperElBox.height}px)`\n  }, {\n    offset: 0.5,\n    transform: `translateY(0px)`\n  }, {\n    offset: 1,\n    transform: `translateY(${topPosition + wrapperElBox.height}px)`\n  }];\n  const swipeAnimation = createAnimation('toast-swipe-to-dismiss-animation').addElement(wrapperEl)\n  /**\n   * The specific value here does not actually\n   * matter. We just need this to be a positive\n   * value so the animation does not jump\n   * to the end when the user beings to drag.\n   */.duration(100);\n  switch (el.position) {\n    case 'middle':\n      MAX_SWIPE_DISTANCE = hostElHeight + wrapperElBox.height;\n      swipeAnimation.keyframes(SWIPE_UP_DOWN_KEYFRAMES);\n      /**\n       * Toast can be swiped up or down but\n       * should start in the middle of the screen.\n       */\n      swipeAnimation.progressStart(true, 0.5);\n      break;\n    case 'top':\n      /**\n       * The bottom edge of the wrapper\n       * includes the distance between the top\n       * of the screen and the top of the wrapper\n       * as well as the wrapper height so the wrapper\n       * can be dragged fully offscreen.\n       */\n      MAX_SWIPE_DISTANCE = wrapperElBox.bottom;\n      swipeAnimation.keyframes([{\n        offset: 0,\n        transform: `translateY(${toastPosition.top})`\n      }, {\n        offset: 1,\n        transform: 'translateY(-100%)'\n      }]);\n      swipeAnimation.progressStart(true, 0);\n      break;\n    case 'bottom':\n    default:\n      /**\n       * This computes the distance between the\n       * top of the wrapper and the bottom of the\n       * screen including the height of the wrapper\n       * element so it can be dragged fully offscreen.\n       */\n      MAX_SWIPE_DISTANCE = hostElHeight - wrapperElBox.top;\n      swipeAnimation.keyframes([{\n        offset: 0,\n        transform: `translateY(${toastPosition.bottom})`\n      }, {\n        offset: 1,\n        transform: 'translateY(100%)'\n      }]);\n      swipeAnimation.progressStart(true, 0);\n      break;\n  }\n  const computeStep = delta => {\n    return delta * INVERSION_FACTOR / MAX_SWIPE_DISTANCE;\n  };\n  const onMove = detail => {\n    const step = STEP_OFFSET + computeStep(detail.deltaY);\n    swipeAnimation.progressStep(step);\n  };\n  const onEnd = detail => {\n    const velocity = detail.velocityY;\n    const threshold = (detail.deltaY + velocity * 1000) / MAX_SWIPE_DISTANCE * INVERSION_FACTOR;\n    /**\n     * Disable the gesture for the remainder of the animation.\n     * It will be re-enabled if the toast animates back to\n     * its initial presented position.\n     */\n    gesture.enable(false);\n    let shouldDismiss = true;\n    let playTo = 1;\n    let step = 0;\n    let remainingDistance = 0;\n    if (el.position === 'middle') {\n      /**\n       * A middle positioned Toast appears\n       * in the middle of the screen (at animation offset 0.5).\n       * As a result, the threshold will be calculated relative\n       * to this starting position. In other words at animation offset 0.5\n       * the threshold will be 0. We want the middle Toast to be eligible\n       * for dismiss when the user has swiped either half way up or down the\n       * screen. As a result, we divide DISMISS_THRESHOLD in half. We also\n       * consider when the threshold is a negative in the event the\n       * user drags up (since the deltaY will also be negative).\n       */\n      shouldDismiss = threshold >= DISMISS_THRESHOLD / 2 || threshold <= -0.5 / 2;\n      /**\n       * Since we are replacing the keyframes\n       * below the animation always starts from\n       * the beginning of the new keyframes.\n       * Similarly, we are always playing to\n       * the end of the new keyframes.\n       */\n      playTo = 1;\n      step = 0;\n      /**\n       * The Toast should animate from wherever its\n       * current position is to the desired end state.\n       *\n       * To begin, we get the current position of the\n       * Toast for its starting state.\n       */\n      const wrapperElBox = wrapperEl.getBoundingClientRect();\n      const startOffset = wrapperElBox.top - topPosition;\n      const startPosition = `${startOffset}px`;\n      /**\n       * If the deltaY is negative then the user is swiping\n       * up, so the Toast should animate to the top of the screen.\n       * If the deltaY is positive then the user is swiping\n       * down, so the Toast should animate to the bottom of the screen.\n       * We also account for when the deltaY is 0, but realistically\n       * that should never happen because it means the user did not drag\n       * the toast.\n       */\n      const offsetFactor = detail.deltaY <= 0 ? -1 : 1;\n      const endOffset = (topPosition + wrapperElBox.height) * offsetFactor;\n      /**\n       * If the Toast should dismiss\n       * then we need to figure out which edge of\n       * the screen it should animate towards.\n       * By default, the Toast will come\n       * back to its default state in the\n       * middle of the screen.\n       */\n      const endPosition = shouldDismiss ? `${endOffset}px` : '0px';\n      const KEYFRAMES = [{\n        offset: 0,\n        transform: `translateY(${startPosition})`\n      }, {\n        offset: 1,\n        transform: `translateY(${endPosition})`\n      }];\n      swipeAnimation.keyframes(KEYFRAMES);\n      /**\n       * Compute the remaining amount of pixels the\n       * toast needs to move to be fully dismissed.\n       */\n      remainingDistance = endOffset - startOffset;\n    } else {\n      shouldDismiss = threshold >= DISMISS_THRESHOLD;\n      playTo = shouldDismiss ? 1 : 0;\n      step = computeStep(detail.deltaY);\n      /**\n       * Compute the remaining amount of pixels the\n       * toast needs to move to be fully dismissed.\n       */\n      const remainingStepAmount = shouldDismiss ? 1 - step : step;\n      remainingDistance = remainingStepAmount * MAX_SWIPE_DISTANCE;\n    }\n    /**\n     * The animation speed should depend on how quickly\n     * the user flicks the toast across the screen. However,\n     * it should be no slower than 200ms.\n     * We use Math.abs on the remainingDistance because that value\n     * can be negative when swiping up on a middle position toast.\n     */\n    const duration = Math.min(Math.abs(remainingDistance) / Math.abs(velocity), 200);\n    swipeAnimation.onFinish(() => {\n      if (shouldDismiss) {\n        onDismiss();\n        swipeAnimation.destroy();\n      } else {\n        if (el.position === 'middle') {\n          /**\n           * If the toast snapped back to\n           * the middle of the screen we need\n           * to reset the keyframes\n           * so the toast can be swiped\n           * up or down again.\n           */\n          swipeAnimation.keyframes(SWIPE_UP_DOWN_KEYFRAMES).progressStart(true, 0.5);\n        } else {\n          swipeAnimation.progressStart(true, 0);\n        }\n        /**\n         * If the toast did not dismiss then\n         * the user should be able to swipe again.\n         */\n        gesture.enable(true);\n      }\n      /**\n       * This must be a one time callback\n       * otherwise a new callback will\n       * be added every time onEnd runs.\n       */\n    }, {\n      oneTimeCallback: true\n    }).progressEnd(playTo, step, duration);\n  };\n  const gesture = createGesture({\n    el: wrapperEl,\n    gestureName: 'toast-swipe-to-dismiss',\n    gesturePriority: OVERLAY_GESTURE_PRIORITY,\n    /**\n     * Toast only supports vertical swipes.\n     * This needs to be updated if we later\n     * support horizontal swipes.\n     */\n    direction: 'y',\n    onMove,\n    onEnd\n  });\n  return gesture;\n};\nconst toastIosCss = \":host{--border-width:0;--border-style:none;--border-color:initial;--box-shadow:none;--min-width:auto;--width:auto;--min-height:auto;--height:auto;--max-height:auto;--white-space:normal;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);contain:strict;z-index:1001;pointer-events:none}:host{inset-inline-start:0}:host(.overlay-hidden){display:none}:host(.ion-color){--button-color:inherit;color:var(--ion-color-contrast)}:host(.ion-color) .toast-button-cancel{color:inherit}:host(.ion-color) .toast-wrapper{background:var(--ion-color-base)}.toast-wrapper{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);pointer-events:auto}.toast-wrapper{inset-inline-start:var(--start);inset-inline-end:var(--end)}.toast-wrapper.toast-top{-webkit-transform:translate3d(0,  -100%,  0);transform:translate3d(0,  -100%,  0);top:0}.toast-wrapper.toast-bottom{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);bottom:0}.toast-container{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;max-height:inherit;contain:content}.toast-layout-stacked .toast-container{-ms-flex-wrap:wrap;flex-wrap:wrap}.toast-layout-baseline .toast-content{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center}.toast-icon{-webkit-margin-start:16px;margin-inline-start:16px}.toast-content{min-width:0}.toast-message{-ms-flex:1;flex:1;white-space:var(--white-space)}.toast-button-group{display:-ms-flexbox;display:flex}.toast-layout-stacked .toast-button-group{-ms-flex-pack:end;justify-content:end;width:100%}.toast-button{border:0;outline:none;color:var(--button-color);z-index:0}.toast-icon,.toast-button-icon{font-size:1.4em}.toast-button-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}@media (any-hover: hover){.toast-button:hover{cursor:pointer}}:host{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-radius:14px;--button-color:var(--ion-color-primary, #0054e9);--color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--max-width:700px;--max-height:478px;--start:10px;--end:10px;font-size:clamp(14px, 0.875rem, 43.4px)}.toast-wrapper{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;display:block;position:absolute;z-index:10}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.toast-translucent) .toast-wrapper{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}:host(.ion-color.toast-translucent) .toast-wrapper{background:rgba(var(--ion-color-base-rgb), 0.8)}}.toast-wrapper.toast-middle{opacity:0.01}.toast-content{-webkit-padding-start:15px;padding-inline-start:15px;-webkit-padding-end:15px;padding-inline-end:15px;padding-top:15px;padding-bottom:15px}.toast-header{margin-bottom:2px;font-weight:500}.toast-button{-webkit-padding-start:15px;padding-inline-start:15px;-webkit-padding-end:15px;padding-inline-end:15px;padding-top:10px;padding-bottom:10px;min-height:44px;-webkit-transition:background-color, opacity 100ms linear;transition:background-color, opacity 100ms linear;border:0;background-color:transparent;font-family:var(--ion-font-family);font-size:clamp(17px, 1.0625rem, 21.998px);font-weight:500;overflow:hidden}.toast-button.ion-activated{opacity:0.4}@media (any-hover: hover){.toast-button:hover{opacity:0.6}}\";\nconst toastMdCss = \":host{--border-width:0;--border-style:none;--border-color:initial;--box-shadow:none;--min-width:auto;--width:auto;--min-height:auto;--height:auto;--max-height:auto;--white-space:normal;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);contain:strict;z-index:1001;pointer-events:none}:host{inset-inline-start:0}:host(.overlay-hidden){display:none}:host(.ion-color){--button-color:inherit;color:var(--ion-color-contrast)}:host(.ion-color) .toast-button-cancel{color:inherit}:host(.ion-color) .toast-wrapper{background:var(--ion-color-base)}.toast-wrapper{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);pointer-events:auto}.toast-wrapper{inset-inline-start:var(--start);inset-inline-end:var(--end)}.toast-wrapper.toast-top{-webkit-transform:translate3d(0,  -100%,  0);transform:translate3d(0,  -100%,  0);top:0}.toast-wrapper.toast-bottom{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);bottom:0}.toast-container{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;max-height:inherit;contain:content}.toast-layout-stacked .toast-container{-ms-flex-wrap:wrap;flex-wrap:wrap}.toast-layout-baseline .toast-content{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center}.toast-icon{-webkit-margin-start:16px;margin-inline-start:16px}.toast-content{min-width:0}.toast-message{-ms-flex:1;flex:1;white-space:var(--white-space)}.toast-button-group{display:-ms-flexbox;display:flex}.toast-layout-stacked .toast-button-group{-ms-flex-pack:end;justify-content:end;width:100%}.toast-button{border:0;outline:none;color:var(--button-color);z-index:0}.toast-icon,.toast-button-icon{font-size:1.4em}.toast-button-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}@media (any-hover: hover){.toast-button:hover{cursor:pointer}}:host{--background:var(--ion-color-step-800, var(--ion-background-color-step-800, #333333));--border-radius:4px;--box-shadow:0 3px 5px -1px rgba(0, 0, 0, 0.2), 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);--button-color:var(--ion-color-primary, #0054e9);--color:var(--ion-color-step-50, var(--ion-text-color-step-950, #f2f2f2));--max-width:700px;--start:8px;--end:8px;font-size:0.875rem}.toast-wrapper{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;display:block;position:absolute;opacity:0.01;z-index:10}.toast-content{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:14px;padding-bottom:14px}.toast-header{margin-bottom:2px;font-weight:500;line-height:1.25rem}.toast-message{line-height:1.25rem}.toast-layout-baseline .toast-button-group-start{-webkit-margin-start:8px;margin-inline-start:8px}.toast-layout-stacked .toast-button-group-start{-webkit-margin-end:8px;margin-inline-end:8px;margin-top:8px}.toast-layout-baseline .toast-button-group-end{-webkit-margin-end:8px;margin-inline-end:8px}.toast-layout-stacked .toast-button-group-end{-webkit-margin-end:8px;margin-inline-end:8px;margin-bottom:8px}.toast-button{-webkit-padding-start:15px;padding-inline-start:15px;-webkit-padding-end:15px;padding-inline-end:15px;padding-top:10px;padding-bottom:10px;position:relative;background-color:transparent;font-family:var(--ion-font-family);font-size:0.875rem;font-weight:500;letter-spacing:0.84px;text-transform:uppercase;overflow:hidden}.toast-button-cancel{color:var(--ion-color-step-100, var(--ion-text-color-step-900, #e6e6e6))}.toast-button-icon-only{border-radius:50%;-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:9px;padding-bottom:9px;width:36px;height:36px}@media (any-hover: hover){.toast-button:hover{background-color:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08)}.toast-button-cancel:hover{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.08)}}\";\nconst Toast = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.didPresent = createEvent(this, \"ionToastDidPresent\", 7);\n    this.willPresent = createEvent(this, \"ionToastWillPresent\", 7);\n    this.willDismiss = createEvent(this, \"ionToastWillDismiss\", 7);\n    this.didDismiss = createEvent(this, \"ionToastDidDismiss\", 7);\n    this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n    this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n    this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n    this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n    this.delegateController = createDelegateController(this);\n    this.lockController = createLockController();\n    this.triggerController = createTriggerController();\n    this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n    this.presented = false;\n    /**\n     * When `true`, content inside of .toast-content\n     * will have aria-hidden elements removed causing\n     * screen readers to announce the remaining content.\n     */\n    this.revealContentToScreenReader = false;\n    /** @internal */\n    this.hasController = false;\n    /**\n     * How many milliseconds to wait before hiding the toast. By default, it will show\n     * until `dismiss()` is called.\n     */\n    this.duration = config.getNumber('toastDuration', 0);\n    /**\n     * Defines how the message and buttons are laid out in the toast.\n     * 'baseline': The message and the buttons will appear on the same line.\n     * Message text may wrap within the message container.\n     * 'stacked': The buttons containers and message will stack on top\n     * of each other. Use this if you have long text in your buttons.\n     */\n    this.layout = 'baseline';\n    /**\n     * If `true`, the keyboard will be automatically dismissed when the overlay is presented.\n     */\n    this.keyboardClose = false;\n    /**\n     * The starting position of the toast on the screen. Can be tweaked further\n     * using the `positionAnchor` property.\n     */\n    this.position = 'bottom';\n    /**\n     * If `true`, the toast will be translucent.\n     * Only applies when the mode is `\"ios\"` and the device supports\n     * [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).\n     */\n    this.translucent = false;\n    /**\n     * If `true`, the toast will animate.\n     */\n    this.animated = true;\n    /**\n     * If `true`, the toast will open. If `false`, the toast will close.\n     * Use this if you need finer grained control over presentation, otherwise\n     * just use the toastController or the `trigger` property.\n     * Note: `isOpen` will not automatically be set back to `false` when\n     * the toast dismisses. You will need to do that in your code.\n     */\n    this.isOpen = false;\n    this.dispatchCancelHandler = ev => {\n      const role = ev.detail.role;\n      if (isCancel(role)) {\n        const cancelButton = this.getButtons().find(b => b.role === 'cancel');\n        this.callButtonHandler(cancelButton);\n      }\n    };\n    /**\n     * Create a new swipe gesture so Toast\n     * can be swiped to dismiss.\n     */\n    this.createSwipeGesture = toastPosition => {\n      const gesture = this.gesture = createSwipeToDismissGesture(this.el, toastPosition, () => {\n        /**\n         * If the gesture completed then\n         * we should dismiss the toast.\n         */\n        this.dismiss(undefined, GESTURE);\n      });\n      gesture.enable(true);\n    };\n    /**\n     * Destroy an existing swipe gesture\n     * so Toast can no longer be swiped to dismiss.\n     */\n    this.destroySwipeGesture = () => {\n      const {\n        gesture\n      } = this;\n      if (gesture === undefined) {\n        return;\n      }\n      gesture.destroy();\n      this.gesture = undefined;\n    };\n    /**\n     * Returns `true` if swipeGesture\n     * is configured to a value that enables the swipe behavior.\n     * Returns `false` otherwise.\n     */\n    this.prefersSwipeGesture = () => {\n      const {\n        swipeGesture\n      } = this;\n      return swipeGesture === 'vertical';\n    };\n  }\n  swipeGestureChanged() {\n    /**\n     * If the Toast is presented, then we need to destroy\n     * any actives gestures before a new gesture is potentially\n     * created below.\n     *\n     * If the Toast is dismissed, then no gesture should be available\n     * since the Toast is not visible. This case should never\n     * happen since the \"dismiss\" method handles destroying\n     * any active swipe gestures, but we keep this code\n     * around to handle the first case.\n     */\n    this.destroySwipeGesture();\n    /**\n     * A new swipe gesture should only be created\n     * if the Toast is presented. If the Toast is not\n     * yet presented then the \"present\" method will\n     * handle calling the swipe gesture setup function.\n     */\n    if (this.presented && this.prefersSwipeGesture()) {\n      /**\n       * If the Toast is presented then\n       * lastPresentedPosition is defined.\n       */\n      this.createSwipeGesture(this.lastPresentedPosition);\n    }\n  }\n  onIsOpenChange(newValue, oldValue) {\n    if (newValue === true && oldValue === false) {\n      this.present();\n    } else if (newValue === false && oldValue === true) {\n      this.dismiss();\n    }\n  }\n  triggerChanged() {\n    const {\n      trigger,\n      el,\n      triggerController\n    } = this;\n    if (trigger) {\n      triggerController.addClickListener(el, trigger);\n    }\n  }\n  connectedCallback() {\n    prepareOverlay(this.el);\n    this.triggerChanged();\n  }\n  disconnectedCallback() {\n    this.triggerController.removeClickListener();\n  }\n  componentWillLoad() {\n    var _a;\n    if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n      setOverlayId(this.el);\n    }\n  }\n  componentDidLoad() {\n    /**\n     * If toast was rendered with isOpen=\"true\"\n     * then we should open toast immediately.\n     */\n    if (this.isOpen === true) {\n      raf(() => this.present());\n    }\n    /**\n     * When binding values in frameworks such as Angular\n     * it is possible for the value to be set after the Web Component\n     * initializes but before the value watcher is set up in Stencil.\n     * As a result, the watcher callback may not be fired.\n     * We work around this by manually calling the watcher\n     * callback when the component has loaded and the watcher\n     * is configured.\n     */\n    this.triggerChanged();\n  }\n  /**\n   * Present the toast overlay after it has been created.\n   */\n  async present() {\n    const unlock = await this.lockController.lock();\n    await this.delegateController.attachViewToDom();\n    const {\n      el,\n      position\n    } = this;\n    const anchor = this.getAnchorElement();\n    const animationPosition = getAnimationPosition(position, anchor, getIonMode(this), el);\n    /**\n     * Cache the calculated position of the toast, so we can re-use it\n     * in the dismiss animation.\n     */\n    this.lastPresentedPosition = animationPosition;\n    await present(this, 'toastEnter', iosEnterAnimation, mdEnterAnimation, {\n      position,\n      top: animationPosition.top,\n      bottom: animationPosition.bottom\n    });\n    /**\n     * Content is revealed to screen readers after\n     * the transition to avoid jank since this\n     * state updates will cause a re-render.\n     */\n    this.revealContentToScreenReader = true;\n    if (this.duration > 0) {\n      this.durationTimeout = setTimeout(() => this.dismiss(undefined, 'timeout'), this.duration);\n    }\n    /**\n     * If the Toast has a swipe gesture then we can\n     * create the gesture so users can swipe the\n     * presented Toast.\n     */\n    if (this.prefersSwipeGesture()) {\n      this.createSwipeGesture(animationPosition);\n    }\n    unlock();\n  }\n  /**\n   * Dismiss the toast overlay after it has been presented.\n   *\n   * @param data Any data to emit in the dismiss events.\n   * @param role The role of the element that is dismissing the toast.\n   * This can be useful in a button handler for determining which button was\n   * clicked to dismiss the toast.\n   * Some examples include: ``\"cancel\"`, `\"destructive\"`, \"selected\"`, and `\"backdrop\"`.\n   *\n   * This is a no-op if the overlay has not been presented yet. If you want\n   * to remove an overlay from the DOM that was never presented, use the\n   * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n   */\n  async dismiss(data, role) {\n    var _a, _b;\n    const unlock = await this.lockController.lock();\n    const {\n      durationTimeout,\n      position,\n      lastPresentedPosition\n    } = this;\n    if (durationTimeout) {\n      clearTimeout(durationTimeout);\n    }\n    const dismissed = await dismiss(this, data, role, 'toastLeave', iosLeaveAnimation, mdLeaveAnimation,\n    /**\n     * Fetch the cached position that was calculated back in the present\n     * animation. We always want to animate the dismiss from the same\n     * position the present stopped at, so the animation looks continuous.\n     */\n    {\n      position,\n      top: (_a = lastPresentedPosition === null || lastPresentedPosition === void 0 ? void 0 : lastPresentedPosition.top) !== null && _a !== void 0 ? _a : '',\n      bottom: (_b = lastPresentedPosition === null || lastPresentedPosition === void 0 ? void 0 : lastPresentedPosition.bottom) !== null && _b !== void 0 ? _b : ''\n    });\n    if (dismissed) {\n      this.delegateController.removeViewFromDom();\n      this.revealContentToScreenReader = false;\n    }\n    this.lastPresentedPosition = undefined;\n    /**\n     * If the Toast has a swipe gesture then we can\n     * safely destroy it now that it is dismissed.\n     */\n    this.destroySwipeGesture();\n    unlock();\n    return dismissed;\n  }\n  /**\n   * Returns a promise that resolves when the toast did dismiss.\n   */\n  onDidDismiss() {\n    return eventMethod(this.el, 'ionToastDidDismiss');\n  }\n  /**\n   * Returns a promise that resolves when the toast will dismiss.\n   */\n  onWillDismiss() {\n    return eventMethod(this.el, 'ionToastWillDismiss');\n  }\n  getButtons() {\n    const buttons = this.buttons ? this.buttons.map(b => {\n      return typeof b === 'string' ? {\n        text: b\n      } : b;\n    }) : [];\n    return buttons;\n  }\n  /**\n   * Returns the element specified by the positionAnchor prop,\n   * or undefined if prop's value is an ID string and the element\n   * is not found in the DOM.\n   */\n  getAnchorElement() {\n    const {\n      position,\n      positionAnchor,\n      el\n    } = this;\n    /**\n     * If positionAnchor is undefined then\n     * no anchor should be used when presenting the toast.\n     */\n    if (positionAnchor === undefined) {\n      return;\n    }\n    if (position === 'middle' && positionAnchor !== undefined) {\n      printIonWarning('[ion-toast] - The positionAnchor property is ignored when using position=\"middle\".', this.el);\n      return undefined;\n    }\n    if (typeof positionAnchor === 'string') {\n      /**\n       * If the anchor is defined as an ID, find the element.\n       * We do this on every present so the toast doesn't need\n       * to account for the surrounding DOM changing since the\n       * last time it was presented.\n       */\n      const foundEl = document.getElementById(positionAnchor);\n      if (foundEl === null) {\n        printIonWarning(`[ion-toast] - An anchor element with an ID of \"${positionAnchor}\" was not found in the DOM.`, el);\n        return undefined;\n      }\n      return foundEl;\n    }\n    if (positionAnchor instanceof HTMLElement) {\n      return positionAnchor;\n    }\n    printIonWarning('[ion-toast] - Invalid positionAnchor value:', positionAnchor, el);\n    return undefined;\n  }\n  async buttonClick(button) {\n    const role = button.role;\n    if (isCancel(role)) {\n      return this.dismiss(undefined, role);\n    }\n    const shouldDismiss = await this.callButtonHandler(button);\n    if (shouldDismiss) {\n      return this.dismiss(undefined, role);\n    }\n    return Promise.resolve();\n  }\n  async callButtonHandler(button) {\n    if (button === null || button === void 0 ? void 0 : button.handler) {\n      // a handler has been provided, execute it\n      // pass the handler the values from the inputs\n      try {\n        const rtn = await safeCall(button.handler);\n        if (rtn === false) {\n          // if the return value of the handler is false then do not dismiss\n          return false;\n        }\n      } catch (e) {\n        printIonError('[ion-toast] - Exception in callButtonHandler:', e);\n      }\n    }\n    return true;\n  }\n  renderButtons(buttons, side) {\n    if (buttons.length === 0) {\n      return;\n    }\n    const mode = getIonMode(this);\n    const buttonGroupsClasses = {\n      'toast-button-group': true,\n      [`toast-button-group-${side}`]: true\n    };\n    return h(\"div\", {\n      class: buttonGroupsClasses\n    }, buttons.map(b => h(\"button\", Object.assign({}, b.htmlAttributes, {\n      type: \"button\",\n      class: buttonClass(b),\n      tabIndex: 0,\n      onClick: () => this.buttonClick(b),\n      part: buttonPart(b)\n    }), h(\"div\", {\n      class: \"toast-button-inner\"\n    }, b.icon && h(\"ion-icon\", {\n      \"aria-hidden\": \"true\",\n      icon: b.icon,\n      slot: b.text === undefined ? 'icon-only' : undefined,\n      class: \"toast-button-icon\"\n    }), b.text), mode === 'md' && h(\"ion-ripple-effect\", {\n      type: b.icon !== undefined && b.text === undefined ? 'unbounded' : 'bounded'\n    }))));\n  }\n  /**\n   * Render the `message` property.\n   * @param key - A key to give the element a stable identity. This is used to improve compatibility with screen readers.\n   * @param ariaHidden - If \"true\" then content will be hidden from screen readers.\n   */\n  renderToastMessage(key, ariaHidden = null) {\n    const {\n      customHTMLEnabled,\n      message\n    } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", {\n        key: key,\n        \"aria-hidden\": ariaHidden,\n        class: \"toast-message\",\n        part: \"message\",\n        innerHTML: sanitizeDOMString(message)\n      });\n    }\n    return h(\"div\", {\n      key: key,\n      \"aria-hidden\": ariaHidden,\n      class: \"toast-message\",\n      part: \"message\"\n    }, message);\n  }\n  /**\n   * Render the `header` property.\n   * @param key - A key to give the element a stable identity. This is used to improve compatibility with screen readers.\n   * @param ariaHidden - If \"true\" then content will be hidden from screen readers.\n   */\n  renderHeader(key, ariaHidden = null) {\n    return h(\"div\", {\n      key: key,\n      class: \"toast-header\",\n      \"aria-hidden\": ariaHidden,\n      part: \"header\"\n    }, this.header);\n  }\n  render() {\n    const {\n      layout,\n      el,\n      revealContentToScreenReader,\n      header,\n      message\n    } = this;\n    const allButtons = this.getButtons();\n    const startButtons = allButtons.filter(b => b.side === 'start');\n    const endButtons = allButtons.filter(b => b.side !== 'start');\n    const mode = getIonMode(this);\n    const wrapperClass = {\n      'toast-wrapper': true,\n      [`toast-${this.position}`]: true,\n      [`toast-layout-${layout}`]: true\n    };\n    /**\n     * Stacked buttons are only meant to be\n     *  used with one type of button.\n     */\n    if (layout === 'stacked' && startButtons.length > 0 && endButtons.length > 0) {\n      printIonWarning('[ion-toast] - This toast is using start and end buttons with the stacked toast layout. We recommend following the best practice of using either start or end buttons with the stacked toast layout.', el);\n    }\n    return h(Host, Object.assign({\n      key: '425be734aee0eeef281ab1609a9f982ce8b5e852',\n      tabindex: \"-1\"\n    }, this.htmlAttributes, {\n      style: {\n        zIndex: `${60000 + this.overlayIndex}`\n      },\n      class: createColorClasses(this.color, Object.assign(Object.assign({\n        [mode]: true\n      }, getClassMap(this.cssClass)), {\n        'overlay-hidden': true,\n        'toast-translucent': this.translucent\n      })),\n      onIonToastWillDismiss: this.dispatchCancelHandler\n    }), h(\"div\", {\n      key: '7c3ad1a8df9e21fc30fc179c7edebeabcf3c6874',\n      class: wrapperClass\n    }, h(\"div\", {\n      key: 'f950c21e8d7bf92653e0bd52e820ed6b84fa8cf5',\n      class: \"toast-container\",\n      part: \"container\"\n    }, this.renderButtons(startButtons, 'start'), this.icon !== undefined && h(\"ion-icon\", {\n      key: '0266241927dbe16799adb57f9fc11bd5372877b2',\n      class: \"toast-icon\",\n      part: \"icon\",\n      icon: this.icon,\n      lazy: false,\n      \"aria-hidden\": \"true\"\n    }), h(\"div\", {\n      key: '9ef4b87bad672af6a5b693af61375564daf1eeeb',\n      class: \"toast-content\",\n      role: \"status\",\n      \"aria-atomic\": \"true\",\n      \"aria-live\": \"polite\"\n    }, !revealContentToScreenReader && header !== undefined && this.renderHeader('oldHeader', 'true'), !revealContentToScreenReader && message !== undefined && this.renderToastMessage('oldMessage', 'true'), revealContentToScreenReader && header !== undefined && this.renderHeader('header'), revealContentToScreenReader && message !== undefined && this.renderToastMessage('header')), this.renderButtons(endButtons, 'end'))));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"swipeGesture\": [\"swipeGestureChanged\"],\n      \"isOpen\": [\"onIsOpenChange\"],\n      \"trigger\": [\"triggerChanged\"]\n    };\n  }\n};\nconst buttonClass = button => {\n  return {\n    'toast-button': true,\n    'toast-button-icon-only': button.icon !== undefined && button.text === undefined,\n    [`toast-button-${button.role}`]: button.role !== undefined,\n    'ion-focusable': true,\n    'ion-activatable': true\n  };\n};\nconst buttonPart = button => {\n  return isCancel(button.role) ? 'button cancel' : 'button';\n};\nToast.style = {\n  ios: toastIosCss,\n  md: toastMdCss\n};\nexport { Toast as ion_toast };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,SAAS,qBAAqB,UAAU,gBAAgB,MAAM,OAAO;AAKnE,MAAI;AACJ,MAAI,SAAS,MAAM;AACjB,aAAS,aAAa,QAAQ,IAAI;AAAA,EACpC,OAAO;AACL,aAAS,aAAa,QAAQ,KAAK;AAAA,EACrC;AAOA,MAAI,kBAAkB,KAAK;AACzB,yBAAqB,gBAAgB,KAAK;AAC1C,UAAM,MAAM,eAAe,sBAAsB;AACjD,QAAI,aAAa,OAAO;AACtB,gBAAU,IAAI;AAAA,IAChB,WAAW,aAAa,UAAU;AAMhC,gBAAU,IAAI,cAAc,IAAI;AAAA,IAClC;AAKA,WAAO;AAAA,MACL,KAAK,GAAG,MAAM;AAAA,MACd,QAAQ,GAAG,MAAM;AAAA,IACnB;AAAA,EACF,OAAO;AACL,WAAO;AAAA,MACL,KAAK,QAAQ,MAAM;AAAA,MACnB,QAAQ,QAAQ,MAAM;AAAA,IACxB;AAAA,EACF;AACF;AAMA,SAAS,qBAAqB,gBAAgB,OAAO;AACnD,MAAI,eAAe,iBAAiB,MAAM;AACxC,oBAAgB,gKAAgK,KAAK;AAAA,EACvL;AACF;AASA,IAAM,6BAA6B,CAAC,aAAa,kBAAkB;AACjE,SAAO,KAAK,MAAM,cAAc,IAAI,gBAAgB,CAAC;AACvD;AAKA,IAAM,oBAAoB,CAAC,QAAQ,SAAS;AAC1C,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,mBAAmB,gBAAgB;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM,YAAY,KAAK,cAAc,gBAAgB;AACrD,mBAAiB,WAAW,SAAS;AACrC,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,uBAAiB,OAAO,aAAa,qBAAqB,cAAc,GAAG,GAAG;AAC9E;AAAA,IACF,KAAK;AACH,YAAM,cAAc,2BAA2B,OAAO,cAAc,UAAU,YAAY;AAC1F,gBAAU,MAAM,MAAM,GAAG,WAAW;AACpC,uBAAiB,OAAO,WAAW,MAAM,CAAC;AAC1C;AAAA,IACF;AACE,uBAAiB,OAAO,aAAa,oBAAoB,cAAc,MAAM,GAAG;AAChF;AAAA,EACJ;AACA,SAAO,cAAc,OAAO,oCAAoC,EAAE,SAAS,GAAG,EAAE,aAAa,gBAAgB;AAC/G;AAKA,IAAM,oBAAoB,CAAC,QAAQ,SAAS;AAC1C,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,mBAAmB,gBAAgB;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM,YAAY,KAAK,cAAc,gBAAgB;AACrD,mBAAiB,WAAW,SAAS;AACrC,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,uBAAiB,OAAO,aAAa,cAAc,GAAG,KAAK,mBAAmB;AAC9E;AAAA,IACF,KAAK;AACH,uBAAiB,OAAO,WAAW,MAAM,CAAC;AAC1C;AAAA,IACF;AACE,uBAAiB,OAAO,aAAa,cAAc,MAAM,KAAK,kBAAkB;AAChF;AAAA,EACJ;AACA,SAAO,cAAc,OAAO,6BAA6B,EAAE,SAAS,GAAG,EAAE,aAAa,gBAAgB;AACxG;AAKA,IAAM,mBAAmB,CAAC,QAAQ,SAAS;AACzC,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,mBAAmB,gBAAgB;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM,YAAY,KAAK,cAAc,gBAAgB;AACrD,mBAAiB,WAAW,SAAS;AACrC,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,gBAAU,MAAM,YAAY,aAAa,cAAc,GAAG,GAAG;AAC7D,uBAAiB,OAAO,WAAW,MAAM,CAAC;AAC1C;AAAA,IACF,KAAK;AACH,YAAM,cAAc,2BAA2B,OAAO,cAAc,UAAU,YAAY;AAC1F,gBAAU,MAAM,MAAM,GAAG,WAAW;AACpC,uBAAiB,OAAO,WAAW,MAAM,CAAC;AAC1C;AAAA,IACF;AACE,gBAAU,MAAM,YAAY,aAAa,cAAc,MAAM,GAAG;AAChE,uBAAiB,OAAO,WAAW,MAAM,CAAC;AAC1C;AAAA,EACJ;AACA,SAAO,cAAc,OAAO,6BAA6B,EAAE,SAAS,GAAG,EAAE,aAAa,gBAAgB;AACxG;AAKA,IAAM,mBAAmB,YAAU;AACjC,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,mBAAmB,gBAAgB;AACzC,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM,YAAY,KAAK,cAAc,gBAAgB;AACrD,mBAAiB,WAAW,SAAS,EAAE,OAAO,WAAW,MAAM,CAAC;AAChE,SAAO,cAAc,OAAO,6BAA6B,EAAE,SAAS,GAAG,EAAE,aAAa,gBAAgB;AACxG;AASA,IAAM,8BAA8B,CAAC,IAAI,eAAe,cAAc;AAOpE,QAAM,YAAY,eAAe,EAAE,EAAE,cAAc,gBAAgB;AACnE,QAAM,eAAe,GAAG;AACxB,QAAM,eAAe,UAAU,sBAAsB;AAQrD,MAAI,qBAAqB;AAKzB,QAAM,oBAAoB;AAM1B,QAAM,cAAc,GAAG,aAAa,WAAW,MAAM;AAQrD,QAAM,mBAAmB,GAAG,aAAa,QAAQ,KAAK;AAMtD,QAAM,cAAc,2BAA2B,cAAc,aAAa,MAAM;AAChF,QAAM,0BAA0B,CAAC;AAAA,IAC/B,QAAQ;AAAA,IACR,WAAW,eAAe,cAAc,aAAa,MAAM;AAAA,EAC7D,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,WAAW;AAAA,EACb,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,WAAW,cAAc,cAAc,aAAa,MAAM;AAAA,EAC5D,CAAC;AACD,QAAM,iBAAiB,gBAAgB,kCAAkC,EAAE,WAAW,SAAS,EAM3F,SAAS,GAAG;AAChB,UAAQ,GAAG,UAAU;AAAA,IACnB,KAAK;AACH,2BAAqB,eAAe,aAAa;AACjD,qBAAe,UAAU,uBAAuB;AAKhD,qBAAe,cAAc,MAAM,GAAG;AACtC;AAAA,IACF,KAAK;AAQH,2BAAqB,aAAa;AAClC,qBAAe,UAAU,CAAC;AAAA,QACxB,QAAQ;AAAA,QACR,WAAW,cAAc,cAAc,GAAG;AAAA,MAC5C,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,WAAW;AAAA,MACb,CAAC,CAAC;AACF,qBAAe,cAAc,MAAM,CAAC;AACpC;AAAA,IACF,KAAK;AAAA,IACL;AAOE,2BAAqB,eAAe,aAAa;AACjD,qBAAe,UAAU,CAAC;AAAA,QACxB,QAAQ;AAAA,QACR,WAAW,cAAc,cAAc,MAAM;AAAA,MAC/C,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,WAAW;AAAA,MACb,CAAC,CAAC;AACF,qBAAe,cAAc,MAAM,CAAC;AACpC;AAAA,EACJ;AACA,QAAM,cAAc,WAAS;AAC3B,WAAO,QAAQ,mBAAmB;AAAA,EACpC;AACA,QAAM,SAAS,YAAU;AACvB,UAAM,OAAO,cAAc,YAAY,OAAO,MAAM;AACpD,mBAAe,aAAa,IAAI;AAAA,EAClC;AACA,QAAM,QAAQ,YAAU;AACtB,UAAM,WAAW,OAAO;AACxB,UAAM,aAAa,OAAO,SAAS,WAAW,OAAQ,qBAAqB;AAM3E,YAAQ,OAAO,KAAK;AACpB,QAAI,gBAAgB;AACpB,QAAI,SAAS;AACb,QAAI,OAAO;AACX,QAAI,oBAAoB;AACxB,QAAI,GAAG,aAAa,UAAU;AAY5B,sBAAgB,aAAa,oBAAoB,KAAK,aAAa,OAAO;AAQ1E,eAAS;AACT,aAAO;AAQP,YAAMA,gBAAe,UAAU,sBAAsB;AACrD,YAAM,cAAcA,cAAa,MAAM;AACvC,YAAM,gBAAgB,GAAG,WAAW;AAUpC,YAAM,eAAe,OAAO,UAAU,IAAI,KAAK;AAC/C,YAAM,aAAa,cAAcA,cAAa,UAAU;AASxD,YAAM,cAAc,gBAAgB,GAAG,SAAS,OAAO;AACvD,YAAM,YAAY,CAAC;AAAA,QACjB,QAAQ;AAAA,QACR,WAAW,cAAc,aAAa;AAAA,MACxC,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,WAAW,cAAc,WAAW;AAAA,MACtC,CAAC;AACD,qBAAe,UAAU,SAAS;AAKlC,0BAAoB,YAAY;AAAA,IAClC,OAAO;AACL,sBAAgB,aAAa;AAC7B,eAAS,gBAAgB,IAAI;AAC7B,aAAO,YAAY,OAAO,MAAM;AAKhC,YAAM,sBAAsB,gBAAgB,IAAI,OAAO;AACvD,0BAAoB,sBAAsB;AAAA,IAC5C;AAQA,UAAM,WAAW,KAAK,IAAI,KAAK,IAAI,iBAAiB,IAAI,KAAK,IAAI,QAAQ,GAAG,GAAG;AAC/E,mBAAe,SAAS,MAAM;AAC5B,UAAI,eAAe;AACjB,kBAAU;AACV,uBAAe,QAAQ;AAAA,MACzB,OAAO;AACL,YAAI,GAAG,aAAa,UAAU;AAQ5B,yBAAe,UAAU,uBAAuB,EAAE,cAAc,MAAM,GAAG;AAAA,QAC3E,OAAO;AACL,yBAAe,cAAc,MAAM,CAAC;AAAA,QACtC;AAKA,gBAAQ,OAAO,IAAI;AAAA,MACrB;AAAA,IAMF,GAAG;AAAA,MACD,iBAAiB;AAAA,IACnB,CAAC,EAAE,YAAY,QAAQ,MAAM,QAAQ;AAAA,EACvC;AACA,QAAM,UAAU,cAAc;AAAA,IAC5B,IAAI;AAAA,IACJ,aAAa;AAAA,IACb,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMjB,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,QAAQ,MAAM;AAAA,EAClB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,aAAa,YAAY,MAAM,sBAAsB,CAAC;AAC3D,SAAK,cAAc,YAAY,MAAM,uBAAuB,CAAC;AAC7D,SAAK,cAAc,YAAY,MAAM,uBAAuB,CAAC;AAC7D,SAAK,aAAa,YAAY,MAAM,sBAAsB,CAAC;AAC3D,SAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,SAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,SAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,SAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,SAAK,qBAAqB,yBAAyB,IAAI;AACvD,SAAK,iBAAiB,qBAAqB;AAC3C,SAAK,oBAAoB,wBAAwB;AACjD,SAAK,oBAAoB,OAAO,IAAI,6BAA6B,2BAA2B;AAC5F,SAAK,YAAY;AAMjB,SAAK,8BAA8B;AAEnC,SAAK,gBAAgB;AAKrB,SAAK,WAAW,OAAO,UAAU,iBAAiB,CAAC;AAQnD,SAAK,SAAS;AAId,SAAK,gBAAgB;AAKrB,SAAK,WAAW;AAMhB,SAAK,cAAc;AAInB,SAAK,WAAW;AAQhB,SAAK,SAAS;AACd,SAAK,wBAAwB,QAAM;AACjC,YAAM,OAAO,GAAG,OAAO;AACvB,UAAI,SAAS,IAAI,GAAG;AAClB,cAAM,eAAe,KAAK,WAAW,EAAE,KAAK,OAAK,EAAE,SAAS,QAAQ;AACpE,aAAK,kBAAkB,YAAY;AAAA,MACrC;AAAA,IACF;AAKA,SAAK,qBAAqB,mBAAiB;AACzC,YAAM,UAAU,KAAK,UAAU,4BAA4B,KAAK,IAAI,eAAe,MAAM;AAKvF,aAAK,QAAQ,QAAW,OAAO;AAAA,MACjC,CAAC;AACD,cAAQ,OAAO,IAAI;AAAA,IACrB;AAKA,SAAK,sBAAsB,MAAM;AAC/B,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,YAAY,QAAW;AACzB;AAAA,MACF;AACA,cAAQ,QAAQ;AAChB,WAAK,UAAU;AAAA,IACjB;AAMA,SAAK,sBAAsB,MAAM;AAC/B,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,aAAO,iBAAiB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,sBAAsB;AAYpB,SAAK,oBAAoB;AAOzB,QAAI,KAAK,aAAa,KAAK,oBAAoB,GAAG;AAKhD,WAAK,mBAAmB,KAAK,qBAAqB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,eAAe,UAAU,UAAU;AACjC,QAAI,aAAa,QAAQ,aAAa,OAAO;AAC3C,WAAK,QAAQ;AAAA,IACf,WAAW,aAAa,SAAS,aAAa,MAAM;AAClD,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,SAAS;AACX,wBAAkB,iBAAiB,IAAI,OAAO;AAAA,IAChD;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,mBAAe,KAAK,EAAE;AACtB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,uBAAuB;AACrB,SAAK,kBAAkB,oBAAoB;AAAA,EAC7C;AAAA,EACA,oBAAoB;AAClB,QAAI;AACJ,QAAI,GAAG,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAC5E,mBAAa,KAAK,EAAE;AAAA,IACtB;AAAA,EACF;AAAA,EACA,mBAAmB;AAKjB,QAAI,KAAK,WAAW,MAAM;AACxB,UAAI,MAAM,KAAK,QAAQ,CAAC;AAAA,IAC1B;AAUA,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIM,UAAU;AAAA;AACd,YAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,YAAM,KAAK,mBAAmB,gBAAgB;AAC9C,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,SAAS,KAAK,iBAAiB;AACrC,YAAM,oBAAoB,qBAAqB,UAAU,QAAQ,WAAW,IAAI,GAAG,EAAE;AAKrF,WAAK,wBAAwB;AAC7B,YAAM,QAAQ,MAAM,cAAc,mBAAmB,kBAAkB;AAAA,QACrE;AAAA,QACA,KAAK,kBAAkB;AAAA,QACvB,QAAQ,kBAAkB;AAAA,MAC5B,CAAC;AAMD,WAAK,8BAA8B;AACnC,UAAI,KAAK,WAAW,GAAG;AACrB,aAAK,kBAAkB,WAAW,MAAM,KAAK,QAAQ,QAAW,SAAS,GAAG,KAAK,QAAQ;AAAA,MAC3F;AAMA,UAAI,KAAK,oBAAoB,GAAG;AAC9B,aAAK,mBAAmB,iBAAiB;AAAA,MAC3C;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcM,QAAQ,MAAM,MAAM;AAAA;AACxB,UAAI,IAAI;AACR,YAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,iBAAiB;AACnB,qBAAa,eAAe;AAAA,MAC9B;AACA,YAAM,YAAY,MAAM;AAAA,QAAQ;AAAA,QAAM;AAAA,QAAM;AAAA,QAAM;AAAA,QAAc;AAAA,QAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMnF;AAAA,UACE;AAAA,UACA,MAAM,KAAK,0BAA0B,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,SAAS,QAAQ,OAAO,SAAS,KAAK;AAAA,UACrJ,SAAS,KAAK,0BAA0B,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,YAAY,QAAQ,OAAO,SAAS,KAAK;AAAA,QAC7J;AAAA,MAAC;AACD,UAAI,WAAW;AACb,aAAK,mBAAmB,kBAAkB;AAC1C,aAAK,8BAA8B;AAAA,MACrC;AACA,WAAK,wBAAwB;AAK7B,WAAK,oBAAoB;AACzB,aAAO;AACP,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,WAAO,YAAY,KAAK,IAAI,oBAAoB;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,WAAO,YAAY,KAAK,IAAI,qBAAqB;AAAA,EACnD;AAAA,EACA,aAAa;AACX,UAAM,UAAU,KAAK,UAAU,KAAK,QAAQ,IAAI,OAAK;AACnD,aAAO,OAAO,MAAM,WAAW;AAAA,QAC7B,MAAM;AAAA,MACR,IAAI;AAAA,IACN,CAAC,IAAI,CAAC;AACN,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB;AACjB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAKJ,QAAI,mBAAmB,QAAW;AAChC;AAAA,IACF;AACA,QAAI,aAAa,YAAY,mBAAmB,QAAW;AACzD,sBAAgB,sFAAsF,KAAK,EAAE;AAC7G,aAAO;AAAA,IACT;AACA,QAAI,OAAO,mBAAmB,UAAU;AAOtC,YAAM,UAAU,SAAS,eAAe,cAAc;AACtD,UAAI,YAAY,MAAM;AACpB,wBAAgB,kDAAkD,cAAc,+BAA+B,EAAE;AACjH,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,QAAI,0BAA0B,aAAa;AACzC,aAAO;AAAA,IACT;AACA,oBAAgB,+CAA+C,gBAAgB,EAAE;AACjF,WAAO;AAAA,EACT;AAAA,EACM,YAAY,QAAQ;AAAA;AACxB,YAAM,OAAO,OAAO;AACpB,UAAI,SAAS,IAAI,GAAG;AAClB,eAAO,KAAK,QAAQ,QAAW,IAAI;AAAA,MACrC;AACA,YAAM,gBAAgB,MAAM,KAAK,kBAAkB,MAAM;AACzD,UAAI,eAAe;AACjB,eAAO,KAAK,QAAQ,QAAW,IAAI;AAAA,MACrC;AACA,aAAO,QAAQ,QAAQ;AAAA,IACzB;AAAA;AAAA,EACM,kBAAkB,QAAQ;AAAA;AAC9B,UAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AAGlE,YAAI;AACF,gBAAM,MAAM,MAAM,SAAS,OAAO,OAAO;AACzC,cAAI,QAAQ,OAAO;AAEjB,mBAAO;AAAA,UACT;AAAA,QACF,SAAS,GAAG;AACV,wBAAc,iDAAiD,CAAC;AAAA,QAClE;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA,EACA,cAAc,SAAS,MAAM;AAC3B,QAAI,QAAQ,WAAW,GAAG;AACxB;AAAA,IACF;AACA,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,sBAAsB;AAAA,MAC1B,sBAAsB;AAAA,MACtB,CAAC,sBAAsB,IAAI,EAAE,GAAG;AAAA,IAClC;AACA,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,IACT,GAAG,QAAQ,IAAI,OAAK,EAAE,UAAU,OAAO,OAAO,CAAC,GAAG,EAAE,gBAAgB;AAAA,MAClE,MAAM;AAAA,MACN,OAAO,YAAY,CAAC;AAAA,MACpB,UAAU;AAAA,MACV,SAAS,MAAM,KAAK,YAAY,CAAC;AAAA,MACjC,MAAM,WAAW,CAAC;AAAA,IACpB,CAAC,GAAG,EAAE,OAAO;AAAA,MACX,OAAO;AAAA,IACT,GAAG,EAAE,QAAQ,EAAE,YAAY;AAAA,MACzB,eAAe;AAAA,MACf,MAAM,EAAE;AAAA,MACR,MAAM,EAAE,SAAS,SAAY,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC,GAAG,EAAE,IAAI,GAAG,SAAS,QAAQ,EAAE,qBAAqB;AAAA,MACnD,MAAM,EAAE,SAAS,UAAa,EAAE,SAAS,SAAY,cAAc;AAAA,IACrE,CAAC,CAAC,CAAC,CAAC;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,KAAK,aAAa,MAAM;AACzC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,mBAAmB;AACrB,aAAO,EAAE,OAAO;AAAA,QACd;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW,kBAAkB,OAAO;AAAA,MACtC,CAAC;AAAA,IACH;AACA,WAAO,EAAE,OAAO;AAAA,MACd;AAAA,MACA,eAAe;AAAA,MACf,OAAO;AAAA,MACP,MAAM;AAAA,IACR,GAAG,OAAO;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,KAAK,aAAa,MAAM;AACnC,WAAO,EAAE,OAAO;AAAA,MACd;AAAA,MACA,OAAO;AAAA,MACP,eAAe;AAAA,MACf,MAAM;AAAA,IACR,GAAG,KAAK,MAAM;AAAA,EAChB;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,aAAa,KAAK,WAAW;AACnC,UAAM,eAAe,WAAW,OAAO,OAAK,EAAE,SAAS,OAAO;AAC9D,UAAM,aAAa,WAAW,OAAO,OAAK,EAAE,SAAS,OAAO;AAC5D,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,eAAe;AAAA,MACnB,iBAAiB;AAAA,MACjB,CAAC,SAAS,KAAK,QAAQ,EAAE,GAAG;AAAA,MAC5B,CAAC,gBAAgB,MAAM,EAAE,GAAG;AAAA,IAC9B;AAKA,QAAI,WAAW,aAAa,aAAa,SAAS,KAAK,WAAW,SAAS,GAAG;AAC5E,sBAAgB,uMAAuM,EAAE;AAAA,IAC3N;AACA,WAAO,EAAE,MAAM,OAAO,OAAO;AAAA,MAC3B,KAAK;AAAA,MACL,UAAU;AAAA,IACZ,GAAG,KAAK,gBAAgB;AAAA,MACtB,OAAO;AAAA,QACL,QAAQ,GAAG,MAAQ,KAAK,YAAY;AAAA,MACtC;AAAA,MACA,OAAO,mBAAmB,KAAK,OAAO,OAAO,OAAO,OAAO,OAAO;AAAA,QAChE,CAAC,IAAI,GAAG;AAAA,MACV,GAAG,YAAY,KAAK,QAAQ,CAAC,GAAG;AAAA,QAC9B,kBAAkB;AAAA,QAClB,qBAAqB,KAAK;AAAA,MAC5B,CAAC,CAAC;AAAA,MACF,uBAAuB,KAAK;AAAA,IAC9B,CAAC,GAAG,EAAE,OAAO;AAAA,MACX,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,IACR,GAAG,KAAK,cAAc,cAAc,OAAO,GAAG,KAAK,SAAS,UAAa,EAAE,YAAY;AAAA,MACrF,KAAK;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM,KAAK;AAAA,MACX,MAAM;AAAA,MACN,eAAe;AAAA,IACjB,CAAC,GAAG,EAAE,OAAO;AAAA,MACX,KAAK;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,aAAa;AAAA,IACf,GAAG,CAAC,+BAA+B,WAAW,UAAa,KAAK,aAAa,aAAa,MAAM,GAAG,CAAC,+BAA+B,YAAY,UAAa,KAAK,mBAAmB,cAAc,MAAM,GAAG,+BAA+B,WAAW,UAAa,KAAK,aAAa,QAAQ,GAAG,+BAA+B,YAAY,UAAa,KAAK,mBAAmB,QAAQ,CAAC,GAAG,KAAK,cAAc,YAAY,KAAK,CAAC,CAAC,CAAC;AAAA,EACpa;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,gBAAgB,CAAC,qBAAqB;AAAA,MACtC,UAAU,CAAC,gBAAgB;AAAA,MAC3B,WAAW,CAAC,gBAAgB;AAAA,IAC9B;AAAA,EACF;AACF;AACA,IAAM,cAAc,YAAU;AAC5B,SAAO;AAAA,IACL,gBAAgB;AAAA,IAChB,0BAA0B,OAAO,SAAS,UAAa,OAAO,SAAS;AAAA,IACvE,CAAC,gBAAgB,OAAO,IAAI,EAAE,GAAG,OAAO,SAAS;AAAA,IACjD,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,EACrB;AACF;AACA,IAAM,aAAa,YAAU;AAC3B,SAAO,SAAS,OAAO,IAAI,IAAI,kBAAkB;AACnD;AACA,MAAM,QAAQ;AAAA,EACZ,KAAK;AAAA,EACL,IAAI;AACN;", "names": ["wrapperElBox"]}