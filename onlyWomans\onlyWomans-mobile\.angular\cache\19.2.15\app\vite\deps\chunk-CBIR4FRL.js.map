{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/components/index4.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n// TODO(FW-2832): types\nclass Config {\n  constructor() {\n    this.m = new Map();\n  }\n  reset(configObj) {\n    this.m = new Map(Object.entries(configObj));\n  }\n  get(key, fallback) {\n    const value = this.m.get(key);\n    return value !== undefined ? value : fallback;\n  }\n  getBoolean(key, fallback = false) {\n    const val = this.m.get(key);\n    if (val === undefined) {\n      return fallback;\n    }\n    if (typeof val === 'string') {\n      return val === 'true';\n    }\n    return !!val;\n  }\n  getNumber(key, fallback) {\n    const val = parseFloat(this.m.get(key));\n    return isNaN(val) ? fallback !== undefined ? fallback : NaN : val;\n  }\n  set(key, value) {\n    this.m.set(key, value);\n  }\n}\nconst config = /*@__PURE__*/new Config();\nconst configFromSession = win => {\n  try {\n    const configStr = win.sessionStorage.getItem(IONIC_SESSION_KEY);\n    return configStr !== null ? JSON.parse(configStr) : {};\n  } catch (e) {\n    return {};\n  }\n};\nconst saveConfig = (win, c) => {\n  try {\n    win.sessionStorage.setItem(IONIC_SESSION_KEY, JSON.stringify(c));\n  } catch (e) {\n    return;\n  }\n};\nconst configFromURL = win => {\n  const configObj = {};\n  win.location.search.slice(1).split('&').map(entry => entry.split('=')).map(([key, value]) => {\n    try {\n      return [decodeURIComponent(key), decodeURIComponent(value)];\n    } catch (e) {\n      return ['', ''];\n    }\n  }).filter(([key]) => startsWith(key, IONIC_PREFIX)).map(([key, value]) => [key.slice(IONIC_PREFIX.length), value]).forEach(([key, value]) => {\n    configObj[key] = value;\n  });\n  return configObj;\n};\nconst startsWith = (input, search) => {\n  return input.substr(0, search.length) === search;\n};\nconst IONIC_PREFIX = 'ionic:';\nconst IONIC_SESSION_KEY = 'ionic-persist-config';\nvar LogLevel;\n(function (LogLevel) {\n  LogLevel[\"OFF\"] = \"OFF\";\n  LogLevel[\"ERROR\"] = \"ERROR\";\n  LogLevel[\"WARN\"] = \"WARN\";\n})(LogLevel || (LogLevel = {}));\n/**\n * Logs a warning to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n */\nconst printIonWarning = (message, ...params) => {\n  const logLevel = config.get('logLevel', LogLevel.WARN);\n  if ([LogLevel.WARN].includes(logLevel)) {\n    return console.warn(`[Ionic Warning]: ${message}`, ...params);\n  }\n};\n/**\n * Logs an error to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n * @param params - Additional arguments to supply to the console.error.\n */\nconst printIonError = (message, ...params) => {\n  const logLevel = config.get('logLevel', LogLevel.ERROR);\n  if ([LogLevel.ERROR, LogLevel.WARN].includes(logLevel)) {\n    return console.error(`[Ionic Error]: ${message}`, ...params);\n  }\n};\n/**\n * Prints an error informing developers that an implementation requires an element to be used\n * within a specific selector.\n *\n * @param el The web component element this is requiring the element.\n * @param targetSelectors The selector or selectors that were not found.\n */\nconst printRequiredElementError = (el, ...targetSelectors) => {\n  return console.error(`<${el.tagName.toLowerCase()}> must be used inside ${targetSelectors.join(' or ')}.`);\n};\nexport { LogLevel as L, printIonError as a, configFromSession as b, config as c, configFromURL as d, printRequiredElementError as e, printIonWarning as p, saveConfig as s };"], "mappings": ";AAIA,IAAM,SAAN,MAAa;AAAA,EACX,cAAc;AACZ,SAAK,IAAI,oBAAI,IAAI;AAAA,EACnB;AAAA,EACA,MAAM,WAAW;AACf,SAAK,IAAI,IAAI,IAAI,OAAO,QAAQ,SAAS,CAAC;AAAA,EAC5C;AAAA,EACA,IAAI,KAAK,UAAU;AACjB,UAAM,QAAQ,KAAK,EAAE,IAAI,GAAG;AAC5B,WAAO,UAAU,SAAY,QAAQ;AAAA,EACvC;AAAA,EACA,WAAW,KAAK,WAAW,OAAO;AAChC,UAAM,MAAM,KAAK,EAAE,IAAI,GAAG;AAC1B,QAAI,QAAQ,QAAW;AACrB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,QAAQ,UAAU;AAC3B,aAAO,QAAQ;AAAA,IACjB;AACA,WAAO,CAAC,CAAC;AAAA,EACX;AAAA,EACA,UAAU,KAAK,UAAU;AACvB,UAAM,MAAM,WAAW,KAAK,EAAE,IAAI,GAAG,CAAC;AACtC,WAAO,MAAM,GAAG,IAAI,aAAa,SAAY,WAAW,MAAM;AAAA,EAChE;AAAA,EACA,IAAI,KAAK,OAAO;AACd,SAAK,EAAE,IAAI,KAAK,KAAK;AAAA,EACvB;AACF;AACA,IAAM,SAAsB,IAAI,OAAO;AACvC,IAAM,oBAAoB,SAAO;AAC/B,MAAI;AACF,UAAM,YAAY,IAAI,eAAe,QAAQ,iBAAiB;AAC9D,WAAO,cAAc,OAAO,KAAK,MAAM,SAAS,IAAI,CAAC;AAAA,EACvD,SAAS,GAAG;AACV,WAAO,CAAC;AAAA,EACV;AACF;AACA,IAAM,aAAa,CAAC,KAAK,MAAM;AAC7B,MAAI;AACF,QAAI,eAAe,QAAQ,mBAAmB,KAAK,UAAU,CAAC,CAAC;AAAA,EACjE,SAAS,GAAG;AACV;AAAA,EACF;AACF;AACA,IAAM,gBAAgB,SAAO;AAC3B,QAAM,YAAY,CAAC;AACnB,MAAI,SAAS,OAAO,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,WAAS,MAAM,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AAC3F,QAAI;AACF,aAAO,CAAC,mBAAmB,GAAG,GAAG,mBAAmB,KAAK,CAAC;AAAA,IAC5D,SAAS,GAAG;AACV,aAAO,CAAC,IAAI,EAAE;AAAA,IAChB;AAAA,EACF,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,MAAM,WAAW,KAAK,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,IAAI,MAAM,aAAa,MAAM,GAAG,KAAK,CAAC,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC3I,cAAU,GAAG,IAAI;AAAA,EACnB,CAAC;AACD,SAAO;AACT;AACA,IAAM,aAAa,CAAC,OAAO,WAAW;AACpC,SAAO,MAAM,OAAO,GAAG,OAAO,MAAM,MAAM;AAC5C;AACA,IAAM,eAAe;AACrB,IAAM,oBAAoB;AAC1B,IAAI;AAAA,CACH,SAAUA,WAAU;AACnB,EAAAA,UAAS,KAAK,IAAI;AAClB,EAAAA,UAAS,OAAO,IAAI;AACpB,EAAAA,UAAS,MAAM,IAAI;AACrB,GAAG,aAAa,WAAW,CAAC,EAAE;AAO9B,IAAM,kBAAkB,CAAC,YAAY,WAAW;AAC9C,QAAM,WAAW,OAAO,IAAI,YAAY,SAAS,IAAI;AACrD,MAAI,CAAC,SAAS,IAAI,EAAE,SAAS,QAAQ,GAAG;AACtC,WAAO,QAAQ,KAAK,oBAAoB,OAAO,IAAI,GAAG,MAAM;AAAA,EAC9D;AACF;AAQA,IAAM,gBAAgB,CAAC,YAAY,WAAW;AAC5C,QAAM,WAAW,OAAO,IAAI,YAAY,SAAS,KAAK;AACtD,MAAI,CAAC,SAAS,OAAO,SAAS,IAAI,EAAE,SAAS,QAAQ,GAAG;AACtD,WAAO,QAAQ,MAAM,kBAAkB,OAAO,IAAI,GAAG,MAAM;AAAA,EAC7D;AACF;AAQA,IAAM,4BAA4B,CAAC,OAAO,oBAAoB;AAC5D,SAAO,QAAQ,MAAM,IAAI,GAAG,QAAQ,YAAY,CAAC,yBAAyB,gBAAgB,KAAK,MAAM,CAAC,GAAG;AAC3G;", "names": ["LogLevel"]}