import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-beauty',
  templateUrl: './beauty.page.html',
  styleUrls: ['./beauty.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class BeautyPage implements OnInit {

  beautyCategories = [
    { id: 1, name: 'Face Makeup', icon: '💄', color: 'var(--ow-rose-500)' },
    { id: 2, name: 'Eye Makeup', icon: '👁️', color: 'var(--ow-purple-500)' },
    { id: 3, name: 'Lip Products', icon: '💋', color: 'var(--ow-gold-500)' },
    { id: 4, name: 'Skincare', icon: '✨', color: 'var(--ion-color-success)' }
  ];

  beautyProducts = [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      price: 899,
      originalPrice: 1299,
      image: 'assets/beauty/lipstick1.jpg',
      rating: 4.7,
      reviews: 256,
      category: 'Lip Products'
    },
    {
      id: 2,
      name: 'Eyeshadow Palette',
      price: 1299,
      originalPrice: 1899,
      image: 'assets/beauty/eyeshadow1.jpg',
      rating: 4.8,
      reviews: 189,
      category: 'Eye Makeup'
    }
  ];

  tutorials = [
    {
      id: 1,
      title: 'Perfect Winged Eyeliner',
      duration: '5 min',
      thumbnail: 'assets/tutorials/eyeliner.jpg',
      views: '12K'
    },
    {
      id: 2,
      title: 'Glowing Skin Routine',
      duration: '8 min',
      thumbnail: 'assets/tutorials/skincare.jpg',
      views: '8.5K'
    }
  ];

  constructor() { }

  ngOnInit() {
  }

  onCategoryClick(category: any) {
    console.log('Category clicked:', category);
  }

  onProductClick(product: any) {
    console.log('Product clicked:', product);
  }

  onTutorialClick(tutorial: any) {
    console.log('Tutorial clicked:', tutorial);
  }
}
