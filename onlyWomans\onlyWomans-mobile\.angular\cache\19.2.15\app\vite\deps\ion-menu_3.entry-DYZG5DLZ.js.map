{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-menu_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, l as config, o as printIonError, e as getIonMode, a as isPlatform, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-hHmYLOfE.js';\nimport { o as getPresentedOverlay, B as BACKDROP, n as focusFirstDescendant, q as focusLastDescendant, G as GESTURE } from './overlays-8Y2rA-ps.js';\nimport { G as GESTURE_CONTROLLER } from './gesture-controller-BTEOs1at.js';\nimport { shouldUseCloseWatcher } from './hardware-back-button-DcH0BbDp.js';\nimport { o as isEndSide, i as inheritAriaAttributes, l as assert, e as clamp } from './helpers-1O4D2b7y.js';\nimport { m as menuController } from './index-D8sncTHY.js';\nimport { h as hostContext, c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { u as menuOutline, v as menuSharp } from './index-BLV6ykCk.js';\nimport './index-ZjP4CjeZ.js';\nimport './framework-delegate-DxcnWic_.js';\nimport './animation-BWcUKtbn.js';\nconst menuIosCss = \":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width, var(--width));min-width:var(--side-min-width, var(--min-width));max-width:var(--side-max-width, var(--max-width))}:host(.menu-pane-visible.split-pane-side){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none;box-shadow:none;z-index:0}:host(.menu-pane-visible.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}:host(.menu-pane-visible.split-pane-side){-ms-flex-order:-1;order:-1}:host(.menu-pane-visible.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-pane-visible.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-pane-visible.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-type-push){z-index:1000}:host(.menu-type-push) .show-backdrop{display:block}\";\nconst menuMdCss = \":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width, var(--width));min-width:var(--side-min-width, var(--min-width));max-width:var(--side-max-width, var(--max-width))}:host(.menu-pane-visible.split-pane-side){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none;box-shadow:none;z-index:0}:host(.menu-pane-visible.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}:host(.menu-pane-visible.split-pane-side){-ms-flex-order:-1;order:-1}:host(.menu-pane-visible.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-pane-visible.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-pane-visible.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-type-overlay) .menu-inner{-webkit-box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18);box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18)}\";\nconst iosEasing = 'cubic-bezier(0.32,0.72,0,1)';\nconst mdEasing = 'cubic-bezier(0.0,0.0,0.2,1)';\nconst iosEasingReverse = 'cubic-bezier(1, 0, 0.68, 0.28)';\nconst mdEasingReverse = 'cubic-bezier(0.4, 0, 0.6, 1)';\nconst Menu = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionWillOpen = createEvent(this, \"ionWillOpen\", 7);\n    this.ionWillClose = createEvent(this, \"ionWillClose\", 7);\n    this.ionDidOpen = createEvent(this, \"ionDidOpen\", 7);\n    this.ionDidClose = createEvent(this, \"ionDidClose\", 7);\n    this.ionMenuChange = createEvent(this, \"ionMenuChange\", 7);\n    this.lastOnEnd = 0;\n    this.blocker = GESTURE_CONTROLLER.createBlocker({\n      disableScroll: true\n    });\n    this.didLoad = false;\n    /**\n     * Flag used to determine if an open/close\n     * operation was cancelled. For example, if\n     * an app calls \"menu.open\" then disables the menu\n     * part way through the animation, then this would\n     * be considered a cancelled operation.\n     */\n    this.operationCancelled = false;\n    this.isAnimating = false;\n    this._isOpen = false;\n    this.inheritedAttributes = {};\n    this.handleFocus = ev => {\n      /**\n       * Overlays have their own focus trapping listener\n       * so we do not want the two listeners to conflict\n       * with each other. If the top-most overlay that is\n       * open does not contain this ion-menu, then ion-menu's\n       * focus trapping should not run.\n       */\n      const lastOverlay = getPresentedOverlay(document);\n      if (lastOverlay && !lastOverlay.contains(this.el)) {\n        return;\n      }\n      this.trapKeyboardFocus(ev, document);\n    };\n    /**\n     * If true, then the menu should be\n     * visible within a split pane.\n     * If false, then the menu is hidden.\n     * However, the menu-button/menu-toggle\n     * components can be used to open the\n     * menu.\n     */\n    this.isPaneVisible = false;\n    this.isEndSide = false;\n    /**\n     * If `true`, the menu is disabled.\n     */\n    this.disabled = false;\n    /**\n     * Which side of the view the menu should be placed.\n     */\n    this.side = 'start';\n    /**\n     * If `true`, swiping the menu is enabled.\n     */\n    this.swipeGesture = true;\n    /**\n     * The edge threshold for dragging the menu open.\n     * If a drag/swipe happens over this value, the menu is not triggered.\n     */\n    this.maxEdgeStart = 50;\n  }\n  typeChanged(type, oldType) {\n    const contentEl = this.contentEl;\n    if (contentEl) {\n      if (oldType !== undefined) {\n        contentEl.classList.remove(`menu-content-${oldType}`);\n      }\n      contentEl.classList.add(`menu-content-${type}`);\n      contentEl.removeAttribute('style');\n    }\n    if (this.menuInnerEl) {\n      // Remove effects of previous animations\n      this.menuInnerEl.removeAttribute('style');\n    }\n    this.animation = undefined;\n  }\n  disabledChanged() {\n    this.updateState();\n    this.ionMenuChange.emit({\n      disabled: this.disabled,\n      open: this._isOpen\n    });\n  }\n  sideChanged() {\n    this.isEndSide = isEndSide(this.side);\n    /**\n     * Menu direction animation is calculated based on the document direction.\n     * If the document direction changes, we need to create a new animation.\n     */\n    this.animation = undefined;\n  }\n  swipeGestureChanged() {\n    this.updateState();\n  }\n  async connectedCallback() {\n    // TODO: connectedCallback is fired in CE build\n    // before WC is defined. This needs to be fixed in Stencil.\n    if (typeof customElements !== 'undefined' && customElements != null) {\n      await customElements.whenDefined('ion-menu');\n    }\n    if (this.type === undefined) {\n      this.type = config.get('menuType', 'overlay');\n    }\n    const content = this.contentId !== undefined ? document.getElementById(this.contentId) : null;\n    if (content === null) {\n      printIonError('[ion-menu] - Must have a \"content\" element to listen for drag events on.');\n      return;\n    }\n    if (this.el.contains(content)) {\n      printIonError(`[ion-menu] - The \"contentId\" should refer to the main view's ion-content, not the ion-content inside of the ion-menu.`);\n    }\n    this.contentEl = content;\n    // add menu's content classes\n    content.classList.add('menu-content');\n    this.typeChanged(this.type, undefined);\n    this.sideChanged();\n    // register this menu with the app's menu controller\n    menuController._register(this);\n    this.menuChanged();\n    this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n      el: document,\n      gestureName: 'menu-swipe',\n      gesturePriority: 30,\n      threshold: 10,\n      blurOnStart: true,\n      canStart: ev => this.canStart(ev),\n      onWillStart: () => this.onWillStart(),\n      onStart: () => this.onStart(),\n      onMove: ev => this.onMove(ev),\n      onEnd: ev => this.onEnd(ev)\n    });\n    this.updateState();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  async componentDidLoad() {\n    this.didLoad = true;\n    /**\n     * A menu inside of a split pane is assumed\n     * to be a side pane.\n     *\n     * When the menu is loaded it needs to\n     * see if it should be considered visible inside\n     * of the split pane. If the split pane is\n     * hidden then the menu should be too.\n     */\n    const splitPane = this.el.closest('ion-split-pane');\n    if (splitPane !== null) {\n      this.isPaneVisible = await splitPane.isVisible();\n    }\n    this.menuChanged();\n    this.updateState();\n  }\n  menuChanged() {\n    /**\n     * Inform dependent components such as ion-menu-button\n     * that the menu is ready. Note that we only want to do this\n     * once the menu has been rendered which is why we check for didLoad.\n     */\n    if (this.didLoad) {\n      this.ionMenuChange.emit({\n        disabled: this.disabled,\n        open: this._isOpen\n      });\n    }\n  }\n  async disconnectedCallback() {\n    /**\n     * The menu should be closed when it is\n     * unmounted from the DOM.\n     * This is an async call, so we need to wait for\n     * this to finish otherwise contentEl\n     * will not have MENU_CONTENT_OPEN removed.\n     */\n    await this.close(false);\n    this.blocker.destroy();\n    menuController._unregister(this);\n    if (this.animation) {\n      this.animation.destroy();\n    }\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n    this.animation = undefined;\n    this.contentEl = undefined;\n  }\n  onSplitPaneChanged(ev) {\n    const closestSplitPane = this.el.closest('ion-split-pane');\n    if (closestSplitPane !== null && closestSplitPane === ev.target) {\n      this.isPaneVisible = ev.detail.visible;\n      this.updateState();\n    }\n  }\n  onBackdropClick(ev) {\n    // TODO(FW-2832): type (CustomEvent triggers errors which should be sorted)\n    if (this._isOpen && this.lastOnEnd < ev.timeStamp - 100) {\n      const shouldClose = ev.composedPath ? !ev.composedPath().includes(this.menuInnerEl) : false;\n      if (shouldClose) {\n        ev.preventDefault();\n        ev.stopPropagation();\n        this.close(undefined, BACKDROP);\n      }\n    }\n  }\n  onKeydown(ev) {\n    if (ev.key === 'Escape') {\n      this.close(undefined, BACKDROP);\n    }\n  }\n  /**\n   * Returns `true` is the menu is open.\n   */\n  isOpen() {\n    return Promise.resolve(this._isOpen);\n  }\n  /**\n   * Returns `true` is the menu is active.\n   *\n   * A menu is active when it can be opened or closed, meaning it's enabled\n   * and it's not part of a `ion-split-pane`.\n   */\n  isActive() {\n    return Promise.resolve(this._isActive());\n  }\n  /**\n   * Opens the menu. If the menu is already open or it can't be opened,\n   * it returns `false`.\n   */\n  open(animated = true) {\n    return this.setOpen(true, animated);\n  }\n  /**\n   * Closes the menu. If the menu is already closed or it can't be closed,\n   * it returns `false`.\n   */\n  close(animated = true, role) {\n    return this.setOpen(false, animated, role);\n  }\n  /**\n   * Toggles the menu. If the menu is already open, it will try to close, otherwise it will try to open it.\n   * If the operation can't be completed successfully, it returns `false`.\n   */\n  toggle(animated = true) {\n    return this.setOpen(!this._isOpen, animated);\n  }\n  /**\n   * Opens or closes the button.\n   * If the operation can't be completed successfully, it returns `false`.\n   */\n  setOpen(shouldOpen, animated = true, role) {\n    return menuController._setOpen(this, shouldOpen, animated, role);\n  }\n  trapKeyboardFocus(ev, doc) {\n    const target = ev.target;\n    if (!target) {\n      return;\n    }\n    /**\n     * If the target is inside the menu contents, let the browser\n     * focus as normal and keep a log of the last focused element.\n     */\n    if (this.el.contains(target)) {\n      this.lastFocus = target;\n    } else {\n      /**\n       * Otherwise, we are about to have focus go out of the menu.\n       * Wrap the focus to either the first or last element.\n       */\n      const {\n        el\n      } = this;\n      /**\n       * Once we call `focusFirstDescendant`, another focus event\n       * will fire, which will cause `lastFocus` to be updated\n       * before we can run the code after that. We cache the value\n       * here to avoid that.\n       */\n      focusFirstDescendant(el);\n      /**\n       * If the cached last focused element is the same as the now-\n       * active element, that means the user was on the first element\n       * already and pressed Shift + Tab, so we need to wrap to the\n       * last descendant.\n       */\n      if (this.lastFocus === doc.activeElement) {\n        focusLastDescendant(el);\n      }\n    }\n  }\n  async _setOpen(shouldOpen, animated = true, role) {\n    // If the menu is disabled or it is currently being animated, let's do nothing\n    if (!this._isActive() || this.isAnimating || shouldOpen === this._isOpen) {\n      return false;\n    }\n    this.beforeAnimation(shouldOpen, role);\n    await this.loadAnimation();\n    await this.startAnimation(shouldOpen, animated);\n    /**\n     * If the animation was cancelled then\n     * return false because the operation\n     * did not succeed.\n     */\n    if (this.operationCancelled) {\n      this.operationCancelled = false;\n      return false;\n    }\n    this.afterAnimation(shouldOpen, role);\n    return true;\n  }\n  async loadAnimation() {\n    // Menu swipe animation takes the menu's inner width as parameter,\n    // If `offsetWidth` changes, we need to create a new animation.\n    const width = this.menuInnerEl.offsetWidth;\n    /**\n     * Menu direction animation is calculated based on the document direction.\n     * If the document direction changes, we need to create a new animation.\n     */\n    const isEndSide$1 = isEndSide(this.side);\n    if (width === this.width && this.animation !== undefined && isEndSide$1 === this.isEndSide) {\n      return;\n    }\n    this.width = width;\n    this.isEndSide = isEndSide$1;\n    // Destroy existing animation\n    if (this.animation) {\n      this.animation.destroy();\n      this.animation = undefined;\n    }\n    // Create new animation\n    const animation = this.animation = await menuController._createAnimation(this.type, this);\n    if (!config.getBoolean('animated', true)) {\n      animation.duration(0);\n    }\n    animation.fill('both');\n  }\n  async startAnimation(shouldOpen, animated) {\n    const isReversed = !shouldOpen;\n    const mode = getIonMode(this);\n    const easing = mode === 'ios' ? iosEasing : mdEasing;\n    const easingReverse = mode === 'ios' ? iosEasingReverse : mdEasingReverse;\n    const ani = this.animation.direction(isReversed ? 'reverse' : 'normal').easing(isReversed ? easingReverse : easing);\n    if (animated) {\n      await ani.play();\n    } else {\n      ani.play({\n        sync: true\n      });\n    }\n    /**\n     * We run this after the play invocation\n     * instead of using ani.onFinish so that\n     * multiple onFinish callbacks do not get\n     * run if an animation is played, stopped,\n     * and then played again.\n     */\n    if (ani.getDirection() === 'reverse') {\n      ani.direction('normal');\n    }\n  }\n  _isActive() {\n    return !this.disabled && !this.isPaneVisible;\n  }\n  canSwipe() {\n    return this.swipeGesture && !this.isAnimating && this._isActive();\n  }\n  canStart(detail) {\n    // Do not allow swipe gesture if a modal is open\n    const isModalPresented = !!document.querySelector('ion-modal.show-modal');\n    if (isModalPresented || !this.canSwipe()) {\n      return false;\n    }\n    if (this._isOpen) {\n      return true;\n    } else if (menuController._getOpenSync()) {\n      return false;\n    }\n    return checkEdgeSide(window, detail.currentX, this.isEndSide, this.maxEdgeStart);\n  }\n  onWillStart() {\n    this.beforeAnimation(!this._isOpen, GESTURE);\n    return this.loadAnimation();\n  }\n  onStart() {\n    if (!this.isAnimating || !this.animation) {\n      assert(false, 'isAnimating has to be true');\n      return;\n    }\n    // the cloned animation should not use an easing curve during seek\n    this.animation.progressStart(true, this._isOpen ? 1 : 0);\n  }\n  onMove(detail) {\n    if (!this.isAnimating || !this.animation) {\n      assert(false, 'isAnimating has to be true');\n      return;\n    }\n    const delta = computeDelta(detail.deltaX, this._isOpen, this.isEndSide);\n    const stepValue = delta / this.width;\n    this.animation.progressStep(this._isOpen ? 1 - stepValue : stepValue);\n  }\n  onEnd(detail) {\n    if (!this.isAnimating || !this.animation) {\n      assert(false, 'isAnimating has to be true');\n      return;\n    }\n    const isOpen = this._isOpen;\n    const isEndSide = this.isEndSide;\n    const delta = computeDelta(detail.deltaX, isOpen, isEndSide);\n    const width = this.width;\n    const stepValue = delta / width;\n    const velocity = detail.velocityX;\n    const z = width / 2.0;\n    const shouldCompleteRight = velocity >= 0 && (velocity > 0.2 || detail.deltaX > z);\n    const shouldCompleteLeft = velocity <= 0 && (velocity < -0.2 || detail.deltaX < -z);\n    const shouldComplete = isOpen ? isEndSide ? shouldCompleteRight : shouldCompleteLeft : isEndSide ? shouldCompleteLeft : shouldCompleteRight;\n    let shouldOpen = !isOpen && shouldComplete;\n    if (isOpen && !shouldComplete) {\n      shouldOpen = true;\n    }\n    this.lastOnEnd = detail.currentTime;\n    // Account for rounding errors in JS\n    let newStepValue = shouldComplete ? 0.001 : -1e-3;\n    /**\n     * stepValue can sometimes return a negative\n     * value, but you can't have a negative time value\n     * for the cubic bezier curve (at least with web animations)\n     */\n    const adjustedStepValue = stepValue < 0 ? 0.01 : stepValue;\n    /**\n     * Animation will be reversed here, so need to\n     * reverse the easing curve as well\n     *\n     * Additionally, we need to account for the time relative\n     * to the new easing curve, as `stepValue` is going to be given\n     * in terms of a linear curve.\n     */\n    newStepValue += getTimeGivenProgression([0, 0], [0.4, 0], [0.6, 1], [1, 1], clamp(0, adjustedStepValue, 0.9999))[0] || 0;\n    const playTo = this._isOpen ? !shouldComplete : shouldComplete;\n    this.animation.easing('cubic-bezier(0.4, 0.0, 0.6, 1)').onFinish(() => this.afterAnimation(shouldOpen, GESTURE), {\n      oneTimeCallback: true\n    }).progressEnd(playTo ? 1 : 0, this._isOpen ? 1 - newStepValue : newStepValue, 300);\n  }\n  beforeAnimation(shouldOpen, role) {\n    assert(!this.isAnimating, '_before() should not be called while animating');\n    /**\n     * When the menu is presented on an Android device, TalkBack's focus rings\n     * may appear in the wrong position due to the transition (specifically\n     * `transform` styles). This occurs because the focus rings are initially\n     * displayed at the starting position of the elements before the transition\n     * begins. This workaround ensures the focus rings do not appear in the\n     * incorrect location.\n     *\n     * If this solution is applied to iOS devices, then it leads to a bug where\n     * the overlays cannot be accessed by screen readers. This is due to\n     * VoiceOver not being able to update the accessibility tree when the\n     * `aria-hidden` is removed.\n     */\n    if (isPlatform('android')) {\n      this.el.setAttribute('aria-hidden', 'true');\n    }\n    // this places the menu into the correct location before it animates in\n    // this css class doesn't actually kick off any animations\n    this.el.classList.add(SHOW_MENU);\n    /**\n     * We add a tabindex here so that focus trapping\n     * still works even if the menu does not have\n     * any focusable elements slotted inside. The\n     * focus trapping utility will fallback to focusing\n     * the menu so focus does not leave when the menu\n     * is open.\n     */\n    this.el.setAttribute('tabindex', '0');\n    if (this.backdropEl) {\n      this.backdropEl.classList.add(SHOW_BACKDROP);\n    }\n    // add css class and hide content behind menu from screen readers\n    if (this.contentEl) {\n      this.contentEl.classList.add(MENU_CONTENT_OPEN);\n      /**\n       * When the menu is open and overlaying the main\n       * content, the main content should not be announced\n       * by the screenreader as the menu is the main\n       * focus. This is useful with screenreaders that have\n       * \"read from top\" gestures that read the entire\n       * page from top to bottom when activated.\n       * This should be done before the animation starts\n       * so that users cannot accidentally scroll\n       * the content while dragging a menu open.\n       */\n      this.contentEl.setAttribute('aria-hidden', 'true');\n    }\n    this.blocker.block();\n    this.isAnimating = true;\n    if (shouldOpen) {\n      this.ionWillOpen.emit();\n    } else {\n      this.ionWillClose.emit({\n        role\n      });\n    }\n  }\n  afterAnimation(isOpen, role) {\n    var _a;\n    // keep opening/closing the menu disabled for a touch more yet\n    // only add listeners/css if it's enabled and isOpen\n    // and only remove listeners/css if it's not open\n    // emit opened/closed events\n    this._isOpen = isOpen;\n    this.isAnimating = false;\n    if (!this._isOpen) {\n      this.blocker.unblock();\n    }\n    if (isOpen) {\n      /**\n       * When the menu is presented on an Android device, TalkBack's focus rings\n       * may appear in the wrong position due to the transition (specifically\n       * `transform` styles). The menu is hidden from screen readers during the\n       * transition to prevent this. Once the transition is complete, the menu\n       * is shown again.\n       */\n      if (isPlatform('android')) {\n        this.el.removeAttribute('aria-hidden');\n      }\n      // emit open event\n      this.ionDidOpen.emit();\n      /**\n       * Move focus to the menu to prepare focus trapping, as long as\n       * it isn't already focused. Use the host element instead of the\n       * first descendant to avoid the scroll position jumping around.\n       */\n      const focusedMenu = (_a = document.activeElement) === null || _a === void 0 ? void 0 : _a.closest('ion-menu');\n      if (focusedMenu !== this.el) {\n        this.el.focus();\n      }\n      // start focus trapping\n      document.addEventListener('focus', this.handleFocus, true);\n    } else {\n      this.el.removeAttribute('aria-hidden');\n      // remove css classes and unhide content from screen readers\n      this.el.classList.remove(SHOW_MENU);\n      /**\n       * Remove tabindex from the menu component\n       * so that is cannot be tabbed to.\n       */\n      this.el.removeAttribute('tabindex');\n      if (this.contentEl) {\n        this.contentEl.classList.remove(MENU_CONTENT_OPEN);\n        /**\n         * Remove aria-hidden so screen readers\n         * can announce the main content again\n         * now that the menu is not the main focus.\n         */\n        this.contentEl.removeAttribute('aria-hidden');\n      }\n      if (this.backdropEl) {\n        this.backdropEl.classList.remove(SHOW_BACKDROP);\n      }\n      if (this.animation) {\n        this.animation.stop();\n      }\n      // emit close event\n      this.ionDidClose.emit({\n        role\n      });\n      // undo focus trapping so multiple menus don't collide\n      document.removeEventListener('focus', this.handleFocus, true);\n    }\n  }\n  updateState() {\n    const isActive = this._isActive();\n    if (this.gesture) {\n      this.gesture.enable(isActive && this.swipeGesture);\n    }\n    /**\n     * If the menu is disabled but it is still open\n     * then we should close the menu immediately.\n     * Additionally, if the menu is in the process\n     * of animating {open, close} and the menu is disabled\n     * then it should still be closed immediately.\n     */\n    if (!isActive) {\n      /**\n       * It is possible to disable the menu while\n       * it is mid-animation. When this happens, we\n       * need to set the operationCancelled flag\n       * so that this._setOpen knows to return false\n       * and not run the \"afterAnimation\" callback.\n       */\n      if (this.isAnimating) {\n        this.operationCancelled = true;\n      }\n      /**\n       * If the menu is disabled then we should\n       * forcibly close the menu even if it is open.\n       */\n      this.afterAnimation(false, GESTURE);\n    }\n  }\n  render() {\n    const {\n      type,\n      disabled,\n      el,\n      isPaneVisible,\n      inheritedAttributes,\n      side\n    } = this;\n    const mode = getIonMode(this);\n    /**\n     * If the Close Watcher is enabled then\n     * the ionBackButton listener in the menu controller\n     * will handle closing the menu when Escape is pressed.\n     */\n    return h(Host, {\n      key: '9e4ae9476a76781f1d228395c9af9e1c39ec82bb',\n      onKeyDown: shouldUseCloseWatcher() ? null : this.onKeydown,\n      role: \"navigation\",\n      \"aria-label\": inheritedAttributes['aria-label'] || 'menu',\n      class: {\n        [mode]: true,\n        [`menu-type-${type}`]: true,\n        'menu-enabled': !disabled,\n        [`menu-side-${side}`]: true,\n        'menu-pane-visible': isPaneVisible,\n        'split-pane-side': hostContext('ion-split-pane', el)\n      }\n    }, h(\"div\", {\n      key: 'c6153589d872ac7e3fdf5eedfdb858eb64ccd713',\n      class: \"menu-inner\",\n      part: \"container\",\n      ref: el => this.menuInnerEl = el\n    }, h(\"slot\", {\n      key: '9994aac4b22f17db34c9b8b2aa56b8710b9df645'\n    })), h(\"ion-backdrop\", {\n      key: 'f09ac30cc4dd2dcb10628965e659dae5a23baf98',\n      ref: el => this.backdropEl = el,\n      class: \"menu-backdrop\",\n      tappable: false,\n      stopPropagation: false,\n      part: \"backdrop\"\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"type\": [\"typeChanged\"],\n      \"disabled\": [\"disabledChanged\"],\n      \"side\": [\"sideChanged\"],\n      \"swipeGesture\": [\"swipeGestureChanged\"]\n    };\n  }\n};\nconst computeDelta = (deltaX, isOpen, isEndSide) => {\n  return Math.max(0, isOpen !== isEndSide ? -deltaX : deltaX);\n};\nconst checkEdgeSide = (win, posX, isEndSide, maxEdgeStart) => {\n  if (isEndSide) {\n    return posX >= win.innerWidth - maxEdgeStart;\n  } else {\n    return posX <= maxEdgeStart;\n  }\n};\nconst SHOW_MENU = 'show-menu';\nconst SHOW_BACKDROP = 'show-backdrop';\nconst MENU_CONTENT_OPEN = 'menu-content-open';\nMenu.style = {\n  ios: menuIosCss,\n  md: menuMdCss\n};\n\n// Given a menu, return whether or not the menu toggle should be visible\nconst updateVisibility = async menu => {\n  const menuEl = await menuController.get(menu);\n  return !!(menuEl && (await menuEl.isActive()));\n};\nconst menuButtonIosCss = \":host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.1;--border-radius:4px;--color:var(--ion-color-primary, #0054e9);--padding-start:5px;--padding-end:5px;min-height:32px;font-size:clamp(31px, 1.9375rem, 38.13px)}:host(.ion-activated){opacity:0.4}@media (any-hover: hover){:host(:hover){opacity:0.6}}\";\nconst menuButtonMdCss = \":host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.12;--background-hover:currentColor;--background-hover-opacity:.04;--border-radius:50%;--color:initial;--padding-start:8px;--padding-end:8px;width:3rem;height:3rem;font-size:1.5rem}:host(.ion-color.ion-focused)::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}\";\nconst MenuButton = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.inheritedAttributes = {};\n    this.visible = false;\n    /**\n     * If `true`, the user cannot interact with the menu button.\n     */\n    this.disabled = false;\n    /**\n     * Automatically hides the menu button when the corresponding menu is not active\n     */\n    this.autoHide = true;\n    /**\n     * The type of the button.\n     */\n    this.type = 'button';\n    this.onClick = async () => {\n      return menuController.toggle(this.menu);\n    };\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  componentDidLoad() {\n    this.visibilityChanged();\n  }\n  async visibilityChanged() {\n    this.visible = await updateVisibility(this.menu);\n  }\n  render() {\n    const {\n      color,\n      disabled,\n      inheritedAttributes\n    } = this;\n    const mode = getIonMode(this);\n    const menuIcon = config.get('menuIcon', mode === 'ios' ? menuOutline : menuSharp);\n    const hidden = this.autoHide && !this.visible;\n    const attrs = {\n      type: this.type\n    };\n    const ariaLabel = inheritedAttributes['aria-label'] || 'menu';\n    return h(Host, {\n      key: '9f0f0e50d39a6872508220c58e64bb2092a0d7ef',\n      onClick: this.onClick,\n      \"aria-disabled\": disabled ? 'true' : null,\n      \"aria-hidden\": hidden ? 'true' : null,\n      class: createColorClasses(color, {\n        [mode]: true,\n        button: true,\n        // ion-buttons target .button\n        'menu-button-hidden': hidden,\n        'menu-button-disabled': disabled,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'ion-activatable': true,\n        'ion-focusable': true\n      })\n    }, h(\"button\", Object.assign({\n      key: 'ffebf7083d23501839970059ef8e411b571de197'\n    }, attrs, {\n      disabled: disabled,\n      class: \"button-native\",\n      part: \"native\",\n      \"aria-label\": ariaLabel\n    }), h(\"span\", {\n      key: 'cab0c1c763b3ce33ef11dba1d230f66126e59424',\n      class: \"button-inner\"\n    }, h(\"slot\", {\n      key: 'ccfd2be8479b75b5c63e97e1ca7dfe203e9b36ee'\n    }, h(\"ion-icon\", {\n      key: 'ac254fe7f327b08f1ae3fcea89d5cf0e83c9a96c',\n      part: \"icon\",\n      icon: menuIcon,\n      mode: mode,\n      lazy: false,\n      \"aria-hidden\": \"true\"\n    }))), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: 'f0f17c4ca96e3eed3c1727ee00578d40af8f0115',\n      type: \"unbounded\"\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nMenuButton.style = {\n  ios: menuButtonIosCss,\n  md: menuButtonMdCss\n};\nconst menuToggleCss = \":host(.menu-toggle-hidden){display:none}\";\nconst MenuToggle = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.visible = false;\n    /**\n     * Automatically hides the content when the corresponding menu is not active.\n     *\n     * By default, it's `true`. Change it to `false` in order to\n     * keep `ion-menu-toggle` always visible regardless the state of the menu.\n     */\n    this.autoHide = true;\n    this.onClick = () => {\n      return menuController.toggle(this.menu);\n    };\n  }\n  connectedCallback() {\n    this.visibilityChanged();\n  }\n  async visibilityChanged() {\n    this.visible = await updateVisibility(this.menu);\n  }\n  render() {\n    const mode = getIonMode(this);\n    const hidden = this.autoHide && !this.visible;\n    return h(Host, {\n      key: 'cd567114769a30bd3871ed5d15bf42aed39956e1',\n      onClick: this.onClick,\n      \"aria-hidden\": hidden ? 'true' : null,\n      class: {\n        [mode]: true,\n        'menu-toggle-hidden': hidden\n      }\n    }, h(\"slot\", {\n      key: '773d4cff95ca75f23578b1e1dca53c9933f28a33'\n    }));\n  }\n};\nMenuToggle.style = menuToggleCss;\nexport { Menu as ion_menu, MenuButton as ion_menu_button, MenuToggle as ion_menu_toggle };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,WAAW;AACjB,IAAM,mBAAmB;AACzB,IAAM,kBAAkB;AACxB,IAAM,OAAO,MAAM;AAAA,EACjB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,cAAc,YAAY,MAAM,eAAe,CAAC;AACrD,SAAK,eAAe,YAAY,MAAM,gBAAgB,CAAC;AACvD,SAAK,aAAa,YAAY,MAAM,cAAc,CAAC;AACnD,SAAK,cAAc,YAAY,MAAM,eAAe,CAAC;AACrD,SAAK,gBAAgB,YAAY,MAAM,iBAAiB,CAAC;AACzD,SAAK,YAAY;AACjB,SAAK,UAAU,mBAAmB,cAAc;AAAA,MAC9C,eAAe;AAAA,IACjB,CAAC;AACD,SAAK,UAAU;AAQf,SAAK,qBAAqB;AAC1B,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,sBAAsB,CAAC;AAC5B,SAAK,cAAc,QAAM;AAQvB,YAAM,cAAc,oBAAoB,QAAQ;AAChD,UAAI,eAAe,CAAC,YAAY,SAAS,KAAK,EAAE,GAAG;AACjD;AAAA,MACF;AACA,WAAK,kBAAkB,IAAI,QAAQ;AAAA,IACrC;AASA,SAAK,gBAAgB;AACrB,SAAK,YAAY;AAIjB,SAAK,WAAW;AAIhB,SAAK,OAAO;AAIZ,SAAK,eAAe;AAKpB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,YAAY,MAAM,SAAS;AACzB,UAAM,YAAY,KAAK;AACvB,QAAI,WAAW;AACb,UAAI,YAAY,QAAW;AACzB,kBAAU,UAAU,OAAO,gBAAgB,OAAO,EAAE;AAAA,MACtD;AACA,gBAAU,UAAU,IAAI,gBAAgB,IAAI,EAAE;AAC9C,gBAAU,gBAAgB,OAAO;AAAA,IACnC;AACA,QAAI,KAAK,aAAa;AAEpB,WAAK,YAAY,gBAAgB,OAAO;AAAA,IAC1C;AACA,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,kBAAkB;AAChB,SAAK,YAAY;AACjB,SAAK,cAAc,KAAK;AAAA,MACtB,UAAU,KAAK;AAAA,MACf,MAAM,KAAK;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,YAAY,UAAU,KAAK,IAAI;AAKpC,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,sBAAsB;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACM,oBAAoB;AAAA;AAGxB,UAAI,OAAO,mBAAmB,eAAe,kBAAkB,MAAM;AACnE,cAAM,eAAe,YAAY,UAAU;AAAA,MAC7C;AACA,UAAI,KAAK,SAAS,QAAW;AAC3B,aAAK,OAAO,OAAO,IAAI,YAAY,SAAS;AAAA,MAC9C;AACA,YAAM,UAAU,KAAK,cAAc,SAAY,SAAS,eAAe,KAAK,SAAS,IAAI;AACzF,UAAI,YAAY,MAAM;AACpB,sBAAc,0EAA0E;AACxF;AAAA,MACF;AACA,UAAI,KAAK,GAAG,SAAS,OAAO,GAAG;AAC7B,sBAAc,uHAAuH;AAAA,MACvI;AACA,WAAK,YAAY;AAEjB,cAAQ,UAAU,IAAI,cAAc;AACpC,WAAK,YAAY,KAAK,MAAM,MAAS;AACrC,WAAK,YAAY;AAEjB,qBAAe,UAAU,IAAI;AAC7B,WAAK,YAAY;AACjB,WAAK,WAAW,MAAM,OAAO,8BAAqB,GAAG,cAAc;AAAA,QACjE,IAAI;AAAA,QACJ,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,aAAa;AAAA,QACb,UAAU,QAAM,KAAK,SAAS,EAAE;AAAA,QAChC,aAAa,MAAM,KAAK,YAAY;AAAA,QACpC,SAAS,MAAM,KAAK,QAAQ;AAAA,QAC5B,QAAQ,QAAM,KAAK,OAAO,EAAE;AAAA,QAC5B,OAAO,QAAM,KAAK,MAAM,EAAE;AAAA,MAC5B,CAAC;AACD,WAAK,YAAY;AAAA,IACnB;AAAA;AAAA,EACA,oBAAoB;AAClB,SAAK,sBAAsB,sBAAsB,KAAK,EAAE;AAAA,EAC1D;AAAA,EACM,mBAAmB;AAAA;AACvB,WAAK,UAAU;AAUf,YAAM,YAAY,KAAK,GAAG,QAAQ,gBAAgB;AAClD,UAAI,cAAc,MAAM;AACtB,aAAK,gBAAgB,MAAM,UAAU,UAAU;AAAA,MACjD;AACA,WAAK,YAAY;AACjB,WAAK,YAAY;AAAA,IACnB;AAAA;AAAA,EACA,cAAc;AAMZ,QAAI,KAAK,SAAS;AAChB,WAAK,cAAc,KAAK;AAAA,QACtB,UAAU,KAAK;AAAA,QACf,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACM,uBAAuB;AAAA;AAQ3B,YAAM,KAAK,MAAM,KAAK;AACtB,WAAK,QAAQ,QAAQ;AACrB,qBAAe,YAAY,IAAI;AAC/B,UAAI,KAAK,WAAW;AAClB,aAAK,UAAU,QAAQ;AAAA,MACzB;AACA,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ,QAAQ;AACrB,aAAK,UAAU;AAAA,MACjB;AACA,WAAK,YAAY;AACjB,WAAK,YAAY;AAAA,IACnB;AAAA;AAAA,EACA,mBAAmB,IAAI;AACrB,UAAM,mBAAmB,KAAK,GAAG,QAAQ,gBAAgB;AACzD,QAAI,qBAAqB,QAAQ,qBAAqB,GAAG,QAAQ;AAC/D,WAAK,gBAAgB,GAAG,OAAO;AAC/B,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,gBAAgB,IAAI;AAElB,QAAI,KAAK,WAAW,KAAK,YAAY,GAAG,YAAY,KAAK;AACvD,YAAM,cAAc,GAAG,eAAe,CAAC,GAAG,aAAa,EAAE,SAAS,KAAK,WAAW,IAAI;AACtF,UAAI,aAAa;AACf,WAAG,eAAe;AAClB,WAAG,gBAAgB;AACnB,aAAK,MAAM,QAAW,QAAQ;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,IAAI;AACZ,QAAI,GAAG,QAAQ,UAAU;AACvB,WAAK,MAAM,QAAW,QAAQ;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,WAAO,QAAQ,QAAQ,KAAK,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,QAAQ,QAAQ,KAAK,UAAU,CAAC;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,WAAW,MAAM;AACpB,WAAO,KAAK,QAAQ,MAAM,QAAQ;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,WAAW,MAAM,MAAM;AAC3B,WAAO,KAAK,QAAQ,OAAO,UAAU,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,WAAW,MAAM;AACtB,WAAO,KAAK,QAAQ,CAAC,KAAK,SAAS,QAAQ;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,YAAY,WAAW,MAAM,MAAM;AACzC,WAAO,eAAe,SAAS,MAAM,YAAY,UAAU,IAAI;AAAA,EACjE;AAAA,EACA,kBAAkB,IAAI,KAAK;AACzB,UAAM,SAAS,GAAG;AAClB,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AAKA,QAAI,KAAK,GAAG,SAAS,MAAM,GAAG;AAC5B,WAAK,YAAY;AAAA,IACnB,OAAO;AAKL,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AAOJ,2BAAqB,EAAE;AAOvB,UAAI,KAAK,cAAc,IAAI,eAAe;AACxC,4BAAoB,EAAE;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA,EACM,SAAS,YAAY,WAAW,MAAM,MAAM;AAAA;AAEhD,UAAI,CAAC,KAAK,UAAU,KAAK,KAAK,eAAe,eAAe,KAAK,SAAS;AACxE,eAAO;AAAA,MACT;AACA,WAAK,gBAAgB,YAAY,IAAI;AACrC,YAAM,KAAK,cAAc;AACzB,YAAM,KAAK,eAAe,YAAY,QAAQ;AAM9C,UAAI,KAAK,oBAAoB;AAC3B,aAAK,qBAAqB;AAC1B,eAAO;AAAA,MACT;AACA,WAAK,eAAe,YAAY,IAAI;AACpC,aAAO;AAAA,IACT;AAAA;AAAA,EACM,gBAAgB;AAAA;AAGpB,YAAM,QAAQ,KAAK,YAAY;AAK/B,YAAM,cAAc,UAAU,KAAK,IAAI;AACvC,UAAI,UAAU,KAAK,SAAS,KAAK,cAAc,UAAa,gBAAgB,KAAK,WAAW;AAC1F;AAAA,MACF;AACA,WAAK,QAAQ;AACb,WAAK,YAAY;AAEjB,UAAI,KAAK,WAAW;AAClB,aAAK,UAAU,QAAQ;AACvB,aAAK,YAAY;AAAA,MACnB;AAEA,YAAM,YAAY,KAAK,YAAY,MAAM,eAAe,iBAAiB,KAAK,MAAM,IAAI;AACxF,UAAI,CAAC,OAAO,WAAW,YAAY,IAAI,GAAG;AACxC,kBAAU,SAAS,CAAC;AAAA,MACtB;AACA,gBAAU,KAAK,MAAM;AAAA,IACvB;AAAA;AAAA,EACM,eAAe,YAAY,UAAU;AAAA;AACzC,YAAM,aAAa,CAAC;AACpB,YAAM,OAAO,WAAW,IAAI;AAC5B,YAAM,SAAS,SAAS,QAAQ,YAAY;AAC5C,YAAM,gBAAgB,SAAS,QAAQ,mBAAmB;AAC1D,YAAM,MAAM,KAAK,UAAU,UAAU,aAAa,YAAY,QAAQ,EAAE,OAAO,aAAa,gBAAgB,MAAM;AAClH,UAAI,UAAU;AACZ,cAAM,IAAI,KAAK;AAAA,MACjB,OAAO;AACL,YAAI,KAAK;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAQA,UAAI,IAAI,aAAa,MAAM,WAAW;AACpC,YAAI,UAAU,QAAQ;AAAA,MACxB;AAAA,IACF;AAAA;AAAA,EACA,YAAY;AACV,WAAO,CAAC,KAAK,YAAY,CAAC,KAAK;AAAA,EACjC;AAAA,EACA,WAAW;AACT,WAAO,KAAK,gBAAgB,CAAC,KAAK,eAAe,KAAK,UAAU;AAAA,EAClE;AAAA,EACA,SAAS,QAAQ;AAEf,UAAM,mBAAmB,CAAC,CAAC,SAAS,cAAc,sBAAsB;AACxE,QAAI,oBAAoB,CAAC,KAAK,SAAS,GAAG;AACxC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,SAAS;AAChB,aAAO;AAAA,IACT,WAAW,eAAe,aAAa,GAAG;AACxC,aAAO;AAAA,IACT;AACA,WAAO,cAAc,QAAQ,OAAO,UAAU,KAAK,WAAW,KAAK,YAAY;AAAA,EACjF;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,CAAC,KAAK,SAAS,OAAO;AAC3C,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EACA,UAAU;AACR,QAAI,CAAC,KAAK,eAAe,CAAC,KAAK,WAAW;AACxC,aAAO,OAAO,4BAA4B;AAC1C;AAAA,IACF;AAEA,SAAK,UAAU,cAAc,MAAM,KAAK,UAAU,IAAI,CAAC;AAAA,EACzD;AAAA,EACA,OAAO,QAAQ;AACb,QAAI,CAAC,KAAK,eAAe,CAAC,KAAK,WAAW;AACxC,aAAO,OAAO,4BAA4B;AAC1C;AAAA,IACF;AACA,UAAM,QAAQ,aAAa,OAAO,QAAQ,KAAK,SAAS,KAAK,SAAS;AACtE,UAAM,YAAY,QAAQ,KAAK;AAC/B,SAAK,UAAU,aAAa,KAAK,UAAU,IAAI,YAAY,SAAS;AAAA,EACtE;AAAA,EACA,MAAM,QAAQ;AACZ,QAAI,CAAC,KAAK,eAAe,CAAC,KAAK,WAAW;AACxC,aAAO,OAAO,4BAA4B;AAC1C;AAAA,IACF;AACA,UAAM,SAAS,KAAK;AACpB,UAAMA,aAAY,KAAK;AACvB,UAAM,QAAQ,aAAa,OAAO,QAAQ,QAAQA,UAAS;AAC3D,UAAM,QAAQ,KAAK;AACnB,UAAM,YAAY,QAAQ;AAC1B,UAAM,WAAW,OAAO;AACxB,UAAM,IAAI,QAAQ;AAClB,UAAM,sBAAsB,YAAY,MAAM,WAAW,OAAO,OAAO,SAAS;AAChF,UAAM,qBAAqB,YAAY,MAAM,WAAW,QAAQ,OAAO,SAAS,CAAC;AACjF,UAAM,iBAAiB,SAASA,aAAY,sBAAsB,qBAAqBA,aAAY,qBAAqB;AACxH,QAAI,aAAa,CAAC,UAAU;AAC5B,QAAI,UAAU,CAAC,gBAAgB;AAC7B,mBAAa;AAAA,IACf;AACA,SAAK,YAAY,OAAO;AAExB,QAAI,eAAe,iBAAiB,OAAQ;AAM5C,UAAM,oBAAoB,YAAY,IAAI,OAAO;AASjD,oBAAgB,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,mBAAmB,MAAM,CAAC,EAAE,CAAC,KAAK;AACvH,UAAM,SAAS,KAAK,UAAU,CAAC,iBAAiB;AAChD,SAAK,UAAU,OAAO,gCAAgC,EAAE,SAAS,MAAM,KAAK,eAAe,YAAY,OAAO,GAAG;AAAA,MAC/G,iBAAiB;AAAA,IACnB,CAAC,EAAE,YAAY,SAAS,IAAI,GAAG,KAAK,UAAU,IAAI,eAAe,cAAc,GAAG;AAAA,EACpF;AAAA,EACA,gBAAgB,YAAY,MAAM;AAChC,WAAO,CAAC,KAAK,aAAa,gDAAgD;AAc1E,QAAI,WAAW,SAAS,GAAG;AACzB,WAAK,GAAG,aAAa,eAAe,MAAM;AAAA,IAC5C;AAGA,SAAK,GAAG,UAAU,IAAI,SAAS;AAS/B,SAAK,GAAG,aAAa,YAAY,GAAG;AACpC,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,UAAU,IAAI,aAAa;AAAA,IAC7C;AAEA,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,UAAU,IAAI,iBAAiB;AAY9C,WAAK,UAAU,aAAa,eAAe,MAAM;AAAA,IACnD;AACA,SAAK,QAAQ,MAAM;AACnB,SAAK,cAAc;AACnB,QAAI,YAAY;AACd,WAAK,YAAY,KAAK;AAAA,IACxB,OAAO;AACL,WAAK,aAAa,KAAK;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,eAAe,QAAQ,MAAM;AAC3B,QAAI;AAKJ,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,QAAQ,QAAQ;AAAA,IACvB;AACA,QAAI,QAAQ;AAQV,UAAI,WAAW,SAAS,GAAG;AACzB,aAAK,GAAG,gBAAgB,aAAa;AAAA,MACvC;AAEA,WAAK,WAAW,KAAK;AAMrB,YAAM,eAAe,KAAK,SAAS,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,UAAU;AAC5G,UAAI,gBAAgB,KAAK,IAAI;AAC3B,aAAK,GAAG,MAAM;AAAA,MAChB;AAEA,eAAS,iBAAiB,SAAS,KAAK,aAAa,IAAI;AAAA,IAC3D,OAAO;AACL,WAAK,GAAG,gBAAgB,aAAa;AAErC,WAAK,GAAG,UAAU,OAAO,SAAS;AAKlC,WAAK,GAAG,gBAAgB,UAAU;AAClC,UAAI,KAAK,WAAW;AAClB,aAAK,UAAU,UAAU,OAAO,iBAAiB;AAMjD,aAAK,UAAU,gBAAgB,aAAa;AAAA,MAC9C;AACA,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,UAAU,OAAO,aAAa;AAAA,MAChD;AACA,UAAI,KAAK,WAAW;AAClB,aAAK,UAAU,KAAK;AAAA,MACtB;AAEA,WAAK,YAAY,KAAK;AAAA,QACpB;AAAA,MACF,CAAC;AAED,eAAS,oBAAoB,SAAS,KAAK,aAAa,IAAI;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,cAAc;AACZ,UAAM,WAAW,KAAK,UAAU;AAChC,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,OAAO,YAAY,KAAK,YAAY;AAAA,IACnD;AAQA,QAAI,CAAC,UAAU;AAQb,UAAI,KAAK,aAAa;AACpB,aAAK,qBAAqB;AAAA,MAC5B;AAKA,WAAK,eAAe,OAAO,OAAO;AAAA,IACpC;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,WAAW,IAAI;AAM5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,WAAW,sBAAsB,IAAI,OAAO,KAAK;AAAA,MACjD,MAAM;AAAA,MACN,cAAc,oBAAoB,YAAY,KAAK;AAAA,MACnD,OAAO;AAAA,QACL,CAAC,IAAI,GAAG;AAAA,QACR,CAAC,aAAa,IAAI,EAAE,GAAG;AAAA,QACvB,gBAAgB,CAAC;AAAA,QACjB,CAAC,aAAa,IAAI,EAAE,GAAG;AAAA,QACvB,qBAAqB;AAAA,QACrB,mBAAmB,YAAY,kBAAkB,EAAE;AAAA,MACrD;AAAA,IACF,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN,KAAK,CAAAC,QAAM,KAAK,cAAcA;AAAA,IAChC,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,CAAC,GAAG,EAAE,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL,KAAK,CAAAA,QAAM,KAAK,aAAaA;AAAA,MAC7B,OAAO;AAAA,MACP,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,MAAM;AAAA,IACR,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,QAAQ,CAAC,aAAa;AAAA,MACtB,YAAY,CAAC,iBAAiB;AAAA,MAC9B,QAAQ,CAAC,aAAa;AAAA,MACtB,gBAAgB,CAAC,qBAAqB;AAAA,IACxC;AAAA,EACF;AACF;AACA,IAAM,eAAe,CAAC,QAAQ,QAAQD,eAAc;AAClD,SAAO,KAAK,IAAI,GAAG,WAAWA,aAAY,CAAC,SAAS,MAAM;AAC5D;AACA,IAAM,gBAAgB,CAAC,KAAK,MAAMA,YAAW,iBAAiB;AAC5D,MAAIA,YAAW;AACb,WAAO,QAAQ,IAAI,aAAa;AAAA,EAClC,OAAO;AACL,WAAO,QAAQ;AAAA,EACjB;AACF;AACA,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,oBAAoB;AAC1B,KAAK,QAAQ;AAAA,EACX,KAAK;AAAA,EACL,IAAI;AACN;AAGA,IAAM,mBAAmB,CAAM,SAAQ;AACrC,QAAM,SAAS,MAAM,eAAe,IAAI,IAAI;AAC5C,SAAO,CAAC,EAAE,WAAW,MAAM,OAAO,SAAS;AAC7C;AACA,IAAM,mBAAmB;AACzB,IAAM,kBAAkB;AACxB,IAAM,aAAa,MAAM;AAAA,EACvB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,sBAAsB,CAAC;AAC5B,SAAK,UAAU;AAIf,SAAK,WAAW;AAIhB,SAAK,WAAW;AAIhB,SAAK,OAAO;AACZ,SAAK,UAAU,MAAY;AACzB,aAAO,eAAe,OAAO,KAAK,IAAI;AAAA,IACxC;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,sBAAsB,sBAAsB,KAAK,EAAE;AAAA,EAC1D;AAAA,EACA,mBAAmB;AACjB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACM,oBAAoB;AAAA;AACxB,WAAK,UAAU,MAAM,iBAAiB,KAAK,IAAI;AAAA,IACjD;AAAA;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,WAAW,OAAO,IAAI,YAAY,SAAS,QAAQ,cAAc,SAAS;AAChF,UAAM,SAAS,KAAK,YAAY,CAAC,KAAK;AACtC,UAAM,QAAQ;AAAA,MACZ,MAAM,KAAK;AAAA,IACb;AACA,UAAM,YAAY,oBAAoB,YAAY,KAAK;AACvD,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,SAAS,KAAK;AAAA,MACd,iBAAiB,WAAW,SAAS;AAAA,MACrC,eAAe,SAAS,SAAS;AAAA,MACjC,OAAO,mBAAmB,OAAO;AAAA,QAC/B,CAAC,IAAI,GAAG;AAAA,QACR,QAAQ;AAAA;AAAA,QAER,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,cAAc,YAAY,eAAe,KAAK,EAAE;AAAA,QAChD,oBAAoB,YAAY,sBAAsB,KAAK,EAAE;AAAA,QAC7D,mBAAmB;AAAA,QACnB,iBAAiB;AAAA,MACnB,CAAC;AAAA,IACH,GAAG,EAAE,UAAU,OAAO,OAAO;AAAA,MAC3B,KAAK;AAAA,IACP,GAAG,OAAO;AAAA,MACR;AAAA,MACA,OAAO;AAAA,MACP,MAAM;AAAA,MACN,cAAc;AAAA,IAChB,CAAC,GAAG,EAAE,QAAQ;AAAA,MACZ,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,GAAG,EAAE,YAAY;AAAA,MACf,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN;AAAA,MACA,MAAM;AAAA,MACN,eAAe;AAAA,IACjB,CAAC,CAAC,CAAC,GAAG,SAAS,QAAQ,EAAE,qBAAqB;AAAA,MAC5C,KAAK;AAAA,MACL,MAAM;AAAA,IACR,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;AACA,WAAW,QAAQ;AAAA,EACjB,KAAK;AAAA,EACL,IAAI;AACN;AACA,IAAM,gBAAgB;AACtB,IAAM,aAAa,MAAM;AAAA,EACvB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,UAAU;AAOf,SAAK,WAAW;AAChB,SAAK,UAAU,MAAM;AACnB,aAAO,eAAe,OAAO,KAAK,IAAI;AAAA,IACxC;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACM,oBAAoB;AAAA;AACxB,WAAK,UAAU,MAAM,iBAAiB,KAAK,IAAI;AAAA,IACjD;AAAA;AAAA,EACA,SAAS;AACP,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,SAAS,KAAK,YAAY,CAAC,KAAK;AACtC,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,SAAS,KAAK;AAAA,MACd,eAAe,SAAS,SAAS;AAAA,MACjC,OAAO;AAAA,QACL,CAAC,IAAI,GAAG;AAAA,QACR,sBAAsB;AAAA,MACxB;AAAA,IACF,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,CAAC;AAAA,EACJ;AACF;AACA,WAAW,QAAQ;", "names": ["isEndSide", "el"]}