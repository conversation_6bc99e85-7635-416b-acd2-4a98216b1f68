{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/config-AaTyISnm.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { o as printIonError } from './index-B_U9CtaY.js';\n\n/**\n * Does a simple sanitization of all elements\n * in an untrusted string\n */\nconst sanitizeDOMString = untrustedString => {\n  try {\n    if (untrustedString instanceof IonicSafeString) {\n      return untrustedString.value;\n    }\n    if (!isSanitizerEnabled() || typeof untrustedString !== 'string' || untrustedString === '') {\n      return untrustedString;\n    }\n    /**\n     * onload is fired when appending to a document\n     * fragment in Chrome. If a string\n     * contains onload then we should not\n     * attempt to add this to the fragment.\n     */\n    if (untrustedString.includes('onload=')) {\n      return '';\n    }\n    /**\n     * Create a document fragment\n     * separate from the main DOM,\n     * create a div to do our work in\n     */\n    const documentFragment = document.createDocumentFragment();\n    const workingDiv = document.createElement('div');\n    documentFragment.appendChild(workingDiv);\n    workingDiv.innerHTML = untrustedString;\n    /**\n     * Remove any elements\n     * that are blocked\n     */\n    blockedTags.forEach(blockedTag => {\n      const getElementsToRemove = documentFragment.querySelectorAll(blockedTag);\n      for (let elementIndex = getElementsToRemove.length - 1; elementIndex >= 0; elementIndex--) {\n        const element = getElementsToRemove[elementIndex];\n        if (element.parentNode) {\n          element.parentNode.removeChild(element);\n        } else {\n          documentFragment.removeChild(element);\n        }\n        /**\n         * We still need to sanitize\n         * the children of this element\n         * as they are left behind\n         */\n        const childElements = getElementChildren(element);\n        /* eslint-disable-next-line */\n        for (let childIndex = 0; childIndex < childElements.length; childIndex++) {\n          sanitizeElement(childElements[childIndex]);\n        }\n      }\n    });\n    /**\n     * Go through remaining elements and remove\n     * non-allowed attribs\n     */\n    // IE does not support .children on document fragments, only .childNodes\n    const dfChildren = getElementChildren(documentFragment);\n    /* eslint-disable-next-line */\n    for (let childIndex = 0; childIndex < dfChildren.length; childIndex++) {\n      sanitizeElement(dfChildren[childIndex]);\n    }\n    // Append document fragment to div\n    const fragmentDiv = document.createElement('div');\n    fragmentDiv.appendChild(documentFragment);\n    // First child is always the div we did our work in\n    const getInnerDiv = fragmentDiv.querySelector('div');\n    return getInnerDiv !== null ? getInnerDiv.innerHTML : fragmentDiv.innerHTML;\n  } catch (err) {\n    printIonError('sanitizeDOMString', err);\n    return '';\n  }\n};\n/**\n * Clean up current element based on allowed attributes\n * and then recursively dig down into any child elements to\n * clean those up as well\n */\n// TODO(FW-2832): type (using Element triggers other type errors as well)\nconst sanitizeElement = element => {\n  // IE uses childNodes, so ignore nodes that are not elements\n  if (element.nodeType && element.nodeType !== 1) {\n    return;\n  }\n  /**\n   * If attributes is not a NamedNodeMap\n   * then we should remove the element entirely.\n   * This helps avoid DOM Clobbering attacks where\n   * attributes is overridden.\n   */\n  if (typeof NamedNodeMap !== 'undefined' && !(element.attributes instanceof NamedNodeMap)) {\n    element.remove();\n    return;\n  }\n  for (let i = element.attributes.length - 1; i >= 0; i--) {\n    const attribute = element.attributes.item(i);\n    const attributeName = attribute.name;\n    // remove non-allowed attribs\n    if (!allowedAttributes.includes(attributeName.toLowerCase())) {\n      element.removeAttribute(attributeName);\n      continue;\n    }\n    // clean up any allowed attribs\n    // that attempt to do any JS funny-business\n    const attributeValue = attribute.value;\n    /**\n     * We also need to check the property value\n     * as javascript: can allow special characters\n     * such as &Tab; and still be valid (i.e. java&Tab;script)\n     */\n    const propertyValue = element[attributeName];\n    /* eslint-disable */\n    if (attributeValue != null && attributeValue.toLowerCase().includes('javascript:') || propertyValue != null && propertyValue.toLowerCase().includes('javascript:')) {\n      element.removeAttribute(attributeName);\n    }\n    /* eslint-enable */\n  }\n  /**\n   * Sanitize any nested children\n   */\n  const childElements = getElementChildren(element);\n  /* eslint-disable-next-line */\n  for (let i = 0; i < childElements.length; i++) {\n    sanitizeElement(childElements[i]);\n  }\n};\n/**\n * IE doesn't always support .children\n * so we revert to .childNodes instead\n */\n// TODO(FW-2832): type\nconst getElementChildren = el => {\n  return el.children != null ? el.children : el.childNodes;\n};\nconst isSanitizerEnabled = () => {\n  var _a;\n  const win = window;\n  const config = (_a = win === null || win === void 0 ? void 0 : win.Ionic) === null || _a === void 0 ? void 0 : _a.config;\n  if (config) {\n    if (config.get) {\n      return config.get('sanitizerEnabled', true);\n    } else {\n      return config.sanitizerEnabled === true || config.sanitizerEnabled === undefined;\n    }\n  }\n  return true;\n};\nconst allowedAttributes = ['class', 'id', 'href', 'src', 'name', 'slot'];\nconst blockedTags = ['script', 'style', 'iframe', 'meta', 'link', 'object', 'embed'];\nclass IonicSafeString {\n  constructor(value) {\n    this.value = value;\n  }\n}\nconst setupConfig = config => {\n  const win = window;\n  const Ionic = win.Ionic;\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n  if (Ionic && Ionic.config && Ionic.config.constructor.name !== 'Object') {\n    return;\n  }\n  win.Ionic = win.Ionic || {};\n  win.Ionic.config = Object.assign(Object.assign({}, win.Ionic.config), config);\n  return win.Ionic.config;\n};\nconst getMode = () => {\n  var _a;\n  const win = window;\n  const config = (_a = win === null || win === void 0 ? void 0 : win.Ionic) === null || _a === void 0 ? void 0 : _a.config;\n  if (config) {\n    if (config.mode) {\n      return config.mode;\n    } else {\n      return config.get('mode');\n    }\n  }\n  return 'md';\n};\nconst ENABLE_HTML_CONTENT_DEFAULT = false;\nexport { ENABLE_HTML_CONTENT_DEFAULT as E, IonicSafeString as I, sanitizeDOMString as a, getMode as g, setupConfig as s };"], "mappings": ";;;;;AASA,IAAM,oBAAoB,qBAAmB;AAC3C,MAAI;AACF,QAAI,2BAA2B,iBAAiB;AAC9C,aAAO,gBAAgB;AAAA,IACzB;AACA,QAAI,CAAC,mBAAmB,KAAK,OAAO,oBAAoB,YAAY,oBAAoB,IAAI;AAC1F,aAAO;AAAA,IACT;AAOA,QAAI,gBAAgB,SAAS,SAAS,GAAG;AACvC,aAAO;AAAA,IACT;AAMA,UAAM,mBAAmB,SAAS,uBAAuB;AACzD,UAAM,aAAa,SAAS,cAAc,KAAK;AAC/C,qBAAiB,YAAY,UAAU;AACvC,eAAW,YAAY;AAKvB,gBAAY,QAAQ,gBAAc;AAChC,YAAM,sBAAsB,iBAAiB,iBAAiB,UAAU;AACxE,eAAS,eAAe,oBAAoB,SAAS,GAAG,gBAAgB,GAAG,gBAAgB;AACzF,cAAM,UAAU,oBAAoB,YAAY;AAChD,YAAI,QAAQ,YAAY;AACtB,kBAAQ,WAAW,YAAY,OAAO;AAAA,QACxC,OAAO;AACL,2BAAiB,YAAY,OAAO;AAAA,QACtC;AAMA,cAAM,gBAAgB,mBAAmB,OAAO;AAEhD,iBAAS,aAAa,GAAG,aAAa,cAAc,QAAQ,cAAc;AACxE,0BAAgB,cAAc,UAAU,CAAC;AAAA,QAC3C;AAAA,MACF;AAAA,IACF,CAAC;AAMD,UAAM,aAAa,mBAAmB,gBAAgB;AAEtD,aAAS,aAAa,GAAG,aAAa,WAAW,QAAQ,cAAc;AACrE,sBAAgB,WAAW,UAAU,CAAC;AAAA,IACxC;AAEA,UAAM,cAAc,SAAS,cAAc,KAAK;AAChD,gBAAY,YAAY,gBAAgB;AAExC,UAAM,cAAc,YAAY,cAAc,KAAK;AACnD,WAAO,gBAAgB,OAAO,YAAY,YAAY,YAAY;AAAA,EACpE,SAAS,KAAK;AACZ,kBAAc,qBAAqB,GAAG;AACtC,WAAO;AAAA,EACT;AACF;AAOA,IAAM,kBAAkB,aAAW;AAEjC,MAAI,QAAQ,YAAY,QAAQ,aAAa,GAAG;AAC9C;AAAA,EACF;AAOA,MAAI,OAAO,iBAAiB,eAAe,EAAE,QAAQ,sBAAsB,eAAe;AACxF,YAAQ,OAAO;AACf;AAAA,EACF;AACA,WAAS,IAAI,QAAQ,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AACvD,UAAM,YAAY,QAAQ,WAAW,KAAK,CAAC;AAC3C,UAAM,gBAAgB,UAAU;AAEhC,QAAI,CAAC,kBAAkB,SAAS,cAAc,YAAY,CAAC,GAAG;AAC5D,cAAQ,gBAAgB,aAAa;AACrC;AAAA,IACF;AAGA,UAAM,iBAAiB,UAAU;AAMjC,UAAM,gBAAgB,QAAQ,aAAa;AAE3C,QAAI,kBAAkB,QAAQ,eAAe,YAAY,EAAE,SAAS,aAAa,KAAK,iBAAiB,QAAQ,cAAc,YAAY,EAAE,SAAS,aAAa,GAAG;AAClK,cAAQ,gBAAgB,aAAa;AAAA,IACvC;AAAA,EAEF;AAIA,QAAM,gBAAgB,mBAAmB,OAAO;AAEhD,WAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,oBAAgB,cAAc,CAAC,CAAC;AAAA,EAClC;AACF;AAMA,IAAM,qBAAqB,QAAM;AAC/B,SAAO,GAAG,YAAY,OAAO,GAAG,WAAW,GAAG;AAChD;AACA,IAAM,qBAAqB,MAAM;AAC/B,MAAI;AACJ,QAAM,MAAM;AACZ,QAAM,UAAU,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG;AAClH,MAAI,QAAQ;AACV,QAAI,OAAO,KAAK;AACd,aAAO,OAAO,IAAI,oBAAoB,IAAI;AAAA,IAC5C,OAAO;AACL,aAAO,OAAO,qBAAqB,QAAQ,OAAO,qBAAqB;AAAA,IACzE;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,oBAAoB,CAAC,SAAS,MAAM,QAAQ,OAAO,QAAQ,MAAM;AACvE,IAAM,cAAc,CAAC,UAAU,SAAS,UAAU,QAAQ,QAAQ,UAAU,OAAO;AACnF,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,OAAO;AACjB,SAAK,QAAQ;AAAA,EACf;AACF;AACA,IAAM,cAAc,YAAU;AAC5B,QAAM,MAAM;AACZ,QAAM,QAAQ,IAAI;AAElB,MAAI,SAAS,MAAM,UAAU,MAAM,OAAO,YAAY,SAAS,UAAU;AACvE;AAAA,EACF;AACA,MAAI,QAAQ,IAAI,SAAS,CAAC;AAC1B,MAAI,MAAM,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,MAAM,MAAM,GAAG,MAAM;AAC5E,SAAO,IAAI,MAAM;AACnB;AAcA,IAAM,8BAA8B;", "names": []}