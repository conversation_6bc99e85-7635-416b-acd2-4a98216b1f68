import { Routes } from '@angular/router';
import { TabsPage } from './tabs.page';

export const routes: Routes = [
  {
    path: 'tabs',
    component: TabsPage,
    children: [
      {
        path: 'home',
        loadComponent: () =>
          import('../pages/home/<USER>').then((m) => m.HomePage),
      },
      {
        path: 'shop',
        loadComponent: () =>
          import('../pages/shop/shop.page').then((m) => m.ShopPage),
      },
      {
        path: 'beauty',
        loadComponent: () =>
          import('../pages/beauty/beauty.page').then((m) => m.BeautyPage),
      },
      {
        path: 'wishlist',
        loadComponent: () =>
          import('../pages/wishlist/wishlist.page').then((m) => m.WishlistPage),
      },
      {
        path: 'profile',
        loadComponent: () =>
          import('../pages/profile/profile.page').then((m) => m.ProfilePage),
      },
      {
        path: '',
        redirectTo: '/tabs/home',
        pathMatch: 'full',
      },
    ],
  },
  {
    path: '',
    redirectTo: '/tabs/home',
    pathMatch: 'full',
  },
];
