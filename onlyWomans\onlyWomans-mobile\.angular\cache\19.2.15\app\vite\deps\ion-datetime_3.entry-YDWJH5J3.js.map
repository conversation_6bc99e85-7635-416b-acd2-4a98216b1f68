{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-datetime_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { o as printIonError, m as printIonWarning, r as registerInstance, d as createEvent, e as getIonMode, w as writeTask, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { startFocusVisible } from './focus-visible-BmVRXR1y.js';\nimport { r as raf, g as getElementRoot, a as renderHiddenInput, e as clamp } from './helpers-1O4D2b7y.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { c as createColorClasses, g as getClassMap } from './theme-DiVJyqlX.js';\nimport { l as chevronDown, o as caretUpSharp, p as chevronForward, q as caretDownSharp, c as chevronBack } from './index-BLV6ykCk.js';\nimport { i as isBefore, a as isAfter, g as getPreviousMonth, b as getNextMonth, c as isSameDay, d as getDay, e as generateDayAriaLabel, v as validateParts, f as getPartsFromCalendarDay, h as getNextYear, j as getPreviousYear, k as getEndOfWeek, l as getStartOfWeek, m as getPreviousDay, n as getNextDay, o as getPreviousWeek, p as getNextWeek, q as parseMinParts, r as parseMaxParts, s as parseDate, w as warnIfValueOutOfBounds, t as parseAmPm, u as clampDate, x as convertToArrayOfNumbers, y as convertDataToISO, z as getToday, A as getClosestValidDate, B as generateMonths, C as getNumDaysInMonth, D as getCombinedDateColumnData, E as getMonthColumnData, F as getDayColumnData, G as getYearColumnData, H as isMonthFirstLocale, I as getTimeColumnsData, J as isLocaleDayPeriodRTL, K as calculateHourFromAMPM, L as getDaysOfWeek, M as getMonthAndYear, N as getDaysOfMonth, O as getHourCycle, P as getLocalizedTime, Q as getLocalizedDateTime, R as formatValue } from './data-GIsHsYIB.js';\nimport { c as createLockController } from './lock-controller-B-hirT0v.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, i as isCancel, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod, s as safeCall } from './overlays-8Y2rA-ps.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport { a as hapticSelectionChanged, h as hapticSelectionEnd, b as hapticSelectionStart } from './haptic-DzAMWJuk.js';\nimport './index-ZjP4CjeZ.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './framework-delegate-DxcnWic_.js';\nimport './gesture-controller-BTEOs1at.js';\nimport './capacitor-CFERIeaU.js';\nconst isYearDisabled = (refYear, minParts, maxParts) => {\n  if (minParts && minParts.year > refYear) {\n    return true;\n  }\n  if (maxParts && maxParts.year < refYear) {\n    return true;\n  }\n  return false;\n};\n/**\n * Returns true if a given day should\n * not be interactive according to its value,\n * or the max/min dates.\n */\nconst isDayDisabled = (refParts, minParts, maxParts, dayValues) => {\n  /**\n   * If this is a filler date (i.e. padding)\n   * then the date is disabled.\n   */\n  if (refParts.day === null) {\n    return true;\n  }\n  /**\n   * If user passed in a list of acceptable day values\n   * check to make sure that the date we are looking\n   * at is in this array.\n   */\n  if (dayValues !== undefined && !dayValues.includes(refParts.day)) {\n    return true;\n  }\n  /**\n   * Given a min date, perform the following\n   * checks. If any of them are true, then the\n   * day should be disabled:\n   * 1. Is the current year < the min allowed year?\n   * 2. Is the current year === min allowed year,\n   * but the current month < the min allowed month?\n   * 3. Is the current year === min allowed year, the\n   * current month === min allow month, but the current\n   * day < the min allowed day?\n   */\n  if (minParts && isBefore(refParts, minParts)) {\n    return true;\n  }\n  /**\n   * Given a max date, perform the following\n   * checks. If any of them are true, then the\n   * day should be disabled:\n   * 1. Is the current year > the max allowed year?\n   * 2. Is the current year === max allowed year,\n   * but the current month > the max allowed month?\n   * 3. Is the current year === max allowed year, the\n   * current month === max allow month, but the current\n   * day > the max allowed day?\n   */\n  if (maxParts && isAfter(refParts, maxParts)) {\n    return true;\n  }\n  /**\n   * If none of these checks\n   * passed then the date should\n   * be interactive.\n   */\n  return false;\n};\n/**\n * Given a locale, a date, the selected date(s), and today's date,\n * generate the state for a given calendar day button.\n */\nconst getCalendarDayState = (locale, refParts, activeParts, todayParts, minParts, maxParts, dayValues) => {\n  /**\n   * activeParts signals what day(s) are currently selected in the datetime.\n   * If multiple=\"true\", this will be an array, but the logic in this util\n   * is the same whether we have one selected day or many because we're only\n   * calculating the state for one button. So, we treat a single activeParts value\n   * the same as an array of length one.\n   */\n  const activePartsArray = Array.isArray(activeParts) ? activeParts : [activeParts];\n  /**\n   * The day button is active if it is selected, or in other words, if refParts\n   * matches at least one selected date.\n   */\n  const isActive = activePartsArray.find(parts => isSameDay(refParts, parts)) !== undefined;\n  const isToday = isSameDay(refParts, todayParts);\n  const disabled = isDayDisabled(refParts, minParts, maxParts, dayValues);\n  /**\n   * Note that we always return one object regardless of whether activeParts\n   * was an array, since we pare down to one value for isActive.\n   */\n  return {\n    disabled,\n    isActive,\n    isToday,\n    ariaSelected: isActive ? 'true' : null,\n    ariaLabel: generateDayAriaLabel(locale, isToday, refParts),\n    text: refParts.day != null ? getDay(locale, refParts) : null\n  };\n};\n/**\n * Returns `true` if the month is disabled given the\n * current date value and min/max date constraints.\n */\nconst isMonthDisabled = (refParts, {\n  minParts,\n  maxParts\n}) => {\n  // If the year is disabled then the month is disabled.\n  if (isYearDisabled(refParts.year, minParts, maxParts)) {\n    return true;\n  }\n  // If the date value is before the min date, then the month is disabled.\n  // If the date value is after the max date, then the month is disabled.\n  if (minParts && isBefore(refParts, minParts) || maxParts && isAfter(refParts, maxParts)) {\n    return true;\n  }\n  return false;\n};\n/**\n * Given a working date, an optional minimum date range,\n * and an optional maximum date range; determine if the\n * previous navigation button is disabled.\n */\nconst isPrevMonthDisabled = (refParts, minParts, maxParts) => {\n  const prevMonth = Object.assign(Object.assign({}, getPreviousMonth(refParts)), {\n    day: null\n  });\n  return isMonthDisabled(prevMonth, {\n    minParts,\n    maxParts\n  });\n};\n/**\n * Given a working date and a maximum date range,\n * determine if the next navigation button is disabled.\n */\nconst isNextMonthDisabled = (refParts, maxParts) => {\n  const nextMonth = Object.assign(Object.assign({}, getNextMonth(refParts)), {\n    day: null\n  });\n  return isMonthDisabled(nextMonth, {\n    maxParts\n  });\n};\n/**\n * Given the value of the highlightedDates property\n * and an ISO string, return the styles to use for\n * that date, or undefined if none are found.\n */\nconst getHighlightStyles = (highlightedDates, dateIsoString, el) => {\n  if (Array.isArray(highlightedDates)) {\n    const dateStringWithoutTime = dateIsoString.split('T')[0];\n    const matchingHighlight = highlightedDates.find(hd => hd.date === dateStringWithoutTime);\n    if (matchingHighlight) {\n      return {\n        textColor: matchingHighlight.textColor,\n        backgroundColor: matchingHighlight.backgroundColor\n      };\n    }\n  } else {\n    /**\n     * Wrap in a try-catch to prevent exceptions in the user's function\n     * from interrupting the calendar's rendering.\n     */\n    try {\n      return highlightedDates(dateIsoString);\n    } catch (e) {\n      printIonError('[ion-datetime] - Exception thrown from provided `highlightedDates` callback. Please check your function and try again.', el, e);\n    }\n  }\n  return undefined;\n};\n\n/**\n * If a time zone is provided in the format options, the rendered text could\n * differ from what was selected in the Datetime, which could cause\n * confusion.\n */\nconst warnIfTimeZoneProvided = (el, formatOptions) => {\n  var _a, _b, _c, _d;\n  if (((_a = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) === null || _a === void 0 ? void 0 : _a.timeZone) || ((_b = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) === null || _b === void 0 ? void 0 : _b.timeZoneName) || ((_c = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) === null || _c === void 0 ? void 0 : _c.timeZone) || ((_d = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) === null || _d === void 0 ? void 0 : _d.timeZoneName)) {\n    printIonWarning('[ion-datetime] - \"timeZone\" and \"timeZoneName\" are not supported in \"formatOptions\".', el);\n  }\n};\nconst checkForPresentationFormatMismatch = (el, presentation, formatOptions) => {\n  // formatOptions is not required\n  if (!formatOptions) return;\n  // If formatOptions is provided, the date and/or time objects are required, depending on the presentation\n  switch (presentation) {\n    case 'date':\n    case 'month-year':\n    case 'month':\n    case 'year':\n      if (formatOptions.date === undefined) {\n        printIonWarning(`[ion-datetime] - The '${presentation}' presentation requires a date object in formatOptions.`, el);\n      }\n      break;\n    case 'time':\n      if (formatOptions.time === undefined) {\n        printIonWarning(`[ion-datetime] - The 'time' presentation requires a time object in formatOptions.`, el);\n      }\n      break;\n    case 'date-time':\n    case 'time-date':\n      if (formatOptions.date === undefined && formatOptions.time === undefined) {\n        printIonWarning(`[ion-datetime] - The '${presentation}' presentation requires either a date or time object (or both) in formatOptions.`, el);\n      }\n      break;\n  }\n};\nconst datetimeIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-button{--background:transparent}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:not(.calendar-day-adjacent-day):focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons .calendar-month-year-toggle{color:var(--ion-color-base)}.calendar-month-year{min-width:0}.calendar-month-year-toggle{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;position:relative;border:0;outline:none;background:transparent;cursor:pointer;z-index:1}.calendar-month-year-toggle::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:opacity 15ms linear, background-color 15ms linear;transition:opacity 15ms linear, background-color 15ms linear;z-index:-1}.calendar-month-year-toggle.ion-focused::after{background:currentColor}.calendar-month-year-toggle:disabled{opacity:0.3;pointer-events:none}.calendar-month-year-toggle ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0}.calendar-month-year-toggle #toggle-wrapper{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center}ion-picker{--highlight-background:var(--wheel-highlight-background);--highlight-border-radius:var(--wheel-highlight-border-radius);--fade-background-rgb:var(--wheel-fade-background-rgb)}:host{--background:var(--ion-color-light, #f4f5f8);--background-rgb:var(--ion-color-light-rgb, 244, 245, 248);--title-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}:host(.datetime-presentation-date-time:not(.datetime-prefer-wheel)),:host(.datetime-presentation-time-date:not(.datetime-prefer-wheel)),:host(.datetime-presentation-date:not(.datetime-prefer-wheel)){min-height:350px}:host .datetime-header{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:16px;padding-bottom:16px;border-bottom:0.55px solid var(--ion-color-step-200, var(--ion-background-color-step-200, #cccccc));font-size:min(0.875rem, 22.4px)}:host .datetime-header .datetime-title{color:var(--title-color)}:host .datetime-header .datetime-selected-date{margin-top:10px}.calendar-month-year-toggle{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;min-height:44px;font-size:min(1rem, 25.6px);font-weight:600}.calendar-month-year-toggle.ion-focused::after{opacity:0.15}.calendar-month-year-toggle #toggle-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:10px;margin-bottom:10px}:host .calendar-action-buttons .calendar-month-year-toggle ion-icon,:host .calendar-action-buttons ion-buttons ion-button{color:var(--ion-color-base)}:host .calendar-action-buttons ion-buttons{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:0}:host .calendar-action-buttons ion-buttons ion-button{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host .calendar-days-of-week{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;color:var(--ion-color-step-300, var(--ion-text-color-step-700, #b3b3b3));font-size:min(0.75rem, 19.2px);font-weight:600;line-height:24px;text-transform:uppercase}@supports (border-radius: mod(1px, 1px)){.calendar-days-of-week .day-of-week{width:clamp(20px, calc(mod(min(1rem, 24px), 24px) * 10), 100%);height:24px;overflow:hidden}.calendar-day{border-radius:max(8px, mod(min(1rem, 24px), 24px) * 10)}}@supports ((border-radius: mod(1px, 1px)) and (background: -webkit-named-image(apple-pay-logo-black)) and (not (contain-intrinsic-size: none))) or (not (border-radius: mod(1px, 1px))){.calendar-days-of-week .day-of-week{width:auto;height:auto;overflow:initial}.calendar-day{border-radius:32px}}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;-ms-flex-align:center;align-items:center;height:calc(100% - 16px)}:host .calendar-day-wrapper{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;height:0;min-height:1rem}:host .calendar-day{width:40px;min-width:40px;height:40px;font-size:min(1.25rem, 32px)}.calendar-day.calendar-day-active{background:rgba(var(--ion-color-base-rgb), 0.2);font-size:min(1.375rem, 35.2px)}:host .calendar-day.calendar-day-today{color:var(--ion-color-base)}:host .calendar-day.calendar-day-active,:host .calendar-day.calendar-day-adjacent-day.calendar-day-active{color:var(--ion-color-base);font-weight:600}:host .calendar-day.calendar-day-today.calendar-day-active{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host .calendar-day.calendar-day-adjacent-day{color:var(--ion-color-step-300, var(--ion-text-color-step-700, #b3b3b3))}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:16px;font-size:min(1rem, 25.6px)}:host .datetime-time .time-header{font-weight:600}:host .datetime-buttons{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;border-top:0.55px solid var(--ion-color-step-200, var(--ion-background-color-step-200, #cccccc))}:host .datetime-buttons ::slotted(ion-buttons),:host .datetime-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between}:host .datetime-action-buttons{width:100%}\";\nconst datetimeMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-button{--background:transparent}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:not(.calendar-day-adjacent-day):focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons .calendar-month-year-toggle{color:var(--ion-color-base)}.calendar-month-year{min-width:0}.calendar-month-year-toggle{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;position:relative;border:0;outline:none;background:transparent;cursor:pointer;z-index:1}.calendar-month-year-toggle::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:opacity 15ms linear, background-color 15ms linear;transition:opacity 15ms linear, background-color 15ms linear;z-index:-1}.calendar-month-year-toggle.ion-focused::after{background:currentColor}.calendar-month-year-toggle:disabled{opacity:0.3;pointer-events:none}.calendar-month-year-toggle ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0}.calendar-month-year-toggle #toggle-wrapper{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center}ion-picker{--highlight-background:var(--wheel-highlight-background);--highlight-border-radius:var(--wheel-highlight-border-radius);--fade-background-rgb:var(--wheel-fade-background-rgb)}:host{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #ffffff));--title-color:var(--ion-color-contrast)}:host .datetime-header{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:20px;padding-bottom:20px;background:var(--ion-color-base);color:var(--title-color)}:host .datetime-header .datetime-title{font-size:0.75rem;text-transform:uppercase}:host .datetime-header .datetime-selected-date{margin-top:30px;font-size:2.125rem}:host .calendar-action-buttons ion-button{--color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}.calendar-month-year-toggle{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:12px;padding-bottom:12px;min-height:48px;background:transparent;color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959));z-index:1}.calendar-month-year-toggle.ion-focused::after{opacity:0.04}.calendar-month-year-toggle ion-ripple-effect{color:currentColor}@media (any-hover: hover){.calendar-month-year-toggle.ion-activatable:not(.ion-focused):hover::after{background:currentColor;opacity:0.04}}:host .calendar-days-of-week{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:0px;padding-bottom:0px;color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray));font-size:0.875rem;line-height:36px}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:4px;padding-bottom:4px;grid-template-rows:repeat(6, 1fr)}:host .calendar-day{width:42px;min-width:42px;height:42px;font-size:0.875rem}:host .calendar-day.calendar-day-today{border:1px solid var(--ion-color-base);color:var(--ion-color-base)}:host .calendar-day.calendar-day-active,:host .calendar-day.calendar-day-adjacent-day.calendar-day-active{color:var(--ion-color-contrast)}.calendar-day.calendar-day-active,.calendar-day.calendar-day-active:focus{border:1px solid var(--ion-color-base);background:var(--ion-color-base)}:host .calendar-day.calendar-day-adjacent-day{color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:8px}:host .time-header{color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}:host(.datetime-presentation-month) .datetime-year,:host(.datetime-presentation-year) .datetime-year,:host(.datetime-presentation-month-year) .datetime-year{margin-top:20px;margin-bottom:20px}:host .datetime-buttons{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:10px;padding-bottom:10px;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:end;justify-content:flex-end}\";\nconst Datetime = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionCancel = createEvent(this, \"ionCancel\", 7);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.ionRender = createEvent(this, \"ionRender\", 7);\n    this.inputId = `ion-dt-${datetimeIds++}`;\n    this.prevPresentation = null;\n    this.showMonthAndYear = false;\n    this.activeParts = [];\n    this.workingParts = {\n      month: 5,\n      day: 28,\n      year: 2021,\n      hour: 13,\n      minute: 52,\n      ampm: 'pm',\n      isAdjacentDay: false\n    };\n    this.isTimePopoverOpen = false;\n    /**\n     * The color to use from your application's color palette.\n     * Default options are: `\"primary\"`, `\"secondary\"`, `\"tertiary\"`, `\"success\"`, `\"warning\"`, `\"danger\"`, `\"light\"`, `\"medium\"`, and `\"dark\"`.\n     * For more information on colors, see [theming](/docs/theming/basics).\n     */\n    this.color = 'primary';\n    /**\n     * The name of the control, which is submitted with the form data.\n     */\n    this.name = this.inputId;\n    /**\n     * If `true`, the user cannot interact with the datetime.\n     */\n    this.disabled = false;\n    /**\n     * If `true`, the datetime appears normal but the selected date cannot be changed.\n     */\n    this.readonly = false;\n    /**\n     * If `true`, the datetime calendar displays a six-week (42-day) layout,\n     * including days from the previous and next months to fill the grid.\n     * These adjacent days are selectable unless disabled.\n     */\n    this.showAdjacentDays = false;\n    /**\n     * Which values you want to select. `\"date\"` will show\n     * a calendar picker to select the month, day, and year. `\"time\"`\n     * will show a time picker to select the hour, minute, and (optionally)\n     * AM/PM. `\"date-time\"` will show the date picker first and time picker second.\n     * `\"time-date\"` will show the time picker first and date picker second.\n     */\n    this.presentation = 'date-time';\n    /**\n     * The text to display on the picker's cancel button.\n     */\n    this.cancelText = 'Cancel';\n    /**\n     * The text to display on the picker's \"Done\" button.\n     */\n    this.doneText = 'Done';\n    /**\n     * The text to display on the picker's \"Clear\" button.\n     */\n    this.clearText = 'Clear';\n    /**\n     * The locale to use for `ion-datetime`. This\n     * impacts month and day name formatting.\n     * The `\"default\"` value refers to the default\n     * locale set by your device.\n     */\n    this.locale = 'default';\n    /**\n     * The first day of the week to use for `ion-datetime`. The\n     * default value is `0` and represents Sunday.\n     */\n    this.firstDayOfWeek = 0;\n    /**\n     * If `true`, multiple dates can be selected at once. Only\n     * applies to `presentation=\"date\"` and `preferWheel=\"false\"`.\n     */\n    this.multiple = false;\n    /**\n     * If `true`, a header will be shown above the calendar\n     * picker. This will include both the slotted title, and\n     * the selected date.\n     */\n    this.showDefaultTitle = false;\n    /**\n     * If `true`, the default \"Cancel\" and \"OK\" buttons\n     * will be rendered at the bottom of the `ion-datetime`\n     * component. Developers can also use the `button` slot\n     * if they want to customize these buttons. If custom\n     * buttons are set in the `button` slot then the\n     * default buttons will not be rendered.\n     */\n    this.showDefaultButtons = false;\n    /**\n     * If `true`, a \"Clear\" button will be rendered alongside\n     * the default \"Cancel\" and \"OK\" buttons at the bottom of the `ion-datetime`\n     * component. Developers can also use the `button` slot\n     * if they want to customize these buttons. If custom\n     * buttons are set in the `button` slot then the\n     * default buttons will not be rendered.\n     */\n    this.showClearButton = false;\n    /**\n     * If `true`, the default \"Time\" label will be rendered\n     * for the time selector of the `ion-datetime` component.\n     * Developers can also use the `time-label` slot\n     * if they want to customize this label. If a custom\n     * label is set in the `time-label` slot then the\n     * default label will not be rendered.\n     */\n    this.showDefaultTimeLabel = true;\n    /**\n     * If `cover`, the `ion-datetime` will expand to cover the full width of its container.\n     * If `fixed`, the `ion-datetime` will have a fixed width.\n     */\n    this.size = 'fixed';\n    /**\n     * If `true`, a wheel picker will be rendered instead of a calendar grid\n     * where possible. If `false`, a calendar grid will be rendered instead of\n     * a wheel picker where possible.\n     *\n     * A wheel picker can be rendered instead of a grid when `presentation` is\n     * one of the following values: `\"date\"`, `\"date-time\"`, or `\"time-date\"`.\n     *\n     * A wheel picker will always be rendered regardless of\n     * the `preferWheel` value when `presentation` is one of the following values:\n     * `\"time\"`, `\"month\"`, `\"month-year\"`, or `\"year\"`.\n     */\n    this.preferWheel = false;\n    this.warnIfIncorrectValueUsage = () => {\n      const {\n        multiple,\n        value\n      } = this;\n      if (!multiple && Array.isArray(value)) {\n        /**\n         * We do some processing on the `value` array so\n         * that it looks more like an array when logged to\n         * the console.\n         * Example given ['a', 'b']\n         * Default toString() behavior: a,b\n         * Custom behavior: ['a', 'b']\n         */\n        printIonWarning(`[ion-datetime] - An array of values was passed, but multiple is \"false\". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the \"value\" property when multiple=\"false\".\n\n  Value Passed: [${value.map(v => `'${v}'`).join(', ')}]\n`, this.el);\n      }\n    };\n    this.setValue = value => {\n      this.value = value;\n      this.ionChange.emit({\n        value\n      });\n    };\n    /**\n     * Returns the DatetimePart interface\n     * to use when rendering an initial set of\n     * data. This should be used when rendering an\n     * interface in an environment where the `value`\n     * may not be set. This function works\n     * by returning the first selected date and then\n     * falling back to defaultParts if no active date\n     * is selected.\n     */\n    this.getActivePartsWithFallback = () => {\n      var _a;\n      const {\n        defaultParts\n      } = this;\n      return (_a = this.getActivePart()) !== null && _a !== void 0 ? _a : defaultParts;\n    };\n    this.getActivePart = () => {\n      const {\n        activeParts\n      } = this;\n      return Array.isArray(activeParts) ? activeParts[0] : activeParts;\n    };\n    this.closeParentOverlay = role => {\n      const popoverOrModal = this.el.closest('ion-modal, ion-popover');\n      if (popoverOrModal) {\n        popoverOrModal.dismiss(undefined, role);\n      }\n    };\n    this.setWorkingParts = parts => {\n      this.workingParts = Object.assign({}, parts);\n    };\n    this.setActiveParts = (parts, removeDate = false) => {\n      /** if the datetime component is in readonly mode,\n       * allow browsing of the calendar without changing\n       * the set value\n       */\n      if (this.readonly) {\n        return;\n      }\n      const {\n        multiple,\n        minParts,\n        maxParts,\n        activeParts\n      } = this;\n      /**\n       * When setting the active parts, it is possible\n       * to set invalid data. For example,\n       * when updating January 31 to February,\n       * February 31 does not exist. As a result\n       * we need to validate the active parts and\n       * ensure that we are only setting valid dates.\n       * Additionally, we need to update the working parts\n       * too in the event that the validated parts are different.\n       */\n      const validatedParts = validateParts(parts, minParts, maxParts);\n      this.setWorkingParts(validatedParts);\n      if (multiple) {\n        const activePartsArray = Array.isArray(activeParts) ? activeParts : [activeParts];\n        if (removeDate) {\n          this.activeParts = activePartsArray.filter(p => !isSameDay(p, validatedParts));\n        } else {\n          this.activeParts = [...activePartsArray, validatedParts];\n        }\n      } else {\n        this.activeParts = Object.assign({}, validatedParts);\n      }\n      const hasSlottedButtons = this.el.querySelector('[slot=\"buttons\"]') !== null;\n      if (hasSlottedButtons || this.showDefaultButtons) {\n        return;\n      }\n      this.confirm();\n    };\n    this.initializeKeyboardListeners = () => {\n      const calendarBodyRef = this.calendarBodyRef;\n      if (!calendarBodyRef) {\n        return;\n      }\n      const root = this.el.shadowRoot;\n      /**\n       * Get a reference to the month\n       * element we are currently viewing.\n       */\n      const currentMonth = calendarBodyRef.querySelector('.calendar-month:nth-of-type(2)');\n      /**\n       * When focusing the calendar body, we want to pass focus\n       * to the working day, but other days should\n       * only be accessible using the arrow keys. Pressing\n       * Tab should jump between bodies of selectable content.\n       */\n      const checkCalendarBodyFocus = ev => {\n        var _a;\n        const record = ev[0];\n        /**\n         * If calendar body was already focused\n         * when this fired or if the calendar body\n         * if not currently focused, we should not re-focus\n         * the inner day.\n         */\n        if (((_a = record.oldValue) === null || _a === void 0 ? void 0 : _a.includes('ion-focused')) || !calendarBodyRef.classList.contains('ion-focused')) {\n          return;\n        }\n        this.focusWorkingDay(currentMonth);\n      };\n      const mo = new MutationObserver(checkCalendarBodyFocus);\n      mo.observe(calendarBodyRef, {\n        attributeFilter: ['class'],\n        attributeOldValue: true\n      });\n      this.destroyKeyboardMO = () => {\n        mo === null || mo === void 0 ? void 0 : mo.disconnect();\n      };\n      /**\n       * We must use keydown not keyup as we want\n       * to prevent scrolling when using the arrow keys.\n       */\n      calendarBodyRef.addEventListener('keydown', ev => {\n        const activeElement = root.activeElement;\n        if (!activeElement || !activeElement.classList.contains('calendar-day')) {\n          return;\n        }\n        const parts = getPartsFromCalendarDay(activeElement);\n        let partsToFocus;\n        switch (ev.key) {\n          case 'ArrowDown':\n            ev.preventDefault();\n            partsToFocus = getNextWeek(parts);\n            break;\n          case 'ArrowUp':\n            ev.preventDefault();\n            partsToFocus = getPreviousWeek(parts);\n            break;\n          case 'ArrowRight':\n            ev.preventDefault();\n            partsToFocus = getNextDay(parts);\n            break;\n          case 'ArrowLeft':\n            ev.preventDefault();\n            partsToFocus = getPreviousDay(parts);\n            break;\n          case 'Home':\n            ev.preventDefault();\n            partsToFocus = getStartOfWeek(parts);\n            break;\n          case 'End':\n            ev.preventDefault();\n            partsToFocus = getEndOfWeek(parts);\n            break;\n          case 'PageUp':\n            ev.preventDefault();\n            partsToFocus = ev.shiftKey ? getPreviousYear(parts) : getPreviousMonth(parts);\n            break;\n          case 'PageDown':\n            ev.preventDefault();\n            partsToFocus = ev.shiftKey ? getNextYear(parts) : getNextMonth(parts);\n            break;\n          /**\n           * Do not preventDefault here\n           * as we do not want to override other\n           * browser defaults such as pressing Enter/Space\n           * to select a day.\n           */\n          default:\n            return;\n        }\n        /**\n         * If the day we want to move focus to is\n         * disabled, do not do anything.\n         */\n        if (isDayDisabled(partsToFocus, this.minParts, this.maxParts)) {\n          return;\n        }\n        this.setWorkingParts(Object.assign(Object.assign({}, this.workingParts), partsToFocus));\n        /**\n         * Give view a chance to re-render\n         * then move focus to the new working day\n         */\n        requestAnimationFrame(() => this.focusWorkingDay(currentMonth));\n      });\n    };\n    this.focusWorkingDay = currentMonth => {\n      /**\n       * Get the number of offset days so\n       * we know how much to offset our next selector by\n       * to grab the correct calendar-day element.\n       */\n      const {\n        day,\n        month,\n        year\n      } = this.workingParts;\n      const firstOfMonth = new Date(`${month}/1/${year}`).getDay();\n      const offset = firstOfMonth >= this.firstDayOfWeek ? firstOfMonth - this.firstDayOfWeek : 7 - (this.firstDayOfWeek - firstOfMonth);\n      if (day === null) {\n        return;\n      }\n      /**\n       * Get the calendar day element\n       * and focus it.\n       */\n      const dayEl = currentMonth.querySelector(`.calendar-day-wrapper:nth-of-type(${offset + day}) .calendar-day`);\n      if (dayEl) {\n        dayEl.focus();\n      }\n    };\n    this.processMinParts = () => {\n      const {\n        min,\n        defaultParts\n      } = this;\n      if (min === undefined) {\n        this.minParts = undefined;\n        return;\n      }\n      this.minParts = parseMinParts(min, defaultParts);\n    };\n    this.processMaxParts = () => {\n      const {\n        max,\n        defaultParts\n      } = this;\n      if (max === undefined) {\n        this.maxParts = undefined;\n        return;\n      }\n      this.maxParts = parseMaxParts(max, defaultParts);\n    };\n    this.initializeCalendarListener = () => {\n      const calendarBodyRef = this.calendarBodyRef;\n      if (!calendarBodyRef) {\n        return;\n      }\n      /**\n       * For performance reasons, we only render 3\n       * months at a time: The current month, the previous\n       * month, and the next month. We have a scroll listener\n       * on the calendar body to append/prepend new months.\n       *\n       * We can do this because Stencil is smart enough to not\n       * re-create the .calendar-month containers, but rather\n       * update the content within those containers.\n       *\n       * As an added bonus, WebKit has some troubles with\n       * scroll-snap-stop: always, so not rendering all of\n       * the months in a row allows us to mostly sidestep\n       * that issue.\n       */\n      const months = calendarBodyRef.querySelectorAll('.calendar-month');\n      const startMonth = months[0];\n      const workingMonth = months[1];\n      const endMonth = months[2];\n      const mode = getIonMode(this);\n      const needsiOSRubberBandFix = mode === 'ios' && typeof navigator !== 'undefined' && navigator.maxTouchPoints > 1;\n      /**\n       * Before setting up the scroll listener,\n       * scroll the middle month into view.\n       * scrollIntoView() will scroll entire page\n       * if element is not in viewport. Use scrollLeft instead.\n       */\n      writeTask(() => {\n        calendarBodyRef.scrollLeft = startMonth.clientWidth * (isRTL(this.el) ? -1 : 1);\n        const getChangedMonth = parts => {\n          const box = calendarBodyRef.getBoundingClientRect();\n          /**\n           * If the current scroll position is all the way to the left\n           * then we have scrolled to the previous month.\n           * Otherwise, assume that we have scrolled to the next\n           * month. We have a tolerance of 2px to account for\n           * sub pixel rendering.\n           *\n           * Check below the next line ensures that we did not\n           * swipe and abort (i.e. we swiped but we are still on the current month).\n           */\n          const condition = isRTL(this.el) ? calendarBodyRef.scrollLeft >= -2 : calendarBodyRef.scrollLeft <= 2;\n          const month = condition ? startMonth : endMonth;\n          /**\n           * The edge of the month must be lined up with\n           * the edge of the calendar body in order for\n           * the component to update. Otherwise, it\n           * may be the case that the user has paused their\n           * swipe or the browser has not finished snapping yet.\n           * Rather than check if the x values are equal,\n           * we give it a tolerance of 2px to account for\n           * sub pixel rendering.\n           */\n          const monthBox = month.getBoundingClientRect();\n          if (Math.abs(monthBox.x - box.x) > 2) return;\n          /**\n           * If we're force-rendering a month, assume we've\n           * scrolled to that and return it.\n           *\n           * If forceRenderDate is ever used in a context where the\n           * forced month is not immediately auto-scrolled to, this\n           * should be updated to also check whether `month` has the\n           * same month and year as the forced date.\n           */\n          const {\n            forceRenderDate\n          } = this;\n          if (forceRenderDate !== undefined) {\n            return {\n              month: forceRenderDate.month,\n              year: forceRenderDate.year,\n              day: forceRenderDate.day\n            };\n          }\n          /**\n           * From here, we can determine if the start\n           * month or the end month was scrolled into view.\n           * If no month was changed, then we can return from\n           * the scroll callback early.\n           */\n          if (month === startMonth) {\n            return getPreviousMonth(parts);\n          } else if (month === endMonth) {\n            return getNextMonth(parts);\n          } else {\n            return;\n          }\n        };\n        const updateActiveMonth = () => {\n          if (needsiOSRubberBandFix) {\n            calendarBodyRef.style.removeProperty('pointer-events');\n            appliediOSRubberBandFix = false;\n          }\n          /**\n           * If the month did not change\n           * then we can return early.\n           */\n          const newDate = getChangedMonth(this.workingParts);\n          if (!newDate) return;\n          const {\n            month,\n            day,\n            year\n          } = newDate;\n          if (isMonthDisabled({\n            month,\n            year,\n            day: null\n          }, {\n            minParts: Object.assign(Object.assign({}, this.minParts), {\n              day: null\n            }),\n            maxParts: Object.assign(Object.assign({}, this.maxParts), {\n              day: null\n            })\n          })) {\n            return;\n          }\n          /**\n           * Prevent scrolling for other browsers\n           * to give the DOM time to update and the container\n           * time to properly snap.\n           */\n          calendarBodyRef.style.setProperty('overflow', 'hidden');\n          /**\n           * Use a writeTask here to ensure\n           * that the state is updated and the\n           * correct month is scrolled into view\n           * in the same frame. This is not\n           * typically a problem on newer devices\n           * but older/slower device may have a flicker\n           * if we did not do this.\n           */\n          writeTask(() => {\n            this.setWorkingParts(Object.assign(Object.assign({}, this.workingParts), {\n              month,\n              day: day,\n              year\n            }));\n            calendarBodyRef.scrollLeft = workingMonth.clientWidth * (isRTL(this.el) ? -1 : 1);\n            calendarBodyRef.style.removeProperty('overflow');\n            if (this.resolveForceDateScrolling) {\n              this.resolveForceDateScrolling();\n            }\n          });\n        };\n        /**\n         * When the container finishes scrolling we\n         * need to update the DOM with the selected month.\n         */\n        let scrollTimeout;\n        /**\n         * We do not want to attempt to set pointer-events\n         * multiple times within a single swipe gesture as\n         * that adds unnecessary work to the main thread.\n         */\n        let appliediOSRubberBandFix = false;\n        const scrollCallback = () => {\n          if (scrollTimeout) {\n            clearTimeout(scrollTimeout);\n          }\n          /**\n           * On iOS it is possible to quickly rubber band\n           * the scroll area before the scroll timeout has fired.\n           * This results in users reaching the end of the scrollable\n           * container before the DOM has updated.\n           * By setting `pointer-events: none` we can ensure that\n           * subsequent swipes do not happen while the container\n           * is snapping.\n           */\n          if (!appliediOSRubberBandFix && needsiOSRubberBandFix) {\n            calendarBodyRef.style.setProperty('pointer-events', 'none');\n            appliediOSRubberBandFix = true;\n          }\n          // Wait ~3 frames\n          scrollTimeout = setTimeout(updateActiveMonth, 50);\n        };\n        calendarBodyRef.addEventListener('scroll', scrollCallback);\n        this.destroyCalendarListener = () => {\n          calendarBodyRef.removeEventListener('scroll', scrollCallback);\n        };\n      });\n    };\n    /**\n     * Clean up all listeners except for the overlay\n     * listener. This is so that we can re-create the listeners\n     * if the datetime has been hidden/presented by a modal or popover.\n     */\n    this.destroyInteractionListeners = () => {\n      const {\n        destroyCalendarListener,\n        destroyKeyboardMO\n      } = this;\n      if (destroyCalendarListener !== undefined) {\n        destroyCalendarListener();\n      }\n      if (destroyKeyboardMO !== undefined) {\n        destroyKeyboardMO();\n      }\n    };\n    this.processValue = value => {\n      const hasValue = value !== null && value !== undefined && value !== '' && (!Array.isArray(value) || value.length > 0);\n      const valueToProcess = hasValue ? parseDate(value) : this.defaultParts;\n      const {\n        minParts,\n        maxParts,\n        workingParts,\n        el\n      } = this;\n      this.warnIfIncorrectValueUsage();\n      /**\n       * Return early if the value wasn't parsed correctly, such as\n       * if an improperly formatted date string was provided.\n       */\n      if (!valueToProcess) {\n        return;\n      }\n      /**\n       * Datetime should only warn of out of bounds values\n       * if set by the user. If the `value` is undefined,\n       * we will default to today's date which may be out\n       * of bounds. In this case, the warning makes it look\n       * like the developer did something wrong which is\n       * not true.\n       */\n      if (hasValue) {\n        warnIfValueOutOfBounds(valueToProcess, minParts, maxParts);\n      }\n      /**\n       * If there are multiple values, clamp to the last one.\n       * This is because the last value is the one that the user\n       * has most recently interacted with.\n       */\n      const singleValue = Array.isArray(valueToProcess) ? valueToProcess[valueToProcess.length - 1] : valueToProcess;\n      const targetValue = clampDate(singleValue, minParts, maxParts);\n      const {\n        month,\n        day,\n        year,\n        hour,\n        minute\n      } = targetValue;\n      const ampm = parseAmPm(hour);\n      /**\n       * Since `activeParts` indicates a value that been explicitly selected\n       * either by the user or the app, only update `activeParts` if the\n       * `value` property is set.\n       */\n      if (hasValue) {\n        if (Array.isArray(valueToProcess)) {\n          this.activeParts = [...valueToProcess];\n        } else {\n          this.activeParts = {\n            month,\n            day,\n            year,\n            hour,\n            minute,\n            ampm\n          };\n        }\n      } else {\n        /**\n         * Reset the active parts if the value is not set.\n         * This will clear the selected calendar day when\n         * performing a clear action or using the reset() method.\n         */\n        this.activeParts = [];\n      }\n      const didChangeMonth = month !== undefined && month !== workingParts.month || year !== undefined && year !== workingParts.year;\n      const bodyIsVisible = el.classList.contains('datetime-ready');\n      const {\n        isGridStyle,\n        showMonthAndYear\n      } = this;\n      if (isGridStyle && didChangeMonth && bodyIsVisible && !showMonthAndYear) {\n        /**\n         * Only animate if:\n         * 1. We're using grid style (wheel style pickers should just jump to new value)\n         * 2. The month and/or year actually changed, and both are defined (otherwise there's nothing to animate to)\n         * 3. The calendar body is visible (prevents animation when in collapsed datetime-button, for example)\n         * 4. The month/year picker is not open (since you wouldn't see the animation anyway)\n         */\n        this.animateToDate(targetValue);\n      } else {\n        this.setWorkingParts({\n          month,\n          day,\n          year,\n          hour,\n          minute,\n          ampm\n        });\n      }\n    };\n    this.animateToDate = async targetValue => {\n      const {\n        workingParts\n      } = this;\n      /**\n       * Tell other render functions that we need to force the\n       * target month to appear in place of the actual next/prev month.\n       * Because this is a State variable, a rerender will be triggered\n       * automatically, updating the rendered months.\n       */\n      this.forceRenderDate = targetValue;\n      /**\n       * Flag that we've started scrolling to the forced date.\n       * The resolve function will be called by the datetime's\n       * scroll listener when it's done updating everything.\n       * This is a replacement for making prev/nextMonth async,\n       * since the logic we're waiting on is in a listener.\n       */\n      const forceDateScrollingPromise = new Promise(resolve => {\n        this.resolveForceDateScrolling = resolve;\n      });\n      /**\n       * Animate smoothly to the forced month. This will also update\n       * workingParts and correct the surrounding months for us.\n       */\n      const targetMonthIsBefore = isBefore(targetValue, workingParts);\n      targetMonthIsBefore ? this.prevMonth() : this.nextMonth();\n      await forceDateScrollingPromise;\n      this.resolveForceDateScrolling = undefined;\n      this.forceRenderDate = undefined;\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.hasValue = () => {\n      return this.value != null;\n    };\n    this.nextMonth = () => {\n      const calendarBodyRef = this.calendarBodyRef;\n      if (!calendarBodyRef) {\n        return;\n      }\n      const nextMonth = calendarBodyRef.querySelector('.calendar-month:last-of-type');\n      if (!nextMonth) {\n        return;\n      }\n      const left = nextMonth.offsetWidth * 2;\n      calendarBodyRef.scrollTo({\n        top: 0,\n        left: left * (isRTL(this.el) ? -1 : 1),\n        behavior: 'smooth'\n      });\n    };\n    this.prevMonth = () => {\n      const calendarBodyRef = this.calendarBodyRef;\n      if (!calendarBodyRef) {\n        return;\n      }\n      const prevMonth = calendarBodyRef.querySelector('.calendar-month:first-of-type');\n      if (!prevMonth) {\n        return;\n      }\n      calendarBodyRef.scrollTo({\n        top: 0,\n        left: 0,\n        behavior: 'smooth'\n      });\n    };\n    this.toggleMonthAndYearView = () => {\n      this.showMonthAndYear = !this.showMonthAndYear;\n    };\n  }\n  formatOptionsChanged() {\n    const {\n      el,\n      formatOptions,\n      presentation\n    } = this;\n    checkForPresentationFormatMismatch(el, presentation, formatOptions);\n    warnIfTimeZoneProvided(el, formatOptions);\n  }\n  disabledChanged() {\n    this.emitStyle();\n  }\n  minChanged() {\n    this.processMinParts();\n  }\n  maxChanged() {\n    this.processMaxParts();\n  }\n  presentationChanged() {\n    const {\n      el,\n      formatOptions,\n      presentation\n    } = this;\n    checkForPresentationFormatMismatch(el, presentation, formatOptions);\n  }\n  get isGridStyle() {\n    const {\n      presentation,\n      preferWheel\n    } = this;\n    const hasDatePresentation = presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n    return hasDatePresentation && !preferWheel;\n  }\n  yearValuesChanged() {\n    this.parsedYearValues = convertToArrayOfNumbers(this.yearValues);\n  }\n  monthValuesChanged() {\n    this.parsedMonthValues = convertToArrayOfNumbers(this.monthValues);\n  }\n  dayValuesChanged() {\n    this.parsedDayValues = convertToArrayOfNumbers(this.dayValues);\n  }\n  hourValuesChanged() {\n    this.parsedHourValues = convertToArrayOfNumbers(this.hourValues);\n  }\n  minuteValuesChanged() {\n    this.parsedMinuteValues = convertToArrayOfNumbers(this.minuteValues);\n  }\n  /**\n   * Update the datetime value when the value changes\n   */\n  async valueChanged() {\n    const {\n      value\n    } = this;\n    if (this.hasValue()) {\n      this.processValue(value);\n    }\n    this.emitStyle();\n    this.ionValueChange.emit({\n      value\n    });\n  }\n  /**\n   * Confirms the selected datetime value, updates the\n   * `value` property, and optionally closes the popover\n   * or modal that the datetime was presented in.\n   */\n  async confirm(closeOverlay = false) {\n    const {\n      isCalendarPicker,\n      activeParts,\n      preferWheel,\n      workingParts\n    } = this;\n    /**\n     * We only update the value if the presentation is not a calendar picker.\n     */\n    if (activeParts !== undefined || !isCalendarPicker) {\n      const activePartsIsArray = Array.isArray(activeParts);\n      if (activePartsIsArray && activeParts.length === 0) {\n        if (preferWheel) {\n          /**\n           * If the datetime is using a wheel picker, but the\n           * active parts are empty, then the user has confirmed the\n           * initial value (working parts) presented to them.\n           */\n          this.setValue(convertDataToISO(workingParts));\n        } else {\n          this.setValue(undefined);\n        }\n      } else {\n        this.setValue(convertDataToISO(activeParts));\n      }\n    }\n    if (closeOverlay) {\n      this.closeParentOverlay(CONFIRM_ROLE);\n    }\n  }\n  /**\n   * Resets the internal state of the datetime but does not update the value.\n   * Passing a valid ISO-8601 string will reset the state of the component to the provided date.\n   * If no value is provided, the internal state will be reset to the clamped value of the min, max and today.\n   */\n  async reset(startDate) {\n    this.processValue(startDate);\n  }\n  /**\n   * Emits the ionCancel event and\n   * optionally closes the popover\n   * or modal that the datetime was\n   * presented in.\n   */\n  async cancel(closeOverlay = false) {\n    this.ionCancel.emit();\n    if (closeOverlay) {\n      this.closeParentOverlay(CANCEL_ROLE);\n    }\n  }\n  get isCalendarPicker() {\n    const {\n      presentation\n    } = this;\n    return presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n  }\n  connectedCallback() {\n    this.clearFocusVisible = startFocusVisible(this.el).destroy;\n  }\n  disconnectedCallback() {\n    if (this.clearFocusVisible) {\n      this.clearFocusVisible();\n      this.clearFocusVisible = undefined;\n    }\n  }\n  initializeListeners() {\n    this.initializeCalendarListener();\n    this.initializeKeyboardListeners();\n  }\n  componentDidLoad() {\n    const {\n      el,\n      intersectionTrackerRef\n    } = this;\n    /**\n     * If a scrollable element is hidden using `display: none`,\n     * it will not have a scroll height meaning we cannot scroll elements\n     * into view. As a result, we will need to wait for the datetime to become\n     * visible if used inside of a modal or a popover otherwise the scrollable\n     * areas will not have the correct values snapped into place.\n     */\n    const visibleCallback = entries => {\n      const ev = entries[0];\n      if (!ev.isIntersecting) {\n        return;\n      }\n      this.initializeListeners();\n      /**\n       * TODO FW-2793: Datetime needs a frame to ensure that it\n       * can properly scroll contents into view. As a result\n       * we hide the scrollable content until after that frame\n       * so users do not see the content quickly shifting. The downside\n       * is that the content will pop into view a frame after. Maybe there\n       * is a better way to handle this?\n       */\n      writeTask(() => {\n        this.el.classList.add('datetime-ready');\n      });\n    };\n    const visibleIO = new IntersectionObserver(visibleCallback, {\n      threshold: 0.01,\n      root: el\n    });\n    /**\n     * Use raf to avoid a race condition between the component loading and\n     * its display animation starting (such as when shown in a modal). This\n     * could cause the datetime to start at a visibility of 0, erroneously\n     * triggering the `hiddenIO` observer below.\n     */\n    raf(() => visibleIO === null || visibleIO === void 0 ? void 0 : visibleIO.observe(intersectionTrackerRef));\n    /**\n     * We need to clean up listeners when the datetime is hidden\n     * in a popover/modal so that we can properly scroll containers\n     * back into view if they are re-presented. When the datetime is hidden\n     * the scroll areas have scroll widths/heights of 0px, so any snapping\n     * we did originally has been lost.\n     */\n    const hiddenCallback = entries => {\n      const ev = entries[0];\n      if (ev.isIntersecting) {\n        return;\n      }\n      this.destroyInteractionListeners();\n      /**\n       * When datetime is hidden, we need to make sure that\n       * the month/year picker is closed. Otherwise,\n       * it will be open when the datetime re-appears\n       * and the scroll area of the calendar grid will be 0.\n       * As a result, the wrong month will be shown.\n       */\n      this.showMonthAndYear = false;\n      writeTask(() => {\n        this.el.classList.remove('datetime-ready');\n      });\n    };\n    const hiddenIO = new IntersectionObserver(hiddenCallback, {\n      threshold: 0,\n      root: el\n    });\n    raf(() => hiddenIO === null || hiddenIO === void 0 ? void 0 : hiddenIO.observe(intersectionTrackerRef));\n    /**\n     * Datetime uses Ionic components that emit\n     * ionFocus and ionBlur. These events are\n     * composed meaning they will cross\n     * the shadow dom boundary. We need to\n     * stop propagation on these events otherwise\n     * developers will see 2 ionFocus or 2 ionBlur\n     * events at a time.\n     */\n    const root = getElementRoot(this.el);\n    root.addEventListener('ionFocus', ev => ev.stopPropagation());\n    root.addEventListener('ionBlur', ev => ev.stopPropagation());\n  }\n  /**\n   * When the presentation is changed, all calendar content is recreated,\n   * so we need to re-init behavior with the new elements.\n   */\n  componentDidRender() {\n    const {\n      presentation,\n      prevPresentation,\n      calendarBodyRef,\n      minParts,\n      preferWheel,\n      forceRenderDate\n    } = this;\n    /**\n     * TODO(FW-2165)\n     * Remove this when https://bugs.webkit.org/show_bug.cgi?id=235960 is fixed.\n     * When using `min`, we add `scroll-snap-align: none`\n     * to the disabled month so that users cannot scroll to it.\n     * This triggers a bug in WebKit where the scroll position is reset.\n     * Since the month change logic is handled by a scroll listener,\n     * this causes the month to change leading to `scroll-snap-align`\n     * changing again, thus changing the scroll position again and causing\n     * an infinite loop.\n     * This issue only applies to the calendar grid, so we can disable\n     * it if the calendar grid is not being used.\n     */\n    const hasCalendarGrid = !preferWheel && ['date-time', 'time-date', 'date'].includes(presentation);\n    if (minParts !== undefined && hasCalendarGrid && calendarBodyRef) {\n      const workingMonth = calendarBodyRef.querySelector('.calendar-month:nth-of-type(1)');\n      /**\n       * We need to make sure the datetime is not in the process\n       * of scrolling to a new datetime value if the value\n       * is updated programmatically.\n       * Otherwise, the datetime will appear to not scroll at all because\n       * we are resetting the scroll position to the center of the view.\n       * Prior to the datetime's value being updated programmatically,\n       * the calendarBodyRef is scrolled such that the middle month is centered\n       * in the view. The below code updates the scroll position so the middle\n       * month is also centered in the view. Since the scroll position did not change,\n       * the scroll callback in this file does not fire,\n       * and the resolveForceDateScrolling promise never resolves.\n       */\n      if (workingMonth && forceRenderDate === undefined) {\n        calendarBodyRef.scrollLeft = workingMonth.clientWidth * (isRTL(this.el) ? -1 : 1);\n      }\n    }\n    if (prevPresentation === null) {\n      this.prevPresentation = presentation;\n      return;\n    }\n    if (presentation === prevPresentation) {\n      return;\n    }\n    this.prevPresentation = presentation;\n    this.destroyInteractionListeners();\n    this.initializeListeners();\n    /**\n     * The month/year picker from the date interface\n     * should be closed as it is not available in non-date\n     * interfaces.\n     */\n    this.showMonthAndYear = false;\n    raf(() => {\n      this.ionRender.emit();\n    });\n  }\n  componentWillLoad() {\n    const {\n      el,\n      formatOptions,\n      highlightedDates,\n      multiple,\n      presentation,\n      preferWheel\n    } = this;\n    if (multiple) {\n      if (presentation !== 'date') {\n        printIonWarning('[ion-datetime] - Multiple date selection is only supported for presentation=\"date\".', el);\n      }\n      if (preferWheel) {\n        printIonWarning('[ion-datetime] - Multiple date selection is not supported with preferWheel=\"true\".', el);\n      }\n    }\n    if (highlightedDates !== undefined) {\n      if (presentation !== 'date' && presentation !== 'date-time' && presentation !== 'time-date') {\n        printIonWarning('[ion-datetime] - The highlightedDates property is only supported with the date, date-time, and time-date presentations.', el);\n      }\n      if (preferWheel) {\n        printIonWarning('[ion-datetime] - The highlightedDates property is not supported with preferWheel=\"true\".', el);\n      }\n    }\n    if (formatOptions) {\n      checkForPresentationFormatMismatch(el, presentation, formatOptions);\n      warnIfTimeZoneProvided(el, formatOptions);\n    }\n    const hourValues = this.parsedHourValues = convertToArrayOfNumbers(this.hourValues);\n    const minuteValues = this.parsedMinuteValues = convertToArrayOfNumbers(this.minuteValues);\n    const monthValues = this.parsedMonthValues = convertToArrayOfNumbers(this.monthValues);\n    const yearValues = this.parsedYearValues = convertToArrayOfNumbers(this.yearValues);\n    const dayValues = this.parsedDayValues = convertToArrayOfNumbers(this.dayValues);\n    const todayParts = this.todayParts = parseDate(getToday());\n    this.processMinParts();\n    this.processMaxParts();\n    this.defaultParts = getClosestValidDate({\n      refParts: todayParts,\n      monthValues,\n      dayValues,\n      yearValues,\n      hourValues,\n      minuteValues,\n      minParts: this.minParts,\n      maxParts: this.maxParts\n    });\n    this.processValue(this.value);\n    this.emitStyle();\n  }\n  emitStyle() {\n    this.ionStyle.emit({\n      interactive: true,\n      datetime: true,\n      'interactive-disabled': this.disabled\n    });\n  }\n  /**\n   * Universal render methods\n   * These are pieces of datetime that\n   * are rendered independently of presentation.\n   */\n  renderFooter() {\n    const {\n      disabled,\n      readonly,\n      showDefaultButtons,\n      showClearButton\n    } = this;\n    /**\n     * The cancel, clear, and confirm buttons\n     * should not be interactive if the datetime\n     * is disabled or readonly.\n     */\n    const isButtonDisabled = disabled || readonly;\n    const hasSlottedButtons = this.el.querySelector('[slot=\"buttons\"]') !== null;\n    if (!hasSlottedButtons && !showDefaultButtons && !showClearButton) {\n      return;\n    }\n    const clearButtonClick = () => {\n      this.reset();\n      this.setValue(undefined);\n    };\n    /**\n     * By default we render two buttons:\n     * Cancel - Dismisses the datetime and\n     * does not update the `value` prop.\n     * OK - Dismisses the datetime and\n     * updates the `value` prop.\n     */\n    return h(\"div\", {\n      class: \"datetime-footer\"\n    }, h(\"div\", {\n      class: \"datetime-buttons\"\n    }, h(\"div\", {\n      class: {\n        ['datetime-action-buttons']: true,\n        ['has-clear-button']: this.showClearButton\n      }\n    }, h(\"slot\", {\n      name: \"buttons\"\n    }, h(\"ion-buttons\", null, showDefaultButtons && h(\"ion-button\", {\n      id: \"cancel-button\",\n      color: this.color,\n      onClick: () => this.cancel(true),\n      disabled: isButtonDisabled\n    }, this.cancelText), h(\"div\", {\n      class: \"datetime-action-buttons-container\"\n    }, showClearButton && h(\"ion-button\", {\n      id: \"clear-button\",\n      color: this.color,\n      onClick: () => clearButtonClick(),\n      disabled: isButtonDisabled\n    }, this.clearText), showDefaultButtons && h(\"ion-button\", {\n      id: \"confirm-button\",\n      color: this.color,\n      onClick: () => this.confirm(true),\n      disabled: isButtonDisabled\n    }, this.doneText)))))));\n  }\n  /**\n   * Wheel picker render methods\n   */\n  renderWheelPicker(forcePresentation = this.presentation) {\n    /**\n     * If presentation=\"time-date\" we switch the\n     * order of the render array here instead of\n     * manually reordering each date/time picker\n     * column with CSS. This allows for additional\n     * flexibility if we need to render subsets\n     * of the date/time data or do additional ordering\n     * within the child render functions.\n     */\n    const renderArray = forcePresentation === 'time-date' ? [this.renderTimePickerColumns(forcePresentation), this.renderDatePickerColumns(forcePresentation)] : [this.renderDatePickerColumns(forcePresentation), this.renderTimePickerColumns(forcePresentation)];\n    return h(\"ion-picker\", null, renderArray);\n  }\n  renderDatePickerColumns(forcePresentation) {\n    return forcePresentation === 'date-time' || forcePresentation === 'time-date' ? this.renderCombinedDatePickerColumn() : this.renderIndividualDatePickerColumns(forcePresentation);\n  }\n  renderCombinedDatePickerColumn() {\n    const {\n      defaultParts,\n      disabled,\n      workingParts,\n      locale,\n      minParts,\n      maxParts,\n      todayParts,\n      isDateEnabled\n    } = this;\n    const activePart = this.getActivePartsWithFallback();\n    /**\n     * By default, generate a range of 3 months:\n     * Previous month, current month, and next month\n     */\n    const monthsToRender = generateMonths(workingParts);\n    const lastMonth = monthsToRender[monthsToRender.length - 1];\n    /**\n     * Ensure that users can select the entire window of dates.\n     */\n    monthsToRender[0].day = 1;\n    lastMonth.day = getNumDaysInMonth(lastMonth.month, lastMonth.year);\n    /**\n     * Narrow the dates rendered based on min/max dates (if any).\n     * The `min` date is used if the min is after the generated min month.\n     * The `max` date is used if the max is before the generated max month.\n     * This ensures that the sliding window always stays at 3 months\n     * but still allows future dates to be lazily rendered based on any min/max\n     * constraints.\n     */\n    const min = minParts !== undefined && isAfter(minParts, monthsToRender[0]) ? minParts : monthsToRender[0];\n    const max = maxParts !== undefined && isBefore(maxParts, lastMonth) ? maxParts : lastMonth;\n    const result = getCombinedDateColumnData(locale, todayParts, min, max, this.parsedDayValues, this.parsedMonthValues);\n    let items = result.items;\n    const parts = result.parts;\n    if (isDateEnabled) {\n      items = items.map((itemObject, index) => {\n        const referenceParts = parts[index];\n        let disabled;\n        try {\n          /**\n           * The `isDateEnabled` implementation is try-catch wrapped\n           * to prevent exceptions in the user's function from\n           * interrupting the calendar rendering.\n           */\n          disabled = !isDateEnabled(convertDataToISO(referenceParts));\n        } catch (e) {\n          printIonError('[ion-datetime] - Exception thrown from provided `isDateEnabled` function. Please check your function and try again.', e);\n        }\n        return Object.assign(Object.assign({}, itemObject), {\n          disabled\n        });\n      });\n    }\n    /**\n     * If we have selected a day already, then default the column\n     * to that value. Otherwise, set it to the default date.\n     */\n    const todayString = workingParts.day !== null ? `${workingParts.year}-${workingParts.month}-${workingParts.day}` : `${defaultParts.year}-${defaultParts.month}-${defaultParts.day}`;\n    return h(\"ion-picker-column\", {\n      \"aria-label\": \"Select a date\",\n      class: \"date-column\",\n      color: this.color,\n      disabled: disabled,\n      value: todayString,\n      onIonChange: ev => {\n        const {\n          value\n        } = ev.detail;\n        const findPart = parts.find(({\n          month,\n          day,\n          year\n        }) => value === `${year}-${month}-${day}`);\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), findPart));\n        this.setActiveParts(Object.assign(Object.assign({}, activePart), findPart));\n        ev.stopPropagation();\n      }\n    }, items.map(item => h(\"ion-picker-column-option\", {\n      part: item.value === todayString ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART,\n      key: item.value,\n      disabled: item.disabled,\n      value: item.value\n    }, item.text)));\n  }\n  renderIndividualDatePickerColumns(forcePresentation) {\n    const {\n      workingParts,\n      isDateEnabled\n    } = this;\n    const shouldRenderMonths = forcePresentation !== 'year' && forcePresentation !== 'time';\n    const months = shouldRenderMonths ? getMonthColumnData(this.locale, workingParts, this.minParts, this.maxParts, this.parsedMonthValues) : [];\n    const shouldRenderDays = forcePresentation === 'date';\n    let days = shouldRenderDays ? getDayColumnData(this.locale, workingParts, this.minParts, this.maxParts, this.parsedDayValues) : [];\n    if (isDateEnabled) {\n      days = days.map(dayObject => {\n        const {\n          value\n        } = dayObject;\n        const valueNum = typeof value === 'string' ? parseInt(value) : value;\n        const referenceParts = {\n          month: workingParts.month,\n          day: valueNum,\n          year: workingParts.year\n        };\n        let disabled;\n        try {\n          /**\n           * The `isDateEnabled` implementation is try-catch wrapped\n           * to prevent exceptions in the user's function from\n           * interrupting the calendar rendering.\n           */\n          disabled = !isDateEnabled(convertDataToISO(referenceParts));\n        } catch (e) {\n          printIonError('[ion-datetime] - Exception thrown from provided `isDateEnabled` function. Please check your function and try again.', e);\n        }\n        return Object.assign(Object.assign({}, dayObject), {\n          disabled\n        });\n      });\n    }\n    const shouldRenderYears = forcePresentation !== 'month' && forcePresentation !== 'time';\n    const years = shouldRenderYears ? getYearColumnData(this.locale, this.defaultParts, this.minParts, this.maxParts, this.parsedYearValues) : [];\n    /**\n     * Certain locales show the day before the month.\n     */\n    const showMonthFirst = isMonthFirstLocale(this.locale, {\n      month: 'numeric',\n      day: 'numeric'\n    });\n    let renderArray = [];\n    if (showMonthFirst) {\n      renderArray = [this.renderMonthPickerColumn(months), this.renderDayPickerColumn(days), this.renderYearPickerColumn(years)];\n    } else {\n      renderArray = [this.renderDayPickerColumn(days), this.renderMonthPickerColumn(months), this.renderYearPickerColumn(years)];\n    }\n    return renderArray;\n  }\n  renderDayPickerColumn(days) {\n    var _a;\n    if (days.length === 0) {\n      return [];\n    }\n    const {\n      disabled,\n      workingParts\n    } = this;\n    const activePart = this.getActivePartsWithFallback();\n    const pickerColumnValue = (_a = workingParts.day !== null ? workingParts.day : this.defaultParts.day) !== null && _a !== void 0 ? _a : undefined;\n    return h(\"ion-picker-column\", {\n      \"aria-label\": \"Select a day\",\n      class: \"day-column\",\n      color: this.color,\n      disabled: disabled,\n      value: pickerColumnValue,\n      onIonChange: ev => {\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          day: ev.detail.value\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, activePart), {\n          day: ev.detail.value\n        }));\n        ev.stopPropagation();\n      }\n    }, days.map(day => h(\"ion-picker-column-option\", {\n      part: day.value === pickerColumnValue ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART,\n      key: day.value,\n      disabled: day.disabled,\n      value: day.value\n    }, day.text)));\n  }\n  renderMonthPickerColumn(months) {\n    if (months.length === 0) {\n      return [];\n    }\n    const {\n      disabled,\n      workingParts\n    } = this;\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"ion-picker-column\", {\n      \"aria-label\": \"Select a month\",\n      class: \"month-column\",\n      color: this.color,\n      disabled: disabled,\n      value: workingParts.month,\n      onIonChange: ev => {\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          month: ev.detail.value\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, activePart), {\n          month: ev.detail.value\n        }));\n        ev.stopPropagation();\n      }\n    }, months.map(month => h(\"ion-picker-column-option\", {\n      part: month.value === workingParts.month ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART,\n      key: month.value,\n      disabled: month.disabled,\n      value: month.value\n    }, month.text)));\n  }\n  renderYearPickerColumn(years) {\n    if (years.length === 0) {\n      return [];\n    }\n    const {\n      disabled,\n      workingParts\n    } = this;\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"ion-picker-column\", {\n      \"aria-label\": \"Select a year\",\n      class: \"year-column\",\n      color: this.color,\n      disabled: disabled,\n      value: workingParts.year,\n      onIonChange: ev => {\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          year: ev.detail.value\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, activePart), {\n          year: ev.detail.value\n        }));\n        ev.stopPropagation();\n      }\n    }, years.map(year => h(\"ion-picker-column-option\", {\n      part: year.value === workingParts.year ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART,\n      key: year.value,\n      disabled: year.disabled,\n      value: year.value\n    }, year.text)));\n  }\n  renderTimePickerColumns(forcePresentation) {\n    if (['date', 'month', 'month-year', 'year'].includes(forcePresentation)) {\n      return [];\n    }\n    /**\n     * If a user has not selected a date,\n     * then we should show all times. If the\n     * user has selected a date (even if it has\n     * not been confirmed yet), we should apply\n     * the max and min restrictions so that the\n     * time picker shows values that are\n     * appropriate for the selected date.\n     */\n    const activePart = this.getActivePart();\n    const userHasSelectedDate = activePart !== undefined;\n    const {\n      hoursData,\n      minutesData,\n      dayPeriodData\n    } = getTimeColumnsData(this.locale, this.workingParts, this.hourCycle, userHasSelectedDate ? this.minParts : undefined, userHasSelectedDate ? this.maxParts : undefined, this.parsedHourValues, this.parsedMinuteValues);\n    return [this.renderHourPickerColumn(hoursData), this.renderMinutePickerColumn(minutesData), this.renderDayPeriodPickerColumn(dayPeriodData)];\n  }\n  renderHourPickerColumn(hoursData) {\n    const {\n      disabled,\n      workingParts\n    } = this;\n    if (hoursData.length === 0) return [];\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"ion-picker-column\", {\n      \"aria-label\": \"Select an hour\",\n      color: this.color,\n      disabled: disabled,\n      value: activePart.hour,\n      numericInput: true,\n      onIonChange: ev => {\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          hour: ev.detail.value\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, this.getActivePartsWithFallback()), {\n          hour: ev.detail.value\n        }));\n        ev.stopPropagation();\n      }\n    }, hoursData.map(hour => h(\"ion-picker-column-option\", {\n      part: hour.value === activePart.hour ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART,\n      key: hour.value,\n      disabled: hour.disabled,\n      value: hour.value\n    }, hour.text)));\n  }\n  renderMinutePickerColumn(minutesData) {\n    const {\n      disabled,\n      workingParts\n    } = this;\n    if (minutesData.length === 0) return [];\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"ion-picker-column\", {\n      \"aria-label\": \"Select a minute\",\n      color: this.color,\n      disabled: disabled,\n      value: activePart.minute,\n      numericInput: true,\n      onIonChange: ev => {\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          minute: ev.detail.value\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, this.getActivePartsWithFallback()), {\n          minute: ev.detail.value\n        }));\n        ev.stopPropagation();\n      }\n    }, minutesData.map(minute => h(\"ion-picker-column-option\", {\n      part: minute.value === activePart.minute ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART,\n      key: minute.value,\n      disabled: minute.disabled,\n      value: minute.value\n    }, minute.text)));\n  }\n  renderDayPeriodPickerColumn(dayPeriodData) {\n    const {\n      disabled,\n      workingParts\n    } = this;\n    if (dayPeriodData.length === 0) {\n      return [];\n    }\n    const activePart = this.getActivePartsWithFallback();\n    const isDayPeriodRTL = isLocaleDayPeriodRTL(this.locale);\n    return h(\"ion-picker-column\", {\n      \"aria-label\": \"Select a day period\",\n      style: isDayPeriodRTL ? {\n        order: '-1'\n      } : {},\n      color: this.color,\n      disabled: disabled,\n      value: activePart.ampm,\n      onIonChange: ev => {\n        const hour = calculateHourFromAMPM(workingParts, ev.detail.value);\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          ampm: ev.detail.value,\n          hour\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, this.getActivePartsWithFallback()), {\n          ampm: ev.detail.value,\n          hour\n        }));\n        ev.stopPropagation();\n      }\n    }, dayPeriodData.map(dayPeriod => h(\"ion-picker-column-option\", {\n      part: dayPeriod.value === activePart.ampm ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART,\n      key: dayPeriod.value,\n      disabled: dayPeriod.disabled,\n      value: dayPeriod.value\n    }, dayPeriod.text)));\n  }\n  renderWheelView(forcePresentation) {\n    const {\n      locale\n    } = this;\n    const showMonthFirst = isMonthFirstLocale(locale);\n    const columnOrder = showMonthFirst ? 'month-first' : 'year-first';\n    return h(\"div\", {\n      class: {\n        [`wheel-order-${columnOrder}`]: true\n      }\n    }, this.renderWheelPicker(forcePresentation));\n  }\n  /**\n   * Grid Render Methods\n   */\n  renderCalendarHeader(mode) {\n    const {\n      disabled\n    } = this;\n    const expandedIcon = mode === 'ios' ? chevronDown : caretUpSharp;\n    const collapsedIcon = mode === 'ios' ? chevronForward : caretDownSharp;\n    const prevMonthDisabled = disabled || isPrevMonthDisabled(this.workingParts, this.minParts, this.maxParts);\n    const nextMonthDisabled = disabled || isNextMonthDisabled(this.workingParts, this.maxParts);\n    // don't use the inheritAttributes util because it removes dir from the host, and we still need that\n    const hostDir = this.el.getAttribute('dir') || undefined;\n    return h(\"div\", {\n      class: \"calendar-header\"\n    }, h(\"div\", {\n      class: \"calendar-action-buttons\"\n    }, h(\"div\", {\n      class: \"calendar-month-year\"\n    }, h(\"button\", {\n      class: {\n        'calendar-month-year-toggle': true,\n        'ion-activatable': true,\n        'ion-focusable': true\n      },\n      part: \"month-year-button\",\n      disabled: disabled,\n      \"aria-label\": this.showMonthAndYear ? 'Hide year picker' : 'Show year picker',\n      onClick: () => this.toggleMonthAndYearView()\n    }, h(\"span\", {\n      id: \"toggle-wrapper\"\n    }, getMonthAndYear(this.locale, this.workingParts), h(\"ion-icon\", {\n      \"aria-hidden\": \"true\",\n      icon: this.showMonthAndYear ? expandedIcon : collapsedIcon,\n      lazy: false,\n      flipRtl: true\n    })), mode === 'md' && h(\"ion-ripple-effect\", null))), h(\"div\", {\n      class: \"calendar-next-prev\"\n    }, h(\"ion-buttons\", null, h(\"ion-button\", {\n      \"aria-label\": \"Previous month\",\n      disabled: prevMonthDisabled,\n      onClick: () => this.prevMonth()\n    }, h(\"ion-icon\", {\n      dir: hostDir,\n      \"aria-hidden\": \"true\",\n      slot: \"icon-only\",\n      icon: chevronBack,\n      lazy: false,\n      flipRtl: true\n    })), h(\"ion-button\", {\n      \"aria-label\": \"Next month\",\n      disabled: nextMonthDisabled,\n      onClick: () => this.nextMonth()\n    }, h(\"ion-icon\", {\n      dir: hostDir,\n      \"aria-hidden\": \"true\",\n      slot: \"icon-only\",\n      icon: chevronForward,\n      lazy: false,\n      flipRtl: true\n    }))))), h(\"div\", {\n      class: \"calendar-days-of-week\",\n      \"aria-hidden\": \"true\"\n    }, getDaysOfWeek(this.locale, mode, this.firstDayOfWeek % 7).map(d => {\n      return h(\"div\", {\n        class: \"day-of-week\"\n      }, d);\n    })));\n  }\n  renderMonth(month, year) {\n    const {\n      disabled,\n      readonly\n    } = this;\n    const yearAllowed = this.parsedYearValues === undefined || this.parsedYearValues.includes(year);\n    const monthAllowed = this.parsedMonthValues === undefined || this.parsedMonthValues.includes(month);\n    const isCalMonthDisabled = !yearAllowed || !monthAllowed;\n    const isDatetimeDisabled = disabled || readonly;\n    const swipeDisabled = disabled || isMonthDisabled({\n      month,\n      year,\n      day: null\n    }, {\n      // The day is not used when checking if a month is disabled.\n      // Users should be able to access the min or max month, even if the\n      // min/max date is out of bounds (e.g. min is set to Feb 15, Feb should not be disabled).\n      minParts: Object.assign(Object.assign({}, this.minParts), {\n        day: null\n      }),\n      maxParts: Object.assign(Object.assign({}, this.maxParts), {\n        day: null\n      })\n    });\n    // The working month should never have swipe disabled.\n    // Otherwise the CSS scroll snap will not work and the user\n    // can free-scroll the calendar.\n    const isWorkingMonth = this.workingParts.month === month && this.workingParts.year === year;\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"div\", {\n      \"aria-hidden\": !isWorkingMonth ? 'true' : null,\n      class: {\n        'calendar-month': true,\n        // Prevents scroll snap swipe gestures for months outside of the min/max bounds\n        'calendar-month-disabled': !isWorkingMonth && swipeDisabled\n      }\n    }, h(\"div\", {\n      class: \"calendar-month-grid\"\n    }, getDaysOfMonth(month, year, this.firstDayOfWeek % 7, this.showAdjacentDays).map((dateObject, index) => {\n      const {\n        day,\n        dayOfWeek,\n        isAdjacentDay\n      } = dateObject;\n      const {\n        el,\n        highlightedDates,\n        isDateEnabled,\n        multiple,\n        showAdjacentDays\n      } = this;\n      let _month = month;\n      let _year = year;\n      if (showAdjacentDays && isAdjacentDay && day !== null) {\n        if (day > 20) {\n          // Leading with the adjacent day from the previous month\n          // if its a adjacent day and is higher than '20' (last week even in feb)\n          if (month === 1) {\n            _year = year - 1;\n            _month = 12;\n          } else {\n            _month = month - 1;\n          }\n        } else if (day < 15) {\n          // Leading with the adjacent day from the next month\n          // if its a adjacent day and is lower than '15' (first two weeks)\n          if (month === 12) {\n            _year = year + 1;\n            _month = 1;\n          } else {\n            _month = month + 1;\n          }\n        }\n      }\n      const referenceParts = {\n        month: _month,\n        day,\n        year: _year,\n        isAdjacentDay\n      };\n      const isCalendarPadding = day === null;\n      const {\n        isActive,\n        isToday,\n        ariaLabel,\n        ariaSelected,\n        disabled: isDayDisabled,\n        text\n      } = getCalendarDayState(this.locale, referenceParts, this.activeParts, this.todayParts, this.minParts, this.maxParts, this.parsedDayValues);\n      const dateIsoString = convertDataToISO(referenceParts);\n      let isCalDayDisabled = isCalMonthDisabled || isDayDisabled;\n      if (!isCalDayDisabled && isDateEnabled !== undefined) {\n        try {\n          /**\n           * The `isDateEnabled` implementation is try-catch wrapped\n           * to prevent exceptions in the user's function from\n           * interrupting the calendar rendering.\n           */\n          isCalDayDisabled = !isDateEnabled(dateIsoString);\n        } catch (e) {\n          printIonError('[ion-datetime] - Exception thrown from provided `isDateEnabled` function. Please check your function and try again.', el, e);\n        }\n      }\n      /**\n       * Some days are constrained through max & min or allowed dates\n       * and also disabled because the component is readonly or disabled.\n       * These need to be displayed differently.\n       */\n      const isCalDayConstrained = isCalDayDisabled && isDatetimeDisabled;\n      const isButtonDisabled = isCalDayDisabled || isDatetimeDisabled;\n      let dateStyle = undefined;\n      /**\n       * Custom highlight styles should not override the style for selected dates,\n       * nor apply to \"filler days\" at the start of the grid.\n       */\n      if (highlightedDates !== undefined && !isActive && day !== null && !isAdjacentDay) {\n        dateStyle = getHighlightStyles(highlightedDates, dateIsoString, el);\n      }\n      let dateParts = undefined;\n      // \"Filler days\" at the beginning of the grid should not get the calendar day\n      // CSS parts added to them\n      if (!isCalendarPadding && !isAdjacentDay) {\n        dateParts = `calendar-day${isActive ? ' active' : ''}${isToday ? ' today' : ''}${isCalDayDisabled ? ' disabled' : ''}`;\n      } else if (isAdjacentDay) {\n        dateParts = `calendar-day${isCalDayDisabled ? ' disabled' : ''}`;\n      }\n      return h(\"div\", {\n        class: \"calendar-day-wrapper\"\n      }, h(\"button\", {\n        // We need to use !important for the inline styles here because\n        // otherwise the CSS shadow parts will override these styles.\n        // See https://github.com/WICG/webcomponents/issues/847\n        // Both the CSS shadow parts and highlightedDates styles are\n        // provided by the developer, but highlightedDates styles should\n        // always take priority.\n        ref: el => {\n          if (el) {\n            el.style.setProperty('color', `${dateStyle ? dateStyle.textColor : ''}`, 'important');\n            el.style.setProperty('background-color', `${dateStyle ? dateStyle.backgroundColor : ''}`, 'important');\n          }\n        },\n        tabindex: \"-1\",\n        \"data-day\": day,\n        \"data-month\": _month,\n        \"data-year\": _year,\n        \"data-index\": index,\n        \"data-day-of-week\": dayOfWeek,\n        disabled: isButtonDisabled,\n        class: {\n          'calendar-day-padding': isCalendarPadding,\n          'calendar-day': true,\n          'calendar-day-active': isActive,\n          'calendar-day-constrained': isCalDayConstrained,\n          'calendar-day-today': isToday,\n          'calendar-day-adjacent-day': isAdjacentDay\n        },\n        part: dateParts,\n        \"aria-hidden\": isCalendarPadding ? 'true' : null,\n        \"aria-selected\": ariaSelected,\n        \"aria-label\": ariaLabel,\n        onClick: () => {\n          if (isCalendarPadding) {\n            return;\n          }\n          if (isAdjacentDay) {\n            // The user selected a day outside the current month. Ignore this button, as the month will be re-rendered.\n            this.el.blur();\n            this.activeParts = Object.assign(Object.assign({}, activePart), referenceParts);\n            this.animateToDate(referenceParts);\n            this.confirm();\n          } else {\n            this.setWorkingParts(Object.assign(Object.assign({}, this.workingParts), referenceParts));\n            // Multiple only needs date info so we can wipe out other fields like time.\n            if (multiple) {\n              this.setActiveParts(referenceParts, isActive);\n            } else {\n              this.setActiveParts(Object.assign(Object.assign({}, activePart), referenceParts));\n            }\n          }\n        }\n      }, text));\n    })));\n  }\n  renderCalendarBody() {\n    return h(\"div\", {\n      class: \"calendar-body ion-focusable\",\n      ref: el => this.calendarBodyRef = el,\n      tabindex: \"0\"\n    }, generateMonths(this.workingParts, this.forceRenderDate).map(({\n      month,\n      year\n    }) => {\n      return this.renderMonth(month, year);\n    }));\n  }\n  renderCalendar(mode) {\n    return h(\"div\", {\n      class: \"datetime-calendar\",\n      key: \"datetime-calendar\"\n    }, this.renderCalendarHeader(mode), this.renderCalendarBody());\n  }\n  renderTimeLabel() {\n    const hasSlottedTimeLabel = this.el.querySelector('[slot=\"time-label\"]') !== null;\n    if (!hasSlottedTimeLabel && !this.showDefaultTimeLabel) {\n      return;\n    }\n    return h(\"slot\", {\n      name: \"time-label\"\n    }, \"Time\");\n  }\n  renderTimeOverlay() {\n    const {\n      disabled,\n      hourCycle,\n      isTimePopoverOpen,\n      locale,\n      formatOptions\n    } = this;\n    const computedHourCycle = getHourCycle(locale, hourCycle);\n    const activePart = this.getActivePartsWithFallback();\n    return [h(\"div\", {\n      class: \"time-header\"\n    }, this.renderTimeLabel()), h(\"button\", {\n      class: {\n        'time-body': true,\n        'time-body-active': isTimePopoverOpen\n      },\n      part: `time-button${isTimePopoverOpen ? ' active' : ''}`,\n      \"aria-expanded\": \"false\",\n      \"aria-haspopup\": \"true\",\n      disabled: disabled,\n      onClick: async ev => {\n        const {\n          popoverRef\n        } = this;\n        if (popoverRef) {\n          this.isTimePopoverOpen = true;\n          popoverRef.present(new CustomEvent('ionShadowTarget', {\n            detail: {\n              ionShadowTarget: ev.target\n            }\n          }));\n          await popoverRef.onWillDismiss();\n          this.isTimePopoverOpen = false;\n        }\n      }\n    }, getLocalizedTime(locale, activePart, computedHourCycle, formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time)), h(\"ion-popover\", {\n      alignment: \"center\",\n      translucent: true,\n      overlayIndex: 1,\n      arrow: false,\n      onWillPresent: ev => {\n        /**\n         * Intersection Observers do not consistently fire between Blink and Webkit\n         * when toggling the visibility of the popover and trying to scroll the picker\n         * column to the correct time value.\n         *\n         * This will correctly scroll the element position to the correct time value,\n         * before the popover is fully presented.\n         */\n        const cols = ev.target.querySelectorAll('ion-picker-column');\n        // TODO (FW-615): Potentially remove this when intersection observers are fixed in picker column\n        cols.forEach(col => col.scrollActiveItemIntoView());\n      },\n      style: {\n        '--offset-y': '-10px',\n        '--min-width': 'fit-content'\n      },\n      // Allow native browser keyboard events to support up/down/home/<USER>\n      // navigation within the time picker.\n      keyboardEvents: true,\n      ref: el => this.popoverRef = el\n    }, this.renderWheelPicker('time'))];\n  }\n  getHeaderSelectedDateText() {\n    var _a;\n    const {\n      activeParts,\n      formatOptions,\n      multiple,\n      titleSelectedDatesFormatter\n    } = this;\n    const isArray = Array.isArray(activeParts);\n    let headerText;\n    if (multiple && isArray && activeParts.length !== 1) {\n      headerText = `${activeParts.length} days`; // default/fallback for multiple selection\n      if (titleSelectedDatesFormatter !== undefined) {\n        try {\n          headerText = titleSelectedDatesFormatter(convertDataToISO(activeParts));\n        } catch (e) {\n          printIonError('[ion-datetime] - Exception in provided `titleSelectedDatesFormatter`:', e);\n        }\n      }\n    } else {\n      // for exactly 1 day selected (multiple set or not), show a formatted version of that\n      headerText = getLocalizedDateTime(this.locale, this.getActivePartsWithFallback(), (_a = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _a !== void 0 ? _a : {\n        weekday: 'short',\n        month: 'short',\n        day: 'numeric'\n      });\n    }\n    return headerText;\n  }\n  renderHeader(showExpandedHeader = true) {\n    const hasSlottedTitle = this.el.querySelector('[slot=\"title\"]') !== null;\n    if (!hasSlottedTitle && !this.showDefaultTitle) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"datetime-header\"\n    }, h(\"div\", {\n      class: \"datetime-title\"\n    }, h(\"slot\", {\n      name: \"title\"\n    }, \"Select Date\")), showExpandedHeader && h(\"div\", {\n      class: \"datetime-selected-date\"\n    }, this.getHeaderSelectedDateText()));\n  }\n  /**\n   * Render time picker inside of datetime.\n   * Do not pass color prop to segment on\n   * iOS mode. MD segment has been customized and\n   * should take on the color prop, but iOS\n   * should just be the default segment.\n   */\n  renderTime() {\n    const {\n      presentation\n    } = this;\n    const timeOnlyPresentation = presentation === 'time';\n    return h(\"div\", {\n      class: \"datetime-time\"\n    }, timeOnlyPresentation ? this.renderWheelPicker() : this.renderTimeOverlay());\n  }\n  /**\n   * Renders the month/year picker that is\n   * displayed on the calendar grid.\n   * The .datetime-year class has additional\n   * styles that let us show/hide the\n   * picker when the user clicks on the\n   * toggle in the calendar header.\n   */\n  renderCalendarViewMonthYearPicker() {\n    return h(\"div\", {\n      class: \"datetime-year\"\n    }, this.renderWheelView('month-year'));\n  }\n  /**\n   * Render entry point\n   * All presentation types are rendered from here.\n   */\n  renderDatetime(mode) {\n    const {\n      presentation,\n      preferWheel\n    } = this;\n    /**\n     * Certain presentation types have separate grid and wheel displays.\n     * If preferWheel is true then we should show a wheel picker instead.\n     */\n    const hasWheelVariant = presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n    if (preferWheel && hasWheelVariant) {\n      return [this.renderHeader(false), this.renderWheelView(), this.renderFooter()];\n    }\n    switch (presentation) {\n      case 'date-time':\n        return [this.renderHeader(), this.renderCalendar(mode), this.renderCalendarViewMonthYearPicker(), this.renderTime(), this.renderFooter()];\n      case 'time-date':\n        return [this.renderHeader(), this.renderTime(), this.renderCalendar(mode), this.renderCalendarViewMonthYearPicker(), this.renderFooter()];\n      case 'time':\n        return [this.renderHeader(false), this.renderTime(), this.renderFooter()];\n      case 'month':\n      case 'month-year':\n      case 'year':\n        return [this.renderHeader(false), this.renderWheelView(), this.renderFooter()];\n      default:\n        return [this.renderHeader(), this.renderCalendar(mode), this.renderCalendarViewMonthYearPicker(), this.renderFooter()];\n    }\n  }\n  render() {\n    const {\n      name,\n      value,\n      disabled,\n      el,\n      color,\n      readonly,\n      showMonthAndYear,\n      preferWheel,\n      presentation,\n      size,\n      isGridStyle\n    } = this;\n    const mode = getIonMode(this);\n    const isMonthAndYearPresentation = presentation === 'year' || presentation === 'month' || presentation === 'month-year';\n    const shouldShowMonthAndYear = showMonthAndYear || isMonthAndYearPresentation;\n    const monthYearPickerOpen = showMonthAndYear && !isMonthAndYearPresentation;\n    const hasDatePresentation = presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n    const hasWheelVariant = hasDatePresentation && preferWheel;\n    renderHiddenInput(true, el, name, formatValue(value), disabled);\n    return h(Host, {\n      key: '7ea46c27aa5fb01b748dd4d6eb2340ad13f65175',\n      \"aria-disabled\": disabled ? 'true' : null,\n      onFocus: this.onFocus,\n      onBlur: this.onBlur,\n      class: Object.assign({}, createColorClasses(color, {\n        [mode]: true,\n        ['datetime-readonly']: readonly,\n        ['datetime-disabled']: disabled,\n        'show-month-and-year': shouldShowMonthAndYear,\n        'month-year-picker-open': monthYearPickerOpen,\n        [`datetime-presentation-${presentation}`]: true,\n        [`datetime-size-${size}`]: true,\n        [`datetime-prefer-wheel`]: hasWheelVariant,\n        [`datetime-grid`]: isGridStyle\n      }))\n    }, h(\"div\", {\n      key: '6964378424c58b053e23279d08eaedf28dfcc315',\n      class: \"intersection-tracker\",\n      ref: el => this.intersectionTrackerRef = el\n    }), this.renderDatetime(mode));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"formatOptions\": [\"formatOptionsChanged\"],\n      \"disabled\": [\"disabledChanged\"],\n      \"min\": [\"minChanged\"],\n      \"max\": [\"maxChanged\"],\n      \"presentation\": [\"presentationChanged\"],\n      \"yearValues\": [\"yearValuesChanged\"],\n      \"monthValues\": [\"monthValuesChanged\"],\n      \"dayValues\": [\"dayValuesChanged\"],\n      \"hourValues\": [\"hourValuesChanged\"],\n      \"minuteValues\": [\"minuteValuesChanged\"],\n      \"value\": [\"valueChanged\"]\n    };\n  }\n};\nlet datetimeIds = 0;\nconst CANCEL_ROLE = 'datetime-cancel';\nconst CONFIRM_ROLE = 'datetime-confirm';\nconst WHEEL_ITEM_PART = 'wheel-item';\nconst WHEEL_ITEM_ACTIVE_PART = `active`;\nDatetime.style = {\n  ios: datetimeIosCss,\n  md: datetimeMdCss\n};\n\n/**\n * iOS Picker Enter Animation\n */\nconst iosEnterAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  wrapperAnimation.addElement(baseEl.querySelector('.picker-wrapper')).fromTo('transform', 'translateY(100%)', 'translateY(0%)');\n  return baseAnimation.addElement(baseEl).easing('cubic-bezier(.36,.66,.04,1)').duration(400).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Picker Leave Animation\n */\nconst iosLeaveAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0.01);\n  wrapperAnimation.addElement(baseEl.querySelector('.picker-wrapper')).fromTo('transform', 'translateY(0%)', 'translateY(100%)');\n  return baseAnimation.addElement(baseEl).easing('cubic-bezier(.36,.66,.04,1)').duration(400).addAnimation([backdropAnimation, wrapperAnimation]);\n};\nconst pickerIosCss = \".sc-ion-picker-legacy-ios-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.sc-ion-picker-legacy-ios-h{inset-inline-start:0}.overlay-hidden.sc-ion-picker-legacy-ios-h{display:none}.picker-wrapper.sc-ion-picker-legacy-ios{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-legacy-ios{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-legacy-ios{border:0;font-family:inherit}.picker-button.sc-ion-picker-legacy-ios:active,.picker-button.sc-ion-picker-legacy-ios:focus{outline:none}.picker-columns.sc-ion-picker-legacy-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-legacy-ios,.picker-below-highlight.sc-ion-picker-legacy-ios{display:none;pointer-events:none}.sc-ion-picker-legacy-ios-h{--background:var(--ion-background-color, #fff);--border-width:1px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-legacy-ios{display:-ms-flexbox;display:flex;height:44px;border-bottom:0.55px solid var(--border-color)}.picker-toolbar-button.sc-ion-picker-legacy-ios{-ms-flex:1;flex:1;text-align:end}.picker-toolbar-button.sc-ion-picker-legacy-ios:last-child .picker-button.sc-ion-picker-legacy-ios{font-weight:600}.picker-toolbar-button.sc-ion-picker-legacy-ios:first-child{font-weight:normal;text-align:start}.picker-button.sc-ion-picker-legacy-ios,.picker-button.ion-activated.sc-ion-picker-legacy-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1em;padding-inline-start:1em;-webkit-padding-end:1em;padding-inline-end:1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #0054e9);font-size:16px}.picker-columns.sc-ion-picker-legacy-ios{height:215px;-webkit-perspective:1000px;perspective:1000px}.picker-above-highlight.sc-ion-picker-legacy-ios{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:81px;border-bottom:1px solid var(--border-color);background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to bottom, var(--background, var(--ion-background-color, #fff)) 20%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:10}.picker-above-highlight.sc-ion-picker-legacy-ios{inset-inline-start:0}.picker-below-highlight.sc-ion-picker-legacy-ios{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:119px;border-top:1px solid var(--border-color);background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to top, var(--background, var(--ion-background-color, #fff)) 30%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:11}.picker-below-highlight.sc-ion-picker-legacy-ios{inset-inline-start:0}\";\nconst pickerMdCss = \".sc-ion-picker-legacy-md-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.sc-ion-picker-legacy-md-h{inset-inline-start:0}.overlay-hidden.sc-ion-picker-legacy-md-h{display:none}.picker-wrapper.sc-ion-picker-legacy-md{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-legacy-md{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-legacy-md{border:0;font-family:inherit}.picker-button.sc-ion-picker-legacy-md:active,.picker-button.sc-ion-picker-legacy-md:focus{outline:none}.picker-columns.sc-ion-picker-legacy-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-legacy-md,.picker-below-highlight.sc-ion-picker-legacy-md{display:none;pointer-events:none}.sc-ion-picker-legacy-md-h{--background:var(--ion-background-color, #fff);--border-width:0.55px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-legacy-md{display:-ms-flexbox;display:flex;-ms-flex-pack:end;justify-content:flex-end;height:44px}.picker-button.sc-ion-picker-legacy-md,.picker-button.ion-activated.sc-ion-picker-legacy-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1.1em;padding-inline-start:1.1em;-webkit-padding-end:1.1em;padding-inline-end:1.1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #0054e9);font-size:14px;font-weight:500;text-transform:uppercase;-webkit-box-shadow:none;box-shadow:none}.picker-columns.sc-ion-picker-legacy-md{height:216px;-webkit-perspective:1800px;perspective:1800px}.picker-above-highlight.sc-ion-picker-legacy-md{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:81px;border-bottom:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to bottom, var(--ion-background-color, #fff) 20%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:10}.picker-above-highlight.sc-ion-picker-legacy-md{inset-inline-start:0}.picker-below-highlight.sc-ion-picker-legacy-md{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:119px;border-top:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to top, var(--ion-background-color, #fff) 30%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:11}.picker-below-highlight.sc-ion-picker-legacy-md{inset-inline-start:0}\";\nconst Picker = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.didPresent = createEvent(this, \"ionPickerDidPresent\", 7);\n    this.willPresent = createEvent(this, \"ionPickerWillPresent\", 7);\n    this.willDismiss = createEvent(this, \"ionPickerWillDismiss\", 7);\n    this.didDismiss = createEvent(this, \"ionPickerDidDismiss\", 7);\n    this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n    this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n    this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n    this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n    this.delegateController = createDelegateController(this);\n    this.lockController = createLockController();\n    this.triggerController = createTriggerController();\n    this.presented = false;\n    /** @internal */\n    this.hasController = false;\n    /**\n     * If `true`, the keyboard will be automatically dismissed when the overlay is presented.\n     */\n    this.keyboardClose = true;\n    /**\n     * Array of buttons to be displayed at the top of the picker.\n     */\n    this.buttons = [];\n    /**\n     * Array of columns to be displayed in the picker.\n     */\n    this.columns = [];\n    /**\n     * Number of milliseconds to wait before dismissing the picker.\n     */\n    this.duration = 0;\n    /**\n     * If `true`, a backdrop will be displayed behind the picker.\n     */\n    this.showBackdrop = true;\n    /**\n     * If `true`, the picker will be dismissed when the backdrop is clicked.\n     */\n    this.backdropDismiss = true;\n    /**\n     * If `true`, the picker will animate.\n     */\n    this.animated = true;\n    /**\n     * If `true`, the picker will open. If `false`, the picker will close.\n     * Use this if you need finer grained control over presentation, otherwise\n     * just use the pickerController or the `trigger` property.\n     * Note: `isOpen` will not automatically be set back to `false` when\n     * the picker dismisses. You will need to do that in your code.\n     */\n    this.isOpen = false;\n    this.onBackdropTap = () => {\n      this.dismiss(undefined, BACKDROP);\n    };\n    this.dispatchCancelHandler = ev => {\n      const role = ev.detail.role;\n      if (isCancel(role)) {\n        const cancelButton = this.buttons.find(b => b.role === 'cancel');\n        this.callButtonHandler(cancelButton);\n      }\n    };\n  }\n  onIsOpenChange(newValue, oldValue) {\n    if (newValue === true && oldValue === false) {\n      this.present();\n    } else if (newValue === false && oldValue === true) {\n      this.dismiss();\n    }\n  }\n  triggerChanged() {\n    const {\n      trigger,\n      el,\n      triggerController\n    } = this;\n    if (trigger) {\n      triggerController.addClickListener(el, trigger);\n    }\n  }\n  connectedCallback() {\n    prepareOverlay(this.el);\n    this.triggerChanged();\n  }\n  disconnectedCallback() {\n    this.triggerController.removeClickListener();\n  }\n  componentWillLoad() {\n    var _a;\n    if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n      setOverlayId(this.el);\n    }\n  }\n  componentDidLoad() {\n    printIonWarning('[ion-picker-legacy] - ion-picker-legacy and ion-picker-legacy-column have been deprecated in favor of new versions of the ion-picker and ion-picker-column components. These new components display inline with your page content allowing for more presentation flexibility than before.', this.el);\n    /**\n     * If picker was rendered with isOpen=\"true\"\n     * then we should open picker immediately.\n     */\n    if (this.isOpen === true) {\n      raf(() => this.present());\n    }\n    /**\n     * When binding values in frameworks such as Angular\n     * it is possible for the value to be set after the Web Component\n     * initializes but before the value watcher is set up in Stencil.\n     * As a result, the watcher callback may not be fired.\n     * We work around this by manually calling the watcher\n     * callback when the component has loaded and the watcher\n     * is configured.\n     */\n    this.triggerChanged();\n  }\n  /**\n   * Present the picker overlay after it has been created.\n   */\n  async present() {\n    const unlock = await this.lockController.lock();\n    await this.delegateController.attachViewToDom();\n    await present(this, 'pickerEnter', iosEnterAnimation, iosEnterAnimation, undefined);\n    if (this.duration > 0) {\n      this.durationTimeout = setTimeout(() => this.dismiss(), this.duration);\n    }\n    unlock();\n  }\n  /**\n   * Dismiss the picker overlay after it has been presented.\n   *\n   * @param data Any data to emit in the dismiss events.\n   * @param role The role of the element that is dismissing the picker.\n   * This can be useful in a button handler for determining which button was\n   * clicked to dismiss the picker.\n   * Some examples include: ``\"cancel\"`, `\"destructive\"`, \"selected\"`, and `\"backdrop\"`.\n   */\n  async dismiss(data, role) {\n    const unlock = await this.lockController.lock();\n    if (this.durationTimeout) {\n      clearTimeout(this.durationTimeout);\n    }\n    const dismissed = await dismiss(this, data, role, 'pickerLeave', iosLeaveAnimation, iosLeaveAnimation);\n    if (dismissed) {\n      this.delegateController.removeViewFromDom();\n    }\n    unlock();\n    return dismissed;\n  }\n  /**\n   * Returns a promise that resolves when the picker did dismiss.\n   */\n  onDidDismiss() {\n    return eventMethod(this.el, 'ionPickerDidDismiss');\n  }\n  /**\n   * Returns a promise that resolves when the picker will dismiss.\n   */\n  onWillDismiss() {\n    return eventMethod(this.el, 'ionPickerWillDismiss');\n  }\n  /**\n   * Get the column that matches the specified name.\n   *\n   * @param name The name of the column.\n   */\n  getColumn(name) {\n    return Promise.resolve(this.columns.find(column => column.name === name));\n  }\n  async buttonClick(button) {\n    const role = button.role;\n    if (isCancel(role)) {\n      return this.dismiss(undefined, role);\n    }\n    const shouldDismiss = await this.callButtonHandler(button);\n    if (shouldDismiss) {\n      return this.dismiss(this.getSelected(), button.role);\n    }\n    return Promise.resolve();\n  }\n  async callButtonHandler(button) {\n    if (button) {\n      // a handler has been provided, execute it\n      // pass the handler the values from the inputs\n      const rtn = await safeCall(button.handler, this.getSelected());\n      if (rtn === false) {\n        // if the return value of the handler is false then do not dismiss\n        return false;\n      }\n    }\n    return true;\n  }\n  getSelected() {\n    const selected = {};\n    this.columns.forEach((col, index) => {\n      const selectedColumn = col.selectedIndex !== undefined ? col.options[col.selectedIndex] : undefined;\n      selected[col.name] = {\n        text: selectedColumn ? selectedColumn.text : undefined,\n        value: selectedColumn ? selectedColumn.value : undefined,\n        columnIndex: index\n      };\n    });\n    return selected;\n  }\n  render() {\n    const {\n      htmlAttributes\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, Object.assign({\n      key: 'b95440747eb80cba23ae676d399d5e5816722c58',\n      \"aria-modal\": \"true\",\n      tabindex: \"-1\"\n    }, htmlAttributes, {\n      style: {\n        zIndex: `${20000 + this.overlayIndex}`\n      },\n      class: Object.assign({\n        [mode]: true,\n        // Used internally for styling\n        [`picker-${mode}`]: true,\n        'overlay-hidden': true\n      }, getClassMap(this.cssClass)),\n      onIonBackdropTap: this.onBackdropTap,\n      onIonPickerWillDismiss: this.dispatchCancelHandler\n    }), h(\"ion-backdrop\", {\n      key: '169d1c83ef40e7fcb134219a585298b403a70b0f',\n      visible: this.showBackdrop,\n      tappable: this.backdropDismiss\n    }), h(\"div\", {\n      key: '98518e5f5cea2dfb8dfa63d9545e9ae3a5765023',\n      tabindex: \"0\",\n      \"aria-hidden\": \"true\"\n    }), h(\"div\", {\n      key: '151755ab8eb23f9adafbfe201349398f5a69dee7',\n      class: \"picker-wrapper ion-overlay-wrapper\",\n      role: \"dialog\"\n    }, h(\"div\", {\n      key: '5dcf93b2f4fe8f4fce7c7aec8f85ef45a03ef470',\n      class: \"picker-toolbar\"\n    }, this.buttons.map(b => h(\"div\", {\n      class: buttonWrapperClass(b)\n    }, h(\"button\", {\n      type: \"button\",\n      onClick: () => this.buttonClick(b),\n      class: buttonClass(b)\n    }, b.text)))), h(\"div\", {\n      key: 'fd5d66708edd38adc5a4d2fad7298969398a05e3',\n      class: \"picker-columns\"\n    }, h(\"div\", {\n      key: '1b5830fd6cef1016af7736792c514965d6cb38a8',\n      class: \"picker-above-highlight\"\n    }), this.presented && this.columns.map(c => h(\"ion-picker-legacy-column\", {\n      col: c\n    })), h(\"div\", {\n      key: 'c6edeca7afd69e13c9c66ba36f261974fd0f8f78',\n      class: \"picker-below-highlight\"\n    }))), h(\"div\", {\n      key: 'e2a4b24710e30579b14b82dbfd3761b2187797b5',\n      tabindex: \"0\",\n      \"aria-hidden\": \"true\"\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"isOpen\": [\"onIsOpenChange\"],\n      \"trigger\": [\"triggerChanged\"]\n    };\n  }\n};\nconst buttonWrapperClass = button => {\n  return {\n    [`picker-toolbar-${button.role}`]: button.role !== undefined,\n    'picker-toolbar-button': true\n  };\n};\nconst buttonClass = button => {\n  return Object.assign({\n    'picker-button': true,\n    'ion-activatable': true\n  }, getClassMap(button.cssClass));\n};\nPicker.style = {\n  ios: pickerIosCss,\n  md: pickerMdCss\n};\nconst pickerColumnIosCss = \".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}.picker-opt{inset-inline-start:0}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:20px;line-height:42px;pointer-events:none}.picker-opt{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:center center;transform-origin:center center;height:46px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:20px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}:host-context([dir=rtl]) .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}[dir=rtl] .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}}\";\nconst pickerColumnMdCss = \".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}.picker-opt{inset-inline-start:0}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:22px;line-height:42px;pointer-events:none}.picker-opt{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;height:43px;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:22px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}.picker-prefix,.picker-suffix,.picker-opt.picker-opt-selected{color:var(--ion-color-primary, #0054e9)}\";\nconst PickerColumnCmp = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionPickerColChange = createEvent(this, \"ionPickerColChange\", 7);\n    this.optHeight = 0;\n    this.rotateFactor = 0;\n    this.scaleFactor = 1;\n    this.velocity = 0;\n    this.y = 0;\n    this.noAnimate = true;\n    // `colDidChange` is a flag that gets set when the column is changed\n    // dynamically. When this flag is set, the column will refresh\n    // after the component re-renders to incorporate the new column data.\n    // This is necessary because `this.refresh` queries for the option elements,\n    // so it needs to wait for the latest elements to be available in the DOM.\n    // Ex: column is created with 3 options. User updates the column data\n    // to have 5 options. The column will still think it only has 3 options.\n    this.colDidChange = false;\n  }\n  colChanged() {\n    this.colDidChange = true;\n  }\n  async connectedCallback() {\n    let pickerRotateFactor = 0;\n    let pickerScaleFactor = 0.81;\n    const mode = getIonMode(this);\n    if (mode === 'ios') {\n      pickerRotateFactor = -0.46;\n      pickerScaleFactor = 1;\n    }\n    this.rotateFactor = pickerRotateFactor;\n    this.scaleFactor = pickerScaleFactor;\n    this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n      el: this.el,\n      gestureName: 'picker-swipe',\n      gesturePriority: 100,\n      threshold: 0,\n      passive: false,\n      onStart: ev => this.onStart(ev),\n      onMove: ev => this.onMove(ev),\n      onEnd: ev => this.onEnd(ev)\n    });\n    this.gesture.enable();\n    // Options have not been initialized yet\n    // Animation must be disabled through the `noAnimate` flag\n    // Otherwise, the options will render\n    // at the top of the column and transition down\n    this.tmrId = setTimeout(() => {\n      this.noAnimate = false;\n      // After initialization, `refresh()` will be called\n      // At this point, animation will be enabled. The options will\n      // animate as they are being selected.\n      this.refresh(true);\n    }, 250);\n  }\n  componentDidLoad() {\n    this.onDomChange();\n  }\n  componentDidUpdate() {\n    // Options may have changed since last update.\n    if (this.colDidChange) {\n      // Animation must be disabled through the `onDomChange` parameter.\n      // Otherwise, the recently added options will render\n      // at the top of the column and transition down\n      this.onDomChange(true, false);\n      this.colDidChange = false;\n    }\n  }\n  disconnectedCallback() {\n    if (this.rafId !== undefined) cancelAnimationFrame(this.rafId);\n    if (this.tmrId) clearTimeout(this.tmrId);\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n  }\n  emitColChange() {\n    this.ionPickerColChange.emit(this.col);\n  }\n  setSelected(selectedIndex, duration) {\n    // if there is a selected index, then figure out it's y position\n    // if there isn't a selected index, then just use the top y position\n    const y = selectedIndex > -1 ? -(selectedIndex * this.optHeight) : 0;\n    this.velocity = 0;\n    // set what y position we're at\n    if (this.rafId !== undefined) cancelAnimationFrame(this.rafId);\n    this.update(y, duration, true);\n    this.emitColChange();\n  }\n  update(y, duration, saveY) {\n    if (!this.optsEl) {\n      return;\n    }\n    // ensure we've got a good round number :)\n    let translateY = 0;\n    let translateZ = 0;\n    const {\n      col,\n      rotateFactor\n    } = this;\n    const prevSelected = col.selectedIndex;\n    const selectedIndex = col.selectedIndex = this.indexForY(-y);\n    const durationStr = duration === 0 ? '' : duration + 'ms';\n    const scaleStr = `scale(${this.scaleFactor})`;\n    const children = this.optsEl.children;\n    for (let i = 0; i < children.length; i++) {\n      const button = children[i];\n      const opt = col.options[i];\n      const optOffset = i * this.optHeight + y;\n      let transform = '';\n      if (rotateFactor !== 0) {\n        const rotateX = optOffset * rotateFactor;\n        if (Math.abs(rotateX) <= 90) {\n          translateY = 0;\n          translateZ = 90;\n          transform = `rotateX(${rotateX}deg) `;\n        } else {\n          translateY = -9999;\n        }\n      } else {\n        translateZ = 0;\n        translateY = optOffset;\n      }\n      const selected = selectedIndex === i;\n      transform += `translate3d(0px,${translateY}px,${translateZ}px) `;\n      if (this.scaleFactor !== 1 && !selected) {\n        transform += scaleStr;\n      }\n      // Update transition duration\n      if (this.noAnimate) {\n        opt.duration = 0;\n        button.style.transitionDuration = '';\n      } else if (duration !== opt.duration) {\n        opt.duration = duration;\n        button.style.transitionDuration = durationStr;\n      }\n      // Update transform\n      if (transform !== opt.transform) {\n        opt.transform = transform;\n      }\n      button.style.transform = transform;\n      /**\n       * Ensure that the select column\n       * item has the selected class\n       */\n      opt.selected = selected;\n      if (selected) {\n        button.classList.add(PICKER_OPT_SELECTED);\n      } else {\n        button.classList.remove(PICKER_OPT_SELECTED);\n      }\n    }\n    this.col.prevSelected = prevSelected;\n    if (saveY) {\n      this.y = y;\n    }\n    if (this.lastIndex !== selectedIndex) {\n      // have not set a last index yet\n      hapticSelectionChanged();\n      this.lastIndex = selectedIndex;\n    }\n  }\n  decelerate() {\n    if (this.velocity !== 0) {\n      // still decelerating\n      this.velocity *= DECELERATION_FRICTION;\n      // do not let it go slower than a velocity of 1\n      this.velocity = this.velocity > 0 ? Math.max(this.velocity, 1) : Math.min(this.velocity, -1);\n      let y = this.y + this.velocity;\n      if (y > this.minY) {\n        // whoops, it's trying to scroll up farther than the options we have!\n        y = this.minY;\n        this.velocity = 0;\n      } else if (y < this.maxY) {\n        // gahh, it's trying to scroll down farther than we can!\n        y = this.maxY;\n        this.velocity = 0;\n      }\n      this.update(y, 0, true);\n      const notLockedIn = Math.round(y) % this.optHeight !== 0 || Math.abs(this.velocity) > 1;\n      if (notLockedIn) {\n        // isn't locked in yet, keep decelerating until it is\n        this.rafId = requestAnimationFrame(() => this.decelerate());\n      } else {\n        this.velocity = 0;\n        this.emitColChange();\n        hapticSelectionEnd();\n      }\n    } else if (this.y % this.optHeight !== 0) {\n      // needs to still get locked into a position so options line up\n      const currentPos = Math.abs(this.y % this.optHeight);\n      // create a velocity in the direction it needs to scroll\n      this.velocity = currentPos > this.optHeight / 2 ? 1 : -1;\n      this.decelerate();\n    }\n  }\n  indexForY(y) {\n    return Math.min(Math.max(Math.abs(Math.round(y / this.optHeight)), 0), this.col.options.length - 1);\n  }\n  onStart(detail) {\n    // We have to prevent default in order to block scrolling under the picker\n    // but we DO NOT have to stop propagation, since we still want\n    // some \"click\" events to capture\n    if (detail.event.cancelable) {\n      detail.event.preventDefault();\n    }\n    detail.event.stopPropagation();\n    hapticSelectionStart();\n    // reset everything\n    if (this.rafId !== undefined) cancelAnimationFrame(this.rafId);\n    const options = this.col.options;\n    let minY = options.length - 1;\n    let maxY = 0;\n    for (let i = 0; i < options.length; i++) {\n      if (!options[i].disabled) {\n        minY = Math.min(minY, i);\n        maxY = Math.max(maxY, i);\n      }\n    }\n    this.minY = -(minY * this.optHeight);\n    this.maxY = -(maxY * this.optHeight);\n  }\n  onMove(detail) {\n    if (detail.event.cancelable) {\n      detail.event.preventDefault();\n    }\n    detail.event.stopPropagation();\n    // update the scroll position relative to pointer start position\n    let y = this.y + detail.deltaY;\n    if (y > this.minY) {\n      // scrolling up higher than scroll area\n      y = Math.pow(y, 0.8);\n      this.bounceFrom = y;\n    } else if (y < this.maxY) {\n      // scrolling down below scroll area\n      y += Math.pow(this.maxY - y, 0.9);\n      this.bounceFrom = y;\n    } else {\n      this.bounceFrom = 0;\n    }\n    this.update(y, 0, false);\n  }\n  onEnd(detail) {\n    if (this.bounceFrom > 0) {\n      // bounce back up\n      this.update(this.minY, 100, true);\n      this.emitColChange();\n      return;\n    } else if (this.bounceFrom < 0) {\n      // bounce back down\n      this.update(this.maxY, 100, true);\n      this.emitColChange();\n      return;\n    }\n    this.velocity = clamp(-90, detail.velocityY * 23, MAX_PICKER_SPEED);\n    if (this.velocity === 0 && detail.deltaY === 0) {\n      const opt = detail.event.target.closest('.picker-opt');\n      if (opt === null || opt === void 0 ? void 0 : opt.hasAttribute('opt-index')) {\n        this.setSelected(parseInt(opt.getAttribute('opt-index'), 10), TRANSITION_DURATION);\n      }\n    } else {\n      this.y += detail.deltaY;\n      if (Math.abs(detail.velocityY) < 0.05) {\n        const isScrollingUp = detail.deltaY > 0;\n        const optHeightFraction = Math.abs(this.y) % this.optHeight / this.optHeight;\n        if (isScrollingUp && optHeightFraction > 0.5) {\n          this.velocity = Math.abs(this.velocity) * -1;\n        } else if (!isScrollingUp && optHeightFraction <= 0.5) {\n          this.velocity = Math.abs(this.velocity);\n        }\n      }\n      this.decelerate();\n    }\n  }\n  refresh(forceRefresh, animated) {\n    var _a;\n    let min = this.col.options.length - 1;\n    let max = 0;\n    const options = this.col.options;\n    for (let i = 0; i < options.length; i++) {\n      if (!options[i].disabled) {\n        min = Math.min(min, i);\n        max = Math.max(max, i);\n      }\n    }\n    /**\n     * Only update selected value if column has a\n     * velocity of 0. If it does not, then the\n     * column is animating might land on\n     * a value different than the value at\n     * selectedIndex\n     */\n    if (this.velocity !== 0) {\n      return;\n    }\n    const selectedIndex = clamp(min, (_a = this.col.selectedIndex) !== null && _a !== void 0 ? _a : 0, max);\n    if (this.col.prevSelected !== selectedIndex || forceRefresh) {\n      const y = selectedIndex * this.optHeight * -1;\n      const duration = animated ? TRANSITION_DURATION : 0;\n      this.velocity = 0;\n      this.update(y, duration, true);\n    }\n  }\n  onDomChange(forceRefresh, animated) {\n    const colEl = this.optsEl;\n    if (colEl) {\n      // DOM READ\n      // We perfom a DOM read over a rendered item, this needs to happen after the first render or after the column has changed\n      this.optHeight = colEl.firstElementChild ? colEl.firstElementChild.clientHeight : 0;\n    }\n    this.refresh(forceRefresh, animated);\n  }\n  render() {\n    const col = this.col;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'ed32d108dd94f0302fb453c31a3497ebae65ec37',\n      class: Object.assign({\n        [mode]: true,\n        'picker-col': true,\n        'picker-opts-left': this.col.align === 'left',\n        'picker-opts-right': this.col.align === 'right'\n      }, getClassMap(col.cssClass)),\n      style: {\n        'max-width': this.col.columnWidth\n      }\n    }, col.prefix && h(\"div\", {\n      key: '9f0634890e66fd4ae74f826d1eea3431de121393',\n      class: \"picker-prefix\",\n      style: {\n        width: col.prefixWidth\n      }\n    }, col.prefix), h(\"div\", {\n      key: '337e996e5be91af16446085fe22436f213b771eb',\n      class: \"picker-opts\",\n      style: {\n        maxWidth: col.optionsWidth\n      },\n      ref: el => this.optsEl = el\n    }, col.options.map((o, index) => h(\"button\", {\n      \"aria-label\": o.ariaLabel,\n      class: {\n        'picker-opt': true,\n        'picker-opt-disabled': !!o.disabled\n      },\n      \"opt-index\": index\n    }, o.text))), col.suffix && h(\"div\", {\n      key: 'd69a132599d78d9e5107f12228978cfce4e43098',\n      class: \"picker-suffix\",\n      style: {\n        width: col.suffixWidth\n      }\n    }, col.suffix));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"col\": [\"colChanged\"]\n    };\n  }\n};\nconst PICKER_OPT_SELECTED = 'picker-opt-selected';\nconst DECELERATION_FRICTION = 0.97;\nconst MAX_PICKER_SPEED = 90;\nconst TRANSITION_DURATION = 150;\nPickerColumnCmp.style = {\n  ios: pickerColumnIosCss,\n  md: pickerColumnMdCss\n};\nexport { Datetime as ion_datetime, Picker as ion_picker_legacy, PickerColumnCmp as ion_picker_legacy_column };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,IAAM,iBAAiB,CAAC,SAAS,UAAU,aAAa;AACtD,MAAI,YAAY,SAAS,OAAO,SAAS;AACvC,WAAO;AAAA,EACT;AACA,MAAI,YAAY,SAAS,OAAO,SAAS;AACvC,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAMA,IAAM,gBAAgB,CAAC,UAAU,UAAU,UAAU,cAAc;AAKjE,MAAI,SAAS,QAAQ,MAAM;AACzB,WAAO;AAAA,EACT;AAMA,MAAI,cAAc,UAAa,CAAC,UAAU,SAAS,SAAS,GAAG,GAAG;AAChE,WAAO;AAAA,EACT;AAYA,MAAI,YAAY,SAAS,UAAU,QAAQ,GAAG;AAC5C,WAAO;AAAA,EACT;AAYA,MAAI,YAAY,QAAQ,UAAU,QAAQ,GAAG;AAC3C,WAAO;AAAA,EACT;AAMA,SAAO;AACT;AAKA,IAAM,sBAAsB,CAAC,QAAQ,UAAU,aAAa,YAAY,UAAU,UAAU,cAAc;AAQxG,QAAM,mBAAmB,MAAM,QAAQ,WAAW,IAAI,cAAc,CAAC,WAAW;AAKhF,QAAM,WAAW,iBAAiB,KAAK,WAAS,UAAU,UAAU,KAAK,CAAC,MAAM;AAChF,QAAM,UAAU,UAAU,UAAU,UAAU;AAC9C,QAAM,WAAW,cAAc,UAAU,UAAU,UAAU,SAAS;AAKtE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc,WAAW,SAAS;AAAA,IAClC,WAAW,qBAAqB,QAAQ,SAAS,QAAQ;AAAA,IACzD,MAAM,SAAS,OAAO,OAAO,OAAO,QAAQ,QAAQ,IAAI;AAAA,EAC1D;AACF;AAKA,IAAM,kBAAkB,CAAC,UAAU;AAAA,EACjC;AAAA,EACA;AACF,MAAM;AAEJ,MAAI,eAAe,SAAS,MAAM,UAAU,QAAQ,GAAG;AACrD,WAAO;AAAA,EACT;AAGA,MAAI,YAAY,SAAS,UAAU,QAAQ,KAAK,YAAY,QAAQ,UAAU,QAAQ,GAAG;AACvF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAMA,IAAM,sBAAsB,CAAC,UAAU,UAAU,aAAa;AAC5D,QAAM,YAAY,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,QAAQ,CAAC,GAAG;AAAA,IAC7E,KAAK;AAAA,EACP,CAAC;AACD,SAAO,gBAAgB,WAAW;AAAA,IAChC;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAKA,IAAM,sBAAsB,CAAC,UAAU,aAAa;AAClD,QAAM,YAAY,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,aAAa,QAAQ,CAAC,GAAG;AAAA,IACzE,KAAK;AAAA,EACP,CAAC;AACD,SAAO,gBAAgB,WAAW;AAAA,IAChC;AAAA,EACF,CAAC;AACH;AAMA,IAAM,qBAAqB,CAAC,kBAAkB,eAAe,OAAO;AAClE,MAAI,MAAM,QAAQ,gBAAgB,GAAG;AACnC,UAAM,wBAAwB,cAAc,MAAM,GAAG,EAAE,CAAC;AACxD,UAAM,oBAAoB,iBAAiB,KAAK,QAAM,GAAG,SAAS,qBAAqB;AACvF,QAAI,mBAAmB;AACrB,aAAO;AAAA,QACL,WAAW,kBAAkB;AAAA,QAC7B,iBAAiB,kBAAkB;AAAA,MACrC;AAAA,IACF;AAAA,EACF,OAAO;AAKL,QAAI;AACF,aAAO,iBAAiB,aAAa;AAAA,IACvC,SAAS,GAAG;AACV,oBAAc,0HAA0H,IAAI,CAAC;AAAA,IAC/I;AAAA,EACF;AACA,SAAO;AACT;AAOA,IAAM,yBAAyB,CAAC,IAAI,kBAAkB;AACpD,MAAI,IAAI,IAAI,IAAI;AAChB,QAAM,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,mBAAmB,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe;AAChkB,oBAAgB,wFAAwF,EAAE;AAAA,EAC5G;AACF;AACA,IAAM,qCAAqC,CAAC,IAAI,cAAc,kBAAkB;AAE9E,MAAI,CAAC,cAAe;AAEpB,UAAQ,cAAc;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,UAAI,cAAc,SAAS,QAAW;AACpC,wBAAgB,yBAAyB,YAAY,2DAA2D,EAAE;AAAA,MACpH;AACA;AAAA,IACF,KAAK;AACH,UAAI,cAAc,SAAS,QAAW;AACpC,wBAAgB,qFAAqF,EAAE;AAAA,MACzG;AACA;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,UAAI,cAAc,SAAS,UAAa,cAAc,SAAS,QAAW;AACxE,wBAAgB,yBAAyB,YAAY,oFAAoF,EAAE;AAAA,MAC7I;AACA;AAAA,EACJ;AACF;AACA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,IAAM,WAAW,MAAM;AAAA,EACrB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,UAAU,UAAU,aAAa;AACtC,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,cAAc,CAAC;AACpB,SAAK,eAAe;AAAA,MAClB,OAAO;AAAA,MACP,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,eAAe;AAAA,IACjB;AACA,SAAK,oBAAoB;AAMzB,SAAK,QAAQ;AAIb,SAAK,OAAO,KAAK;AAIjB,SAAK,WAAW;AAIhB,SAAK,WAAW;AAMhB,SAAK,mBAAmB;AAQxB,SAAK,eAAe;AAIpB,SAAK,aAAa;AAIlB,SAAK,WAAW;AAIhB,SAAK,YAAY;AAOjB,SAAK,SAAS;AAKd,SAAK,iBAAiB;AAKtB,SAAK,WAAW;AAMhB,SAAK,mBAAmB;AASxB,SAAK,qBAAqB;AAS1B,SAAK,kBAAkB;AASvB,SAAK,uBAAuB;AAK5B,SAAK,OAAO;AAaZ,SAAK,cAAc;AACnB,SAAK,4BAA4B,MAAM;AACrC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,CAAC,YAAY,MAAM,QAAQ,KAAK,GAAG;AASrC,wBAAgB;AAAA;AAAA,mBAEL,MAAM,IAAI,OAAK,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC;AAAA,GACnD,KAAK,EAAE;AAAA,MACJ;AAAA,IACF;AACA,SAAK,WAAW,WAAS;AACvB,WAAK,QAAQ;AACb,WAAK,UAAU,KAAK;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH;AAWA,SAAK,6BAA6B,MAAM;AACtC,UAAI;AACJ,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,cAAQ,KAAK,KAAK,cAAc,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,IACtE;AACA,SAAK,gBAAgB,MAAM;AACzB,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,aAAO,MAAM,QAAQ,WAAW,IAAI,YAAY,CAAC,IAAI;AAAA,IACvD;AACA,SAAK,qBAAqB,UAAQ;AAChC,YAAM,iBAAiB,KAAK,GAAG,QAAQ,wBAAwB;AAC/D,UAAI,gBAAgB;AAClB,uBAAe,QAAQ,QAAW,IAAI;AAAA,MACxC;AAAA,IACF;AACA,SAAK,kBAAkB,WAAS;AAC9B,WAAK,eAAe,OAAO,OAAO,CAAC,GAAG,KAAK;AAAA,IAC7C;AACA,SAAK,iBAAiB,CAAC,OAAO,aAAa,UAAU;AAKnD,UAAI,KAAK,UAAU;AACjB;AAAA,MACF;AACA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AAWJ,YAAM,iBAAiB,cAAc,OAAO,UAAU,QAAQ;AAC9D,WAAK,gBAAgB,cAAc;AACnC,UAAI,UAAU;AACZ,cAAM,mBAAmB,MAAM,QAAQ,WAAW,IAAI,cAAc,CAAC,WAAW;AAChF,YAAI,YAAY;AACd,eAAK,cAAc,iBAAiB,OAAO,OAAK,CAAC,UAAU,GAAG,cAAc,CAAC;AAAA,QAC/E,OAAO;AACL,eAAK,cAAc,CAAC,GAAG,kBAAkB,cAAc;AAAA,QACzD;AAAA,MACF,OAAO;AACL,aAAK,cAAc,OAAO,OAAO,CAAC,GAAG,cAAc;AAAA,MACrD;AACA,YAAM,oBAAoB,KAAK,GAAG,cAAc,kBAAkB,MAAM;AACxE,UAAI,qBAAqB,KAAK,oBAAoB;AAChD;AAAA,MACF;AACA,WAAK,QAAQ;AAAA,IACf;AACA,SAAK,8BAA8B,MAAM;AACvC,YAAM,kBAAkB,KAAK;AAC7B,UAAI,CAAC,iBAAiB;AACpB;AAAA,MACF;AACA,YAAM,OAAO,KAAK,GAAG;AAKrB,YAAM,eAAe,gBAAgB,cAAc,gCAAgC;AAOnF,YAAM,yBAAyB,QAAM;AACnC,YAAI;AACJ,cAAM,SAAS,GAAG,CAAC;AAOnB,cAAM,KAAK,OAAO,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,aAAa,MAAM,CAAC,gBAAgB,UAAU,SAAS,aAAa,GAAG;AAClJ;AAAA,QACF;AACA,aAAK,gBAAgB,YAAY;AAAA,MACnC;AACA,YAAM,KAAK,IAAI,iBAAiB,sBAAsB;AACtD,SAAG,QAAQ,iBAAiB;AAAA,QAC1B,iBAAiB,CAAC,OAAO;AAAA,QACzB,mBAAmB;AAAA,MACrB,CAAC;AACD,WAAK,oBAAoB,MAAM;AAC7B,eAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAAA,MACxD;AAKA,sBAAgB,iBAAiB,WAAW,QAAM;AAChD,cAAM,gBAAgB,KAAK;AAC3B,YAAI,CAAC,iBAAiB,CAAC,cAAc,UAAU,SAAS,cAAc,GAAG;AACvE;AAAA,QACF;AACA,cAAM,QAAQ,wBAAwB,aAAa;AACnD,YAAI;AACJ,gBAAQ,GAAG,KAAK;AAAA,UACd,KAAK;AACH,eAAG,eAAe;AAClB,2BAAe,YAAY,KAAK;AAChC;AAAA,UACF,KAAK;AACH,eAAG,eAAe;AAClB,2BAAe,gBAAgB,KAAK;AACpC;AAAA,UACF,KAAK;AACH,eAAG,eAAe;AAClB,2BAAe,WAAW,KAAK;AAC/B;AAAA,UACF,KAAK;AACH,eAAG,eAAe;AAClB,2BAAe,eAAe,KAAK;AACnC;AAAA,UACF,KAAK;AACH,eAAG,eAAe;AAClB,2BAAe,eAAe,KAAK;AACnC;AAAA,UACF,KAAK;AACH,eAAG,eAAe;AAClB,2BAAe,aAAa,KAAK;AACjC;AAAA,UACF,KAAK;AACH,eAAG,eAAe;AAClB,2BAAe,GAAG,WAAW,gBAAgB,KAAK,IAAI,iBAAiB,KAAK;AAC5E;AAAA,UACF,KAAK;AACH,eAAG,eAAe;AAClB,2BAAe,GAAG,WAAW,YAAY,KAAK,IAAI,aAAa,KAAK;AACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOF;AACE;AAAA,QACJ;AAKA,YAAI,cAAc,cAAc,KAAK,UAAU,KAAK,QAAQ,GAAG;AAC7D;AAAA,QACF;AACA,aAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,YAAY,GAAG,YAAY,CAAC;AAKtF,8BAAsB,MAAM,KAAK,gBAAgB,YAAY,CAAC;AAAA,MAChE,CAAC;AAAA,IACH;AACA,SAAK,kBAAkB,kBAAgB;AAMrC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,YAAM,gBAAe,oBAAI,KAAK,GAAG,KAAK,MAAM,IAAI,EAAE,GAAE,OAAO;AAC3D,YAAM,SAAS,gBAAgB,KAAK,iBAAiB,eAAe,KAAK,iBAAiB,KAAK,KAAK,iBAAiB;AACrH,UAAI,QAAQ,MAAM;AAChB;AAAA,MACF;AAKA,YAAM,QAAQ,aAAa,cAAc,qCAAqC,SAAS,GAAG,iBAAiB;AAC3G,UAAI,OAAO;AACT,cAAM,MAAM;AAAA,MACd;AAAA,IACF;AACA,SAAK,kBAAkB,MAAM;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,QAAQ,QAAW;AACrB,aAAK,WAAW;AAChB;AAAA,MACF;AACA,WAAK,WAAW,cAAc,KAAK,YAAY;AAAA,IACjD;AACA,SAAK,kBAAkB,MAAM;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,QAAQ,QAAW;AACrB,aAAK,WAAW;AAChB;AAAA,MACF;AACA,WAAK,WAAW,cAAc,KAAK,YAAY;AAAA,IACjD;AACA,SAAK,6BAA6B,MAAM;AACtC,YAAM,kBAAkB,KAAK;AAC7B,UAAI,CAAC,iBAAiB;AACpB;AAAA,MACF;AAgBA,YAAM,SAAS,gBAAgB,iBAAiB,iBAAiB;AACjE,YAAM,aAAa,OAAO,CAAC;AAC3B,YAAM,eAAe,OAAO,CAAC;AAC7B,YAAM,WAAW,OAAO,CAAC;AACzB,YAAM,OAAO,WAAW,IAAI;AAC5B,YAAM,wBAAwB,SAAS,SAAS,OAAO,cAAc,eAAe,UAAU,iBAAiB;AAO/G,gBAAU,MAAM;AACd,wBAAgB,aAAa,WAAW,eAAe,MAAM,KAAK,EAAE,IAAI,KAAK;AAC7E,cAAM,kBAAkB,WAAS;AAC/B,gBAAM,MAAM,gBAAgB,sBAAsB;AAWlD,gBAAM,YAAY,MAAM,KAAK,EAAE,IAAI,gBAAgB,cAAc,KAAK,gBAAgB,cAAc;AACpG,gBAAM,QAAQ,YAAY,aAAa;AAWvC,gBAAM,WAAW,MAAM,sBAAsB;AAC7C,cAAI,KAAK,IAAI,SAAS,IAAI,IAAI,CAAC,IAAI,EAAG;AAUtC,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,cAAI,oBAAoB,QAAW;AACjC,mBAAO;AAAA,cACL,OAAO,gBAAgB;AAAA,cACvB,MAAM,gBAAgB;AAAA,cACtB,KAAK,gBAAgB;AAAA,YACvB;AAAA,UACF;AAOA,cAAI,UAAU,YAAY;AACxB,mBAAO,iBAAiB,KAAK;AAAA,UAC/B,WAAW,UAAU,UAAU;AAC7B,mBAAO,aAAa,KAAK;AAAA,UAC3B,OAAO;AACL;AAAA,UACF;AAAA,QACF;AACA,cAAM,oBAAoB,MAAM;AAC9B,cAAI,uBAAuB;AACzB,4BAAgB,MAAM,eAAe,gBAAgB;AACrD,sCAA0B;AAAA,UAC5B;AAKA,gBAAM,UAAU,gBAAgB,KAAK,YAAY;AACjD,cAAI,CAAC,QAAS;AACd,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,gBAAgB;AAAA,YAClB;AAAA,YACA;AAAA,YACA,KAAK;AAAA,UACP,GAAG;AAAA,YACD,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,GAAG;AAAA,cACxD,KAAK;AAAA,YACP,CAAC;AAAA,YACD,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,GAAG;AAAA,cACxD,KAAK;AAAA,YACP,CAAC;AAAA,UACH,CAAC,GAAG;AACF;AAAA,UACF;AAMA,0BAAgB,MAAM,YAAY,YAAY,QAAQ;AAUtD,oBAAU,MAAM;AACd,iBAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,YAAY,GAAG;AAAA,cACvE;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC,CAAC;AACF,4BAAgB,aAAa,aAAa,eAAe,MAAM,KAAK,EAAE,IAAI,KAAK;AAC/E,4BAAgB,MAAM,eAAe,UAAU;AAC/C,gBAAI,KAAK,2BAA2B;AAClC,mBAAK,0BAA0B;AAAA,YACjC;AAAA,UACF,CAAC;AAAA,QACH;AAKA,YAAI;AAMJ,YAAI,0BAA0B;AAC9B,cAAM,iBAAiB,MAAM;AAC3B,cAAI,eAAe;AACjB,yBAAa,aAAa;AAAA,UAC5B;AAUA,cAAI,CAAC,2BAA2B,uBAAuB;AACrD,4BAAgB,MAAM,YAAY,kBAAkB,MAAM;AAC1D,sCAA0B;AAAA,UAC5B;AAEA,0BAAgB,WAAW,mBAAmB,EAAE;AAAA,QAClD;AACA,wBAAgB,iBAAiB,UAAU,cAAc;AACzD,aAAK,0BAA0B,MAAM;AACnC,0BAAgB,oBAAoB,UAAU,cAAc;AAAA,QAC9D;AAAA,MACF,CAAC;AAAA,IACH;AAMA,SAAK,8BAA8B,MAAM;AACvC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,4BAA4B,QAAW;AACzC,gCAAwB;AAAA,MAC1B;AACA,UAAI,sBAAsB,QAAW;AACnC,0BAAkB;AAAA,MACpB;AAAA,IACF;AACA,SAAK,eAAe,WAAS;AAC3B,YAAM,WAAW,UAAU,QAAQ,UAAU,UAAa,UAAU,OAAO,CAAC,MAAM,QAAQ,KAAK,KAAK,MAAM,SAAS;AACnH,YAAM,iBAAiB,WAAW,UAAU,KAAK,IAAI,KAAK;AAC1D,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,WAAK,0BAA0B;AAK/B,UAAI,CAAC,gBAAgB;AACnB;AAAA,MACF;AASA,UAAI,UAAU;AACZ,+BAAuB,gBAAgB,UAAU,QAAQ;AAAA,MAC3D;AAMA,YAAM,cAAc,MAAM,QAAQ,cAAc,IAAI,eAAe,eAAe,SAAS,CAAC,IAAI;AAChG,YAAM,cAAc,UAAU,aAAa,UAAU,QAAQ;AAC7D,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,OAAO,UAAU,IAAI;AAM3B,UAAI,UAAU;AACZ,YAAI,MAAM,QAAQ,cAAc,GAAG;AACjC,eAAK,cAAc,CAAC,GAAG,cAAc;AAAA,QACvC,OAAO;AACL,eAAK,cAAc;AAAA,YACjB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF,OAAO;AAML,aAAK,cAAc,CAAC;AAAA,MACtB;AACA,YAAM,iBAAiB,UAAU,UAAa,UAAU,aAAa,SAAS,SAAS,UAAa,SAAS,aAAa;AAC1H,YAAM,gBAAgB,GAAG,UAAU,SAAS,gBAAgB;AAC5D,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,eAAe,kBAAkB,iBAAiB,CAAC,kBAAkB;AAQvE,aAAK,cAAc,WAAW;AAAA,MAChC,OAAO;AACL,aAAK,gBAAgB;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,SAAK,gBAAgB,CAAM,gBAAe;AACxC,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AAOJ,WAAK,kBAAkB;AAQvB,YAAM,4BAA4B,IAAI,QAAQ,aAAW;AACvD,aAAK,4BAA4B;AAAA,MACnC,CAAC;AAKD,YAAM,sBAAsB,SAAS,aAAa,YAAY;AAC9D,4BAAsB,KAAK,UAAU,IAAI,KAAK,UAAU;AACxD,YAAM;AACN,WAAK,4BAA4B;AACjC,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,UAAU,MAAM;AACnB,WAAK,SAAS,KAAK;AAAA,IACrB;AACA,SAAK,SAAS,MAAM;AAClB,WAAK,QAAQ,KAAK;AAAA,IACpB;AACA,SAAK,WAAW,MAAM;AACpB,aAAO,KAAK,SAAS;AAAA,IACvB;AACA,SAAK,YAAY,MAAM;AACrB,YAAM,kBAAkB,KAAK;AAC7B,UAAI,CAAC,iBAAiB;AACpB;AAAA,MACF;AACA,YAAM,YAAY,gBAAgB,cAAc,8BAA8B;AAC9E,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AACA,YAAM,OAAO,UAAU,cAAc;AACrC,sBAAgB,SAAS;AAAA,QACvB,KAAK;AAAA,QACL,MAAM,QAAQ,MAAM,KAAK,EAAE,IAAI,KAAK;AAAA,QACpC,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,SAAK,YAAY,MAAM;AACrB,YAAM,kBAAkB,KAAK;AAC7B,UAAI,CAAC,iBAAiB;AACpB;AAAA,MACF;AACA,YAAM,YAAY,gBAAgB,cAAc,+BAA+B;AAC/E,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AACA,sBAAgB,SAAS;AAAA,QACvB,KAAK;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,SAAK,yBAAyB,MAAM;AAClC,WAAK,mBAAmB,CAAC,KAAK;AAAA,IAChC;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,uCAAmC,IAAI,cAAc,aAAa;AAClE,2BAAuB,IAAI,aAAa;AAAA,EAC1C;AAAA,EACA,kBAAkB;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,aAAa;AACX,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,aAAa;AACX,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,sBAAsB;AACpB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,uCAAmC,IAAI,cAAc,aAAa;AAAA,EACpE;AAAA,EACA,IAAI,cAAc;AAChB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,sBAAsB,iBAAiB,UAAU,iBAAiB,eAAe,iBAAiB;AACxG,WAAO,uBAAuB,CAAC;AAAA,EACjC;AAAA,EACA,oBAAoB;AAClB,SAAK,mBAAmB,wBAAwB,KAAK,UAAU;AAAA,EACjE;AAAA,EACA,qBAAqB;AACnB,SAAK,oBAAoB,wBAAwB,KAAK,WAAW;AAAA,EACnE;AAAA,EACA,mBAAmB;AACjB,SAAK,kBAAkB,wBAAwB,KAAK,SAAS;AAAA,EAC/D;AAAA,EACA,oBAAoB;AAClB,SAAK,mBAAmB,wBAAwB,KAAK,UAAU;AAAA,EACjE;AAAA,EACA,sBAAsB;AACpB,SAAK,qBAAqB,wBAAwB,KAAK,YAAY;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA,EAIM,eAAe;AAAA;AACnB,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,KAAK,SAAS,GAAG;AACnB,aAAK,aAAa,KAAK;AAAA,MACzB;AACA,WAAK,UAAU;AACf,WAAK,eAAe,KAAK;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,QAAQ,eAAe,OAAO;AAAA;AAClC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AAIJ,UAAI,gBAAgB,UAAa,CAAC,kBAAkB;AAClD,cAAM,qBAAqB,MAAM,QAAQ,WAAW;AACpD,YAAI,sBAAsB,YAAY,WAAW,GAAG;AAClD,cAAI,aAAa;AAMf,iBAAK,SAAS,iBAAiB,YAAY,CAAC;AAAA,UAC9C,OAAO;AACL,iBAAK,SAAS,MAAS;AAAA,UACzB;AAAA,QACF,OAAO;AACL,eAAK,SAAS,iBAAiB,WAAW,CAAC;AAAA,QAC7C;AAAA,MACF;AACA,UAAI,cAAc;AAChB,aAAK,mBAAmB,YAAY;AAAA,MACtC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,MAAM,WAAW;AAAA;AACrB,WAAK,aAAa,SAAS;AAAA,IAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,OAAO,eAAe,OAAO;AAAA;AACjC,WAAK,UAAU,KAAK;AACpB,UAAI,cAAc;AAChB,aAAK,mBAAmB,WAAW;AAAA,MACrC;AAAA,IACF;AAAA;AAAA,EACA,IAAI,mBAAmB;AACrB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,iBAAiB,UAAU,iBAAiB,eAAe,iBAAiB;AAAA,EACrF;AAAA,EACA,oBAAoB;AAClB,SAAK,oBAAoB,kBAAkB,KAAK,EAAE,EAAE;AAAA,EACtD;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB;AACvB,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,SAAK,2BAA2B;AAChC,SAAK,4BAA4B;AAAA,EACnC;AAAA,EACA,mBAAmB;AACjB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AAQJ,UAAM,kBAAkB,aAAW;AACjC,YAAM,KAAK,QAAQ,CAAC;AACpB,UAAI,CAAC,GAAG,gBAAgB;AACtB;AAAA,MACF;AACA,WAAK,oBAAoB;AASzB,gBAAU,MAAM;AACd,aAAK,GAAG,UAAU,IAAI,gBAAgB;AAAA,MACxC,CAAC;AAAA,IACH;AACA,UAAM,YAAY,IAAI,qBAAqB,iBAAiB;AAAA,MAC1D,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAOD,QAAI,MAAM,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,QAAQ,sBAAsB,CAAC;AAQzG,UAAM,iBAAiB,aAAW;AAChC,YAAM,KAAK,QAAQ,CAAC;AACpB,UAAI,GAAG,gBAAgB;AACrB;AAAA,MACF;AACA,WAAK,4BAA4B;AAQjC,WAAK,mBAAmB;AACxB,gBAAU,MAAM;AACd,aAAK,GAAG,UAAU,OAAO,gBAAgB;AAAA,MAC3C,CAAC;AAAA,IACH;AACA,UAAM,WAAW,IAAI,qBAAqB,gBAAgB;AAAA,MACxD,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AACD,QAAI,MAAM,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,QAAQ,sBAAsB,CAAC;AAUtG,UAAM,OAAO,eAAe,KAAK,EAAE;AACnC,SAAK,iBAAiB,YAAY,QAAM,GAAG,gBAAgB,CAAC;AAC5D,SAAK,iBAAiB,WAAW,QAAM,GAAG,gBAAgB,CAAC;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAcJ,UAAM,kBAAkB,CAAC,eAAe,CAAC,aAAa,aAAa,MAAM,EAAE,SAAS,YAAY;AAChG,QAAI,aAAa,UAAa,mBAAmB,iBAAiB;AAChE,YAAM,eAAe,gBAAgB,cAAc,gCAAgC;AAcnF,UAAI,gBAAgB,oBAAoB,QAAW;AACjD,wBAAgB,aAAa,aAAa,eAAe,MAAM,KAAK,EAAE,IAAI,KAAK;AAAA,MACjF;AAAA,IACF;AACA,QAAI,qBAAqB,MAAM;AAC7B,WAAK,mBAAmB;AACxB;AAAA,IACF;AACA,QAAI,iBAAiB,kBAAkB;AACrC;AAAA,IACF;AACA,SAAK,mBAAmB;AACxB,SAAK,4BAA4B;AACjC,SAAK,oBAAoB;AAMzB,SAAK,mBAAmB;AACxB,QAAI,MAAM;AACR,WAAK,UAAU,KAAK;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,UAAU;AACZ,UAAI,iBAAiB,QAAQ;AAC3B,wBAAgB,uFAAuF,EAAE;AAAA,MAC3G;AACA,UAAI,aAAa;AACf,wBAAgB,sFAAsF,EAAE;AAAA,MAC1G;AAAA,IACF;AACA,QAAI,qBAAqB,QAAW;AAClC,UAAI,iBAAiB,UAAU,iBAAiB,eAAe,iBAAiB,aAAa;AAC3F,wBAAgB,2HAA2H,EAAE;AAAA,MAC/I;AACA,UAAI,aAAa;AACf,wBAAgB,4FAA4F,EAAE;AAAA,MAChH;AAAA,IACF;AACA,QAAI,eAAe;AACjB,yCAAmC,IAAI,cAAc,aAAa;AAClE,6BAAuB,IAAI,aAAa;AAAA,IAC1C;AACA,UAAM,aAAa,KAAK,mBAAmB,wBAAwB,KAAK,UAAU;AAClF,UAAM,eAAe,KAAK,qBAAqB,wBAAwB,KAAK,YAAY;AACxF,UAAM,cAAc,KAAK,oBAAoB,wBAAwB,KAAK,WAAW;AACrF,UAAM,aAAa,KAAK,mBAAmB,wBAAwB,KAAK,UAAU;AAClF,UAAM,YAAY,KAAK,kBAAkB,wBAAwB,KAAK,SAAS;AAC/E,UAAM,aAAa,KAAK,aAAa,UAAU,SAAS,CAAC;AACzD,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,eAAe,oBAAoB;AAAA,MACtC,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU,KAAK;AAAA,MACf,UAAU,KAAK;AAAA,IACjB,CAAC;AACD,SAAK,aAAa,KAAK,KAAK;AAC5B,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,YAAY;AACV,SAAK,SAAS,KAAK;AAAA,MACjB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB,KAAK;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AACb,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAMJ,UAAM,mBAAmB,YAAY;AACrC,UAAM,oBAAoB,KAAK,GAAG,cAAc,kBAAkB,MAAM;AACxE,QAAI,CAAC,qBAAqB,CAAC,sBAAsB,CAAC,iBAAiB;AACjE;AAAA,IACF;AACA,UAAM,mBAAmB,MAAM;AAC7B,WAAK,MAAM;AACX,WAAK,SAAS,MAAS;AAAA,IACzB;AAQA,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,OAAO;AAAA,QACL,CAAC,yBAAyB,GAAG;AAAA,QAC7B,CAAC,kBAAkB,GAAG,KAAK;AAAA,MAC7B;AAAA,IACF,GAAG,EAAE,QAAQ;AAAA,MACX,MAAM;AAAA,IACR,GAAG,EAAE,eAAe,MAAM,sBAAsB,EAAE,cAAc;AAAA,MAC9D,IAAI;AAAA,MACJ,OAAO,KAAK;AAAA,MACZ,SAAS,MAAM,KAAK,OAAO,IAAI;AAAA,MAC/B,UAAU;AAAA,IACZ,GAAG,KAAK,UAAU,GAAG,EAAE,OAAO;AAAA,MAC5B,OAAO;AAAA,IACT,GAAG,mBAAmB,EAAE,cAAc;AAAA,MACpC,IAAI;AAAA,MACJ,OAAO,KAAK;AAAA,MACZ,SAAS,MAAM,iBAAiB;AAAA,MAChC,UAAU;AAAA,IACZ,GAAG,KAAK,SAAS,GAAG,sBAAsB,EAAE,cAAc;AAAA,MACxD,IAAI;AAAA,MACJ,OAAO,KAAK;AAAA,MACZ,SAAS,MAAM,KAAK,QAAQ,IAAI;AAAA,MAChC,UAAU;AAAA,IACZ,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,oBAAoB,KAAK,cAAc;AAUvD,UAAM,cAAc,sBAAsB,cAAc,CAAC,KAAK,wBAAwB,iBAAiB,GAAG,KAAK,wBAAwB,iBAAiB,CAAC,IAAI,CAAC,KAAK,wBAAwB,iBAAiB,GAAG,KAAK,wBAAwB,iBAAiB,CAAC;AAC9P,WAAO,EAAE,cAAc,MAAM,WAAW;AAAA,EAC1C;AAAA,EACA,wBAAwB,mBAAmB;AACzC,WAAO,sBAAsB,eAAe,sBAAsB,cAAc,KAAK,+BAA+B,IAAI,KAAK,kCAAkC,iBAAiB;AAAA,EAClL;AAAA,EACA,iCAAiC;AAC/B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,aAAa,KAAK,2BAA2B;AAKnD,UAAM,iBAAiB,eAAe,YAAY;AAClD,UAAM,YAAY,eAAe,eAAe,SAAS,CAAC;AAI1D,mBAAe,CAAC,EAAE,MAAM;AACxB,cAAU,MAAM,kBAAkB,UAAU,OAAO,UAAU,IAAI;AASjE,UAAM,MAAM,aAAa,UAAa,QAAQ,UAAU,eAAe,CAAC,CAAC,IAAI,WAAW,eAAe,CAAC;AACxG,UAAM,MAAM,aAAa,UAAa,SAAS,UAAU,SAAS,IAAI,WAAW;AACjF,UAAM,SAAS,0BAA0B,QAAQ,YAAY,KAAK,KAAK,KAAK,iBAAiB,KAAK,iBAAiB;AACnH,QAAI,QAAQ,OAAO;AACnB,UAAM,QAAQ,OAAO;AACrB,QAAI,eAAe;AACjB,cAAQ,MAAM,IAAI,CAAC,YAAY,UAAU;AACvC,cAAM,iBAAiB,MAAM,KAAK;AAClC,YAAIA;AACJ,YAAI;AAMF,UAAAA,YAAW,CAAC,cAAc,iBAAiB,cAAc,CAAC;AAAA,QAC5D,SAAS,GAAG;AACV,wBAAc,uHAAuH,CAAC;AAAA,QACxI;AACA,eAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG;AAAA,UAClD,UAAAA;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAKA,UAAM,cAAc,aAAa,QAAQ,OAAO,GAAG,aAAa,IAAI,IAAI,aAAa,KAAK,IAAI,aAAa,GAAG,KAAK,GAAG,aAAa,IAAI,IAAI,aAAa,KAAK,IAAI,aAAa,GAAG;AACjL,WAAO,EAAE,qBAAqB;AAAA,MAC5B,cAAc;AAAA,MACd,OAAO;AAAA,MACP,OAAO,KAAK;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,MACP,aAAa,QAAM;AACjB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI,GAAG;AACP,cAAM,WAAW,MAAM,KAAK,CAAC;AAAA,UAC3B;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,UAAU,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE;AACzC,aAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,QAAQ,CAAC;AAC7E,aAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG,QAAQ,CAAC;AAC1E,WAAG,gBAAgB;AAAA,MACrB;AAAA,IACF,GAAG,MAAM,IAAI,UAAQ,EAAE,4BAA4B;AAAA,MACjD,MAAM,KAAK,UAAU,cAAc,GAAG,eAAe,IAAI,sBAAsB,KAAK;AAAA,MACpF,KAAK,KAAK;AAAA,MACV,UAAU,KAAK;AAAA,MACf,OAAO,KAAK;AAAA,IACd,GAAG,KAAK,IAAI,CAAC,CAAC;AAAA,EAChB;AAAA,EACA,kCAAkC,mBAAmB;AACnD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,qBAAqB,sBAAsB,UAAU,sBAAsB;AACjF,UAAM,SAAS,qBAAqB,mBAAmB,KAAK,QAAQ,cAAc,KAAK,UAAU,KAAK,UAAU,KAAK,iBAAiB,IAAI,CAAC;AAC3I,UAAM,mBAAmB,sBAAsB;AAC/C,QAAI,OAAO,mBAAmB,iBAAiB,KAAK,QAAQ,cAAc,KAAK,UAAU,KAAK,UAAU,KAAK,eAAe,IAAI,CAAC;AACjI,QAAI,eAAe;AACjB,aAAO,KAAK,IAAI,eAAa;AAC3B,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,WAAW,OAAO,UAAU,WAAW,SAAS,KAAK,IAAI;AAC/D,cAAM,iBAAiB;AAAA,UACrB,OAAO,aAAa;AAAA,UACpB,KAAK;AAAA,UACL,MAAM,aAAa;AAAA,QACrB;AACA,YAAI;AACJ,YAAI;AAMF,qBAAW,CAAC,cAAc,iBAAiB,cAAc,CAAC;AAAA,QAC5D,SAAS,GAAG;AACV,wBAAc,uHAAuH,CAAC;AAAA,QACxI;AACA,eAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,SAAS,GAAG;AAAA,UACjD;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,UAAM,oBAAoB,sBAAsB,WAAW,sBAAsB;AACjF,UAAM,QAAQ,oBAAoB,kBAAkB,KAAK,QAAQ,KAAK,cAAc,KAAK,UAAU,KAAK,UAAU,KAAK,gBAAgB,IAAI,CAAC;AAI5I,UAAM,iBAAiB,mBAAmB,KAAK,QAAQ;AAAA,MACrD,OAAO;AAAA,MACP,KAAK;AAAA,IACP,CAAC;AACD,QAAI,cAAc,CAAC;AACnB,QAAI,gBAAgB;AAClB,oBAAc,CAAC,KAAK,wBAAwB,MAAM,GAAG,KAAK,sBAAsB,IAAI,GAAG,KAAK,uBAAuB,KAAK,CAAC;AAAA,IAC3H,OAAO;AACL,oBAAc,CAAC,KAAK,sBAAsB,IAAI,GAAG,KAAK,wBAAwB,MAAM,GAAG,KAAK,uBAAuB,KAAK,CAAC;AAAA,IAC3H;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB,MAAM;AAC1B,QAAI;AACJ,QAAI,KAAK,WAAW,GAAG;AACrB,aAAO,CAAC;AAAA,IACV;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,aAAa,KAAK,2BAA2B;AACnD,UAAM,qBAAqB,KAAK,aAAa,QAAQ,OAAO,aAAa,MAAM,KAAK,aAAa,SAAS,QAAQ,OAAO,SAAS,KAAK;AACvI,WAAO,EAAE,qBAAqB;AAAA,MAC5B,cAAc;AAAA,MACd,OAAO;AAAA,MACP,OAAO,KAAK;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,MACP,aAAa,QAAM;AACjB,aAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG;AAAA,UAClE,KAAK,GAAG,OAAO;AAAA,QACjB,CAAC,CAAC;AACF,aAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG;AAAA,UAC/D,KAAK,GAAG,OAAO;AAAA,QACjB,CAAC,CAAC;AACF,WAAG,gBAAgB;AAAA,MACrB;AAAA,IACF,GAAG,KAAK,IAAI,SAAO,EAAE,4BAA4B;AAAA,MAC/C,MAAM,IAAI,UAAU,oBAAoB,GAAG,eAAe,IAAI,sBAAsB,KAAK;AAAA,MACzF,KAAK,IAAI;AAAA,MACT,UAAU,IAAI;AAAA,MACd,OAAO,IAAI;AAAA,IACb,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,EACf;AAAA,EACA,wBAAwB,QAAQ;AAC9B,QAAI,OAAO,WAAW,GAAG;AACvB,aAAO,CAAC;AAAA,IACV;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,aAAa,KAAK,2BAA2B;AACnD,WAAO,EAAE,qBAAqB;AAAA,MAC5B,cAAc;AAAA,MACd,OAAO;AAAA,MACP,OAAO,KAAK;AAAA,MACZ;AAAA,MACA,OAAO,aAAa;AAAA,MACpB,aAAa,QAAM;AACjB,aAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG;AAAA,UAClE,OAAO,GAAG,OAAO;AAAA,QACnB,CAAC,CAAC;AACF,aAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG;AAAA,UAC/D,OAAO,GAAG,OAAO;AAAA,QACnB,CAAC,CAAC;AACF,WAAG,gBAAgB;AAAA,MACrB;AAAA,IACF,GAAG,OAAO,IAAI,WAAS,EAAE,4BAA4B;AAAA,MACnD,MAAM,MAAM,UAAU,aAAa,QAAQ,GAAG,eAAe,IAAI,sBAAsB,KAAK;AAAA,MAC5F,KAAK,MAAM;AAAA,MACX,UAAU,MAAM;AAAA,MAChB,OAAO,MAAM;AAAA,IACf,GAAG,MAAM,IAAI,CAAC,CAAC;AAAA,EACjB;AAAA,EACA,uBAAuB,OAAO;AAC5B,QAAI,MAAM,WAAW,GAAG;AACtB,aAAO,CAAC;AAAA,IACV;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,aAAa,KAAK,2BAA2B;AACnD,WAAO,EAAE,qBAAqB;AAAA,MAC5B,cAAc;AAAA,MACd,OAAO;AAAA,MACP,OAAO,KAAK;AAAA,MACZ;AAAA,MACA,OAAO,aAAa;AAAA,MACpB,aAAa,QAAM;AACjB,aAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG;AAAA,UAClE,MAAM,GAAG,OAAO;AAAA,QAClB,CAAC,CAAC;AACF,aAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG;AAAA,UAC/D,MAAM,GAAG,OAAO;AAAA,QAClB,CAAC,CAAC;AACF,WAAG,gBAAgB;AAAA,MACrB;AAAA,IACF,GAAG,MAAM,IAAI,UAAQ,EAAE,4BAA4B;AAAA,MACjD,MAAM,KAAK,UAAU,aAAa,OAAO,GAAG,eAAe,IAAI,sBAAsB,KAAK;AAAA,MAC1F,KAAK,KAAK;AAAA,MACV,UAAU,KAAK;AAAA,MACf,OAAO,KAAK;AAAA,IACd,GAAG,KAAK,IAAI,CAAC,CAAC;AAAA,EAChB;AAAA,EACA,wBAAwB,mBAAmB;AACzC,QAAI,CAAC,QAAQ,SAAS,cAAc,MAAM,EAAE,SAAS,iBAAiB,GAAG;AACvE,aAAO,CAAC;AAAA,IACV;AAUA,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,sBAAsB,eAAe;AAC3C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,mBAAmB,KAAK,QAAQ,KAAK,cAAc,KAAK,WAAW,sBAAsB,KAAK,WAAW,QAAW,sBAAsB,KAAK,WAAW,QAAW,KAAK,kBAAkB,KAAK,kBAAkB;AACvN,WAAO,CAAC,KAAK,uBAAuB,SAAS,GAAG,KAAK,yBAAyB,WAAW,GAAG,KAAK,4BAA4B,aAAa,CAAC;AAAA,EAC7I;AAAA,EACA,uBAAuB,WAAW;AAChC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,UAAU,WAAW,EAAG,QAAO,CAAC;AACpC,UAAM,aAAa,KAAK,2BAA2B;AACnD,WAAO,EAAE,qBAAqB;AAAA,MAC5B,cAAc;AAAA,MACd,OAAO,KAAK;AAAA,MACZ;AAAA,MACA,OAAO,WAAW;AAAA,MAClB,cAAc;AAAA,MACd,aAAa,QAAM;AACjB,aAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG;AAAA,UAClE,MAAM,GAAG,OAAO;AAAA,QAClB,CAAC,CAAC;AACF,aAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,2BAA2B,CAAC,GAAG;AAAA,UACtF,MAAM,GAAG,OAAO;AAAA,QAClB,CAAC,CAAC;AACF,WAAG,gBAAgB;AAAA,MACrB;AAAA,IACF,GAAG,UAAU,IAAI,UAAQ,EAAE,4BAA4B;AAAA,MACrD,MAAM,KAAK,UAAU,WAAW,OAAO,GAAG,eAAe,IAAI,sBAAsB,KAAK;AAAA,MACxF,KAAK,KAAK;AAAA,MACV,UAAU,KAAK;AAAA,MACf,OAAO,KAAK;AAAA,IACd,GAAG,KAAK,IAAI,CAAC,CAAC;AAAA,EAChB;AAAA,EACA,yBAAyB,aAAa;AACpC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,YAAY,WAAW,EAAG,QAAO,CAAC;AACtC,UAAM,aAAa,KAAK,2BAA2B;AACnD,WAAO,EAAE,qBAAqB;AAAA,MAC5B,cAAc;AAAA,MACd,OAAO,KAAK;AAAA,MACZ;AAAA,MACA,OAAO,WAAW;AAAA,MAClB,cAAc;AAAA,MACd,aAAa,QAAM;AACjB,aAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG;AAAA,UAClE,QAAQ,GAAG,OAAO;AAAA,QACpB,CAAC,CAAC;AACF,aAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,2BAA2B,CAAC,GAAG;AAAA,UACtF,QAAQ,GAAG,OAAO;AAAA,QACpB,CAAC,CAAC;AACF,WAAG,gBAAgB;AAAA,MACrB;AAAA,IACF,GAAG,YAAY,IAAI,YAAU,EAAE,4BAA4B;AAAA,MACzD,MAAM,OAAO,UAAU,WAAW,SAAS,GAAG,eAAe,IAAI,sBAAsB,KAAK;AAAA,MAC5F,KAAK,OAAO;AAAA,MACZ,UAAU,OAAO;AAAA,MACjB,OAAO,OAAO;AAAA,IAChB,GAAG,OAAO,IAAI,CAAC,CAAC;AAAA,EAClB;AAAA,EACA,4BAA4B,eAAe;AACzC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,cAAc,WAAW,GAAG;AAC9B,aAAO,CAAC;AAAA,IACV;AACA,UAAM,aAAa,KAAK,2BAA2B;AACnD,UAAM,iBAAiB,qBAAqB,KAAK,MAAM;AACvD,WAAO,EAAE,qBAAqB;AAAA,MAC5B,cAAc;AAAA,MACd,OAAO,iBAAiB;AAAA,QACtB,OAAO;AAAA,MACT,IAAI,CAAC;AAAA,MACL,OAAO,KAAK;AAAA,MACZ;AAAA,MACA,OAAO,WAAW;AAAA,MAClB,aAAa,QAAM;AACjB,cAAM,OAAO,sBAAsB,cAAc,GAAG,OAAO,KAAK;AAChE,aAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG;AAAA,UAClE,MAAM,GAAG,OAAO;AAAA,UAChB;AAAA,QACF,CAAC,CAAC;AACF,aAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,2BAA2B,CAAC,GAAG;AAAA,UACtF,MAAM,GAAG,OAAO;AAAA,UAChB;AAAA,QACF,CAAC,CAAC;AACF,WAAG,gBAAgB;AAAA,MACrB;AAAA,IACF,GAAG,cAAc,IAAI,eAAa,EAAE,4BAA4B;AAAA,MAC9D,MAAM,UAAU,UAAU,WAAW,OAAO,GAAG,eAAe,IAAI,sBAAsB,KAAK;AAAA,MAC7F,KAAK,UAAU;AAAA,MACf,UAAU,UAAU;AAAA,MACpB,OAAO,UAAU;AAAA,IACnB,GAAG,UAAU,IAAI,CAAC,CAAC;AAAA,EACrB;AAAA,EACA,gBAAgB,mBAAmB;AACjC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,iBAAiB,mBAAmB,MAAM;AAChD,UAAM,cAAc,iBAAiB,gBAAgB;AACrD,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,QACL,CAAC,eAAe,WAAW,EAAE,GAAG;AAAA,MAClC;AAAA,IACF,GAAG,KAAK,kBAAkB,iBAAiB,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB,MAAM;AACzB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,SAAS,QAAQ,cAAc;AACpD,UAAM,gBAAgB,SAAS,QAAQ,iBAAiB;AACxD,UAAM,oBAAoB,YAAY,oBAAoB,KAAK,cAAc,KAAK,UAAU,KAAK,QAAQ;AACzG,UAAM,oBAAoB,YAAY,oBAAoB,KAAK,cAAc,KAAK,QAAQ;AAE1F,UAAM,UAAU,KAAK,GAAG,aAAa,KAAK,KAAK;AAC/C,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,OAAO;AAAA,IACT,GAAG,EAAE,UAAU;AAAA,MACb,OAAO;AAAA,QACL,8BAA8B;AAAA,QAC9B,mBAAmB;AAAA,QACnB,iBAAiB;AAAA,MACnB;AAAA,MACA,MAAM;AAAA,MACN;AAAA,MACA,cAAc,KAAK,mBAAmB,qBAAqB;AAAA,MAC3D,SAAS,MAAM,KAAK,uBAAuB;AAAA,IAC7C,GAAG,EAAE,QAAQ;AAAA,MACX,IAAI;AAAA,IACN,GAAG,gBAAgB,KAAK,QAAQ,KAAK,YAAY,GAAG,EAAE,YAAY;AAAA,MAChE,eAAe;AAAA,MACf,MAAM,KAAK,mBAAmB,eAAe;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC,CAAC,GAAG,SAAS,QAAQ,EAAE,qBAAqB,IAAI,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,MAC7D,OAAO;AAAA,IACT,GAAG,EAAE,eAAe,MAAM,EAAE,cAAc;AAAA,MACxC,cAAc;AAAA,MACd,UAAU;AAAA,MACV,SAAS,MAAM,KAAK,UAAU;AAAA,IAChC,GAAG,EAAE,YAAY;AAAA,MACf,KAAK;AAAA,MACL,eAAe;AAAA,MACf,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC,CAAC,GAAG,EAAE,cAAc;AAAA,MACnB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,SAAS,MAAM,KAAK,UAAU;AAAA,IAChC,GAAG,EAAE,YAAY;AAAA,MACf,KAAK;AAAA,MACL,eAAe;AAAA,MACf,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,MACf,OAAO;AAAA,MACP,eAAe;AAAA,IACjB,GAAG,cAAc,KAAK,QAAQ,MAAM,KAAK,iBAAiB,CAAC,EAAE,IAAI,OAAK;AACpE,aAAO,EAAE,OAAO;AAAA,QACd,OAAO;AAAA,MACT,GAAG,CAAC;AAAA,IACN,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,YAAY,OAAO,MAAM;AACvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,cAAc,KAAK,qBAAqB,UAAa,KAAK,iBAAiB,SAAS,IAAI;AAC9F,UAAM,eAAe,KAAK,sBAAsB,UAAa,KAAK,kBAAkB,SAAS,KAAK;AAClG,UAAM,qBAAqB,CAAC,eAAe,CAAC;AAC5C,UAAM,qBAAqB,YAAY;AACvC,UAAM,gBAAgB,YAAY,gBAAgB;AAAA,MAChD;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACP,GAAG;AAAA;AAAA;AAAA;AAAA,MAID,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,GAAG;AAAA,QACxD,KAAK;AAAA,MACP,CAAC;AAAA,MACD,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,GAAG;AAAA,QACxD,KAAK;AAAA,MACP,CAAC;AAAA,IACH,CAAC;AAID,UAAM,iBAAiB,KAAK,aAAa,UAAU,SAAS,KAAK,aAAa,SAAS;AACvF,UAAM,aAAa,KAAK,2BAA2B;AACnD,WAAO,EAAE,OAAO;AAAA,MACd,eAAe,CAAC,iBAAiB,SAAS;AAAA,MAC1C,OAAO;AAAA,QACL,kBAAkB;AAAA;AAAA,QAElB,2BAA2B,CAAC,kBAAkB;AAAA,MAChD;AAAA,IACF,GAAG,EAAE,OAAO;AAAA,MACV,OAAO;AAAA,IACT,GAAG,eAAe,OAAO,MAAM,KAAK,iBAAiB,GAAG,KAAK,gBAAgB,EAAE,IAAI,CAAC,YAAY,UAAU;AACxG,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,SAAS;AACb,UAAI,QAAQ;AACZ,UAAI,oBAAoB,iBAAiB,QAAQ,MAAM;AACrD,YAAI,MAAM,IAAI;AAGZ,cAAI,UAAU,GAAG;AACf,oBAAQ,OAAO;AACf,qBAAS;AAAA,UACX,OAAO;AACL,qBAAS,QAAQ;AAAA,UACnB;AAAA,QACF,WAAW,MAAM,IAAI;AAGnB,cAAI,UAAU,IAAI;AAChB,oBAAQ,OAAO;AACf,qBAAS;AAAA,UACX,OAAO;AACL,qBAAS,QAAQ;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AACA,YAAM,iBAAiB;AAAA,QACrB,OAAO;AAAA,QACP;AAAA,QACA,MAAM;AAAA,QACN;AAAA,MACF;AACA,YAAM,oBAAoB,QAAQ;AAClC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAUC;AAAA,QACV;AAAA,MACF,IAAI,oBAAoB,KAAK,QAAQ,gBAAgB,KAAK,aAAa,KAAK,YAAY,KAAK,UAAU,KAAK,UAAU,KAAK,eAAe;AAC1I,YAAM,gBAAgB,iBAAiB,cAAc;AACrD,UAAI,mBAAmB,sBAAsBA;AAC7C,UAAI,CAAC,oBAAoB,kBAAkB,QAAW;AACpD,YAAI;AAMF,6BAAmB,CAAC,cAAc,aAAa;AAAA,QACjD,SAAS,GAAG;AACV,wBAAc,uHAAuH,IAAI,CAAC;AAAA,QAC5I;AAAA,MACF;AAMA,YAAM,sBAAsB,oBAAoB;AAChD,YAAM,mBAAmB,oBAAoB;AAC7C,UAAI,YAAY;AAKhB,UAAI,qBAAqB,UAAa,CAAC,YAAY,QAAQ,QAAQ,CAAC,eAAe;AACjF,oBAAY,mBAAmB,kBAAkB,eAAe,EAAE;AAAA,MACpE;AACA,UAAI,YAAY;AAGhB,UAAI,CAAC,qBAAqB,CAAC,eAAe;AACxC,oBAAY,eAAe,WAAW,YAAY,EAAE,GAAG,UAAU,WAAW,EAAE,GAAG,mBAAmB,cAAc,EAAE;AAAA,MACtH,WAAW,eAAe;AACxB,oBAAY,eAAe,mBAAmB,cAAc,EAAE;AAAA,MAChE;AACA,aAAO,EAAE,OAAO;AAAA,QACd,OAAO;AAAA,MACT,GAAG,EAAE,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOb,KAAK,CAAAC,QAAM;AACT,cAAIA,KAAI;AACN,YAAAA,IAAG,MAAM,YAAY,SAAS,GAAG,YAAY,UAAU,YAAY,EAAE,IAAI,WAAW;AACpF,YAAAA,IAAG,MAAM,YAAY,oBAAoB,GAAG,YAAY,UAAU,kBAAkB,EAAE,IAAI,WAAW;AAAA,UACvG;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,aAAa;AAAA,QACb,cAAc;AAAA,QACd,oBAAoB;AAAA,QACpB,UAAU;AAAA,QACV,OAAO;AAAA,UACL,wBAAwB;AAAA,UACxB,gBAAgB;AAAA,UAChB,uBAAuB;AAAA,UACvB,4BAA4B;AAAA,UAC5B,sBAAsB;AAAA,UACtB,6BAA6B;AAAA,QAC/B;AAAA,QACA,MAAM;AAAA,QACN,eAAe,oBAAoB,SAAS;AAAA,QAC5C,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,SAAS,MAAM;AACb,cAAI,mBAAmB;AACrB;AAAA,UACF;AACA,cAAI,eAAe;AAEjB,iBAAK,GAAG,KAAK;AACb,iBAAK,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG,cAAc;AAC9E,iBAAK,cAAc,cAAc;AACjC,iBAAK,QAAQ;AAAA,UACf,OAAO;AACL,iBAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,YAAY,GAAG,cAAc,CAAC;AAExF,gBAAI,UAAU;AACZ,mBAAK,eAAe,gBAAgB,QAAQ;AAAA,YAC9C,OAAO;AACL,mBAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG,cAAc,CAAC;AAAA,YAClF;AAAA,UACF;AAAA,QACF;AAAA,MACF,GAAG,IAAI,CAAC;AAAA,IACV,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,qBAAqB;AACnB,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,MACP,KAAK,QAAM,KAAK,kBAAkB;AAAA,MAClC,UAAU;AAAA,IACZ,GAAG,eAAe,KAAK,cAAc,KAAK,eAAe,EAAE,IAAI,CAAC;AAAA,MAC9D;AAAA,MACA;AAAA,IACF,MAAM;AACJ,aAAO,KAAK,YAAY,OAAO,IAAI;AAAA,IACrC,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,eAAe,MAAM;AACnB,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,MACP,KAAK;AAAA,IACP,GAAG,KAAK,qBAAqB,IAAI,GAAG,KAAK,mBAAmB,CAAC;AAAA,EAC/D;AAAA,EACA,kBAAkB;AAChB,UAAM,sBAAsB,KAAK,GAAG,cAAc,qBAAqB,MAAM;AAC7E,QAAI,CAAC,uBAAuB,CAAC,KAAK,sBAAsB;AACtD;AAAA,IACF;AACA,WAAO,EAAE,QAAQ;AAAA,MACf,MAAM;AAAA,IACR,GAAG,MAAM;AAAA,EACX;AAAA,EACA,oBAAoB;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,oBAAoB,aAAa,QAAQ,SAAS;AACxD,UAAM,aAAa,KAAK,2BAA2B;AACnD,WAAO,CAAC,EAAE,OAAO;AAAA,MACf,OAAO;AAAA,IACT,GAAG,KAAK,gBAAgB,CAAC,GAAG,EAAE,UAAU;AAAA,MACtC,OAAO;AAAA,QACL,aAAa;AAAA,QACb,oBAAoB;AAAA,MACtB;AAAA,MACA,MAAM,cAAc,oBAAoB,YAAY,EAAE;AAAA,MACtD,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB;AAAA,MACA,SAAS,CAAM,OAAM;AACnB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,YAAY;AACd,eAAK,oBAAoB;AACzB,qBAAW,QAAQ,IAAI,YAAY,mBAAmB;AAAA,YACpD,QAAQ;AAAA,cACN,iBAAiB,GAAG;AAAA,YACtB;AAAA,UACF,CAAC,CAAC;AACF,gBAAM,WAAW,cAAc;AAC/B,eAAK,oBAAoB;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,GAAG,iBAAiB,QAAQ,YAAY,mBAAmB,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,IAAI,CAAC,GAAG,EAAE,eAAe;AAAA,MAC9J,WAAW;AAAA,MACX,aAAa;AAAA,MACb,cAAc;AAAA,MACd,OAAO;AAAA,MACP,eAAe,QAAM;AASnB,cAAM,OAAO,GAAG,OAAO,iBAAiB,mBAAmB;AAE3D,aAAK,QAAQ,SAAO,IAAI,yBAAyB,CAAC;AAAA,MACpD;AAAA,MACA,OAAO;AAAA,QACL,cAAc;AAAA,QACd,eAAe;AAAA,MACjB;AAAA;AAAA;AAAA,MAGA,gBAAgB;AAAA,MAChB,KAAK,QAAM,KAAK,aAAa;AAAA,IAC/B,GAAG,KAAK,kBAAkB,MAAM,CAAC,CAAC;AAAA,EACpC;AAAA,EACA,4BAA4B;AAC1B,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,UAAU,MAAM,QAAQ,WAAW;AACzC,QAAI;AACJ,QAAI,YAAY,WAAW,YAAY,WAAW,GAAG;AACnD,mBAAa,GAAG,YAAY,MAAM;AAClC,UAAI,gCAAgC,QAAW;AAC7C,YAAI;AACF,uBAAa,4BAA4B,iBAAiB,WAAW,CAAC;AAAA,QACxE,SAAS,GAAG;AACV,wBAAc,yEAAyE,CAAC;AAAA,QAC1F;AAAA,MACF;AAAA,IACF,OAAO;AAEL,mBAAa,qBAAqB,KAAK,QAAQ,KAAK,2BAA2B,IAAI,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,OAAO,SAAS,KAAK;AAAA,QACzM,SAAS;AAAA,QACT,OAAO;AAAA,QACP,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa,qBAAqB,MAAM;AACtC,UAAM,kBAAkB,KAAK,GAAG,cAAc,gBAAgB,MAAM;AACpE,QAAI,CAAC,mBAAmB,CAAC,KAAK,kBAAkB;AAC9C;AAAA,IACF;AACA,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,OAAO;AAAA,IACT,GAAG,EAAE,QAAQ;AAAA,MACX,MAAM;AAAA,IACR,GAAG,aAAa,CAAC,GAAG,sBAAsB,EAAE,OAAO;AAAA,MACjD,OAAO;AAAA,IACT,GAAG,KAAK,0BAA0B,CAAC,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa;AACX,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,uBAAuB,iBAAiB;AAC9C,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,IACT,GAAG,uBAAuB,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,oCAAoC;AAClC,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,IACT,GAAG,KAAK,gBAAgB,YAAY,CAAC;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,MAAM;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AAKJ,UAAM,kBAAkB,iBAAiB,UAAU,iBAAiB,eAAe,iBAAiB;AACpG,QAAI,eAAe,iBAAiB;AAClC,aAAO,CAAC,KAAK,aAAa,KAAK,GAAG,KAAK,gBAAgB,GAAG,KAAK,aAAa,CAAC;AAAA,IAC/E;AACA,YAAQ,cAAc;AAAA,MACpB,KAAK;AACH,eAAO,CAAC,KAAK,aAAa,GAAG,KAAK,eAAe,IAAI,GAAG,KAAK,kCAAkC,GAAG,KAAK,WAAW,GAAG,KAAK,aAAa,CAAC;AAAA,MAC1I,KAAK;AACH,eAAO,CAAC,KAAK,aAAa,GAAG,KAAK,WAAW,GAAG,KAAK,eAAe,IAAI,GAAG,KAAK,kCAAkC,GAAG,KAAK,aAAa,CAAC;AAAA,MAC1I,KAAK;AACH,eAAO,CAAC,KAAK,aAAa,KAAK,GAAG,KAAK,WAAW,GAAG,KAAK,aAAa,CAAC;AAAA,MAC1E,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,CAAC,KAAK,aAAa,KAAK,GAAG,KAAK,gBAAgB,GAAG,KAAK,aAAa,CAAC;AAAA,MAC/E;AACE,eAAO,CAAC,KAAK,aAAa,GAAG,KAAK,eAAe,IAAI,GAAG,KAAK,kCAAkC,GAAG,KAAK,aAAa,CAAC;AAAA,IACzH;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,6BAA6B,iBAAiB,UAAU,iBAAiB,WAAW,iBAAiB;AAC3G,UAAM,yBAAyB,oBAAoB;AACnD,UAAM,sBAAsB,oBAAoB,CAAC;AACjD,UAAM,sBAAsB,iBAAiB,UAAU,iBAAiB,eAAe,iBAAiB;AACxG,UAAM,kBAAkB,uBAAuB;AAC/C,sBAAkB,MAAM,IAAI,MAAM,YAAY,KAAK,GAAG,QAAQ;AAC9D,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,iBAAiB,WAAW,SAAS;AAAA,MACrC,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,MACb,OAAO,OAAO,OAAO,CAAC,GAAG,mBAAmB,OAAO;AAAA,QACjD,CAAC,IAAI,GAAG;AAAA,QACR,CAAC,mBAAmB,GAAG;AAAA,QACvB,CAAC,mBAAmB,GAAG;AAAA,QACvB,uBAAuB;AAAA,QACvB,0BAA0B;AAAA,QAC1B,CAAC,yBAAyB,YAAY,EAAE,GAAG;AAAA,QAC3C,CAAC,iBAAiB,IAAI,EAAE,GAAG;AAAA,QAC3B,CAAC,uBAAuB,GAAG;AAAA,QAC3B,CAAC,eAAe,GAAG;AAAA,MACrB,CAAC,CAAC;AAAA,IACJ,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,KAAK,CAAAA,QAAM,KAAK,yBAAyBA;AAAA,IAC3C,CAAC,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,EAC/B;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,iBAAiB,CAAC,sBAAsB;AAAA,MACxC,YAAY,CAAC,iBAAiB;AAAA,MAC9B,OAAO,CAAC,YAAY;AAAA,MACpB,OAAO,CAAC,YAAY;AAAA,MACpB,gBAAgB,CAAC,qBAAqB;AAAA,MACtC,cAAc,CAAC,mBAAmB;AAAA,MAClC,eAAe,CAAC,oBAAoB;AAAA,MACpC,aAAa,CAAC,kBAAkB;AAAA,MAChC,cAAc,CAAC,mBAAmB;AAAA,MAClC,gBAAgB,CAAC,qBAAqB;AAAA,MACtC,SAAS,CAAC,cAAc;AAAA,IAC1B;AAAA,EACF;AACF;AACA,IAAI,cAAc;AAClB,IAAM,cAAc;AACpB,IAAM,eAAe;AACrB,IAAM,kBAAkB;AACxB,IAAM,yBAAyB;AAC/B,SAAS,QAAQ;AAAA,EACf,KAAK;AAAA,EACL,IAAI;AACN;AAKA,IAAM,oBAAoB,YAAU;AAClC,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,QAAM,mBAAmB,gBAAgB;AACzC,oBAAkB,WAAW,OAAO,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,MAAM,yBAAyB,EAAE,aAAa;AAAA,IACjI,kBAAkB;AAAA,EACpB,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC;AACtC,mBAAiB,WAAW,OAAO,cAAc,iBAAiB,CAAC,EAAE,OAAO,aAAa,oBAAoB,gBAAgB;AAC7H,SAAO,cAAc,WAAW,MAAM,EAAE,OAAO,6BAA6B,EAAE,SAAS,GAAG,EAAE,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAChJ;AAKA,IAAM,oBAAoB,YAAU;AAClC,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,QAAM,mBAAmB,gBAAgB;AACzC,oBAAkB,WAAW,OAAO,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,2BAA2B,IAAI;AACpH,mBAAiB,WAAW,OAAO,cAAc,iBAAiB,CAAC,EAAE,OAAO,aAAa,kBAAkB,kBAAkB;AAC7H,SAAO,cAAc,WAAW,MAAM,EAAE,OAAO,6BAA6B,EAAE,SAAS,GAAG,EAAE,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAChJ;AACA,IAAM,eAAe;AACrB,IAAM,cAAc;AACpB,IAAM,SAAS,MAAM;AAAA,EACnB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,aAAa,YAAY,MAAM,uBAAuB,CAAC;AAC5D,SAAK,cAAc,YAAY,MAAM,wBAAwB,CAAC;AAC9D,SAAK,cAAc,YAAY,MAAM,wBAAwB,CAAC;AAC9D,SAAK,aAAa,YAAY,MAAM,uBAAuB,CAAC;AAC5D,SAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,SAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,SAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,SAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,SAAK,qBAAqB,yBAAyB,IAAI;AACvD,SAAK,iBAAiB,qBAAqB;AAC3C,SAAK,oBAAoB,wBAAwB;AACjD,SAAK,YAAY;AAEjB,SAAK,gBAAgB;AAIrB,SAAK,gBAAgB;AAIrB,SAAK,UAAU,CAAC;AAIhB,SAAK,UAAU,CAAC;AAIhB,SAAK,WAAW;AAIhB,SAAK,eAAe;AAIpB,SAAK,kBAAkB;AAIvB,SAAK,WAAW;AAQhB,SAAK,SAAS;AACd,SAAK,gBAAgB,MAAM;AACzB,WAAK,QAAQ,QAAW,QAAQ;AAAA,IAClC;AACA,SAAK,wBAAwB,QAAM;AACjC,YAAM,OAAO,GAAG,OAAO;AACvB,UAAI,SAAS,IAAI,GAAG;AAClB,cAAM,eAAe,KAAK,QAAQ,KAAK,OAAK,EAAE,SAAS,QAAQ;AAC/D,aAAK,kBAAkB,YAAY;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe,UAAU,UAAU;AACjC,QAAI,aAAa,QAAQ,aAAa,OAAO;AAC3C,WAAK,QAAQ;AAAA,IACf,WAAW,aAAa,SAAS,aAAa,MAAM;AAClD,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,SAAS;AACX,wBAAkB,iBAAiB,IAAI,OAAO;AAAA,IAChD;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,mBAAe,KAAK,EAAE;AACtB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,uBAAuB;AACrB,SAAK,kBAAkB,oBAAoB;AAAA,EAC7C;AAAA,EACA,oBAAoB;AAClB,QAAI;AACJ,QAAI,GAAG,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAC5E,mBAAa,KAAK,EAAE;AAAA,IACtB;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,oBAAgB,6RAA6R,KAAK,EAAE;AAKpT,QAAI,KAAK,WAAW,MAAM;AACxB,UAAI,MAAM,KAAK,QAAQ,CAAC;AAAA,IAC1B;AAUA,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIM,UAAU;AAAA;AACd,YAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,YAAM,KAAK,mBAAmB,gBAAgB;AAC9C,YAAM,QAAQ,MAAM,eAAe,mBAAmB,mBAAmB,MAAS;AAClF,UAAI,KAAK,WAAW,GAAG;AACrB,aAAK,kBAAkB,WAAW,MAAM,KAAK,QAAQ,GAAG,KAAK,QAAQ;AAAA,MACvE;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUM,QAAQ,MAAM,MAAM;AAAA;AACxB,YAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,UAAI,KAAK,iBAAiB;AACxB,qBAAa,KAAK,eAAe;AAAA,MACnC;AACA,YAAM,YAAY,MAAM,QAAQ,MAAM,MAAM,MAAM,eAAe,mBAAmB,iBAAiB;AACrG,UAAI,WAAW;AACb,aAAK,mBAAmB,kBAAkB;AAAA,MAC5C;AACA,aAAO;AACP,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,WAAO,YAAY,KAAK,IAAI,qBAAqB;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,WAAO,YAAY,KAAK,IAAI,sBAAsB;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,MAAM;AACd,WAAO,QAAQ,QAAQ,KAAK,QAAQ,KAAK,YAAU,OAAO,SAAS,IAAI,CAAC;AAAA,EAC1E;AAAA,EACM,YAAY,QAAQ;AAAA;AACxB,YAAM,OAAO,OAAO;AACpB,UAAI,SAAS,IAAI,GAAG;AAClB,eAAO,KAAK,QAAQ,QAAW,IAAI;AAAA,MACrC;AACA,YAAM,gBAAgB,MAAM,KAAK,kBAAkB,MAAM;AACzD,UAAI,eAAe;AACjB,eAAO,KAAK,QAAQ,KAAK,YAAY,GAAG,OAAO,IAAI;AAAA,MACrD;AACA,aAAO,QAAQ,QAAQ;AAAA,IACzB;AAAA;AAAA,EACM,kBAAkB,QAAQ;AAAA;AAC9B,UAAI,QAAQ;AAGV,cAAM,MAAM,MAAM,SAAS,OAAO,SAAS,KAAK,YAAY,CAAC;AAC7D,YAAI,QAAQ,OAAO;AAEjB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA,EACA,cAAc;AACZ,UAAM,WAAW,CAAC;AAClB,SAAK,QAAQ,QAAQ,CAAC,KAAK,UAAU;AACnC,YAAM,iBAAiB,IAAI,kBAAkB,SAAY,IAAI,QAAQ,IAAI,aAAa,IAAI;AAC1F,eAAS,IAAI,IAAI,IAAI;AAAA,QACnB,MAAM,iBAAiB,eAAe,OAAO;AAAA,QAC7C,OAAO,iBAAiB,eAAe,QAAQ;AAAA,QAC/C,aAAa;AAAA,MACf;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM,OAAO,OAAO;AAAA,MAC3B,KAAK;AAAA,MACL,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,GAAG,gBAAgB;AAAA,MACjB,OAAO;AAAA,QACL,QAAQ,GAAG,MAAQ,KAAK,YAAY;AAAA,MACtC;AAAA,MACA,OAAO,OAAO,OAAO;AAAA,QACnB,CAAC,IAAI,GAAG;AAAA;AAAA,QAER,CAAC,UAAU,IAAI,EAAE,GAAG;AAAA,QACpB,kBAAkB;AAAA,MACpB,GAAG,YAAY,KAAK,QAAQ,CAAC;AAAA,MAC7B,kBAAkB,KAAK;AAAA,MACvB,wBAAwB,KAAK;AAAA,IAC/B,CAAC,GAAG,EAAE,gBAAgB;AAAA,MACpB,KAAK;AAAA,MACL,SAAS,KAAK;AAAA,MACd,UAAU,KAAK;AAAA,IACjB,CAAC,GAAG,EAAE,OAAO;AAAA,MACX,KAAK;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,IACjB,CAAC,GAAG,EAAE,OAAO;AAAA,MACX,KAAK;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,IACR,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,KAAK,QAAQ,IAAI,OAAK,EAAE,OAAO;AAAA,MAChC,OAAO,mBAAmB,CAAC;AAAA,IAC7B,GAAG,EAAE,UAAU;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAM,KAAK,YAAY,CAAC;AAAA,MACjC,OAAO,YAAY,CAAC;AAAA,IACtB,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,MACtB,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT,CAAC,GAAG,KAAK,aAAa,KAAK,QAAQ,IAAI,OAAK,EAAE,4BAA4B;AAAA,MACxE,KAAK;AAAA,IACP,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,MACZ,KAAK;AAAA,MACL,OAAO;AAAA,IACT,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,MACb,KAAK;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,IACjB,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,UAAU,CAAC,gBAAgB;AAAA,MAC3B,WAAW,CAAC,gBAAgB;AAAA,IAC9B;AAAA,EACF;AACF;AACA,IAAM,qBAAqB,YAAU;AACnC,SAAO;AAAA,IACL,CAAC,kBAAkB,OAAO,IAAI,EAAE,GAAG,OAAO,SAAS;AAAA,IACnD,yBAAyB;AAAA,EAC3B;AACF;AACA,IAAM,cAAc,YAAU;AAC5B,SAAO,OAAO,OAAO;AAAA,IACnB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,EACrB,GAAG,YAAY,OAAO,QAAQ,CAAC;AACjC;AACA,OAAO,QAAQ;AAAA,EACb,KAAK;AAAA,EACL,IAAI;AACN;AACA,IAAM,qBAAqB;AAC3B,IAAM,oBAAoB;AAC1B,IAAM,kBAAkB,MAAM;AAAA,EAC5B,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,qBAAqB,YAAY,MAAM,sBAAsB,CAAC;AACnE,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,IAAI;AACT,SAAK,YAAY;AAQjB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,aAAa;AACX,SAAK,eAAe;AAAA,EACtB;AAAA,EACM,oBAAoB;AAAA;AACxB,UAAI,qBAAqB;AACzB,UAAI,oBAAoB;AACxB,YAAM,OAAO,WAAW,IAAI;AAC5B,UAAI,SAAS,OAAO;AAClB,6BAAqB;AACrB,4BAAoB;AAAA,MACtB;AACA,WAAK,eAAe;AACpB,WAAK,cAAc;AACnB,WAAK,WAAW,MAAM,OAAO,8BAAqB,GAAG,cAAc;AAAA,QACjE,IAAI,KAAK;AAAA,QACT,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,SAAS;AAAA,QACT,SAAS,QAAM,KAAK,QAAQ,EAAE;AAAA,QAC9B,QAAQ,QAAM,KAAK,OAAO,EAAE;AAAA,QAC5B,OAAO,QAAM,KAAK,MAAM,EAAE;AAAA,MAC5B,CAAC;AACD,WAAK,QAAQ,OAAO;AAKpB,WAAK,QAAQ,WAAW,MAAM;AAC5B,aAAK,YAAY;AAIjB,aAAK,QAAQ,IAAI;AAAA,MACnB,GAAG,GAAG;AAAA,IACR;AAAA;AAAA,EACA,mBAAmB;AACjB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,qBAAqB;AAEnB,QAAI,KAAK,cAAc;AAIrB,WAAK,YAAY,MAAM,KAAK;AAC5B,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,UAAU,OAAW,sBAAqB,KAAK,KAAK;AAC7D,QAAI,KAAK,MAAO,cAAa,KAAK,KAAK;AACvC,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,QAAQ;AACrB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,mBAAmB,KAAK,KAAK,GAAG;AAAA,EACvC;AAAA,EACA,YAAY,eAAe,UAAU;AAGnC,UAAM,IAAI,gBAAgB,KAAK,EAAE,gBAAgB,KAAK,aAAa;AACnE,SAAK,WAAW;AAEhB,QAAI,KAAK,UAAU,OAAW,sBAAqB,KAAK,KAAK;AAC7D,SAAK,OAAO,GAAG,UAAU,IAAI;AAC7B,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO,GAAG,UAAU,OAAO;AACzB,QAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,IACF;AAEA,QAAI,aAAa;AACjB,QAAI,aAAa;AACjB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,IAAI;AACzB,UAAM,gBAAgB,IAAI,gBAAgB,KAAK,UAAU,CAAC,CAAC;AAC3D,UAAM,cAAc,aAAa,IAAI,KAAK,WAAW;AACrD,UAAM,WAAW,SAAS,KAAK,WAAW;AAC1C,UAAM,WAAW,KAAK,OAAO;AAC7B,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,SAAS,SAAS,CAAC;AACzB,YAAM,MAAM,IAAI,QAAQ,CAAC;AACzB,YAAM,YAAY,IAAI,KAAK,YAAY;AACvC,UAAI,YAAY;AAChB,UAAI,iBAAiB,GAAG;AACtB,cAAM,UAAU,YAAY;AAC5B,YAAI,KAAK,IAAI,OAAO,KAAK,IAAI;AAC3B,uBAAa;AACb,uBAAa;AACb,sBAAY,WAAW,OAAO;AAAA,QAChC,OAAO;AACL,uBAAa;AAAA,QACf;AAAA,MACF,OAAO;AACL,qBAAa;AACb,qBAAa;AAAA,MACf;AACA,YAAM,WAAW,kBAAkB;AACnC,mBAAa,mBAAmB,UAAU,MAAM,UAAU;AAC1D,UAAI,KAAK,gBAAgB,KAAK,CAAC,UAAU;AACvC,qBAAa;AAAA,MACf;AAEA,UAAI,KAAK,WAAW;AAClB,YAAI,WAAW;AACf,eAAO,MAAM,qBAAqB;AAAA,MACpC,WAAW,aAAa,IAAI,UAAU;AACpC,YAAI,WAAW;AACf,eAAO,MAAM,qBAAqB;AAAA,MACpC;AAEA,UAAI,cAAc,IAAI,WAAW;AAC/B,YAAI,YAAY;AAAA,MAClB;AACA,aAAO,MAAM,YAAY;AAKzB,UAAI,WAAW;AACf,UAAI,UAAU;AACZ,eAAO,UAAU,IAAI,mBAAmB;AAAA,MAC1C,OAAO;AACL,eAAO,UAAU,OAAO,mBAAmB;AAAA,MAC7C;AAAA,IACF;AACA,SAAK,IAAI,eAAe;AACxB,QAAI,OAAO;AACT,WAAK,IAAI;AAAA,IACX;AACA,QAAI,KAAK,cAAc,eAAe;AAEpC,6BAAuB;AACvB,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,aAAa;AACX,QAAI,KAAK,aAAa,GAAG;AAEvB,WAAK,YAAY;AAEjB,WAAK,WAAW,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,UAAU,CAAC,IAAI,KAAK,IAAI,KAAK,UAAU,EAAE;AAC3F,UAAI,IAAI,KAAK,IAAI,KAAK;AACtB,UAAI,IAAI,KAAK,MAAM;AAEjB,YAAI,KAAK;AACT,aAAK,WAAW;AAAA,MAClB,WAAW,IAAI,KAAK,MAAM;AAExB,YAAI,KAAK;AACT,aAAK,WAAW;AAAA,MAClB;AACA,WAAK,OAAO,GAAG,GAAG,IAAI;AACtB,YAAM,cAAc,KAAK,MAAM,CAAC,IAAI,KAAK,cAAc,KAAK,KAAK,IAAI,KAAK,QAAQ,IAAI;AACtF,UAAI,aAAa;AAEf,aAAK,QAAQ,sBAAsB,MAAM,KAAK,WAAW,CAAC;AAAA,MAC5D,OAAO;AACL,aAAK,WAAW;AAChB,aAAK,cAAc;AACnB,2BAAmB;AAAA,MACrB;AAAA,IACF,WAAW,KAAK,IAAI,KAAK,cAAc,GAAG;AAExC,YAAM,aAAa,KAAK,IAAI,KAAK,IAAI,KAAK,SAAS;AAEnD,WAAK,WAAW,aAAa,KAAK,YAAY,IAAI,IAAI;AACtD,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,UAAU,GAAG;AACX,WAAO,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK,IAAI,QAAQ,SAAS,CAAC;AAAA,EACpG;AAAA,EACA,QAAQ,QAAQ;AAId,QAAI,OAAO,MAAM,YAAY;AAC3B,aAAO,MAAM,eAAe;AAAA,IAC9B;AACA,WAAO,MAAM,gBAAgB;AAC7B,yBAAqB;AAErB,QAAI,KAAK,UAAU,OAAW,sBAAqB,KAAK,KAAK;AAC7D,UAAM,UAAU,KAAK,IAAI;AACzB,QAAI,OAAO,QAAQ,SAAS;AAC5B,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAI,CAAC,QAAQ,CAAC,EAAE,UAAU;AACxB,eAAO,KAAK,IAAI,MAAM,CAAC;AACvB,eAAO,KAAK,IAAI,MAAM,CAAC;AAAA,MACzB;AAAA,IACF;AACA,SAAK,OAAO,EAAE,OAAO,KAAK;AAC1B,SAAK,OAAO,EAAE,OAAO,KAAK;AAAA,EAC5B;AAAA,EACA,OAAO,QAAQ;AACb,QAAI,OAAO,MAAM,YAAY;AAC3B,aAAO,MAAM,eAAe;AAAA,IAC9B;AACA,WAAO,MAAM,gBAAgB;AAE7B,QAAI,IAAI,KAAK,IAAI,OAAO;AACxB,QAAI,IAAI,KAAK,MAAM;AAEjB,UAAI,KAAK,IAAI,GAAG,GAAG;AACnB,WAAK,aAAa;AAAA,IACpB,WAAW,IAAI,KAAK,MAAM;AAExB,WAAK,KAAK,IAAI,KAAK,OAAO,GAAG,GAAG;AAChC,WAAK,aAAa;AAAA,IACpB,OAAO;AACL,WAAK,aAAa;AAAA,IACpB;AACA,SAAK,OAAO,GAAG,GAAG,KAAK;AAAA,EACzB;AAAA,EACA,MAAM,QAAQ;AACZ,QAAI,KAAK,aAAa,GAAG;AAEvB,WAAK,OAAO,KAAK,MAAM,KAAK,IAAI;AAChC,WAAK,cAAc;AACnB;AAAA,IACF,WAAW,KAAK,aAAa,GAAG;AAE9B,WAAK,OAAO,KAAK,MAAM,KAAK,IAAI;AAChC,WAAK,cAAc;AACnB;AAAA,IACF;AACA,SAAK,WAAW,MAAM,KAAK,OAAO,YAAY,IAAI,gBAAgB;AAClE,QAAI,KAAK,aAAa,KAAK,OAAO,WAAW,GAAG;AAC9C,YAAM,MAAM,OAAO,MAAM,OAAO,QAAQ,aAAa;AACrD,UAAI,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,aAAa,WAAW,GAAG;AAC3E,aAAK,YAAY,SAAS,IAAI,aAAa,WAAW,GAAG,EAAE,GAAG,mBAAmB;AAAA,MACnF;AAAA,IACF,OAAO;AACL,WAAK,KAAK,OAAO;AACjB,UAAI,KAAK,IAAI,OAAO,SAAS,IAAI,MAAM;AACrC,cAAM,gBAAgB,OAAO,SAAS;AACtC,cAAM,oBAAoB,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,KAAK;AACnE,YAAI,iBAAiB,oBAAoB,KAAK;AAC5C,eAAK,WAAW,KAAK,IAAI,KAAK,QAAQ,IAAI;AAAA,QAC5C,WAAW,CAAC,iBAAiB,qBAAqB,KAAK;AACrD,eAAK,WAAW,KAAK,IAAI,KAAK,QAAQ;AAAA,QACxC;AAAA,MACF;AACA,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,QAAQ,cAAc,UAAU;AAC9B,QAAI;AACJ,QAAI,MAAM,KAAK,IAAI,QAAQ,SAAS;AACpC,QAAI,MAAM;AACV,UAAM,UAAU,KAAK,IAAI;AACzB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAI,CAAC,QAAQ,CAAC,EAAE,UAAU;AACxB,cAAM,KAAK,IAAI,KAAK,CAAC;AACrB,cAAM,KAAK,IAAI,KAAK,CAAC;AAAA,MACvB;AAAA,IACF;AAQA,QAAI,KAAK,aAAa,GAAG;AACvB;AAAA,IACF;AACA,UAAM,gBAAgB,MAAM,MAAM,KAAK,KAAK,IAAI,mBAAmB,QAAQ,OAAO,SAAS,KAAK,GAAG,GAAG;AACtG,QAAI,KAAK,IAAI,iBAAiB,iBAAiB,cAAc;AAC3D,YAAM,IAAI,gBAAgB,KAAK,YAAY;AAC3C,YAAM,WAAW,WAAW,sBAAsB;AAClD,WAAK,WAAW;AAChB,WAAK,OAAO,GAAG,UAAU,IAAI;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,YAAY,cAAc,UAAU;AAClC,UAAM,QAAQ,KAAK;AACnB,QAAI,OAAO;AAGT,WAAK,YAAY,MAAM,oBAAoB,MAAM,kBAAkB,eAAe;AAAA,IACpF;AACA,SAAK,QAAQ,cAAc,QAAQ;AAAA,EACrC;AAAA,EACA,SAAS;AACP,UAAM,MAAM,KAAK;AACjB,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,OAAO,OAAO;AAAA,QACnB,CAAC,IAAI,GAAG;AAAA,QACR,cAAc;AAAA,QACd,oBAAoB,KAAK,IAAI,UAAU;AAAA,QACvC,qBAAqB,KAAK,IAAI,UAAU;AAAA,MAC1C,GAAG,YAAY,IAAI,QAAQ,CAAC;AAAA,MAC5B,OAAO;AAAA,QACL,aAAa,KAAK,IAAI;AAAA,MACxB;AAAA,IACF,GAAG,IAAI,UAAU,EAAE,OAAO;AAAA,MACxB,KAAK;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,QACL,OAAO,IAAI;AAAA,MACb;AAAA,IACF,GAAG,IAAI,MAAM,GAAG,EAAE,OAAO;AAAA,MACvB,KAAK;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,QACL,UAAU,IAAI;AAAA,MAChB;AAAA,MACA,KAAK,QAAM,KAAK,SAAS;AAAA,IAC3B,GAAG,IAAI,QAAQ,IAAI,CAAC,GAAG,UAAU,EAAE,UAAU;AAAA,MAC3C,cAAc,EAAE;AAAA,MAChB,OAAO;AAAA,QACL,cAAc;AAAA,QACd,uBAAuB,CAAC,CAAC,EAAE;AAAA,MAC7B;AAAA,MACA,aAAa;AAAA,IACf,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,UAAU,EAAE,OAAO;AAAA,MACnC,KAAK;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,QACL,OAAO,IAAI;AAAA,MACb;AAAA,IACF,GAAG,IAAI,MAAM,CAAC;AAAA,EAChB;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,OAAO,CAAC,YAAY;AAAA,IACtB;AAAA,EACF;AACF;AACA,IAAM,sBAAsB;AAC5B,IAAM,wBAAwB;AAC9B,IAAM,mBAAmB;AACzB,IAAM,sBAAsB;AAC5B,gBAAgB,QAAQ;AAAA,EACtB,KAAK;AAAA,EACL,IAAI;AACN;", "names": ["disabled", "isDayDisabled", "el"]}