import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Router } from '@angular/router';

@Component({
  selector: 'app-home',
  templateUrl: './home.page.html',
  styleUrls: ['./home.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class HomePage implements OnInit {

  featuredCategories = [
    {
      id: 1,
      name: 'Ethnic Wear',
      icon: '🥻',
      image: 'assets/categories/ethnic.jpg',
      description: 'Traditional elegance',
      color: 'var(--ow-rose-500)'
    },
    {
      id: 2,
      name: 'Western Wear',
      icon: '👗',
      image: 'assets/categories/western.jpg',
      description: 'Modern sophistication',
      color: 'var(--ow-purple-500)'
    },
    {
      id: 3,
      name: 'Beauty & Makeup',
      icon: '💄',
      image: 'assets/categories/beauty.jpg',
      description: 'Enhance your glow',
      color: 'var(--ow-gold-500)'
    },
    {
      id: 4,
      name: 'Accessories',
      icon: '👜',
      image: 'assets/categories/accessories.jpg',
      description: 'Complete your look',
      color: 'var(--ion-color-secondary)'
    }
  ];

  featuredProducts = [
    {
      id: 1,
      name: 'Floral Maxi Dress',
      price: 2499,
      originalPrice: 3499,
      image: 'assets/products/dress1.jpg',
      rating: 4.8,
      reviews: 124,
      badge: 'Trending'
    },
    {
      id: 2,
      name: 'Silk Saree Collection',
      price: 4999,
      originalPrice: 6999,
      image: 'assets/products/saree1.jpg',
      rating: 4.9,
      reviews: 89,
      badge: 'Best Seller'
    },
    {
      id: 3,
      name: 'Matte Lipstick Set',
      price: 899,
      originalPrice: 1299,
      image: 'assets/products/lipstick1.jpg',
      rating: 4.7,
      reviews: 256,
      badge: 'New'
    }
  ];

  offers = [
    {
      title: 'Summer Sale',
      subtitle: 'Up to 70% OFF',
      description: 'On ethnic wear collection',
      image: 'assets/banners/summer-sale.jpg',
      color: 'var(--ow-rose-500)'
    },
    {
      title: 'Beauty Bonanza',
      subtitle: 'Buy 2 Get 1 FREE',
      description: 'On all makeup products',
      image: 'assets/banners/beauty-offer.jpg',
      color: 'var(--ow-purple-500)'
    }
  ];

  constructor(private router: Router) { }

  ngOnInit() {
    // Initialize any data loading here
  }

  onCategoryClick(category: any) {
    // Navigate to category page
    this.router.navigate(['/tabs/shop'], { queryParams: { category: category.id } });
  }

  onProductClick(product: any) {
    // Navigate to product details
    this.router.navigate(['/product', product.id]);
  }

  onOfferClick(offer: any) {
    // Navigate to offer page
    this.router.navigate(['/tabs/shop'], { queryParams: { offer: offer.title } });
  }

  onSearchClick() {
    // Navigate to search page
    this.router.navigate(['/search']);
  }

  onNotificationClick() {
    // Navigate to notifications
    this.router.navigate(['/notifications']);
  }

  onCartClick() {
    // Navigate to cart
    this.router.navigate(['/cart']);
  }

  addToWishlist(product: any, event: Event) {
    event.stopPropagation();
    // Add to wishlist logic
    console.log('Added to wishlist:', product);
  }

  addToCart(product: any, event: Event) {
    event.stopPropagation();
    // Add to cart logic
    console.log('Added to cart:', product);
  }
}
