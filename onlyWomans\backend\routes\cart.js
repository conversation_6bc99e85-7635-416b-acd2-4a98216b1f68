const express = require('express');
const { body, validationResult } = require('express-validator');
const WomenProduct = require('../models/WomenProduct');
const WomenUser = require('../models/WomenUser');
const { auth } = require('../middleware/auth');

const router = express.Router();

// Cart model (embedded in user or separate collection)
const mongoose = require('mongoose');

const cartItemSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'WomenUser', required: true },
  product: { type: mongoose.Schema.Types.ObjectId, ref: 'WomenProduct', required: true },
  quantity: { type: Number, required: true, min: 1, default: 1 },
  size: { type: String },
  color: { type: String },
  price: { type: Number, required: true },
  addedAt: { type: Date, default: Date.now }
});

const Cart = mongoose.model('Cart', cartItemSchema);

// @route   GET /api/cart
// @desc    Get user's cart items
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const cartItems = await Cart.find({ user: req.user._id })
      .populate({
        path: 'product',
        select: 'name brand images price originalPrice discount isActive',
        populate: {
          path: 'vendor',
          select: 'firstName lastName'
        }
      })
      .sort({ addedAt: -1 });

    // Filter out items with inactive products
    const activeCartItems = cartItems.filter(item => item.product && item.product.isActive);

    // Calculate totals
    const subtotal = activeCartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
    const totalItems = activeCartItems.reduce((total, item) => total + item.quantity, 0);

    // Calculate shipping (free shipping over ₹999)
    const shippingThreshold = 999;
    const shippingCost = subtotal >= shippingThreshold ? 0 : 99;
    const total = subtotal + shippingCost;

    res.json({
      success: true,
      data: {
        items: activeCartItems,
        summary: {
          totalItems,
          subtotal,
          shippingCost,
          shippingThreshold,
          total,
          freeShippingEligible: subtotal >= shippingThreshold,
          amountForFreeShipping: Math.max(0, shippingThreshold - subtotal)
        }
      }
    });

  } catch (error) {
    console.error('Get cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch cart items'
    });
  }
});

// @route   POST /api/cart/add
// @desc    Add item to cart
// @access  Private
router.post('/add', [
  auth,
  body('productId').isMongoId().withMessage('Valid product ID is required'),
  body('quantity').isInt({ min: 1, max: 10 }).withMessage('Quantity must be between 1 and 10'),
  body('size').optional().trim(),
  body('color').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { productId, quantity, size, color } = req.body;

    // Check if product exists and is active
    const product = await WomenProduct.findById(productId);
    if (!product || !product.isActive) {
      return res.status(404).json({
        success: false,
        message: 'Product not found or unavailable'
      });
    }

    // Check stock availability
    if (size || color) {
      const sizeStock = size ? product.sizes.find(s => s.size === size)?.stock || 0 : Infinity;
      const colorStock = color ? product.colors.find(c => c.name === color)?.stock || 0 : Infinity;
      const availableStock = Math.min(sizeStock, colorStock);
      
      if (availableStock < quantity) {
        return res.status(400).json({
          success: false,
          message: 'Insufficient stock for selected options'
        });
      }
    } else if (product.totalStock < quantity) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient stock'
      });
    }

    // Check if item already exists in cart
    const existingCartItem = await Cart.findOne({
      user: req.user._id,
      product: productId,
      size: size || null,
      color: color || null
    });

    if (existingCartItem) {
      // Update quantity
      const newQuantity = existingCartItem.quantity + quantity;
      if (newQuantity > 10) {
        return res.status(400).json({
          success: false,
          message: 'Maximum 10 items allowed per product'
        });
      }

      existingCartItem.quantity = newQuantity;
      await existingCartItem.save();

      res.json({
        success: true,
        message: 'Cart updated successfully! 🛍️',
        data: existingCartItem
      });
    } else {
      // Create new cart item
      const cartItem = new Cart({
        user: req.user._id,
        product: productId,
        quantity,
        size,
        color,
        price: product.price
      });

      await cartItem.save();
      await cartItem.populate('product', 'name brand images price');

      res.status(201).json({
        success: true,
        message: 'Item added to cart! 🛍️',
        data: cartItem
      });
    }

    // Update product analytics
    await WomenProduct.findByIdAndUpdate(productId, {
      $inc: { 'analytics.cartAdds': 1 }
    });

  } catch (error) {
    console.error('Add to cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add item to cart'
    });
  }
});

// @route   PUT /api/cart/:itemId
// @desc    Update cart item quantity
// @access  Private
router.put('/:itemId', [
  auth,
  body('quantity').isInt({ min: 1, max: 10 }).withMessage('Quantity must be between 1 and 10')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { quantity } = req.body;

    const cartItem = await Cart.findOne({
      _id: req.params.itemId,
      user: req.user._id
    }).populate('product');

    if (!cartItem) {
      return res.status(404).json({
        success: false,
        message: 'Cart item not found'
      });
    }

    // Check stock availability
    const product = cartItem.product;
    if (cartItem.size || cartItem.color) {
      const sizeStock = cartItem.size ? product.sizes.find(s => s.size === cartItem.size)?.stock || 0 : Infinity;
      const colorStock = cartItem.color ? product.colors.find(c => c.name === cartItem.color)?.stock || 0 : Infinity;
      const availableStock = Math.min(sizeStock, colorStock);
      
      if (availableStock < quantity) {
        return res.status(400).json({
          success: false,
          message: 'Insufficient stock for selected options'
        });
      }
    } else if (product.totalStock < quantity) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient stock'
      });
    }

    cartItem.quantity = quantity;
    await cartItem.save();

    res.json({
      success: true,
      message: 'Cart updated successfully! ✨',
      data: cartItem
    });

  } catch (error) {
    console.error('Update cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update cart item'
    });
  }
});

// @route   DELETE /api/cart/:itemId
// @desc    Remove item from cart
// @access  Private
router.delete('/:itemId', auth, async (req, res) => {
  try {
    const cartItem = await Cart.findOneAndDelete({
      _id: req.params.itemId,
      user: req.user._id
    });

    if (!cartItem) {
      return res.status(404).json({
        success: false,
        message: 'Cart item not found'
      });
    }

    res.json({
      success: true,
      message: 'Item removed from cart! 🗑️'
    });

  } catch (error) {
    console.error('Remove from cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove item from cart'
    });
  }
});

// @route   DELETE /api/cart/clear
// @desc    Clear all cart items
// @access  Private
router.delete('/clear', auth, async (req, res) => {
  try {
    await Cart.deleteMany({ user: req.user._id });

    res.json({
      success: true,
      message: 'Cart cleared successfully! 🧹'
    });

  } catch (error) {
    console.error('Clear cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear cart'
    });
  }
});

// @route   GET /api/cart/count
// @desc    Get cart items count
// @access  Private
router.get('/count', auth, async (req, res) => {
  try {
    const count = await Cart.countDocuments({ user: req.user._id });

    res.json({
      success: true,
      data: {
        count
      }
    });

  } catch (error) {
    console.error('Get cart count error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get cart count'
    });
  }
});

module.exports = router;
