{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-spinner.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, l as config, e as getIonMode, h, j as Host } from './index-B_U9CtaY.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { S as SPINNERS } from './spinner-configs-D4RIp70E.js';\nconst spinnerCss = \":host{display:inline-block;position:relative;width:28px;height:28px;color:var(--color);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.ion-color){color:var(--ion-color-base)}svg{-webkit-transform-origin:center;transform-origin:center;position:absolute;top:0;left:0;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0)}:host-context([dir=rtl]) svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){svg:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}:host(.spinner-lines) line,:host(.spinner-lines-small) line{stroke-width:7px}:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-width:4px}:host(.spinner-lines) line,:host(.spinner-lines-small) line,:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-linecap:round;stroke:currentColor}:host(.spinner-lines) svg,:host(.spinner-lines-small) svg,:host(.spinner-lines-sharp) svg,:host(.spinner-lines-sharp-small) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite}:host(.spinner-bubbles) svg{-webkit-animation:spinner-scale-out 1s linear infinite;animation:spinner-scale-out 1s linear infinite;fill:currentColor}:host(.spinner-circles) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite;fill:currentColor}:host(.spinner-crescent) circle{fill:transparent;stroke-width:4px;stroke-dasharray:128px;stroke-dashoffset:82px;stroke:currentColor}:host(.spinner-crescent) svg{-webkit-animation:spinner-rotate 1s linear infinite;animation:spinner-rotate 1s linear infinite}:host(.spinner-dots) circle{stroke-width:0;fill:currentColor}:host(.spinner-dots) svg{-webkit-animation:spinner-dots 1s linear infinite;animation:spinner-dots 1s linear infinite}:host(.spinner-circular) svg{-webkit-animation:spinner-circular linear infinite;animation:spinner-circular linear infinite}:host(.spinner-circular) circle{-webkit-animation:spinner-circular-inner ease-in-out infinite;animation:spinner-circular-inner ease-in-out infinite;stroke:currentColor;stroke-dasharray:80px, 200px;stroke-dashoffset:0px;stroke-width:5.6;fill:none}:host(.spinner-paused),:host(.spinner-paused) svg,:host(.spinner-paused) circle{-webkit-animation-play-state:paused;animation-play-state:paused}@-webkit-keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@-webkit-keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@-webkit-keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@-webkit-keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}@keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}\";\nconst Spinner = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    /**\n     * If `true`, the spinner's animation will be paused.\n     */\n    this.paused = false;\n  }\n  getName() {\n    const spinnerName = this.name || config.get('spinner');\n    const mode = getIonMode(this);\n    if (spinnerName) {\n      return spinnerName;\n    }\n    return mode === 'ios' ? 'lines' : 'circular';\n  }\n  render() {\n    var _a;\n    const self = this;\n    const mode = getIonMode(self);\n    const spinnerName = self.getName();\n    const spinner = (_a = SPINNERS[spinnerName]) !== null && _a !== void 0 ? _a : SPINNERS['lines'];\n    const duration = typeof self.duration === 'number' && self.duration > 10 ? self.duration : spinner.dur;\n    const svgs = [];\n    if (spinner.circles !== undefined) {\n      for (let i = 0; i < spinner.circles; i++) {\n        svgs.push(buildCircle(spinner, duration, i, spinner.circles));\n      }\n    } else if (spinner.lines !== undefined) {\n      for (let i = 0; i < spinner.lines; i++) {\n        svgs.push(buildLine(spinner, duration, i, spinner.lines));\n      }\n    }\n    return h(Host, {\n      key: 'a33d6421fcc885995fbc7a348516525f68ca496c',\n      class: createColorClasses(self.color, {\n        [mode]: true,\n        [`spinner-${spinnerName}`]: true,\n        'spinner-paused': self.paused || config.getBoolean('_testing')\n      }),\n      role: \"progressbar\",\n      style: spinner.elmDuration ? {\n        animationDuration: duration + 'ms'\n      } : {}\n    }, svgs);\n  }\n};\nconst buildCircle = (spinner, duration, index, total) => {\n  const data = spinner.fn(duration, index, total);\n  data.style['animation-duration'] = duration + 'ms';\n  return h(\"svg\", {\n    viewBox: data.viewBox || '0 0 64 64',\n    style: data.style\n  }, h(\"circle\", {\n    transform: data.transform || 'translate(32,32)',\n    cx: data.cx,\n    cy: data.cy,\n    r: data.r,\n    style: spinner.elmDuration ? {\n      animationDuration: duration + 'ms'\n    } : {}\n  }));\n};\nconst buildLine = (spinner, duration, index, total) => {\n  const data = spinner.fn(duration, index, total);\n  data.style['animation-duration'] = duration + 'ms';\n  return h(\"svg\", {\n    viewBox: data.viewBox || '0 0 64 64',\n    style: data.style\n  }, h(\"line\", {\n    transform: \"translate(32,32)\",\n    y1: data.y1,\n    y2: data.y2\n  }));\n};\nSpinner.style = spinnerCss;\nexport { Spinner as ion_spinner };"], "mappings": ";;;;;;;;;;;;;;;;AAMA,IAAM,aAAa;AACnB,IAAM,UAAU,MAAM;AAAA,EACpB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAI9B,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,UAAU;AACR,UAAM,cAAc,KAAK,QAAQ,OAAO,IAAI,SAAS;AACrD,UAAM,OAAO,WAAW,IAAI;AAC5B,QAAI,aAAa;AACf,aAAO;AAAA,IACT;AACA,WAAO,SAAS,QAAQ,UAAU;AAAA,EACpC;AAAA,EACA,SAAS;AACP,QAAI;AACJ,UAAM,OAAO;AACb,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,cAAc,KAAK,QAAQ;AACjC,UAAM,WAAW,KAAK,SAAS,WAAW,OAAO,QAAQ,OAAO,SAAS,KAAK,SAAS,OAAO;AAC9F,UAAM,WAAW,OAAO,KAAK,aAAa,YAAY,KAAK,WAAW,KAAK,KAAK,WAAW,QAAQ;AACnG,UAAM,OAAO,CAAC;AACd,QAAI,QAAQ,YAAY,QAAW;AACjC,eAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,KAAK;AACxC,aAAK,KAAK,YAAY,SAAS,UAAU,GAAG,QAAQ,OAAO,CAAC;AAAA,MAC9D;AAAA,IACF,WAAW,QAAQ,UAAU,QAAW;AACtC,eAAS,IAAI,GAAG,IAAI,QAAQ,OAAO,KAAK;AACtC,aAAK,KAAK,UAAU,SAAS,UAAU,GAAG,QAAQ,KAAK,CAAC;AAAA,MAC1D;AAAA,IACF;AACA,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,mBAAmB,KAAK,OAAO;AAAA,QACpC,CAAC,IAAI,GAAG;AAAA,QACR,CAAC,WAAW,WAAW,EAAE,GAAG;AAAA,QAC5B,kBAAkB,KAAK,UAAU,OAAO,WAAW,UAAU;AAAA,MAC/D,CAAC;AAAA,MACD,MAAM;AAAA,MACN,OAAO,QAAQ,cAAc;AAAA,QAC3B,mBAAmB,WAAW;AAAA,MAChC,IAAI,CAAC;AAAA,IACP,GAAG,IAAI;AAAA,EACT;AACF;AACA,IAAM,cAAc,CAAC,SAAS,UAAU,OAAO,UAAU;AACvD,QAAM,OAAO,QAAQ,GAAG,UAAU,OAAO,KAAK;AAC9C,OAAK,MAAM,oBAAoB,IAAI,WAAW;AAC9C,SAAO,EAAE,OAAO;AAAA,IACd,SAAS,KAAK,WAAW;AAAA,IACzB,OAAO,KAAK;AAAA,EACd,GAAG,EAAE,UAAU;AAAA,IACb,WAAW,KAAK,aAAa;AAAA,IAC7B,IAAI,KAAK;AAAA,IACT,IAAI,KAAK;AAAA,IACT,GAAG,KAAK;AAAA,IACR,OAAO,QAAQ,cAAc;AAAA,MAC3B,mBAAmB,WAAW;AAAA,IAChC,IAAI,CAAC;AAAA,EACP,CAAC,CAAC;AACJ;AACA,IAAM,YAAY,CAAC,SAAS,UAAU,OAAO,UAAU;AACrD,QAAM,OAAO,QAAQ,GAAG,UAAU,OAAO,KAAK;AAC9C,OAAK,MAAM,oBAAoB,IAAI,WAAW;AAC9C,SAAO,EAAE,OAAO;AAAA,IACd,SAAS,KAAK,WAAW;AAAA,IACzB,OAAO,KAAK;AAAA,EACd,GAAG,EAAE,QAAQ;AAAA,IACX,WAAW;AAAA,IACX,IAAI,KAAK;AAAA,IACT,IAAI,KAAK;AAAA,EACX,CAAC,CAAC;AACJ;AACA,QAAQ,QAAQ;", "names": []}