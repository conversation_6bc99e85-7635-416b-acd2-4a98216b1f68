// OnlyWomans - Feminine Theme Variables
// Inspired by modern women's fashion apps with elegant and sophisticated colors

/** Ionic CSS Variables - OnlyWomans Feminine Theme **/
:root {
  /** primary - Rose Pink **/
  --ion-color-primary: #ec4899;
  --ion-color-primary-rgb: 236, 72, 153;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #d03f87;
  --ion-color-primary-tint: #ee5ba3;

  /** secondary - Soft Purple **/
  --ion-color-secondary: #a855f7;
  --ion-color-secondary-rgb: 168, 85, 247;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #944bd9;
  --ion-color-secondary-tint: #b166f8;

  /** tertiary - Elegant Gold **/
  --ion-color-tertiary: #f59e0b;
  --ion-color-tertiary-rgb: 245, 158, 11;
  --ion-color-tertiary-contrast: #000000;
  --ion-color-tertiary-contrast-rgb: 0, 0, 0;
  --ion-color-tertiary-shade: #d8890a;
  --ion-color-tertiary-tint: #f6a823;

  /** success - Mint Green **/
  --ion-color-success: #10b981;
  --ion-color-success-rgb: 16, 185, 129;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #0ea372;
  --ion-color-success-tint: #28c28e;

  /** warning - Coral **/
  --ion-color-warning: #f97316;
  --ion-color-warning-rgb: 249, 115, 22;
  --ion-color-warning-contrast: #ffffff;
  --ion-color-warning-contrast-rgb: 255, 255, 255;
  --ion-color-warning-shade: #db6514;
  --ion-color-warning-tint: #fa822d;

  /** danger - Rose Red **/
  --ion-color-danger: #e11d48;
  --ion-color-danger-rgb: 225, 29, 72;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #c61a40;
  --ion-color-danger-tint: #e4345a;

  /** dark - Charcoal **/
  --ion-color-dark: #1f2937;
  --ion-color-dark-rgb: 31, 41, 55;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #1b2431;
  --ion-color-dark-tint: #35404b;

  /** medium - Soft Gray **/
  --ion-color-medium: #6b7280;
  --ion-color-medium-rgb: 107, 114, 128;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255, 255, 255;
  --ion-color-medium-shade: #5e6470;
  --ion-color-medium-tint: #7a818d;

  /** light - Soft Pink Background **/
  --ion-color-light: #fdf2f8;
  --ion-color-light-rgb: 253, 242, 248;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0, 0, 0;
  --ion-color-light-shade: #dfd5da;
  --ion-color-light-tint: #fdf3f9;

  /** Custom OnlyWomans Colors **/
  --ow-rose-50: #fdf2f8;
  --ow-rose-100: #fce7f3;
  --ow-rose-200: #fbcfe8;
  --ow-rose-300: #f9a8d4;
  --ow-rose-400: #f472b6;
  --ow-rose-500: #ec4899;
  --ow-rose-600: #db2777;
  --ow-rose-700: #be185d;
  --ow-rose-800: #9d174d;
  --ow-rose-900: #831843;

  --ow-purple-50: #faf5ff;
  --ow-purple-100: #f3e8ff;
  --ow-purple-200: #e9d5ff;
  --ow-purple-300: #d8b4fe;
  --ow-purple-400: #c084fc;
  --ow-purple-500: #a855f7;
  --ow-purple-600: #9333ea;
  --ow-purple-700: #7c3aed;
  --ow-purple-800: #6b21a8;
  --ow-purple-900: #581c87;

  --ow-gold-50: #fffbeb;
  --ow-gold-100: #fef3c7;
  --ow-gold-200: #fde68a;
  --ow-gold-300: #fcd34d;
  --ow-gold-400: #fbbf24;
  --ow-gold-500: #f59e0b;
  --ow-gold-600: #d97706;
  --ow-gold-700: #b45309;
  --ow-gold-800: #92400e;
  --ow-gold-900: #78350f;

  /** Typography **/
  --ow-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --ow-font-family-display: 'Playfair Display', Georgia, serif;

  /** Spacing **/
  --ow-space-xs: 4px;
  --ow-space-sm: 8px;
  --ow-space-md: 16px;
  --ow-space-lg: 24px;
  --ow-space-xl: 32px;
  --ow-space-2xl: 48px;
  --ow-space-3xl: 64px;

  /** Border Radius **/
  --ow-radius-sm: 6px;
  --ow-radius-md: 12px;
  --ow-radius-lg: 16px;
  --ow-radius-xl: 24px;
  --ow-radius-full: 9999px;

  /** Shadows **/
  --ow-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --ow-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --ow-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ow-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}
