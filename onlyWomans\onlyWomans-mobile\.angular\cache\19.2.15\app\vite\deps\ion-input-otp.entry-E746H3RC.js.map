{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-input-otp.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, m as printIonWarning, e as getIonMode, h, F as Fragment, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { i as inheritAriaAttributes } from './helpers-1O4D2b7y.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nconst inputOtpIosCss = \".sc-ion-input-otp-ios-h{--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--padding-top:16px;--padding-end:0;--padding-bottom:16px;--padding-start:0;--color:initial;--min-width:40px;--separator-width:8px;--separator-height:var(--separator-width);--separator-border-radius:999px;--separator-color:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-size:0.875rem}.input-otp-group.sc-ion-input-otp-ios{-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.native-wrapper.sc-ion-input-otp-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:var(--min-width)}.native-input.sc-ion-input-otp-ios{border-radius:var(--border-radius);width:var(--width);min-width:inherit;height:var(--height);border-width:var(--border-width);border-style:solid;border-color:var(--border-color);background:var(--background);color:var(--color);font-size:inherit;text-align:center;-webkit-appearance:none;-moz-appearance:none;appearance:none}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{caret-color:var(--highlight-color)}.input-otp-description.sc-ion-input-otp-ios{color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));font-size:0.75rem;line-height:1.25rem;text-align:center}.input-otp-description-hidden.sc-ion-input-otp-ios{display:none}.input-otp-separator.sc-ion-input-otp-ios{border-radius:var(--separator-border-radius);-ms-flex-negative:0;flex-shrink:0;width:var(--separator-width);height:var(--separator-height);background:var(--separator-color)}.input-otp-size-small.sc-ion-input-otp-ios-h{--width:40px;--height:40px}.input-otp-size-small.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios{gap:8px}.input-otp-size-medium.sc-ion-input-otp-ios-h{--width:48px;--height:48px}.input-otp-size-large.sc-ion-input-otp-ios-h{--width:56px;--height:56px}.input-otp-size-medium.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios,.input-otp-size-large.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios{gap:12px}.input-otp-shape-round.sc-ion-input-otp-ios-h{--border-radius:16px}.input-otp-shape-soft.sc-ion-input-otp-ios-h{--border-radius:8px}.input-otp-shape-rectangular.sc-ion-input-otp-ios-h{--border-radius:0}.input-otp-fill-outline.sc-ion-input-otp-ios-h{--background:none}.input-otp-fill-solid.sc-ion-input-otp-ios-h{--border-color:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-disabled.sc-ion-input-otp-ios-h{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6))}.input-otp-fill-outline.input-otp-disabled.sc-ion-input-otp-ios-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.input-otp-disabled.sc-ion-input-otp-ios-h,.input-otp-disabled.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:disabled{cursor:not-allowed}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{--border-color:var(--highlight-color);outline:none}.input-otp-fill-outline.input-otp-readonly.sc-ion-input-otp-ios-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-fill-solid.input-otp-disabled.sc-ion-input-otp-ios-h,.input-otp-fill-solid.input-otp-readonly.sc-ion-input-otp-ios-h{--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.ion-touched.ion-invalid.sc-ion-input-otp-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-otp-ios-h{--highlight-color:var(--highlight-color-valid)}.has-focus.ion-valid.sc-ion-input-otp-ios-h,.ion-touched.ion-invalid.sc-ion-input-otp-ios-h{--border-color:var(--highlight-color)}.ion-color.sc-ion-input-otp-ios-h{--highlight-color-focused:var(--ion-color-base)}.input-otp-fill-outline.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{border-color:rgba(var(--ion-color-base-rgb), 0.6)}.input-otp-fill-outline.ion-color.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-outline.ion-color.has-focus.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.has-focus.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:var(--ion-color-danger, #c5000f)}.input-otp-fill-outline.ion-color.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-outline.ion-color.has-focus.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.has-focus.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:var(--ion-color-success, #2dd55b)}.input-otp-fill-outline.input-otp-disabled.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:rgba(var(--ion-color-base-rgb), 0.3)}.sc-ion-input-otp-ios-h{--border-width:0.55px}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{--border-width:1px}.input-otp-fill-outline.sc-ion-input-otp-ios-h{--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))))}\";\nconst inputOtpMdCss = \".sc-ion-input-otp-md-h{--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--padding-top:16px;--padding-end:0;--padding-bottom:16px;--padding-start:0;--color:initial;--min-width:40px;--separator-width:8px;--separator-height:var(--separator-width);--separator-border-radius:999px;--separator-color:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-size:0.875rem}.input-otp-group.sc-ion-input-otp-md{-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.native-wrapper.sc-ion-input-otp-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:var(--min-width)}.native-input.sc-ion-input-otp-md{border-radius:var(--border-radius);width:var(--width);min-width:inherit;height:var(--height);border-width:var(--border-width);border-style:solid;border-color:var(--border-color);background:var(--background);color:var(--color);font-size:inherit;text-align:center;-webkit-appearance:none;-moz-appearance:none;appearance:none}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{caret-color:var(--highlight-color)}.input-otp-description.sc-ion-input-otp-md{color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));font-size:0.75rem;line-height:1.25rem;text-align:center}.input-otp-description-hidden.sc-ion-input-otp-md{display:none}.input-otp-separator.sc-ion-input-otp-md{border-radius:var(--separator-border-radius);-ms-flex-negative:0;flex-shrink:0;width:var(--separator-width);height:var(--separator-height);background:var(--separator-color)}.input-otp-size-small.sc-ion-input-otp-md-h{--width:40px;--height:40px}.input-otp-size-small.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md{gap:8px}.input-otp-size-medium.sc-ion-input-otp-md-h{--width:48px;--height:48px}.input-otp-size-large.sc-ion-input-otp-md-h{--width:56px;--height:56px}.input-otp-size-medium.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md,.input-otp-size-large.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md{gap:12px}.input-otp-shape-round.sc-ion-input-otp-md-h{--border-radius:16px}.input-otp-shape-soft.sc-ion-input-otp-md-h{--border-radius:8px}.input-otp-shape-rectangular.sc-ion-input-otp-md-h{--border-radius:0}.input-otp-fill-outline.sc-ion-input-otp-md-h{--background:none}.input-otp-fill-solid.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-disabled.sc-ion-input-otp-md-h{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6))}.input-otp-fill-outline.input-otp-disabled.sc-ion-input-otp-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.input-otp-disabled.sc-ion-input-otp-md-h,.input-otp-disabled.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:disabled{cursor:not-allowed}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{--border-color:var(--highlight-color);outline:none}.input-otp-fill-outline.input-otp-readonly.sc-ion-input-otp-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-fill-solid.input-otp-disabled.sc-ion-input-otp-md-h,.input-otp-fill-solid.input-otp-readonly.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.ion-touched.ion-invalid.sc-ion-input-otp-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-otp-md-h{--highlight-color:var(--highlight-color-valid)}.has-focus.ion-valid.sc-ion-input-otp-md-h,.ion-touched.ion-invalid.sc-ion-input-otp-md-h{--border-color:var(--highlight-color)}.ion-color.sc-ion-input-otp-md-h{--highlight-color-focused:var(--ion-color-base)}.input-otp-fill-outline.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{border-color:rgba(var(--ion-color-base-rgb), 0.6)}.input-otp-fill-outline.ion-color.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-outline.ion-color.has-focus.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.has-focus.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:var(--ion-color-danger, #c5000f)}.input-otp-fill-outline.ion-color.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-outline.ion-color.has-focus.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.has-focus.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:var(--ion-color-success, #2dd55b)}.input-otp-fill-outline.input-otp-disabled.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:rgba(var(--ion-color-base-rgb), 0.3)}.sc-ion-input-otp-md-h{--border-width:1px}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{--border-width:2px}.input-otp-fill-outline.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3))}\";\nconst InputOTP = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionInput = createEvent(this, \"ionInput\", 7);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionComplete = createEvent(this, \"ionComplete\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.inheritedAttributes = {};\n    this.inputRefs = [];\n    this.inputId = `ion-input-otp-${inputIds++}`;\n    this.parsedSeparators = [];\n    /**\n     * Tracks whether the user is navigating through input boxes using keyboard navigation\n     * (arrow keys, tab) versus mouse clicks. This is used to determine the appropriate\n     * focus behavior when an input box is focused.\n     */\n    this.isKeyboardNavigation = false;\n    this.inputValues = [];\n    this.hasFocus = false;\n    /**\n     * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user.\n     * Available options: `\"off\"`, `\"none\"`, `\"on\"`, `\"sentences\"`, `\"words\"`, `\"characters\"`.\n     */\n    this.autocapitalize = 'off';\n    /**\n     * If `true`, the user cannot interact with the input.\n     */\n    this.disabled = false;\n    /**\n     * The fill for the input boxes. If `\"solid\"` the input boxes will have a background. If\n     * `\"outline\"` the input boxes will be transparent with a border.\n     */\n    this.fill = 'outline';\n    /**\n     * The number of input boxes to display.\n     */\n    this.length = 4;\n    /**\n     * If `true`, the user cannot modify the value.\n     */\n    this.readonly = false;\n    /**\n     * The shape of the input boxes.\n     * If \"round\" they will have an increased border radius.\n     * If \"rectangular\" they will have no border radius.\n     * If \"soft\" they will have a soft border radius.\n     */\n    this.shape = 'round';\n    /**\n     * The size of the input boxes.\n     */\n    this.size = 'medium';\n    /**\n     * The type of input allowed in the input boxes.\n     */\n    this.type = 'number';\n    /**\n     * The value of the input group.\n     */\n    this.value = '';\n    /**\n     * Handles the focus behavior for the input OTP component.\n     *\n     * Focus behavior:\n     * 1. Keyboard navigation: Allow normal focus movement\n     * 2. Mouse click:\n     *    - If clicked box has value: Focus that box\n     *    - If clicked box is empty: Focus first empty box\n     *\n     * Emits the `ionFocus` event when the input group gains focus.\n     */\n    this.onFocus = index => event => {\n      var _a;\n      const {\n        inputRefs\n      } = this;\n      // Only emit ionFocus and set the focusedValue when the\n      // component first gains focus\n      if (!this.hasFocus) {\n        this.ionFocus.emit(event);\n        this.focusedValue = this.value;\n      }\n      this.hasFocus = true;\n      let finalIndex = index;\n      if (!this.isKeyboardNavigation) {\n        // If the clicked box has a value, focus it\n        // Otherwise focus the first empty box\n        const targetIndex = this.inputValues[index] ? index : this.getFirstEmptyIndex();\n        finalIndex = targetIndex === -1 ? this.length - 1 : targetIndex;\n        // Focus the target box\n        (_a = this.inputRefs[finalIndex]) === null || _a === void 0 ? void 0 : _a.focus();\n      }\n      // Update tabIndexes to match the focused box\n      inputRefs.forEach((input, i) => {\n        input.tabIndex = i === finalIndex ? 0 : -1;\n      });\n      // Reset the keyboard navigation flag\n      this.isKeyboardNavigation = false;\n    };\n    /**\n     * Handles the blur behavior for the input OTP component.\n     * Emits the `ionBlur` event when the input group loses focus.\n     */\n    this.onBlur = event => {\n      const {\n        inputRefs\n      } = this;\n      const relatedTarget = event.relatedTarget;\n      // Do not emit blur if we're moving to another input box in the same component\n      const isInternalFocus = relatedTarget != null && inputRefs.includes(relatedTarget);\n      if (!isInternalFocus) {\n        this.hasFocus = false;\n        // Reset tabIndexes when focus leaves the component\n        this.updateTabIndexes();\n        // Always emit ionBlur when focus leaves the component\n        this.ionBlur.emit(event);\n        // Only emit ionChange if the value has actually changed\n        if (this.focusedValue !== this.value) {\n          this.emitIonChange(event);\n        }\n      }\n    };\n    /**\n     * Handles keyboard navigation and input for the OTP component.\n     *\n     * Navigation:\n     * - Backspace: Clears current input and moves to previous box if empty\n     * - Arrow Left/Right: Moves focus between input boxes\n     * - Tab: Allows normal tab navigation between components\n     *\n     * Input Behavior:\n     * - Validates input against the allowed pattern\n     * - When entering a key in a filled box:\n     *   - Shifts existing values right if there is room\n     *   - Updates the value of the input group\n     *   - Prevents default behavior to avoid automatic focus shift\n     */\n    this.onKeyDown = index => event => {\n      const {\n        length\n      } = this;\n      const rtl = isRTL(this.el);\n      const input = event.target;\n      // Meta shortcuts are used to copy, paste, and select text\n      // We don't want to handle these keys here\n      const metaShortcuts = ['a', 'c', 'v', 'x', 'r', 'z', 'y'];\n      const isTextSelection = input.selectionStart !== input.selectionEnd;\n      // Return if the key is a meta shortcut or the input value\n      // text is selected and let the onPaste / onInput handler manage it\n      if (isTextSelection || (event.metaKey || event.ctrlKey) && metaShortcuts.includes(event.key.toLowerCase())) {\n        return;\n      }\n      if (event.key === 'Backspace') {\n        if (this.inputValues[index]) {\n          // Shift all values to the right of the current index left by one\n          for (let i = index; i < length - 1; i++) {\n            this.inputValues[i] = this.inputValues[i + 1];\n          }\n          // Clear the last box\n          this.inputValues[length - 1] = '';\n          // Update all inputRefs to match inputValues\n          for (let i = 0; i < length; i++) {\n            this.inputRefs[i].value = this.inputValues[i] || '';\n          }\n          this.updateValue(event);\n          event.preventDefault();\n        } else if (!this.inputValues[index] && index > 0) {\n          // If current input is empty, move to previous input\n          this.focusPrevious(index);\n        }\n      } else if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {\n        this.isKeyboardNavigation = true;\n        event.preventDefault();\n        const isLeft = event.key === 'ArrowLeft';\n        const shouldMoveNext = isLeft && rtl || !isLeft && !rtl;\n        // Only allow moving to the next input if the current has a value\n        if (shouldMoveNext) {\n          if (this.inputValues[index] && index < length - 1) {\n            this.focusNext(index);\n          }\n        } else {\n          this.focusPrevious(index);\n        }\n      } else if (event.key === 'Tab') {\n        this.isKeyboardNavigation = true;\n        // Let all tab events proceed normally\n        return;\n      }\n      // If the input box contains a value and the key being\n      // entered is a valid key for the input box update the value\n      // and shift the values to the right if there is room.\n      if (this.inputValues[index] && this.validKeyPattern.test(event.key)) {\n        if (!this.inputValues[length - 1]) {\n          for (let i = length - 1; i > index; i--) {\n            this.inputValues[i] = this.inputValues[i - 1];\n            this.inputRefs[i].value = this.inputValues[i] || '';\n          }\n        }\n        this.inputValues[index] = event.key;\n        this.inputRefs[index].value = event.key;\n        this.updateValue(event);\n        // Prevent default to avoid the browser from\n        // automatically moving the focus to the next input\n        event.preventDefault();\n      }\n    };\n    this.onInput = index => event => {\n      const {\n        length,\n        validKeyPattern\n      } = this;\n      const value = event.target.value;\n      // If the value is longer than 1 character (autofill), split it into\n      // characters and filter out invalid ones\n      if (value.length > 1) {\n        const validChars = value.split('').filter(char => validKeyPattern.test(char)).slice(0, length);\n        // If there are no valid characters coming from the\n        // autofill, all input refs have to be cleared after the\n        // browser has finished the autofill behavior\n        if (validChars.length === 0) {\n          requestAnimationFrame(() => {\n            this.inputRefs.forEach(input => {\n              input.value = '';\n            });\n          });\n        }\n        // Update the value of the input group and emit the input change event\n        this.value = validChars.join('');\n        this.updateValue(event);\n        // Focus the first empty input box or the last input box if all boxes\n        // are filled after a small delay to ensure the input boxes have been\n        // updated before moving the focus\n        setTimeout(() => {\n          var _a;\n          const nextIndex = validChars.length < length ? validChars.length : length - 1;\n          (_a = this.inputRefs[nextIndex]) === null || _a === void 0 ? void 0 : _a.focus();\n        }, 20);\n        return;\n      }\n      // Only allow input if it matches the pattern\n      if (value.length > 0 && !validKeyPattern.test(value)) {\n        this.inputRefs[index].value = '';\n        this.inputValues[index] = '';\n        return;\n      }\n      // For single character input, fill the current box\n      this.inputValues[index] = value;\n      this.updateValue(event);\n      if (value.length > 0) {\n        this.focusNext(index);\n      }\n    };\n    /**\n     * Handles pasting text into the input OTP component.\n     * This function prevents the default paste behavior and\n     * validates the pasted text against the allowed pattern.\n     * It then updates the value of the input group and focuses\n     * the next empty input after pasting.\n     */\n    this.onPaste = event => {\n      var _a, _b, _c;\n      const {\n        inputRefs,\n        length,\n        validKeyPattern\n      } = this;\n      event.preventDefault();\n      const pastedText = (_a = event.clipboardData) === null || _a === void 0 ? void 0 : _a.getData('text');\n      // If there is no pasted text, still emit the input change event\n      // because this is how the native input element behaves\n      // but return early because there is nothing to paste.\n      if (!pastedText) {\n        this.emitIonInput(event);\n        return;\n      }\n      const validChars = pastedText.split('').filter(char => validKeyPattern.test(char)).slice(0, length);\n      // Always paste starting at the first box\n      validChars.forEach((char, index) => {\n        if (index < length) {\n          this.inputRefs[index].value = char;\n          this.inputValues[index] = char;\n        }\n      });\n      // Update the value so that all input boxes are updated\n      this.value = validChars.join('');\n      this.updateValue(event);\n      // Focus the next empty input after pasting\n      // If all boxes are filled, focus the last input\n      const nextEmptyIndex = validChars.length;\n      if (nextEmptyIndex < length) {\n        (_b = inputRefs[nextEmptyIndex]) === null || _b === void 0 ? void 0 : _b.focus();\n      } else {\n        (_c = inputRefs[length - 1]) === null || _c === void 0 ? void 0 : _c.focus();\n      }\n    };\n  }\n  /**\n   * Sets focus to an input box.\n   * @param index - The index of the input box to focus (0-based).\n   * If provided and the input box has a value, the input box at that index will be focused.\n   * Otherwise, the first empty input box or the last input if all are filled will be focused.\n   */\n  async setFocus(index) {\n    var _a, _b;\n    if (typeof index === 'number') {\n      const validIndex = Math.max(0, Math.min(index, this.length - 1));\n      (_a = this.inputRefs[validIndex]) === null || _a === void 0 ? void 0 : _a.focus();\n    } else {\n      const tabbableIndex = this.getTabbableIndex();\n      (_b = this.inputRefs[tabbableIndex]) === null || _b === void 0 ? void 0 : _b.focus();\n    }\n  }\n  valueChanged() {\n    this.initializeValues();\n    this.updateTabIndexes();\n  }\n  /**\n   * Processes the separators prop into an array of numbers.\n   *\n   * If the separators prop is not provided, returns an empty array.\n   * If the separators prop is 'all', returns an array of all valid positions (1 to length-1).\n   * If the separators prop is an array, returns it as is.\n   * If the separators prop is a string, splits it by commas and parses each part as a number.\n   *\n   * If the separators are greater than the input length, it will warn and ignore the separators.\n   */\n  processSeparators() {\n    const {\n      separators,\n      length\n    } = this;\n    if (separators === undefined) {\n      this.parsedSeparators = [];\n      return;\n    }\n    if (typeof separators === 'string' && separators !== 'all') {\n      const isValidFormat = /^(\\d+)(,\\d+)*$/.test(separators);\n      if (!isValidFormat) {\n        printIonWarning(`[ion-input-otp] - Invalid separators format. Expected a comma-separated list of numbers, an array of numbers, or \"all\". Received: ${separators}`, this.el);\n        this.parsedSeparators = [];\n        return;\n      }\n    }\n    let separatorValues;\n    if (separators === 'all') {\n      separatorValues = Array.from({\n        length: length - 1\n      }, (_, i) => i + 1);\n    } else if (Array.isArray(separators)) {\n      separatorValues = separators;\n    } else {\n      separatorValues = separators.split(',').map(pos => parseInt(pos, 10)).filter(pos => !isNaN(pos));\n    }\n    // Check for duplicate separator positions\n    const duplicates = separatorValues.filter((pos, index) => separatorValues.indexOf(pos) !== index);\n    if (duplicates.length > 0) {\n      printIonWarning(`[ion-input-otp] - Duplicate separator positions are not allowed. Received: ${separators}`, this.el);\n    }\n    const invalidSeparators = separatorValues.filter(pos => pos > length);\n    if (invalidSeparators.length > 0) {\n      printIonWarning(`[ion-input-otp] - The following separator positions are greater than the input length (${length}): ${invalidSeparators.join(', ')}. These separators will be ignored.`, this.el);\n    }\n    this.parsedSeparators = separatorValues.filter(pos => pos <= length);\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n    this.processSeparators();\n    this.initializeValues();\n  }\n  componentDidLoad() {\n    this.updateTabIndexes();\n  }\n  /**\n   * Get the regex pattern for allowed characters.\n   * If a pattern is provided, use it to create a regex pattern\n   * Otherwise, use the default regex pattern based on type\n   */\n  get validKeyPattern() {\n    return new RegExp(`^${this.getPattern()}$`, 'u');\n  }\n  /**\n   * Gets the string pattern to pass to the input element\n   * and use in the regex for allowed characters.\n   */\n  getPattern() {\n    const {\n      pattern,\n      type\n    } = this;\n    if (pattern) {\n      return pattern;\n    }\n    return type === 'number' ? '[\\\\p{N}]' : '[\\\\p{L}\\\\p{N}]';\n  }\n  /**\n   * Get the default value for inputmode.\n   * If inputmode is provided, use it.\n   * Otherwise, use the default inputmode based on type\n   */\n  getInputmode() {\n    const {\n      inputmode\n    } = this;\n    if (inputmode) {\n      return inputmode;\n    }\n    if (this.type == 'number') {\n      return 'numeric';\n    } else {\n      return 'text';\n    }\n  }\n  /**\n   * Initializes the input values array based on the current value prop.\n   * This splits the value into individual characters and validates them against\n   * the allowed pattern. The values are then used as the values in the native\n   * input boxes and the value of the input group is updated.\n   */\n  initializeValues() {\n    // Clear all input values\n    this.inputValues = Array(this.length).fill('');\n    // If the value is null, undefined, or an empty string, return\n    if (this.value == null || String(this.value).length === 0) {\n      return;\n    }\n    // Split the value into individual characters and validate\n    // them against the allowed pattern\n    const chars = String(this.value).split('').slice(0, this.length);\n    chars.forEach((char, index) => {\n      if (this.validKeyPattern.test(char)) {\n        this.inputValues[index] = char;\n      }\n    });\n    // Update the value without emitting events\n    this.value = this.inputValues.join('');\n  }\n  /**\n   * Updates the value of the input group.\n   * This updates the value of the input group and emits an `ionChange` event.\n   * If all of the input boxes are filled, it emits an `ionComplete` event.\n   */\n  updateValue(event) {\n    const {\n      inputValues,\n      length\n    } = this;\n    const newValue = inputValues.join('');\n    this.value = newValue;\n    this.emitIonInput(event);\n    if (newValue.length === length) {\n      this.ionComplete.emit({\n        value: newValue\n      });\n    }\n  }\n  /**\n   * Emits an `ionChange` event.\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitIonChange(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    this.ionChange.emit({\n      value: newValue,\n      event\n    });\n  }\n  /**\n   * Emits an `ionInput` event.\n   * This is used to emit the input value when the user types,\n   * backspaces, or pastes.\n   */\n  emitIonInput(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    this.ionInput.emit({\n      value: newValue,\n      event\n    });\n  }\n  /**\n   * Focuses the next input box.\n   */\n  focusNext(currentIndex) {\n    var _a;\n    const {\n      inputRefs,\n      length\n    } = this;\n    if (currentIndex < length - 1) {\n      (_a = inputRefs[currentIndex + 1]) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n  }\n  /**\n   * Focuses the previous input box.\n   */\n  focusPrevious(currentIndex) {\n    var _a;\n    const {\n      inputRefs\n    } = this;\n    if (currentIndex > 0) {\n      (_a = inputRefs[currentIndex - 1]) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n  }\n  /**\n   * Searches through the input values and returns the index\n   * of the first empty input.\n   * Returns -1 if all inputs are filled.\n   */\n  getFirstEmptyIndex() {\n    var _a;\n    const {\n      inputValues,\n      length\n    } = this;\n    // Create an array of the same length as the input OTP\n    // and fill it with the input values\n    const values = Array.from({\n      length\n    }, (_, i) => inputValues[i] || '');\n    return (_a = values.findIndex(value => !value || value === '')) !== null && _a !== void 0 ? _a : -1;\n  }\n  /**\n   * Returns the index of the input that should be tabbed to.\n   * If all inputs are filled, returns the last input's index.\n   * Otherwise, returns the index of the first empty input.\n   */\n  getTabbableIndex() {\n    const {\n      length\n    } = this;\n    const firstEmptyIndex = this.getFirstEmptyIndex();\n    return firstEmptyIndex === -1 ? length - 1 : firstEmptyIndex;\n  }\n  /**\n   * Updates the tabIndexes for the input boxes.\n   * This is used to ensure that the correct input is\n   * focused when the user navigates using the tab key.\n   */\n  updateTabIndexes() {\n    const {\n      inputRefs,\n      inputValues,\n      length\n    } = this;\n    // Find first empty index after any filled boxes\n    let firstEmptyIndex = -1;\n    for (let i = 0; i < length; i++) {\n      if (!inputValues[i] || inputValues[i] === '') {\n        firstEmptyIndex = i;\n        break;\n      }\n    }\n    // Update tabIndex and aria-hidden for all inputs\n    inputRefs.forEach((input, index) => {\n      const shouldBeTabbable = firstEmptyIndex === -1 ? index === length - 1 : firstEmptyIndex === index;\n      input.tabIndex = shouldBeTabbable ? 0 : -1;\n      // If the input is empty and not the first empty input,\n      // it should be hidden from screen readers.\n      const isEmpty = !inputValues[index] || inputValues[index] === '';\n      input.setAttribute('aria-hidden', isEmpty && !shouldBeTabbable ? 'true' : 'false');\n    });\n  }\n  /**\n   * Determines if a separator should be shown for a given index by\n   * checking if the index is included in the parsed separators array.\n   */\n  showSeparator(index) {\n    const {\n      length\n    } = this;\n    return this.parsedSeparators.includes(index + 1) && index < length - 1;\n  }\n  render() {\n    var _a, _b;\n    const {\n      autocapitalize,\n      color,\n      disabled,\n      el,\n      fill,\n      hasFocus,\n      inheritedAttributes,\n      inputId,\n      inputRefs,\n      inputValues,\n      length,\n      readonly,\n      shape,\n      size\n    } = this;\n    const mode = getIonMode(this);\n    const inputmode = this.getInputmode();\n    const tabbableIndex = this.getTabbableIndex();\n    const pattern = this.getPattern();\n    const hasDescription = ((_b = (_a = el.querySelector('.input-otp-description')) === null || _a === void 0 ? void 0 : _a.textContent) === null || _b === void 0 ? void 0 : _b.trim()) !== '';\n    return h(Host, {\n      key: 'df8fca036cedea0812185a02e3b655d7d76285e0',\n      class: createColorClasses(color, {\n        [mode]: true,\n        'has-focus': hasFocus,\n        [`input-otp-size-${size}`]: true,\n        [`input-otp-shape-${shape}`]: true,\n        [`input-otp-fill-${fill}`]: true,\n        'input-otp-disabled': disabled,\n        'input-otp-readonly': readonly\n      })\n    }, h(\"div\", Object.assign({\n      key: '831be3f939cf037f0eb8d7e37e0afd4ef9a3c2c5',\n      role: \"group\",\n      \"aria-label\": \"One-time password input\",\n      class: \"input-otp-group\"\n    }, inheritedAttributes), Array.from({\n      length\n    }).map((_, index) => h(Fragment, null, h(\"div\", {\n      class: \"native-wrapper\"\n    }, h(\"input\", {\n      class: \"native-input\",\n      id: `${inputId}-${index}`,\n      \"aria-label\": `Input ${index + 1} of ${length}`,\n      type: \"text\",\n      autoCapitalize: autocapitalize,\n      inputmode: inputmode,\n      pattern: pattern,\n      disabled: disabled,\n      readOnly: readonly,\n      tabIndex: index === tabbableIndex ? 0 : -1,\n      value: inputValues[index] || '',\n      autocomplete: \"one-time-code\",\n      ref: el => inputRefs[index] = el,\n      onInput: this.onInput(index),\n      onBlur: this.onBlur,\n      onFocus: this.onFocus(index),\n      onKeyDown: this.onKeyDown(index),\n      onPaste: this.onPaste\n    })), this.showSeparator(index) && h(\"div\", {\n      class: \"input-otp-separator\"\n    })))), h(\"div\", {\n      key: '5311fedc34f7af3efd5f69e5a3d768055119c4f1',\n      class: {\n        'input-otp-description': true,\n        'input-otp-description-hidden': !hasDescription\n      }\n    }, h(\"slot\", {\n      key: '9e8afa2f7fa76c3092582dc27770fdf565a1b9ba'\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"value\": [\"valueChanged\"],\n      \"separators\": [\"processSeparators\"],\n      \"length\": [\"processSeparators\"]\n    };\n  }\n};\nlet inputIds = 0;\nInputOTP.style = {\n  ios: inputOtpIosCss,\n  md: inputOtpMdCss\n};\nexport { InputOTP as ion_input_otp };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,IAAM,WAAW,MAAM;AAAA,EACrB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,cAAc,YAAY,MAAM,eAAe,CAAC;AACrD,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,sBAAsB,CAAC;AAC5B,SAAK,YAAY,CAAC;AAClB,SAAK,UAAU,iBAAiB,UAAU;AAC1C,SAAK,mBAAmB,CAAC;AAMzB,SAAK,uBAAuB;AAC5B,SAAK,cAAc,CAAC;AACpB,SAAK,WAAW;AAKhB,SAAK,iBAAiB;AAItB,SAAK,WAAW;AAKhB,SAAK,OAAO;AAIZ,SAAK,SAAS;AAId,SAAK,WAAW;AAOhB,SAAK,QAAQ;AAIb,SAAK,OAAO;AAIZ,SAAK,OAAO;AAIZ,SAAK,QAAQ;AAYb,SAAK,UAAU,WAAS,WAAS;AAC/B,UAAI;AACJ,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AAGJ,UAAI,CAAC,KAAK,UAAU;AAClB,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,eAAe,KAAK;AAAA,MAC3B;AACA,WAAK,WAAW;AAChB,UAAI,aAAa;AACjB,UAAI,CAAC,KAAK,sBAAsB;AAG9B,cAAM,cAAc,KAAK,YAAY,KAAK,IAAI,QAAQ,KAAK,mBAAmB;AAC9E,qBAAa,gBAAgB,KAAK,KAAK,SAAS,IAAI;AAEpD,SAAC,KAAK,KAAK,UAAU,UAAU,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,MAClF;AAEA,gBAAU,QAAQ,CAAC,OAAO,MAAM;AAC9B,cAAM,WAAW,MAAM,aAAa,IAAI;AAAA,MAC1C,CAAC;AAED,WAAK,uBAAuB;AAAA,IAC9B;AAKA,SAAK,SAAS,WAAS;AACrB,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM,gBAAgB,MAAM;AAE5B,YAAM,kBAAkB,iBAAiB,QAAQ,UAAU,SAAS,aAAa;AACjF,UAAI,CAAC,iBAAiB;AACpB,aAAK,WAAW;AAEhB,aAAK,iBAAiB;AAEtB,aAAK,QAAQ,KAAK,KAAK;AAEvB,YAAI,KAAK,iBAAiB,KAAK,OAAO;AACpC,eAAK,cAAc,KAAK;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAgBA,SAAK,YAAY,WAAS,WAAS;AACjC,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM,MAAM,MAAM,KAAK,EAAE;AACzB,YAAM,QAAQ,MAAM;AAGpB,YAAM,gBAAgB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AACxD,YAAM,kBAAkB,MAAM,mBAAmB,MAAM;AAGvD,UAAI,oBAAoB,MAAM,WAAW,MAAM,YAAY,cAAc,SAAS,MAAM,IAAI,YAAY,CAAC,GAAG;AAC1G;AAAA,MACF;AACA,UAAI,MAAM,QAAQ,aAAa;AAC7B,YAAI,KAAK,YAAY,KAAK,GAAG;AAE3B,mBAAS,IAAI,OAAO,IAAI,SAAS,GAAG,KAAK;AACvC,iBAAK,YAAY,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC;AAAA,UAC9C;AAEA,eAAK,YAAY,SAAS,CAAC,IAAI;AAE/B,mBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,iBAAK,UAAU,CAAC,EAAE,QAAQ,KAAK,YAAY,CAAC,KAAK;AAAA,UACnD;AACA,eAAK,YAAY,KAAK;AACtB,gBAAM,eAAe;AAAA,QACvB,WAAW,CAAC,KAAK,YAAY,KAAK,KAAK,QAAQ,GAAG;AAEhD,eAAK,cAAc,KAAK;AAAA,QAC1B;AAAA,MACF,WAAW,MAAM,QAAQ,eAAe,MAAM,QAAQ,cAAc;AAClE,aAAK,uBAAuB;AAC5B,cAAM,eAAe;AACrB,cAAM,SAAS,MAAM,QAAQ;AAC7B,cAAM,iBAAiB,UAAU,OAAO,CAAC,UAAU,CAAC;AAEpD,YAAI,gBAAgB;AAClB,cAAI,KAAK,YAAY,KAAK,KAAK,QAAQ,SAAS,GAAG;AACjD,iBAAK,UAAU,KAAK;AAAA,UACtB;AAAA,QACF,OAAO;AACL,eAAK,cAAc,KAAK;AAAA,QAC1B;AAAA,MACF,WAAW,MAAM,QAAQ,OAAO;AAC9B,aAAK,uBAAuB;AAE5B;AAAA,MACF;AAIA,UAAI,KAAK,YAAY,KAAK,KAAK,KAAK,gBAAgB,KAAK,MAAM,GAAG,GAAG;AACnE,YAAI,CAAC,KAAK,YAAY,SAAS,CAAC,GAAG;AACjC,mBAAS,IAAI,SAAS,GAAG,IAAI,OAAO,KAAK;AACvC,iBAAK,YAAY,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC;AAC5C,iBAAK,UAAU,CAAC,EAAE,QAAQ,KAAK,YAAY,CAAC,KAAK;AAAA,UACnD;AAAA,QACF;AACA,aAAK,YAAY,KAAK,IAAI,MAAM;AAChC,aAAK,UAAU,KAAK,EAAE,QAAQ,MAAM;AACpC,aAAK,YAAY,KAAK;AAGtB,cAAM,eAAe;AAAA,MACvB;AAAA,IACF;AACA,SAAK,UAAU,WAAS,WAAS;AAC/B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,QAAQ,MAAM,OAAO;AAG3B,UAAI,MAAM,SAAS,GAAG;AACpB,cAAM,aAAa,MAAM,MAAM,EAAE,EAAE,OAAO,UAAQ,gBAAgB,KAAK,IAAI,CAAC,EAAE,MAAM,GAAG,MAAM;AAI7F,YAAI,WAAW,WAAW,GAAG;AAC3B,gCAAsB,MAAM;AAC1B,iBAAK,UAAU,QAAQ,WAAS;AAC9B,oBAAM,QAAQ;AAAA,YAChB,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAEA,aAAK,QAAQ,WAAW,KAAK,EAAE;AAC/B,aAAK,YAAY,KAAK;AAItB,mBAAW,MAAM;AACf,cAAI;AACJ,gBAAM,YAAY,WAAW,SAAS,SAAS,WAAW,SAAS,SAAS;AAC5E,WAAC,KAAK,KAAK,UAAU,SAAS,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,QACjF,GAAG,EAAE;AACL;AAAA,MACF;AAEA,UAAI,MAAM,SAAS,KAAK,CAAC,gBAAgB,KAAK,KAAK,GAAG;AACpD,aAAK,UAAU,KAAK,EAAE,QAAQ;AAC9B,aAAK,YAAY,KAAK,IAAI;AAC1B;AAAA,MACF;AAEA,WAAK,YAAY,KAAK,IAAI;AAC1B,WAAK,YAAY,KAAK;AACtB,UAAI,MAAM,SAAS,GAAG;AACpB,aAAK,UAAU,KAAK;AAAA,MACtB;AAAA,IACF;AAQA,SAAK,UAAU,WAAS;AACtB,UAAI,IAAI,IAAI;AACZ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,eAAe;AACrB,YAAM,cAAc,KAAK,MAAM,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,MAAM;AAIpG,UAAI,CAAC,YAAY;AACf,aAAK,aAAa,KAAK;AACvB;AAAA,MACF;AACA,YAAM,aAAa,WAAW,MAAM,EAAE,EAAE,OAAO,UAAQ,gBAAgB,KAAK,IAAI,CAAC,EAAE,MAAM,GAAG,MAAM;AAElG,iBAAW,QAAQ,CAAC,MAAM,UAAU;AAClC,YAAI,QAAQ,QAAQ;AAClB,eAAK,UAAU,KAAK,EAAE,QAAQ;AAC9B,eAAK,YAAY,KAAK,IAAI;AAAA,QAC5B;AAAA,MACF,CAAC;AAED,WAAK,QAAQ,WAAW,KAAK,EAAE;AAC/B,WAAK,YAAY,KAAK;AAGtB,YAAM,iBAAiB,WAAW;AAClC,UAAI,iBAAiB,QAAQ;AAC3B,SAAC,KAAK,UAAU,cAAc,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,MACjF,OAAO;AACL,SAAC,KAAK,UAAU,SAAS,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,MAC7E;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,SAAS,OAAO;AAAA;AACpB,UAAI,IAAI;AACR,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,aAAa,KAAK,IAAI,GAAG,KAAK,IAAI,OAAO,KAAK,SAAS,CAAC,CAAC;AAC/D,SAAC,KAAK,KAAK,UAAU,UAAU,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,MAClF,OAAO;AACL,cAAM,gBAAgB,KAAK,iBAAiB;AAC5C,SAAC,KAAK,KAAK,UAAU,aAAa,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,MACrF;AAAA,IACF;AAAA;AAAA,EACA,eAAe;AACb,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,oBAAoB;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,eAAe,QAAW;AAC5B,WAAK,mBAAmB,CAAC;AACzB;AAAA,IACF;AACA,QAAI,OAAO,eAAe,YAAY,eAAe,OAAO;AAC1D,YAAM,gBAAgB,iBAAiB,KAAK,UAAU;AACtD,UAAI,CAAC,eAAe;AAClB,wBAAgB,qIAAqI,UAAU,IAAI,KAAK,EAAE;AAC1K,aAAK,mBAAmB,CAAC;AACzB;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACJ,QAAI,eAAe,OAAO;AACxB,wBAAkB,MAAM,KAAK;AAAA,QAC3B,QAAQ,SAAS;AAAA,MACnB,GAAG,CAAC,GAAG,MAAM,IAAI,CAAC;AAAA,IACpB,WAAW,MAAM,QAAQ,UAAU,GAAG;AACpC,wBAAkB;AAAA,IACpB,OAAO;AACL,wBAAkB,WAAW,MAAM,GAAG,EAAE,IAAI,SAAO,SAAS,KAAK,EAAE,CAAC,EAAE,OAAO,SAAO,CAAC,MAAM,GAAG,CAAC;AAAA,IACjG;AAEA,UAAM,aAAa,gBAAgB,OAAO,CAAC,KAAK,UAAU,gBAAgB,QAAQ,GAAG,MAAM,KAAK;AAChG,QAAI,WAAW,SAAS,GAAG;AACzB,sBAAgB,8EAA8E,UAAU,IAAI,KAAK,EAAE;AAAA,IACrH;AACA,UAAM,oBAAoB,gBAAgB,OAAO,SAAO,MAAM,MAAM;AACpE,QAAI,kBAAkB,SAAS,GAAG;AAChC,sBAAgB,0FAA0F,MAAM,MAAM,kBAAkB,KAAK,IAAI,CAAC,uCAAuC,KAAK,EAAE;AAAA,IAClM;AACA,SAAK,mBAAmB,gBAAgB,OAAO,SAAO,OAAO,MAAM;AAAA,EACrE;AAAA,EACA,oBAAoB;AAClB,SAAK,sBAAsB,sBAAsB,KAAK,EAAE;AACxD,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,mBAAmB;AACjB,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,kBAAkB;AACpB,WAAO,IAAI,OAAO,IAAI,KAAK,WAAW,CAAC,KAAK,GAAG;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,SAAS;AACX,aAAO;AAAA,IACT;AACA,WAAO,SAAS,WAAW,aAAa;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AACb,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,WAAW;AACb,aAAO;AAAA,IACT;AACA,QAAI,KAAK,QAAQ,UAAU;AACzB,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB;AAEjB,SAAK,cAAc,MAAM,KAAK,MAAM,EAAE,KAAK,EAAE;AAE7C,QAAI,KAAK,SAAS,QAAQ,OAAO,KAAK,KAAK,EAAE,WAAW,GAAG;AACzD;AAAA,IACF;AAGA,UAAM,QAAQ,OAAO,KAAK,KAAK,EAAE,MAAM,EAAE,EAAE,MAAM,GAAG,KAAK,MAAM;AAC/D,UAAM,QAAQ,CAAC,MAAM,UAAU;AAC7B,UAAI,KAAK,gBAAgB,KAAK,IAAI,GAAG;AACnC,aAAK,YAAY,KAAK,IAAI;AAAA,MAC5B;AAAA,IACF,CAAC;AAED,SAAK,QAAQ,KAAK,YAAY,KAAK,EAAE;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACjB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,YAAY,KAAK,EAAE;AACpC,SAAK,QAAQ;AACb,SAAK,aAAa,KAAK;AACvB,QAAI,SAAS,WAAW,QAAQ;AAC9B,WAAK,YAAY,KAAK;AAAA,QACpB,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,OAAO;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AAEJ,UAAM,WAAW,SAAS,OAAO,QAAQ,MAAM,SAAS;AACxD,SAAK,UAAU,KAAK;AAAA,MAClB,OAAO;AAAA,MACP;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,OAAO;AAClB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AAEJ,UAAM,WAAW,SAAS,OAAO,QAAQ,MAAM,SAAS;AACxD,SAAK,SAAS,KAAK;AAAA,MACjB,OAAO;AAAA,MACP;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,cAAc;AACtB,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,eAAe,SAAS,GAAG;AAC7B,OAAC,KAAK,UAAU,eAAe,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,IACnF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,cAAc;AAC1B,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,eAAe,GAAG;AACpB,OAAC,KAAK,UAAU,eAAe,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,IACnF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB;AACnB,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AAGJ,UAAM,SAAS,MAAM,KAAK;AAAA,MACxB;AAAA,IACF,GAAG,CAAC,GAAG,MAAM,YAAY,CAAC,KAAK,EAAE;AACjC,YAAQ,KAAK,OAAO,UAAU,WAAS,CAAC,SAAS,UAAU,EAAE,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,EACnG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB;AACjB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,kBAAkB,KAAK,mBAAmB;AAChD,WAAO,oBAAoB,KAAK,SAAS,IAAI;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB;AACjB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,QAAI,kBAAkB;AACtB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAI,CAAC,YAAY,CAAC,KAAK,YAAY,CAAC,MAAM,IAAI;AAC5C,0BAAkB;AAClB;AAAA,MACF;AAAA,IACF;AAEA,cAAU,QAAQ,CAAC,OAAO,UAAU;AAClC,YAAM,mBAAmB,oBAAoB,KAAK,UAAU,SAAS,IAAI,oBAAoB;AAC7F,YAAM,WAAW,mBAAmB,IAAI;AAGxC,YAAM,UAAU,CAAC,YAAY,KAAK,KAAK,YAAY,KAAK,MAAM;AAC9D,YAAM,aAAa,eAAe,WAAW,CAAC,mBAAmB,SAAS,OAAO;AAAA,IACnF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,OAAO;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,KAAK,iBAAiB,SAAS,QAAQ,CAAC,KAAK,QAAQ,SAAS;AAAA,EACvE;AAAA,EACA,SAAS;AACP,QAAI,IAAI;AACR,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,YAAY,KAAK,aAAa;AACpC,UAAM,gBAAgB,KAAK,iBAAiB;AAC5C,UAAM,UAAU,KAAK,WAAW;AAChC,UAAM,mBAAmB,MAAM,KAAK,GAAG,cAAc,wBAAwB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,OAAO;AACzL,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,mBAAmB,OAAO;AAAA,QAC/B,CAAC,IAAI,GAAG;AAAA,QACR,aAAa;AAAA,QACb,CAAC,kBAAkB,IAAI,EAAE,GAAG;AAAA,QAC5B,CAAC,mBAAmB,KAAK,EAAE,GAAG;AAAA,QAC9B,CAAC,kBAAkB,IAAI,EAAE,GAAG;AAAA,QAC5B,sBAAsB;AAAA,QACtB,sBAAsB;AAAA,MACxB,CAAC;AAAA,IACH,GAAG,EAAE,OAAO,OAAO,OAAO;AAAA,MACxB,KAAK;AAAA,MACL,MAAM;AAAA,MACN,cAAc;AAAA,MACd,OAAO;AAAA,IACT,GAAG,mBAAmB,GAAG,MAAM,KAAK;AAAA,MAClC;AAAA,IACF,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU,EAAE,UAAU,MAAM,EAAE,OAAO;AAAA,MAC9C,OAAO;AAAA,IACT,GAAG,EAAE,SAAS;AAAA,MACZ,OAAO;AAAA,MACP,IAAI,GAAG,OAAO,IAAI,KAAK;AAAA,MACvB,cAAc,SAAS,QAAQ,CAAC,OAAO,MAAM;AAAA,MAC7C,MAAM;AAAA,MACN,gBAAgB;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV,UAAU,UAAU,gBAAgB,IAAI;AAAA,MACxC,OAAO,YAAY,KAAK,KAAK;AAAA,MAC7B,cAAc;AAAA,MACd,KAAK,CAAAA,QAAM,UAAU,KAAK,IAAIA;AAAA,MAC9B,SAAS,KAAK,QAAQ,KAAK;AAAA,MAC3B,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK,QAAQ,KAAK;AAAA,MAC3B,WAAW,KAAK,UAAU,KAAK;AAAA,MAC/B,SAAS,KAAK;AAAA,IAChB,CAAC,CAAC,GAAG,KAAK,cAAc,KAAK,KAAK,EAAE,OAAO;AAAA,MACzC,OAAO;AAAA,IACT,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,MACd,KAAK;AAAA,MACL,OAAO;AAAA,QACL,yBAAyB;AAAA,QACzB,gCAAgC,CAAC;AAAA,MACnC;AAAA,IACF,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,SAAS,CAAC,cAAc;AAAA,MACxB,cAAc,CAAC,mBAAmB;AAAA,MAClC,UAAU,CAAC,mBAAmB;AAAA,IAChC;AAAA,EACF;AACF;AACA,IAAI,WAAW;AACf,SAAS,QAAQ;AAAA,EACf,KAAK;AAAA,EACL,IAAI;AACN;", "names": ["el"]}