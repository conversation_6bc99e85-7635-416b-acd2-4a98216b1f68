{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nfunction AppComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9)(3, \"h1\", 10)(4, \"span\", 11);\n    i0.ɵɵtext(5, \"\\uD83C\\uDF38\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 12);\n    i0.ɵɵtext(7, \"OnlyWomans\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"p\", 13);\n    i0.ɵɵtext(9, \"Fashion for Every Woman\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 14);\n    i0.ɵɵelement(11, \"div\", 15)(12, \"div\", 15)(13, \"div\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\", 16);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r0.loadingText);\n  }\n}\nfunction AppComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(router) {\n      this.router = router;\n      this.title = 'OnlyWomans - Fashion for Every Woman';\n      this.isLoading = true;\n      this.loadingText = 'Loading your fashion world...';\n      this.loadingTexts = ['Loading your fashion world...', 'Curating the latest trends...', 'Preparing your style journey...', 'Setting up your wardrobe...', 'Almost ready to shop...'];\n    }\n    ngOnInit() {\n      this.startLoadingSequence();\n      this.setupRouterEvents();\n    }\n    startLoadingSequence() {\n      let textIndex = 0;\n      // Change loading text every 800ms\n      const textInterval = setInterval(() => {\n        textIndex = (textIndex + 1) % this.loadingTexts.length;\n        this.loadingText = this.loadingTexts[textIndex];\n      }, 800);\n      // Hide loading screen after 3 seconds\n      setTimeout(() => {\n        clearInterval(textInterval);\n        this.isLoading = false;\n      }, 3000);\n    }\n    setupRouterEvents() {\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n        // Add page-specific classes or analytics here\n        this.updatePageMeta(event.url);\n      });\n    }\n    updatePageMeta(url) {\n      // Update page title and meta tags based on route\n      const pageTitles = {\n        '/': 'OnlyWomans - Fashion for Every Woman',\n        '/shop': 'Shop - OnlyWomans',\n        '/categories': 'Categories - OnlyWomans',\n        '/beauty': 'Beauty & Makeup - OnlyWomans',\n        '/accessories': 'Accessories - OnlyWomans',\n        '/ethnic': 'Ethnic Wear - OnlyWomans',\n        '/western': 'Western Wear - OnlyWomans',\n        '/cart': 'Shopping Cart - OnlyWomans',\n        '/wishlist': 'My Wishlist - OnlyWomans',\n        '/profile': 'My Profile - OnlyWomans'\n      };\n      const pageTitle = pageTitles[url] || this.title;\n      document.title = pageTitle;\n      // Update meta description\n      const metaDescription = document.querySelector('meta[name=\"description\"]');\n      if (metaDescription) {\n        metaDescription.setAttribute('content', 'Discover the latest in women\\'s fashion, beauty, and accessories. Shop ethnic wear, western outfits, makeup, jewelry and more at OnlyWomans.');\n      }\n    }\n    static {\n      this.ɵfac = function AppComponent_Factory(t) {\n        return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppComponent,\n        selectors: [[\"ow-root\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 7,\n        vars: 4,\n        consts: [[1, \"app-container\"], [\"class\", \"loading-screen\", 4, \"ngIf\"], [\"class\", \"main-content fade-in-up\", 4, \"ngIf\"], [1, \"bg-decorations\"], [1, \"decoration\", \"decoration-1\"], [1, \"decoration\", \"decoration-2\"], [1, \"decoration\", \"decoration-3\"], [1, \"loading-screen\"], [1, \"loading-content\"], [1, \"logo-container\"], [1, \"app-logo\"], [1, \"logo-icon\"], [1, \"logo-text\"], [1, \"tagline\"], [1, \"loading-spinner\"], [1, \"spinner-ring\"], [1, \"loading-text\"], [1, \"main-content\", \"fade-in-up\"]],\n        template: function AppComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, AppComponent_div_1_Template, 16, 1, \"div\", 1)(2, AppComponent_div_2_Template, 2, 0, \"div\", 2);\n            i0.ɵɵelementStart(3, \"div\", 3);\n            i0.ɵɵelement(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          }\n        },\n        dependencies: [CommonModule, i2.NgIf, RouterOutlet],\n        styles: [\".app-container[_ngcontent-%COMP%]{min-height:100vh;position:relative;overflow-x:hidden;background:linear-gradient(135deg,var(--primary-50) 0%,var(--secondary-50) 100%)}.loading-screen[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,var(--primary-100),var(--secondary-100));display:flex;align-items:center;justify-content:center;z-index:9999}.loading-content[_ngcontent-%COMP%]{text-align:center;animation:fadeInUp .8s ease-out}.logo-container[_ngcontent-%COMP%]{margin-bottom:var(--space-2xl)}.app-logo[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:var(--space-md);font-size:var(--font-size-4xl);font-weight:var(--font-weight-bold);color:var(--primary-600);margin:0 0 var(--space-md) 0}.logo-icon[_ngcontent-%COMP%]{font-size:var(--font-size-5xl);animation:pulse 2s infinite}.logo-text[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--primary-600),var(--secondary-600));-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.tagline[_ngcontent-%COMP%]{font-size:var(--font-size-lg);color:var(--text-secondary);font-weight:var(--font-weight-medium);margin:0}.loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;gap:var(--space-sm);margin-bottom:var(--space-xl)}.spinner-ring[_ngcontent-%COMP%]{width:12px;height:12px;border-radius:50%;background:var(--primary-500);animation:_ngcontent-%COMP%_bounce 1.4s ease-in-out infinite both}.spinner-ring[_ngcontent-%COMP%]:nth-child(1){animation-delay:-.32s}.spinner-ring[_ngcontent-%COMP%]:nth-child(2){animation-delay:-.16s}@keyframes _ngcontent-%COMP%_bounce{0%,80%,to{transform:scale(0);opacity:.5}40%{transform:scale(1);opacity:1}}.loading-text[_ngcontent-%COMP%]{font-size:var(--font-size-base);color:var(--text-secondary);margin:0;animation:pulse 2s infinite}.main-content[_ngcontent-%COMP%]{position:relative;z-index:1}.bg-decorations[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;pointer-events:none;z-index:0}.decoration[_ngcontent-%COMP%]{position:absolute;border-radius:50%;opacity:.1;animation:_ngcontent-%COMP%_float 6s ease-in-out infinite}.decoration-1[_ngcontent-%COMP%]{width:200px;height:200px;background:var(--primary-300);top:10%;right:10%;animation-delay:0s}.decoration-2[_ngcontent-%COMP%]{width:150px;height:150px;background:var(--secondary-300);bottom:20%;left:5%;animation-delay:2s}.decoration-3[_ngcontent-%COMP%]{width:100px;height:100px;background:var(--accent-300);top:50%;left:50%;animation-delay:4s}@keyframes _ngcontent-%COMP%_float{0%,to{transform:translateY(0) rotate(0)}50%{transform:translateY(-20px) rotate(180deg)}}.fade-in-up[_ngcontent-%COMP%]{animation:fadeInUp .6s ease-out}@media (max-width: 768px){.app-logo[_ngcontent-%COMP%]{font-size:var(--font-size-3xl);flex-direction:column;gap:var(--space-sm)}.logo-icon[_ngcontent-%COMP%]{font-size:var(--font-size-4xl)}.decoration[_ngcontent-%COMP%]{display:none}}\"]\n      });\n    }\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}