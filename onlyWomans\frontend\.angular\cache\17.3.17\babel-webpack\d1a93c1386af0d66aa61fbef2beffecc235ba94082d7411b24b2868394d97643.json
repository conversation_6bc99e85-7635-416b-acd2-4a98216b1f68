{"ast": null, "code": "import { popScheduler } from '../util/args';\nimport { from } from './from';\nexport function of(...args) {\n  const scheduler = popScheduler(args);\n  return from(args, scheduler);\n}", "map": {"version": 3, "names": ["popScheduler", "from", "of", "args", "scheduler"], "sources": ["E:/Fahion/DFashion/onlyWomans/frontend/node_modules/rxjs/dist/esm/internal/observable/of.js"], "sourcesContent": ["import { popScheduler } from '../util/args';\nimport { from } from './from';\nexport function of(...args) {\n    const scheduler = popScheduler(args);\n    return from(args, scheduler);\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,cAAc;AAC3C,SAASC,IAAI,QAAQ,QAAQ;AAC7B,OAAO,SAASC,EAAEA,CAAC,GAAGC,IAAI,EAAE;EACxB,MAAMC,SAAS,GAAGJ,YAAY,CAACG,IAAI,CAAC;EACpC,OAAOF,IAAI,CAACE,IAAI,EAAEC,SAAS,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}