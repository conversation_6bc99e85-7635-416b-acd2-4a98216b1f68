{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/button-active-Bxcnevju.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as writeTask } from './index-B_U9CtaY.js';\nimport { h as hapticSelectionEnd, a as hapticSelectionChanged, b as hapticSelectionStart } from './haptic-DzAMWJuk.js';\nimport { createGesture } from './index-CfgBF1SE.js';\nconst createButtonActiveGesture = (el, isButton) => {\n  let currentTouchedButton;\n  let initialTouchedButton;\n  const activateButtonAtPoint = (x, y, hapticFeedbackFn) => {\n    if (typeof document === 'undefined') {\n      return;\n    }\n    const target = document.elementFromPoint(x, y);\n    if (!target || !isButton(target) || target.disabled) {\n      clearActiveButton();\n      return;\n    }\n    if (target !== currentTouchedButton) {\n      clearActiveButton();\n      setActiveButton(target, hapticFeedbackFn);\n    }\n  };\n  const setActiveButton = (button, hapticFeedbackFn) => {\n    currentTouchedButton = button;\n    if (!initialTouchedButton) {\n      initialTouchedButton = currentTouchedButton;\n    }\n    const buttonToModify = currentTouchedButton;\n    writeTask(() => buttonToModify.classList.add('ion-activated'));\n    hapticFeedbackFn();\n  };\n  const clearActiveButton = (dispatchClick = false) => {\n    if (!currentTouchedButton) {\n      return;\n    }\n    const buttonToModify = currentTouchedButton;\n    writeTask(() => buttonToModify.classList.remove('ion-activated'));\n    /**\n     * Clicking on one button, but releasing on another button\n     * does not dispatch a click event in browsers, so we\n     * need to do it manually here. Some browsers will\n     * dispatch a click if clicking on one button, dragging over\n     * another button, and releasing on the original button. In that\n     * case, we need to make sure we do not cause a double click there.\n     */\n    if (dispatchClick && initialTouchedButton !== currentTouchedButton) {\n      currentTouchedButton.click();\n    }\n    currentTouchedButton = undefined;\n  };\n  return createGesture({\n    el,\n    gestureName: 'buttonActiveDrag',\n    threshold: 0,\n    onStart: ev => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionStart),\n    onMove: ev => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionChanged),\n    onEnd: () => {\n      clearActiveButton(true);\n      hapticSelectionEnd();\n      initialTouchedButton = undefined;\n    }\n  });\n};\nexport { createButtonActiveGesture as c };"], "mappings": ";;;;;;;;;;;;;AAMA,IAAM,4BAA4B,CAAC,IAAI,aAAa;AAClD,MAAI;AACJ,MAAI;AACJ,QAAM,wBAAwB,CAAC,GAAG,GAAG,qBAAqB;AACxD,QAAI,OAAO,aAAa,aAAa;AACnC;AAAA,IACF;AACA,UAAM,SAAS,SAAS,iBAAiB,GAAG,CAAC;AAC7C,QAAI,CAAC,UAAU,CAAC,SAAS,MAAM,KAAK,OAAO,UAAU;AACnD,wBAAkB;AAClB;AAAA,IACF;AACA,QAAI,WAAW,sBAAsB;AACnC,wBAAkB;AAClB,sBAAgB,QAAQ,gBAAgB;AAAA,IAC1C;AAAA,EACF;AACA,QAAM,kBAAkB,CAAC,QAAQ,qBAAqB;AACpD,2BAAuB;AACvB,QAAI,CAAC,sBAAsB;AACzB,6BAAuB;AAAA,IACzB;AACA,UAAM,iBAAiB;AACvB,cAAU,MAAM,eAAe,UAAU,IAAI,eAAe,CAAC;AAC7D,qBAAiB;AAAA,EACnB;AACA,QAAM,oBAAoB,CAAC,gBAAgB,UAAU;AACnD,QAAI,CAAC,sBAAsB;AACzB;AAAA,IACF;AACA,UAAM,iBAAiB;AACvB,cAAU,MAAM,eAAe,UAAU,OAAO,eAAe,CAAC;AAShE,QAAI,iBAAiB,yBAAyB,sBAAsB;AAClE,2BAAqB,MAAM;AAAA,IAC7B;AACA,2BAAuB;AAAA,EACzB;AACA,SAAO,cAAc;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,IACb,WAAW;AAAA,IACX,SAAS,QAAM,sBAAsB,GAAG,UAAU,GAAG,UAAU,oBAAoB;AAAA,IACnF,QAAQ,QAAM,sBAAsB,GAAG,UAAU,GAAG,UAAU,sBAAsB;AAAA,IACpF,OAAO,MAAM;AACX,wBAAkB,IAAI;AACtB,yBAAmB;AACnB,6BAAuB;AAAA,IACzB;AAAA,EACF,CAAC;AACH;", "names": []}