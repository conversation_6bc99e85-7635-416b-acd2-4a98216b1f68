{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/index-D8sncTHY.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index-ZjP4CjeZ.js';\nimport { MENU_BACK_BUTTON_PRIORITY } from './hardware-back-button-DcH0BbDp.js';\nimport { e as getIonMode, m as printIonWarning } from './index-B_U9CtaY.js';\nimport { c as componentOnReady } from './helpers-1O4D2b7y.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\n\n/**\n * baseAnimation\n * Base class which is extended by the various types. Each\n * type will provide their own animations for open and close\n * and registers itself with Menu.\n */\nconst baseAnimation = isIos => {\n  // https://material.io/guidelines/motion/movement.html#movement-movement-in-out-of-screen-bounds\n  // https://material.io/guidelines/motion/duration-easing.html#duration-easing-natural-easing-curves\n  /**\n   * \"Apply the sharp curve to items temporarily leaving the screen that may return\n   * from the same exit point. When they return, use the deceleration curve. On mobile,\n   * this transition typically occurs over 300ms\" -- MD Motion Guide\n   */\n  return createAnimation().duration(isIos ? 400 : 300);\n};\n\n/**\n * Menu Overlay Type\n * The menu slides over the content. The content\n * itself, which is under the menu, does not move.\n */\nconst menuOverlayAnimation = menu => {\n  let closedX;\n  let openedX;\n  const width = menu.width + 8;\n  const menuAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  if (menu.isEndSide) {\n    // right side\n    closedX = width + 'px';\n    openedX = '0px';\n  } else {\n    // left side\n    closedX = -width + 'px';\n    openedX = '0px';\n  }\n  menuAnimation.addElement(menu.menuInnerEl).fromTo('transform', `translateX(${closedX})`, `translateX(${openedX})`);\n  const mode = getIonMode(menu);\n  const isIos = mode === 'ios';\n  const opacity = isIos ? 0.2 : 0.25;\n  backdropAnimation.addElement(menu.backdropEl).fromTo('opacity', 0.01, opacity);\n  return baseAnimation(isIos).addAnimation([menuAnimation, backdropAnimation]);\n};\n\n/**\n * Menu Push Type\n * The content slides over to reveal the menu underneath.\n * The menu itself also slides over to reveal its bad self.\n */\nconst menuPushAnimation = menu => {\n  let contentOpenedX;\n  let menuClosedX;\n  const mode = getIonMode(menu);\n  const width = menu.width;\n  if (menu.isEndSide) {\n    contentOpenedX = -width + 'px';\n    menuClosedX = width + 'px';\n  } else {\n    contentOpenedX = width + 'px';\n    menuClosedX = -width + 'px';\n  }\n  const menuAnimation = createAnimation().addElement(menu.menuInnerEl).fromTo('transform', `translateX(${menuClosedX})`, 'translateX(0px)');\n  const contentAnimation = createAnimation().addElement(menu.contentEl).fromTo('transform', 'translateX(0px)', `translateX(${contentOpenedX})`);\n  const backdropAnimation = createAnimation().addElement(menu.backdropEl).fromTo('opacity', 0.01, 0.32);\n  return baseAnimation(mode === 'ios').addAnimation([menuAnimation, contentAnimation, backdropAnimation]);\n};\n\n/**\n * Menu Reveal Type\n * The content slides over to reveal the menu underneath.\n * The menu itself, which is under the content, does not move.\n */\nconst menuRevealAnimation = menu => {\n  const mode = getIonMode(menu);\n  const openedX = menu.width * (menu.isEndSide ? -1 : 1) + 'px';\n  const contentOpen = createAnimation().addElement(menu.contentEl) // REVIEW\n  .fromTo('transform', 'translateX(0px)', `translateX(${openedX})`);\n  return baseAnimation(mode === 'ios').addAnimation(contentOpen);\n};\nconst createMenuController = () => {\n  const menuAnimations = new Map();\n  const menus = [];\n  const open = async menu => {\n    const menuEl = await get(menu, true);\n    if (menuEl) {\n      return menuEl.open();\n    }\n    return false;\n  };\n  const close = async menu => {\n    const menuEl = await (menu !== undefined ? get(menu, true) : getOpen());\n    if (menuEl !== undefined) {\n      return menuEl.close();\n    }\n    return false;\n  };\n  const toggle = async menu => {\n    const menuEl = await get(menu, true);\n    if (menuEl) {\n      return menuEl.toggle();\n    }\n    return false;\n  };\n  const enable = async (shouldEnable, menu) => {\n    const menuEl = await get(menu);\n    if (menuEl) {\n      menuEl.disabled = !shouldEnable;\n    }\n    return menuEl;\n  };\n  const swipeGesture = async (shouldEnable, menu) => {\n    const menuEl = await get(menu);\n    if (menuEl) {\n      menuEl.swipeGesture = shouldEnable;\n    }\n    return menuEl;\n  };\n  const isOpen = async menu => {\n    if (menu != null) {\n      const menuEl = await get(menu);\n      // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n      return menuEl !== undefined && menuEl.isOpen();\n    } else {\n      const menuEl = await getOpen();\n      return menuEl !== undefined;\n    }\n  };\n  const isEnabled = async menu => {\n    const menuEl = await get(menu);\n    if (menuEl) {\n      return !menuEl.disabled;\n    }\n    return false;\n  };\n  /**\n   * Finds and returns the menu specified by \"menu\" if registered.\n   * @param menu - The side or ID of the desired menu\n   * @param logOnMultipleSideMenus - If true, this function will log a warning\n   * if \"menu\" is a side but multiple menus on the same side were found. Since this function\n   * is used in multiple places, we default this log to false so that the calling\n   * functions can choose whether or not it is appropriate to log this warning.\n   */\n  const get = async (menu, logOnMultipleSideMenus = false) => {\n    await waitUntilReady();\n    if (menu === 'start' || menu === 'end') {\n      // there could be more than one menu on the same side\n      // so first try to get the enabled one\n      const menuRefs = menus.filter(m => m.side === menu && !m.disabled);\n      if (menuRefs.length >= 1) {\n        if (menuRefs.length > 1 && logOnMultipleSideMenus) {\n          printIonWarning(`menuController queried for a menu on the \"${menu}\" side, but ${menuRefs.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`, menuRefs.map(m => m.el));\n        }\n        return menuRefs[0].el;\n      }\n      // didn't find a menu side that is enabled\n      // so try to get the first menu side found\n      const sideMenuRefs = menus.filter(m => m.side === menu);\n      if (sideMenuRefs.length >= 1) {\n        if (sideMenuRefs.length > 1 && logOnMultipleSideMenus) {\n          printIonWarning(`menuController queried for a menu on the \"${menu}\" side, but ${sideMenuRefs.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`, sideMenuRefs.map(m => m.el));\n        }\n        return sideMenuRefs[0].el;\n      }\n    } else if (menu != null) {\n      // the menuId was not left or right\n      // so try to get the menu by its \"id\"\n      return find(m => m.menuId === menu);\n    }\n    // return the first enabled menu\n    const menuEl = find(m => !m.disabled);\n    if (menuEl) {\n      return menuEl;\n    }\n    // get the first menu in the array, if one exists\n    return menus.length > 0 ? menus[0].el : undefined;\n  };\n  /**\n   * Get the instance of the opened menu. Returns `null` if a menu is not found.\n   */\n  const getOpen = async () => {\n    await waitUntilReady();\n    return _getOpenSync();\n  };\n  /**\n   * Get all menu instances.\n   */\n  const getMenus = async () => {\n    await waitUntilReady();\n    return getMenusSync();\n  };\n  /**\n   * Get whether or not a menu is animating. Returns `true` if any\n   * menu is currently animating.\n   */\n  const isAnimating = async () => {\n    await waitUntilReady();\n    return isAnimatingSync();\n  };\n  const registerAnimation = (name, animation) => {\n    menuAnimations.set(name, animation);\n  };\n  const _register = menu => {\n    if (menus.indexOf(menu) < 0) {\n      menus.push(menu);\n    }\n  };\n  const _unregister = menu => {\n    const index = menus.indexOf(menu);\n    if (index > -1) {\n      menus.splice(index, 1);\n    }\n  };\n  const _setOpen = async (menu, shouldOpen, animated, role) => {\n    if (isAnimatingSync()) {\n      return false;\n    }\n    if (shouldOpen) {\n      const openedMenu = await getOpen();\n      if (openedMenu && menu.el !== openedMenu) {\n        await openedMenu.setOpen(false, false);\n      }\n    }\n    return menu._setOpen(shouldOpen, animated, role);\n  };\n  const _createAnimation = (type, menuCmp) => {\n    const animationBuilder = menuAnimations.get(type); // TODO(FW-2832): type\n    if (!animationBuilder) {\n      throw new Error('animation not registered');\n    }\n    const animation = animationBuilder(menuCmp);\n    return animation;\n  };\n  const _getOpenSync = () => {\n    return find(m => m._isOpen);\n  };\n  const getMenusSync = () => {\n    return menus.map(menu => menu.el);\n  };\n  const isAnimatingSync = () => {\n    return menus.some(menu => menu.isAnimating);\n  };\n  const find = predicate => {\n    const instance = menus.find(predicate);\n    if (instance !== undefined) {\n      return instance.el;\n    }\n    return undefined;\n  };\n  const waitUntilReady = () => {\n    return Promise.all(Array.from(document.querySelectorAll('ion-menu')).map(menu => new Promise(resolve => componentOnReady(menu, resolve))));\n  };\n  registerAnimation('reveal', menuRevealAnimation);\n  registerAnimation('push', menuPushAnimation);\n  registerAnimation('overlay', menuOverlayAnimation);\n  doc === null || doc === void 0 ? void 0 : doc.addEventListener('ionBackButton', ev => {\n    const openMenu = _getOpenSync();\n    if (openMenu) {\n      ev.detail.register(MENU_BACK_BUTTON_PRIORITY, () => {\n        return openMenu.close();\n      });\n    }\n  });\n  return {\n    registerAnimation,\n    get,\n    getMenus,\n    getOpen,\n    isEnabled,\n    swipeGesture,\n    isAnimating,\n    isOpen,\n    enable,\n    toggle,\n    close,\n    open,\n    _getOpenSync,\n    _createAnimation,\n    _register,\n    _unregister,\n    _setOpen\n  };\n};\nconst menuController = /*@__PURE__*/createMenuController();\nexport { menuController as m };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAeA,IAAM,gBAAgB,WAAS;AAQ7B,SAAO,gBAAgB,EAAE,SAAS,QAAQ,MAAM,GAAG;AACrD;AAOA,IAAM,uBAAuB,UAAQ;AACnC,MAAI;AACJ,MAAI;AACJ,QAAM,QAAQ,KAAK,QAAQ;AAC3B,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,MAAI,KAAK,WAAW;AAElB,cAAU,QAAQ;AAClB,cAAU;AAAA,EACZ,OAAO;AAEL,cAAU,CAAC,QAAQ;AACnB,cAAU;AAAA,EACZ;AACA,gBAAc,WAAW,KAAK,WAAW,EAAE,OAAO,aAAa,cAAc,OAAO,KAAK,cAAc,OAAO,GAAG;AACjH,QAAM,OAAO,WAAW,IAAI;AAC5B,QAAM,QAAQ,SAAS;AACvB,QAAM,UAAU,QAAQ,MAAM;AAC9B,oBAAkB,WAAW,KAAK,UAAU,EAAE,OAAO,WAAW,MAAM,OAAO;AAC7E,SAAO,cAAc,KAAK,EAAE,aAAa,CAAC,eAAe,iBAAiB,CAAC;AAC7E;AAOA,IAAM,oBAAoB,UAAQ;AAChC,MAAI;AACJ,MAAI;AACJ,QAAM,OAAO,WAAW,IAAI;AAC5B,QAAM,QAAQ,KAAK;AACnB,MAAI,KAAK,WAAW;AAClB,qBAAiB,CAAC,QAAQ;AAC1B,kBAAc,QAAQ;AAAA,EACxB,OAAO;AACL,qBAAiB,QAAQ;AACzB,kBAAc,CAAC,QAAQ;AAAA,EACzB;AACA,QAAM,gBAAgB,gBAAgB,EAAE,WAAW,KAAK,WAAW,EAAE,OAAO,aAAa,cAAc,WAAW,KAAK,iBAAiB;AACxI,QAAM,mBAAmB,gBAAgB,EAAE,WAAW,KAAK,SAAS,EAAE,OAAO,aAAa,mBAAmB,cAAc,cAAc,GAAG;AAC5I,QAAM,oBAAoB,gBAAgB,EAAE,WAAW,KAAK,UAAU,EAAE,OAAO,WAAW,MAAM,IAAI;AACpG,SAAO,cAAc,SAAS,KAAK,EAAE,aAAa,CAAC,eAAe,kBAAkB,iBAAiB,CAAC;AACxG;AAOA,IAAM,sBAAsB,UAAQ;AAClC,QAAM,OAAO,WAAW,IAAI;AAC5B,QAAM,UAAU,KAAK,SAAS,KAAK,YAAY,KAAK,KAAK;AACzD,QAAM,cAAc,gBAAgB,EAAE,WAAW,KAAK,SAAS,EAC9D,OAAO,aAAa,mBAAmB,cAAc,OAAO,GAAG;AAChE,SAAO,cAAc,SAAS,KAAK,EAAE,aAAa,WAAW;AAC/D;AACA,IAAM,uBAAuB,MAAM;AACjC,QAAM,iBAAiB,oBAAI,IAAI;AAC/B,QAAM,QAAQ,CAAC;AACf,QAAM,OAAO,CAAM,SAAQ;AACzB,UAAM,SAAS,MAAM,IAAI,MAAM,IAAI;AACnC,QAAI,QAAQ;AACV,aAAO,OAAO,KAAK;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,CAAM,SAAQ;AAC1B,UAAM,SAAS,MAAO,SAAS,SAAY,IAAI,MAAM,IAAI,IAAI,QAAQ;AACrE,QAAI,WAAW,QAAW;AACxB,aAAO,OAAO,MAAM;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAM,SAAQ;AAC3B,UAAM,SAAS,MAAM,IAAI,MAAM,IAAI;AACnC,QAAI,QAAQ;AACV,aAAO,OAAO,OAAO;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAO,cAAc,SAAS;AAC3C,UAAM,SAAS,MAAM,IAAI,IAAI;AAC7B,QAAI,QAAQ;AACV,aAAO,WAAW,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AACA,QAAM,eAAe,CAAO,cAAc,SAAS;AACjD,UAAM,SAAS,MAAM,IAAI,IAAI;AAC7B,QAAI,QAAQ;AACV,aAAO,eAAe;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAM,SAAQ;AAC3B,QAAI,QAAQ,MAAM;AAChB,YAAM,SAAS,MAAM,IAAI,IAAI;AAE7B,aAAO,WAAW,UAAa,OAAO,OAAO;AAAA,IAC/C,OAAO;AACL,YAAM,SAAS,MAAM,QAAQ;AAC7B,aAAO,WAAW;AAAA,IACpB;AAAA,EACF;AACA,QAAM,YAAY,CAAM,SAAQ;AAC9B,UAAM,SAAS,MAAM,IAAI,IAAI;AAC7B,QAAI,QAAQ;AACV,aAAO,CAAC,OAAO;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AASA,QAAM,MAAM,CAAO,MAAM,yBAAyB,UAAU;AAC1D,UAAM,eAAe;AACrB,QAAI,SAAS,WAAW,SAAS,OAAO;AAGtC,YAAM,WAAW,MAAM,OAAO,OAAK,EAAE,SAAS,QAAQ,CAAC,EAAE,QAAQ;AACjE,UAAI,SAAS,UAAU,GAAG;AACxB,YAAI,SAAS,SAAS,KAAK,wBAAwB;AACjD,0BAAgB,6CAA6C,IAAI,eAAe,SAAS,MAAM,oJAAoJ,SAAS,IAAI,OAAK,EAAE,EAAE,CAAC;AAAA,QAC5Q;AACA,eAAO,SAAS,CAAC,EAAE;AAAA,MACrB;AAGA,YAAM,eAAe,MAAM,OAAO,OAAK,EAAE,SAAS,IAAI;AACtD,UAAI,aAAa,UAAU,GAAG;AAC5B,YAAI,aAAa,SAAS,KAAK,wBAAwB;AACrD,0BAAgB,6CAA6C,IAAI,eAAe,aAAa,MAAM,oJAAoJ,aAAa,IAAI,OAAK,EAAE,EAAE,CAAC;AAAA,QACpR;AACA,eAAO,aAAa,CAAC,EAAE;AAAA,MACzB;AAAA,IACF,WAAW,QAAQ,MAAM;AAGvB,aAAO,KAAK,OAAK,EAAE,WAAW,IAAI;AAAA,IACpC;AAEA,UAAM,SAAS,KAAK,OAAK,CAAC,EAAE,QAAQ;AACpC,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AAEA,WAAO,MAAM,SAAS,IAAI,MAAM,CAAC,EAAE,KAAK;AAAA,EAC1C;AAIA,QAAM,UAAU,MAAY;AAC1B,UAAM,eAAe;AACrB,WAAO,aAAa;AAAA,EACtB;AAIA,QAAM,WAAW,MAAY;AAC3B,UAAM,eAAe;AACrB,WAAO,aAAa;AAAA,EACtB;AAKA,QAAM,cAAc,MAAY;AAC9B,UAAM,eAAe;AACrB,WAAO,gBAAgB;AAAA,EACzB;AACA,QAAM,oBAAoB,CAAC,MAAM,cAAc;AAC7C,mBAAe,IAAI,MAAM,SAAS;AAAA,EACpC;AACA,QAAM,YAAY,UAAQ;AACxB,QAAI,MAAM,QAAQ,IAAI,IAAI,GAAG;AAC3B,YAAM,KAAK,IAAI;AAAA,IACjB;AAAA,EACF;AACA,QAAM,cAAc,UAAQ;AAC1B,UAAM,QAAQ,MAAM,QAAQ,IAAI;AAChC,QAAI,QAAQ,IAAI;AACd,YAAM,OAAO,OAAO,CAAC;AAAA,IACvB;AAAA,EACF;AACA,QAAM,WAAW,CAAO,MAAM,YAAY,UAAU,SAAS;AAC3D,QAAI,gBAAgB,GAAG;AACrB,aAAO;AAAA,IACT;AACA,QAAI,YAAY;AACd,YAAM,aAAa,MAAM,QAAQ;AACjC,UAAI,cAAc,KAAK,OAAO,YAAY;AACxC,cAAM,WAAW,QAAQ,OAAO,KAAK;AAAA,MACvC;AAAA,IACF;AACA,WAAO,KAAK,SAAS,YAAY,UAAU,IAAI;AAAA,EACjD;AACA,QAAM,mBAAmB,CAAC,MAAM,YAAY;AAC1C,UAAM,mBAAmB,eAAe,IAAI,IAAI;AAChD,QAAI,CAAC,kBAAkB;AACrB,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC5C;AACA,UAAM,YAAY,iBAAiB,OAAO;AAC1C,WAAO;AAAA,EACT;AACA,QAAM,eAAe,MAAM;AACzB,WAAO,KAAK,OAAK,EAAE,OAAO;AAAA,EAC5B;AACA,QAAM,eAAe,MAAM;AACzB,WAAO,MAAM,IAAI,UAAQ,KAAK,EAAE;AAAA,EAClC;AACA,QAAM,kBAAkB,MAAM;AAC5B,WAAO,MAAM,KAAK,UAAQ,KAAK,WAAW;AAAA,EAC5C;AACA,QAAM,OAAO,eAAa;AACxB,UAAM,WAAW,MAAM,KAAK,SAAS;AACrC,QAAI,aAAa,QAAW;AAC1B,aAAO,SAAS;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AACA,QAAM,iBAAiB,MAAM;AAC3B,WAAO,QAAQ,IAAI,MAAM,KAAK,SAAS,iBAAiB,UAAU,CAAC,EAAE,IAAI,UAAQ,IAAI,QAAQ,aAAW,iBAAiB,MAAM,OAAO,CAAC,CAAC,CAAC;AAAA,EAC3I;AACA,oBAAkB,UAAU,mBAAmB;AAC/C,oBAAkB,QAAQ,iBAAiB;AAC3C,oBAAkB,WAAW,oBAAoB;AACjD,UAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,iBAAiB,iBAAiB,QAAM;AACpF,UAAM,WAAW,aAAa;AAC9B,QAAI,UAAU;AACZ,SAAG,OAAO,SAAS,2BAA2B,MAAM;AAClD,eAAO,SAAS,MAAM;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,iBAA8B,qBAAqB;", "names": []}